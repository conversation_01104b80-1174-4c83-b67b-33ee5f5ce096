openapi: 3.0.3
info:
  title: 'Obvio Obvio Documentation'
  description: ''
  version: 1.0.0
servers:
  -
    url: 'http://localhost'
tags:
  -
    name: Endpoints
    description: ''
paths:
  /api/give-my-php-info:
    get:
      summary: ''
      operationId: getApiGiveMyPhpInfo
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: ''
      tags:
        - Endpoints
      security: []
  /api/give-my-php-v:
    get:
      summary: ''
      operationId: getApiGiveMyPhpV
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  php_version: 8.4.6
                properties:
                  php_version:
                    type: string
                    example: 8.4.6
      tags:
        - Endpoints
      security: []
  /api/login:
    post:
      summary: ''
      operationId: postApiLogin
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                password:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - email
                - password
      security: []
  /api/register:
    post:
      summary: ''
      operationId: postApiRegister
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                profile_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                first_name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                last_name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                username:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                password:
                  type: string
                  description: 'Must be at least 6 characters.'
                  example: ']|{+-0pBNvYg'
                  nullable: false
                organization:
                  type: object
                  description: ''
                  example: []
                  nullable: false
                  properties:
                    name:
                      type: string
                      description: ''
                      example: architecto
                      nullable: false
                  required:
                    - name
              required:
                - email
                - profile_id
                - first_name
                - last_name
                - username
                - password
      security: []
  /api/telegram/receive-message:
    post:
      summary: ''
      operationId: postApiTelegramReceiveMessage
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/telegram/{bot_id}/receive':
    post:
      summary: ''
      operationId: postApiTelegramBot_idReceive
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: bot_id
        description: 'The ID of the bot.'
        example: architecto
        required: true
        schema:
          type: string
  /api/whatsapp/webhook:
    get:
      summary: 'Handle WhatsApp webhook verification (GET request)'
      operationId: handleWhatsAppWebhookVerificationGETRequest
      description: ''
      parameters: []
      responses:
        403:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  error: Forbidden
                properties:
                  error:
                    type: string
                    example: Forbidden
      tags:
        - Endpoints
      security: []
    post:
      summary: 'Handle WhatsApp webhook messages (POST request)'
      operationId: handleWhatsAppWebhookMessagesPOSTRequest
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  /api/user:
    get:
      summary: ''
      operationId: getApiUser
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  /api/logout:
    post:
      summary: ''
      operationId: postApiLogout
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  /api/logout_all_sessions:
    post:
      summary: ''
      operationId: postApiLogout_all_sessions
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  /api/user/delete:
    delete:
      summary: ''
      operationId: deleteApiUserDelete
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                confirmation:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - confirmation
      security: []
  /api/users:
    get:
      summary: ''
      operationId: getApiUsers
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiUsers
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                profile_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                first_name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                last_name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                username:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                password:
                  type: string
                  description: 'Must be at least 6 characters.'
                  example: ']|{+-0pBNvYg'
                  nullable: false
              required:
                - email
                - profile_id
                - first_name
                - last_name
                - username
      security: []
  '/api/users/{id}':
    get:
      summary: ''
      operationId: getApiUsersId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiUsersId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the user.'
        example: architecto
        required: true
        schema:
          type: string
  /api/profiles:
    get:
      summary: ''
      operationId: getApiProfiles
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiProfiles
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/profiles/{id}':
    get:
      summary: ''
      operationId: getApiProfilesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiProfilesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiProfilesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the profile.'
        example: architecto
        required: true
        schema:
          type: string
  /api/organizations:
    get:
      summary: ''
      operationId: getApiOrganizations
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiOrganizations
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/organizations/{id}':
    get:
      summary: ''
      operationId: getApiOrganizationsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiOrganizationsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiOrganizationsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the organization.'
        example: architecto
        required: true
        schema:
          type: string
  /api/brands:
    get:
      summary: ''
      operationId: getApiBrands
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiBrands
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/brands/{id}':
    get:
      summary: ''
      operationId: getApiBrandsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiBrandsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiBrandsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the brand.'
        example: architecto
        required: true
        schema:
          type: string
  /api/products:
    get:
      summary: ''
      operationId: getApiProducts
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiProducts
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/products/{id}':
    get:
      summary: ''
      operationId: getApiProductsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiProductsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiProductsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the product.'
        example: architecto
        required: true
        schema:
          type: string
  /api/clients:
    get:
      summary: ''
      operationId: getApiClients
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiClients
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/clients/{id}':
    get:
      summary: ''
      operationId: getApiClientsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiClientsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiClientsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the client.'
        example: architecto
        required: true
        schema:
          type: string
  /api/projects:
    get:
      summary: ''
      operationId: getApiProjects
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiProjects
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/projects/{id}':
    get:
      summary: ''
      operationId: getApiProjectsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiProjectsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiProjectsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the project.'
        example: architecto
        required: true
        schema:
          type: string
  /api/budgets:
    get:
      summary: ''
      operationId: getApiBudgets
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiBudgets
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/budgets/{id}':
    get:
      summary: ''
      operationId: getApiBudgetsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiBudgetsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiBudgetsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the budget.'
        example: architecto
        required: true
        schema:
          type: string
  /api/batches:
    get:
      summary: ''
      operationId: getApiBatches
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiBatches
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                batch_number:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - batch_number
      security: []
  '/api/batches/{id}':
    get:
      summary: ''
      operationId: getApiBatchesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiBatchesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                batch_number:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - batch_number
      security: []
    delete:
      summary: ''
      operationId: deleteApiBatchesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the batch.'
        example: architecto
        required: true
        schema:
          type: string
  /api/stock_entries:
    get:
      summary: ''
      operationId: getApiStock_entries
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiStock_entries
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - quantity
      security: []
  '/api/stock_entries/{id}':
    get:
      summary: ''
      operationId: getApiStock_entriesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiStock_entriesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - quantity
      security: []
    delete:
      summary: ''
      operationId: deleteApiStock_entriesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the stock entry.'
        example: architecto
        required: true
        schema:
          type: string
  /api/stock_exits:
    get:
      summary: ''
      operationId: getApiStock_exits
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiStock_exits
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - quantity
      security: []
  '/api/stock_exits/{id}':
    get:
      summary: ''
      operationId: getApiStock_exitsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiStock_exitsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - quantity
      security: []
    delete:
      summary: ''
      operationId: deleteApiStock_exitsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the stock exit.'
        example: architecto
        required: true
        schema:
          type: string
  /api/stocks:
    get:
      summary: ''
      operationId: getApiStocks
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiStocks
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - quantity
      security: []
  '/api/stocks/{id}':
    get:
      summary: ''
      operationId: getApiStocksId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiStocksId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - quantity
      security: []
    delete:
      summary: ''
      operationId: deleteApiStocksId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the stock.'
        example: architecto
        required: true
        schema:
          type: string
  /api/groups:
    get:
      summary: ''
      operationId: getApiGroups
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiGroups
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/groups/{id}':
    get:
      summary: ''
      operationId: getApiGroupsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiGroupsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiGroupsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the group.'
        example: architecto
        required: true
        schema:
          type: string
  /api/shops:
    get:
      summary: ''
      operationId: getApiShops
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiShops
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/shops/{id}':
    get:
      summary: ''
      operationId: getApiShopsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiShopsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiShopsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the shop.'
        example: architecto
        required: true
        schema:
          type: string
  /api/sales:
    get:
      summary: ''
      operationId: getApiSales
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiSales
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                total_value:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - total_value
      security: []
  '/api/sales/{id}':
    get:
      summary: ''
      operationId: getApiSalesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiSalesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                total_value:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - total_value
      security: []
    delete:
      summary: ''
      operationId: deleteApiSalesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the sale.'
        example: architecto
        required: true
        schema:
          type: string
  /api/items:
    get:
      summary: ''
      operationId: getApiItems
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiItems
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sale_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - sale_id
      security: []
  '/api/items/{id}':
    get:
      summary: ''
      operationId: getApiItemsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiItemsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sale_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                product_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - sale_id
                - product_id
      security: []
    delete:
      summary: ''
      operationId: deleteApiItemsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the item.'
        example: architecto
        required: true
        schema:
          type: string
  /api/departments:
    get:
      summary: ''
      operationId: getApiDepartments
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiDepartments
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/departments/{id}':
    get:
      summary: ''
      operationId: getApiDepartmentsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiDepartmentsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiDepartmentsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the department.'
        example: architecto
        required: true
        schema:
          type: string
  /api/department_users:
    get:
      summary: ''
      operationId: getApiDepartment_users
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiDepartment_users
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                department_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - user_id
                - department_id
      security: []
  '/api/department_users/{id}':
    get:
      summary: ''
      operationId: getApiDepartment_usersId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiDepartment_usersId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the department user.'
        example: architecto
        required: true
        schema:
          type: string
  /api/groups_products:
    get:
      summary: ''
      operationId: getApiGroups_products
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiGroups_products
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                group_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                product_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - group_id
                - product_id
      security: []
  '/api/groups_products/{id}':
    get:
      summary: ''
      operationId: getApiGroups_productsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiGroups_productsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the groups product.'
        example: architecto
        required: true
        schema:
          type: string
  /api/budgets_products:
    get:
      summary: ''
      operationId: getApiBudgets_products
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiBudgets_products
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                budget_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                product_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - budget_id
                - product_id
      security: []
  '/api/budgets_products/{id}':
    get:
      summary: ''
      operationId: getApiBudgets_productsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiBudgets_productsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the budgets product.'
        example: architecto
        required: true
        schema:
          type: string
  /api/projects_products:
    get:
      summary: ''
      operationId: getApiProjects_products
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiProjects_products
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                project_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - project_id
      security: []
  '/api/projects_products/{id}':
    get:
      summary: ''
      operationId: getApiProjects_productsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiProjects_productsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the projects product.'
        example: architecto
        required: true
        schema:
          type: string
  /api/products_histories:
    get:
      summary: ''
      operationId: getApiProducts_histories
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiProducts_histories
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                product_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - product_id
      security: []
  '/api/products_histories/{id}':
    get:
      summary: ''
      operationId: getApiProducts_historiesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiProducts_historiesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the products history.'
        example: architecto
        required: true
        schema:
          type: string
  /api/custom_products:
    get:
      summary: ''
      operationId: getApiCustom_products
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiCustom_products
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/custom_products/{id}':
    get:
      summary: ''
      operationId: getApiCustom_productsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiCustom_productsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiCustom_productsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the custom product.'
        example: architecto
        required: true
        schema:
          type: string
  /api/telegram_users:
    get:
      summary: ''
      operationId: getApiTelegram_users
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiTelegram_users
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                telegram_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - telegram_id
      security: []
  '/api/telegram_users/{id}':
    get:
      summary: ''
      operationId: getApiTelegram_usersId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiTelegram_usersId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiTelegram_usersId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the telegram user.'
        example: architecto
        required: true
        schema:
          type: string
  /api/telegram_bots:
    get:
      summary: ''
      operationId: getApiTelegram_bots
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiTelegram_bots
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                telegram_id:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                bot:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                token:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                url:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - telegram_id
                - bot
                - token
                - url
      security: []
  '/api/telegram_bots/{id}':
    get:
      summary: ''
      operationId: getApiTelegram_botsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiTelegram_botsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiTelegram_botsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the telegram bot.'
        example: architecto
        required: true
        schema:
          type: string
  /api/telegram_chats:
    get:
      summary: ''
      operationId: getApiTelegram_chats
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiTelegram_chats
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/telegram_chats/{id}':
    get:
      summary: ''
      operationId: getApiTelegram_chatsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiTelegram_chatsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiTelegram_chatsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the telegram chat.'
        example: architecto
        required: true
        schema:
          type: string
  /api/telegram_messages:
    get:
      summary: ''
      operationId: getApiTelegram_messages
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiTelegram_messages
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/telegram_messages/{id}':
    get:
      summary: ''
      operationId: getApiTelegram_messagesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiTelegram_messagesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiTelegram_messagesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the telegram message.'
        example: architecto
        required: true
        schema:
          type: string
  /api/flows:
    get:
      summary: ''
      operationId: getApiFlows
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiFlows
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/flows/{id}':
    get:
      summary: ''
      operationId: getApiFlowsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiFlowsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiFlowsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the flow.'
        example: architecto
        required: true
        schema:
          type: string
  /api/steps:
    get:
      summary: ''
      operationId: getApiSteps
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiSteps
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                step:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                is_input:
                  type: boolean
                  description: ''
                  example: false
                  nullable: true
                input:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
              required:
                - step
      security: []
  '/api/steps/{id}':
    get:
      summary: ''
      operationId: getApiStepsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiStepsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                step:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
                is_input:
                  type: boolean
                  description: ''
                  example: false
                  nullable: true
                input:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
              required:
                - step
      security: []
    delete:
      summary: ''
      operationId: deleteApiStepsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the step.'
        example: architecto
        required: true
        schema:
          type: string
  /api/bodies:
    get:
      summary: ''
      operationId: getApiBodies
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiBodies
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/bodies/{id}':
    get:
      summary: ''
      operationId: getApiBodiesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiBodiesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiBodiesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the body.'
        example: architecto
        required: true
        schema:
          type: string
  /api/components:
    get:
      summary: ''
      operationId: getApiComponents
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiComponents
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/components/{id}':
    get:
      summary: ''
      operationId: getApiComponentsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiComponentsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiComponentsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the component.'
        example: architecto
        required: true
        schema:
          type: string
  /api/buttons:
    get:
      summary: ''
      operationId: getApiButtons
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiButtons
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - type
      security: []
  '/api/buttons/{id}':
    get:
      summary: ''
      operationId: getApiButtonsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiButtonsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - type
      security: []
    delete:
      summary: ''
      operationId: deleteApiButtonsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the button.'
        example: architecto
        required: true
        schema:
          type: string
  /api/campaigns:
    get:
      summary: ''
      operationId: getApiCampaigns
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiCampaigns
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/campaigns/{id}':
    get:
      summary: ''
      operationId: getApiCampaignsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiCampaignsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiCampaignsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the campaign.'
        example: architecto
        required: true
        schema:
          type: string
  /api/messages:
    get:
      summary: ''
      operationId: getApiMessages
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiMessages
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - message
      security: []
  '/api/messages/{id}':
    get:
      summary: ''
      operationId: getApiMessagesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiMessagesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - message
      security: []
    delete:
      summary: ''
      operationId: deleteApiMessagesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the message.'
        example: architecto
        required: true
        schema:
          type: string
  /api/interactions:
    get:
      summary: ''
      operationId: getApiInteractions
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiInteractions
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/interactions/{id}':
    get:
      summary: ''
      operationId: getApiInteractionsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiInteractionsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiInteractionsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the interaction.'
        example: architecto
        required: true
        schema:
          type: string
  /api/conversations:
    get:
      summary: ''
      operationId: getApiConversations
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiConversations
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/conversations/{id}':
    get:
      summary: ''
      operationId: getApiConversationsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiConversationsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    delete:
      summary: ''
      operationId: deleteApiConversationsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the conversation.'
        example: architecto
        required: true
        schema:
          type: string
  /api/templates:
    get:
      summary: ''
      operationId: getApiTemplates
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiTemplates
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
  '/api/templates/{id}':
    get:
      summary: ''
      operationId: getApiTemplatesId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiTemplatesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - name
      security: []
    delete:
      summary: ''
      operationId: deleteApiTemplatesId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the template.'
        example: architecto
        required: true
        schema:
          type: string
  /api/parameters:
    get:
      summary: ''
      operationId: getApiParameters
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiParameters
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                organization_id:
                  type: integer
                  description: ''
                  example: 16
                  nullable: true
                campaign_id:
                  type: integer
                  description: ''
                  example: 16
                  nullable: true
                component_id:
                  type: integer
                  description: ''
                  example: 16
                  nullable: true
                type:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
                value:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
                placeholder:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
      security: []
  '/api/parameters/{id}':
    get:
      summary: ''
      operationId: getApiParametersId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiParametersId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                organization_id:
                  type: integer
                  description: ''
                  example: 16
                  nullable: true
                campaign_id:
                  type: integer
                  description: ''
                  example: 16
                  nullable: true
                component_id:
                  type: integer
                  description: ''
                  example: 16
                  nullable: true
                type:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
                value:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
                placeholder:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
      security: []
    delete:
      summary: ''
      operationId: deleteApiParametersId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the parameter.'
        example: architecto
        required: true
        schema:
          type: string
  /api/phone_numbers:
    get:
      summary: 'Display a listing of the resource.'
      operationId: displayAListingOfTheResource
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: 'Store a newly created resource in storage.'
      operationId: storeANewlyCreatedResourceInStorage
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the users table.'
                  example: null
                  nullable: true
                client_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the clients table.'
                  example: null
                  nullable: true
                phone_number:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
                description:
                  type: string
                  description: ''
                  example: 'Eius et animi quos velit et.'
                  nullable: true
                is_active:
                  type: boolean
                  description: ''
                  example: false
                  nullable: true
      security: []
  '/api/phone_numbers/{id}':
    get:
      summary: 'Display the specified resource.'
      operationId: displayTheSpecifiedResource
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: 'Update the specified resource in storage.'
      operationId: updateTheSpecifiedResourceInStorage
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the users table.'
                  example: null
                  nullable: true
                client_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the clients table.'
                  example: null
                  nullable: true
                phone_number:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
                name:
                  type: string
                  description: ''
                  example: architecto
                  nullable: true
                description:
                  type: string
                  description: ''
                  example: 'Eius et animi quos velit et.'
                  nullable: true
                is_active:
                  type: boolean
                  description: ''
                  example: false
                  nullable: true
      security: []
    delete:
      summary: 'Remove the specified resource from storage.'
      operationId: removeTheSpecifiedResourceFromStorage
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the phone number.'
        example: architecto
        required: true
        schema:
          type: string
  /api/flow/save:
    post:
      summary: ''
      operationId: postApiFlowSave
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  /api/template/save:
    post:
      summary: ''
      operationId: postApiTemplateSave
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/template/publish/whatsapp/{id}':
    post:
      summary: 'Publishes a template using the PublishTemplate UseCase.'
      operationId: publishesATemplateUsingThePublishTemplateUseCase
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the whatsapp.'
        example: architecto
        required: true
        schema:
          type: string
  /api/imports:
    get:
      summary: ''
      operationId: getApiImports
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: ''
      operationId: postApiImports
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: 'Must be a file. Must not be greater than 8192 kilobytes.'
                  nullable: false
                model:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - file
                - model
      security: []
  '/api/imports/{id}':
    get:
      summary: ''
      operationId: getApiImportsId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: ''
      operationId: putApiImportsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                model:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - model
      security: []
    delete:
      summary: ''
      operationId: deleteApiImportsId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the import.'
        example: architecto
        required: true
        schema:
          type: string
  '/api/import/{id}/process':
    post:
      summary: ''
      operationId: postApiImportIdProcess
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                skip_header:
                  type: string
                  description: ''
                  example: architecto
                  nullable: false
              required:
                - skip_header
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the import.'
        example: architecto
        required: true
        schema:
          type: string
  '/api/project/budget/{budget_id}':
    post:
      summary: ''
      operationId: postApiProjectBudgetBudget_id
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: budget_id
        description: 'The ID of the budget.'
        example: architecto
        required: true
        schema:
          type: string
  '/api/project/{id}/products':
    post:
      summary: ''
      operationId: postApiProjectIdProducts
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the project.'
        example: architecto
        required: true
        schema:
          type: string
  '/api/budget/{id}/products':
    post:
      summary: ''
      operationId: postApiBudgetIdProducts
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the budget.'
        example: architecto
        required: true
        schema:
          type: string
  /api/reports/stock_entries:
    get:
      summary: ''
      operationId: getApiReportsStock_entries
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  /api/reports/stock_exits:
    get:
      summary: ''
      operationId: getApiReportsStock_exits
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  '/api/batch/{id}/process-at-stock':
    post:
      summary: ''
      operationId: postApiBatchIdProcessAtStock
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the batch.'
        example: architecto
        required: true
        schema:
          type: string
  '/api/logs/fetch/{id}':
    get:
      summary: ''
      operationId: getApiLogsFetchId
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the fetch.'
        example: architecto
        required: true
        schema:
          type: string
  '/api/logs/fetch_from_organization/{organization_id}':
    get:
      summary: ''
      operationId: getApiLogsFetch_from_organizationOrganization_id
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: organization_id
        description: 'The ID of the organization.'
        example: architecto
        required: true
        schema:
          type: string
  /api/logs/fetch_all:
    get:
      summary: ''
      operationId: getApiLogsFetch_all
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  /api/ocr/image-reader:
    post:
      summary: ''
      operationId: postApiOcrImageReader
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: 'Must be a file. Must not be greater than 8192 kilobytes.'
                  nullable: false
              required:
                - file
      security: []
  '/api/reports/{model}/count':
    get:
      summary: ''
      operationId: getApiReportsModelCount
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: model
        description: ''
        example: architecto
        required: true
        schema:
          type: string
  '/api/reports/{model}/sum/{column}':
    get:
      summary: ''
      operationId: getApiReportsModelSumColumn
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: model
        description: ''
        example: architecto
        required: true
        schema:
          type: string
      -
        in: path
        name: column
        description: ''
        example: architecto
        required: true
        schema:
          type: string
  /api/notifications/unread:
    get:
      summary: ''
      operationId: getApiNotificationsUnread
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  /api/notifications/all:
    get:
      summary: ''
      operationId: getApiNotificationsAll
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  /api/notifications/read:
    post:
      summary: ''
      operationId: postApiNotificationsRead
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  '/api/notifications/read-one/{$id}':
    post:
      summary: ''
      operationId: postApiNotificationsReadOneId
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: $id
        description: ''
        example: architecto
        required: true
        schema:
          type: string
