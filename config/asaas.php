<?php

return [
    /*
    |--------------------------------------------------------------------------
    | ASAAS Environment
    |--------------------------------------------------------------------------
    |
    | This value determines which ASAAS environment your application is
    | currently running in. This may determine how you prefer to configure
    | various services that your application utilizes.
    |
    | Supported: "sandbox", "production"
    |
    */
    'environment' => env('ASAAS_ENV', 'sandbox'),

    /*
    |--------------------------------------------------------------------------
    | ASAAS API Tokens
    |--------------------------------------------------------------------------
    |
    | Here you may specify the API tokens for both sandbox and production
    | environments. These tokens are used to authenticate requests to the
    | ASAAS API.
    |
    */
    'token_sandbox' => env('ASAAS_TOKEN_SANDBOX'),
    'token_production' => env('ASAAS_TOKEN_PRODUCTION'),

    /*
    |--------------------------------------------------------------------------
    | ASAAS API URLs
    |--------------------------------------------------------------------------
    |
    | These are the base URLs for the ASAAS API in both environments.
    | You should not need to change these unless ASAAS updates their
    | API endpoints.
    |
    */
    'url_sandbox' => env('ASAAS_URL_SANDBOX', 'https://api-sandbox.asaas.com'),
    'url_production' => env('ASAAS_URL_PRODUCTION', 'https://api.asaas.com'),

    /*
    |--------------------------------------------------------------------------
    | HTTP Client Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration options for the HTTP client used to make requests
    | to the ASAAS API.
    |
    */
    'timeout' => env('ASAAS_TIMEOUT', 30),
    'retry_attempts' => env('ASAAS_RETRY_ATTEMPTS', 3),
    'retry_delay' => env('ASAAS_RETRY_DELAY', 1000), // milliseconds

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for logging ASAAS API requests and responses.
    |
    */
    'log_requests' => env('ASAAS_LOG_REQUESTS', true),
    'log_responses' => env('ASAAS_LOG_RESPONSES', true),
    'log_errors_only' => env('ASAAS_LOG_ERRORS_ONLY', false),
];
