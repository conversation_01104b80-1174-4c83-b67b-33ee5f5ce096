<?php

namespace App\Enums;
enum LanguageType: string
{
 case Africaner = 'af';
 case Albanes = 'sq';
 case Arabe = 'ar';
 case Azerbaijano = 'az';
 case Bengales = 'bn';
 case Bulgaro = 'bg';
 case Catalao = 'ca';
 case Chines_CHN = 'zh_CN';
 case Chines_HKG = 'zh_HK';
 case Chines_TAI = 'zh_TW';
 case Croata = 'hr';
 case Tcheco = 'cs';
 case Dinamarques = 'da';
 case Holandes = 'nl';
 case Ingles = 'en';
 case Ingles_ReinoUnido = 'en_GB';
 case Ingles_EUA = 'en_US';
 case Estoniano = 'et';
 case Filipino = 'fil';
 case Finlandes = 'fi';
 case Frances = 'fr';
 case Alemao = 'de';
 case Grego = 'el';
 case Guzerate = 'gu';
 case Hauca = 'ha';
 case Hebraico = 'he';
 case Hindi = 'hi';
 case Hungaro = 'hu';
 case Indonesio = 'id';
 case Irlandes = 'ga';
 case Italiano = 'it';
 case Japones = 'ja';
 case Canares = 'kn';
 case Cazaque = 'kk';
 case Coreano = 'ko';
 case Laociano = 'lo';
 case Letao = 'lv';
 case Lituano = 'lt';
 case Macedonio = 'mk';
 case Malaio = 'ms';
 case Malaiala = 'ml';
 case Marati = 'mr';
 case Noruegues = 'nb';
 case Persa = 'fa';
 case Polones = 'pl';
 case Portugues_BR = 'pt_BR';
 case Portugues_POR = 'pt_PT';
 case Punjabi = 'pa';
 case Romeno = 'ro';
 case Russo = 'ru';
 case Servio = 'sr';
 case Eslovaco = 'sk';
 case Esloveno = 'sl';
 case Espanhol = 'es';
 case Espanhol_ARG = 'es_AR';
 case Espanhol_ESP = 'es_ES';
 case Espanhol_MEX = 'es_MX';
 case Suaili = 'sw';
 case Sueco = 'sv';
 case Tamil = 'ta';
 case Telugo = 'te';
 case Tailandes = 'th';
 case Turco = 'tr';
 case Ucraniano = 'uk';
 case Urdu = 'ur';
 case Uzbeque = 'uz';
 case Vietnamita = 'vi';
 case Zulu = 'zu';
}