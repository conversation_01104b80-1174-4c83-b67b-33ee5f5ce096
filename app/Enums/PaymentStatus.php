<?php

namespace App\Enums;

enum PaymentStatus: string
{
    case PENDING = 'pending';
    case PENDING_CONFIRMATION = 'pending_confirmation';
    case CONFIRMED = 'confirmed';
    case RECEIVED = 'received';
    case OVERDUE = 'overdue';
    case REFUNDED = 'refunded';
    case RECEIVED_IN_CASH = 'received_in_cash';
    case REFUND_REQUESTED = 'refund_requested';
    case REFUND_IN_PROGRESS = 'refund_in_progress';
    case CHARGEBACK_REQUESTED = 'chargeback_requested';
    case CHARGEBACK_DISPUTE = 'chargeback_dispute';
    case AWAITING_CHARGEBACK_REVERSAL = 'awaiting_chargeback_reversal';
    case DUNNING_REQUESTED = 'dunning_requested';
    case DUNNING_RECEIVED = 'dunning_received';
    case AWAITING_RISK_ANALYSIS = 'awaiting_risk_analysis';

    /**
     * Get human-readable label
     */
    public function label(): string
    {
        return match($this) {
            self::PENDING => 'Pendente',
            self::PENDING_CONFIRMATION => 'Aguardando Confirmação',
            self::CONFIRMED => 'Confirmado',
            self::RECEIVED => 'Recebido',
            self::OVERDUE => 'Vencido',
            self::REFUNDED => 'Estornado',
            self::RECEIVED_IN_CASH => 'Recebido em Dinheiro',
            self::REFUND_REQUESTED => 'Estorno Solicitado',
            self::REFUND_IN_PROGRESS => 'Estorno em Processamento',
            self::CHARGEBACK_REQUESTED => 'Chargeback Solicitado',
            self::CHARGEBACK_DISPUTE => 'Chargeback em Disputa',
            self::AWAITING_CHARGEBACK_REVERSAL => 'Aguardando Reversão de Chargeback',
            self::DUNNING_REQUESTED => 'Negativação Solicitada',
            self::DUNNING_RECEIVED => 'Negativação Recebida',
            self::AWAITING_RISK_ANALYSIS => 'Aguardando Análise de Risco',
        };
    }

    /**
     * Get status color for UI
     */
    public function color(): string
    {
        return match($this) {
            self::PENDING, self::PENDING_CONFIRMATION => 'warning',
            self::CONFIRMED, self::RECEIVED, self::RECEIVED_IN_CASH => 'success',
            self::OVERDUE, self::REFUNDED, self::CHARGEBACK_REQUESTED => 'danger',
            self::REFUND_REQUESTED, self::REFUND_IN_PROGRESS => 'info',
            self::CHARGEBACK_DISPUTE, self::AWAITING_CHARGEBACK_REVERSAL => 'warning',
            self::DUNNING_REQUESTED, self::DUNNING_RECEIVED => 'dark',
            self::AWAITING_RISK_ANALYSIS => 'secondary',
        };
    }

    /**
     * Check if payment is considered paid
     */
    public function isPaid(): bool
    {
        return in_array($this, [
            self::CONFIRMED,
            self::RECEIVED,
            self::RECEIVED_IN_CASH,
        ]);
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return in_array($this, [
            self::PENDING,
            self::PENDING_CONFIRMATION,
            self::AWAITING_RISK_ANALYSIS,
        ]);
    }

    /**
     * Check if payment is overdue
     */
    public function isOverdue(): bool
    {
        return $this === self::OVERDUE;
    }

    /**
     * Check if payment is refunded or being refunded
     */
    public function isRefunded(): bool
    {
        return in_array($this, [
            self::REFUNDED,
            self::REFUND_REQUESTED,
            self::REFUND_IN_PROGRESS,
        ]);
    }

    /**
     * Check if payment has chargeback issues
     */
    public function hasChargebackIssues(): bool
    {
        return in_array($this, [
            self::CHARGEBACK_REQUESTED,
            self::CHARGEBACK_DISPUTE,
            self::AWAITING_CHARGEBACK_REVERSAL,
        ]);
    }

    /**
     * Check if payment is in dunning process
     */
    public function isInDunning(): bool
    {
        return in_array($this, [
            self::DUNNING_REQUESTED,
            self::DUNNING_RECEIVED,
        ]);
    }

    /**
     * Get all paid statuses
     */
    public static function paidStatuses(): array
    {
        return [
            self::CONFIRMED,
            self::RECEIVED,
            self::RECEIVED_IN_CASH,
        ];
    }

    /**
     * Get all pending statuses
     */
    public static function pendingStatuses(): array
    {
        return [
            self::PENDING,
            self::PENDING_CONFIRMATION,
            self::AWAITING_RISK_ANALYSIS,
        ];
    }

    /**
     * Get all problematic statuses
     */
    public static function problematicStatuses(): array
    {
        return [
            self::OVERDUE,
            self::REFUNDED,
            self::REFUND_REQUESTED,
            self::REFUND_IN_PROGRESS,
            self::CHARGEBACK_REQUESTED,
            self::CHARGEBACK_DISPUTE,
            self::AWAITING_CHARGEBACK_REVERSAL,
            self::DUNNING_REQUESTED,
            self::DUNNING_RECEIVED,
        ];
    }

    /**
     * Map ASAAS status to enum
     */
    public static function fromAsaasStatus(string $asaasStatus): self
    {
        return match(strtoupper($asaasStatus)) {
            'PENDING' => self::PENDING,
            'PENDING_CONFIRMATION' => self::PENDING_CONFIRMATION,
            'CONFIRMED' => self::CONFIRMED,
            'RECEIVED' => self::RECEIVED,
            'OVERDUE' => self::OVERDUE,
            'REFUNDED' => self::REFUNDED,
            'RECEIVED_IN_CASH' => self::RECEIVED_IN_CASH,
            'REFUND_REQUESTED' => self::REFUND_REQUESTED,
            'REFUND_IN_PROGRESS' => self::REFUND_IN_PROGRESS,
            'CHARGEBACK_REQUESTED' => self::CHARGEBACK_REQUESTED,
            'CHARGEBACK_DISPUTE' => self::CHARGEBACK_DISPUTE,
            'AWAITING_CHARGEBACK_REVERSAL' => self::AWAITING_CHARGEBACK_REVERSAL,
            'DUNNING_REQUESTED' => self::DUNNING_REQUESTED,
            'DUNNING_RECEIVED' => self::DUNNING_RECEIVED,
            'AWAITING_RISK_ANALYSIS' => self::AWAITING_RISK_ANALYSIS,
            default => self::PENDING,
        };
    }
}
