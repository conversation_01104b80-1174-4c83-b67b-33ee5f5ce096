<?php

namespace App\Enums;

enum CampaignStatus: int
{
    case DRAFT = 1;
    case SCHEDULED = 2;
    case SENDING = 3;
    case COMPLETED = 4;
    case FAILED = 5;
    case CANCELLED = 6;

    /**
     * Get human readable label
     */
    public function label(): string
    {
        return match($this) {
            self::DRAFT => 'Draft',
            self::SCHEDULED => 'Scheduled',
            self::SENDING => 'Sending',
            self::COMPLETED => 'Completed',
            self::FAILED => 'Failed',
            self::CANCELLED => 'Cancelled',
        };
    }

    /**
     * Get status color for UI
     */
    public function color(): string
    {
        return match($this) {
            self::DRAFT => 'secondary',
            self::SCHEDULED => 'info',
            self::SENDING => 'warning',
            self::COMPLETED => 'success',
            self::FAILED => 'danger',
            self::CANCELLED => 'dark',
        };
    }

    /**
     * Get status description
     */
    public function description(): string
    {
        return match($this) {
            self::DRAFT => 'Campaign is being created',
            self::SCHEDULED => 'Campaign is scheduled for future sending',
            self::SENDING => 'Campaign is currently sending messages',
            self::COMPLETED => 'Campaign has been sent successfully',
            self::FAILED => 'Campaign failed during sending',
            self::CANCELLED => 'Campaign was cancelled before sending',
        };
    }

    /**
     * Check if status allows editing
     */
    public function canEdit(): bool
    {
        return in_array($this, [self::DRAFT, self::SCHEDULED]);
    }

    /**
     * Check if status allows cancellation
     */
    public function canCancel(): bool
    {
        return in_array($this, [self::DRAFT, self::SCHEDULED]);
    }

    /**
     * Check if status allows launching
     */
    public function canLaunch(): bool
    {
        return in_array($this, [self::DRAFT, self::SCHEDULED]);
    }

    /**
     * Check if campaign is in progress
     */
    public function isInProgress(): bool
    {
        return $this === self::SENDING;
    }

    /**
     * Check if campaign is finished (completed, failed, or cancelled)
     */
    public function isFinished(): bool
    {
        return in_array($this, [self::COMPLETED, self::FAILED, self::CANCELLED]);
    }

    /**
     * Derive status from boolean fields (for backward compatibility)
     */
    public static function fromBooleans(
        bool $is_sent = false,
        bool $is_sending = false,
        bool $is_scheduled = false,
        bool $has_failed_messages = false,
        bool $is_cancelled = false
    ): self {
        if ($is_cancelled) {
            return self::CANCELLED;
        }
        
        if ($is_sent && !$is_sending) {
            return $has_failed_messages ? self::FAILED : self::COMPLETED;
        }
        
        if ($is_sending) {
            return self::SENDING;
        }
        
        if ($is_scheduled) {
            return self::SCHEDULED;
        }
        
        return self::DRAFT;
    }

    /**
     * Convert status to boolean fields (for backward compatibility)
     */
    public function toBooleans(): array
    {
        return match($this) {
            self::DRAFT => [
                'is_sent' => false,
                'is_sending' => false,
                'is_scheduled' => false,
            ],
            self::SCHEDULED => [
                'is_sent' => false,
                'is_sending' => false,
                'is_scheduled' => true,
            ],
            self::SENDING => [
                'is_sent' => false,
                'is_sending' => true,
                'is_scheduled' => false,
            ],
            self::COMPLETED => [
                'is_sent' => true,
                'is_sending' => false,
                'is_scheduled' => false,
            ],
            self::FAILED => [
                'is_sent' => true,
                'is_sending' => false,
                'is_scheduled' => false,
            ],
            self::CANCELLED => [
                'is_sent' => false,
                'is_sending' => false,
                'is_scheduled' => false,
            ],
        };
    }

    /**
     * Get all available statuses
     */
    public static function all(): array
    {
        return [
            self::DRAFT,
            self::SCHEDULED,
            self::SENDING,
            self::COMPLETED,
            self::FAILED,
            self::CANCELLED,
        ];
    }
}
