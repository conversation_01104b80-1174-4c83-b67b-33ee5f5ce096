<?php

namespace App\Enums;

enum SyncType: string
{
    case MESSAGE = 'message';
    case CAMPAIGN = 'campaign';

    /**
     * Get human readable label
     */
    public function label(): string
    {
        return match($this) {
            self::MESSAGE => 'Message Sync',
            self::CAMPAIGN => 'Campaign Sync',
        };
    }

    /**
     * Get description
     */
    public function description(): string
    {
        return match($this) {
            self::MESSAGE => 'Synchronization of individual message status',
            self::CAMPAIGN => 'Synchronization of all messages in a campaign',
        };
    }
}
