<?php

namespace App\Enums;

enum MessageStatus: int
{
    case is_draft = 1;
    case is_sending = 2;
    case is_failed = 3;
    case is_sent = 4;
    case is_delivered = 5;
    case is_read = 6;

    /**
     * Mapeia status do WhatsApp para MessageStatus
     */
    public static function mapWhatsAppStatusToMessageStatus(string $whatsAppStatus): self
    {
        return match($whatsAppStatus) {
            'sent' => self::is_sent,
            'delivered' => self::is_delivered,
            'read' => self::is_read,
            'failed' => self::is_failed,
            default => self::is_failed, // Status desconhecido é tratado como falha
        };
    }
}
