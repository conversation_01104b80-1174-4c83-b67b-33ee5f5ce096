<?php

namespace App\Enums;

enum SubscriptionStatus: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case EXPIRED = 'expired';
    case OVERDUE = 'overdue';

    /**
     * Get human-readable label
     */
    public function label(): string
    {
        return match($this) {
            self::ACTIVE => 'Ativa',
            self::INACTIVE => 'Inativa',
            self::EXPIRED => 'Expirada',
            self::OVERDUE => 'Em Atraso',
        };
    }

    /**
     * Get status color for UI
     */
    public function color(): string
    {
        return match($this) {
            self::ACTIVE => 'success',
            self::INACTIVE => 'secondary',
            self::EXPIRED => 'dark',
            self::OVERDUE => 'danger',
        };
    }

    /**
     * Check if subscription is active
     */
    public function isActive(): bool
    {
        return $this === self::ACTIVE;
    }

    /**
     * Check if subscription is inactive
     */
    public function isInactive(): bool
    {
        return $this === self::INACTIVE;
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(): bool
    {
        return $this === self::EXPIRED;
    }

    /**
     * Check if subscription is overdue
     */
    public function isOverdue(): bool
    {
        return $this === self::OVERDUE;
    }

    /**
     * Check if subscription needs attention
     */
    public function needsAttention(): bool
    {
        return in_array($this, [
            self::INACTIVE,
            self::EXPIRED,
            self::OVERDUE,
        ]);
    }

    /**
     * Get all active statuses
     */
    public static function activeStatuses(): array
    {
        return [self::ACTIVE];
    }

    /**
     * Get all problematic statuses
     */
    public static function problematicStatuses(): array
    {
        return [
            self::INACTIVE,
            self::EXPIRED,
            self::OVERDUE,
        ];
    }

    /**
     * Map ASAAS status to enum
     */
    public static function fromAsaasStatus(string $asaasStatus): self
    {
        return match(strtoupper($asaasStatus)) {
            'ACTIVE' => self::ACTIVE,
            'INACTIVE' => self::INACTIVE,
            'EXPIRED' => self::EXPIRED,
            'OVERDUE' => self::OVERDUE,
            default => self::INACTIVE,
        };
    }
}
