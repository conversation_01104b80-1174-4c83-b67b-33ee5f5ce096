<?php

namespace App\Enums\ChatBot;

/**
 * Enum for WhatsApp Interactive Message Types
 * 
 * Defines the different types of interactive messages that can be sent via WhatsApp Business API.
 * Each type has specific structure and behavior requirements.
 * 
 * @package App\Enums\ChatBot
 * @see https://developers.facebook.com/docs/whatsapp/cloud-api/reference/messages#interactive-object
 */
enum InteractiveType: string
{
    /**
     * Button interactive message - Contains up to 3 reply buttons
     * Used for simple choice selections with quick reply buttons
     */
    case BUTTON = 'button';

    /**
     * List interactive message - Contains a list of selectable options
     * Used for longer lists of options (up to 10 items per section)
     */
    case LIST = 'list';

    /**
     * Flow interactive message - Triggers a WhatsApp Flow
     * Used for complex multi-step interactive experiences
     */
    case FLOW = 'flow';

    /**
     * Get all available interactive types
     *
     * @return array<string>
     */
    public static function getAll(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Get interactive type description
     *
     * @return string
     */
    public function getDescription(): string
    {
        return match($this) {
            self::BUTTON => 'Mensagem interativa com até 3 botões de resposta',
            self::LIST => 'Mensagem interativa com lista de opções selecionáveis',
            self::FLOW => 'Mensagem interativa que aciona um WhatsApp Flow',
        };
    }

    /**
     * Check if interactive type is valid
     *
     * @param string $type
     * @return bool
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::getAll());
    }

    /**
     * Get interactive type from string value
     *
     * @param string $type
     * @return self|null
     */
    public static function fromString(string $type): ?self
    {
        return self::tryFrom($type);
    }

    /**
     * Get maximum number of options for this interactive type
     *
     * @return int
     */
    public function getMaxOptions(): int
    {
        return match($this) {
            self::BUTTON => 3,
            self::LIST => 10,
            self::FLOW => 1,
        };
    }

    /**
     * Get minimum number of options required for this interactive type
     *
     * @return int
     */
    public function getMinOptions(): int
    {
        return match($this) {
            self::BUTTON => 1,
            self::LIST => 1,
            self::FLOW => 1,
        };
    }

    /**
     * Check if this type supports sections
     *
     * @return bool
     */
    public function supportsSections(): bool
    {
        return match($this) {
            self::LIST => true,
            self::BUTTON, self::FLOW => false,
        };
    }

    /**
     * Check if this type requires a header
     *
     * @return bool
     */
    public function requiresHeader(): bool
    {
        return match($this) {
            self::LIST => true,
            self::BUTTON, self::FLOW => false,
        };
    }

    /**
     * Check if this type supports footer
     *
     * @return bool
     */
    public function supportsFooter(): bool
    {
        return match($this) {
            self::BUTTON, self::LIST => true,
            self::FLOW => false,
        };
    }

    /**
     * Get maximum body text length for this interactive type
     *
     * @return int
     */
    public function getMaxBodyLength(): int
    {
        return match($this) {
            self::BUTTON => 1024,
            self::LIST => 1024,
            self::FLOW => 1024,
        };
    }

    /**
     * Get maximum footer text length for this interactive type
     *
     * @return int
     */
    public function getMaxFooterLength(): int
    {
        return match($this) {
            self::BUTTON, self::LIST => 60,
            self::FLOW => 0, // Flow doesn't support footer
        };
    }

    /**
     * Validate interactive message data for this type
     *
     * @param array $data
     * @return bool
     */
    public function validateMessageData(array $data): bool
    {
        // Check body length
        if (isset($data['body']) && strlen($data['body']) > $this->getMaxBodyLength()) {
            return false;
        }

        // Check footer length if supported
        if ($this->supportsFooter() && isset($data['footer'])) {
            if (strlen($data['footer']) > $this->getMaxFooterLength()) {
                return false;
            }
        }

        // Check header requirement for list
        if ($this->requiresHeader() && empty($data['header'])) {
            return false;
        }

        // Check options count
        $optionsCount = $this->getOptionsCount($data);
        if ($optionsCount < $this->getMinOptions() || $optionsCount > $this->getMaxOptions()) {
            return false;
        }

        return true;
    }

    /**
     * Get options count from message data
     *
     * @param array $data
     * @return int
     */
    private function getOptionsCount(array $data): int
    {
        return match($this) {
            self::BUTTON => count($data['buttons'] ?? []),
            self::LIST => $this->countListOptions($data['sections'] ?? []),
            self::FLOW => 1, // Flow always has 1 option
        };
    }

    /**
     * Count total options in list sections
     *
     * @param array $sections
     * @return int
     */
    private function countListOptions(array $sections): int
    {
        $total = 0;
        foreach ($sections as $section) {
            $total += count($section['rows'] ?? []);
        }
        return $total;
    }

    /**
     * Convert to WhatsApp API interactive payload format
     *
     * @param array $data
     * @return array
     */
    public function toWhatsAppPayload(array $data): array
    {
        $payload = [
            'type' => 'interactive',
            'interactive' => [
                'type' => $this->value,
                'body' => [
                    'text' => $data['body'] ?? ''
                ]
            ]
        ];

        // Add header if provided
        if (!empty($data['header'])) {
            $payload['interactive']['header'] = [
                'type' => 'text',
                'text' => $data['header']
            ];
        }

        // Add footer if supported and provided
        if ($this->supportsFooter() && !empty($data['footer'])) {
            $payload['interactive']['footer'] = [
                'text' => $data['footer']
            ];
        }

        // Add type-specific action
        $payload['interactive']['action'] = match($this) {
            self::BUTTON => ['buttons' => $data['buttons'] ?? []],
            self::LIST => [
                'button' => $data['button_text'] ?? 'Ver opções',
                'sections' => $data['sections'] ?? []
            ],
            self::FLOW => [
                'name' => 'flow',
                'parameters' => $data['flow_parameters'] ?? []
            ],
        };

        return $payload;
    }

    /**
     * Get compatible button types for this interactive type
     *
     * @return array<WhatsAppButtonType>
     */
    public function getCompatibleButtonTypes(): array
    {
        return match($this) {
            self::BUTTON => [WhatsAppButtonType::REPLY],
            self::LIST => [], // List uses rows, not buttons
            self::FLOW => [WhatsAppButtonType::FLOW],
        };
    }

    /**
     * Check if this interactive type is compatible with a button type
     *
     * @param WhatsAppButtonType $buttonType
     * @return bool
     */
    public function isCompatibleWith(WhatsAppButtonType $buttonType): bool
    {
        return in_array($buttonType, $this->getCompatibleButtonTypes());
    }
}
