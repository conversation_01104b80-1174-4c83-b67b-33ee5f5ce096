<?php

namespace App\Jobs;

use App\Repositories\CampaignRepository;
use App\Repositories\CampaignAnalyticsRepository;
use App\Models\CampaignPerformanceSnapshot;
use App\Helpers\DBLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculateCampaignAnalytics implements ShouldQueue
{
    // TODO: this is not implemented at all
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 1800; // 30 minutes
    public int $tries = 3;

    private CampaignRepository $campaignRepository;
    private CampaignAnalyticsRepository $analyticsRepository;

    public function __construct()
    {
        $this->campaignRepository = app(CampaignRepository::class);
        $this->analyticsRepository = app(CampaignAnalyticsRepository::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $processedCount = 0;
            $successCount = 0;
            $errorCount = 0;
            $snapshotsCreated = 0;

            // Get campaigns that need analytics calculation
            $campaigns = $this->getCampaignsNeedingAnalytics();

            if (empty($campaigns) || count($campaigns) === 0) {
                DBLog::logInfo(
                    "No campaigns need analytics calculation",
                    "CalculateCampaignAnalytics",
                );
                return;
            }

            foreach ($campaigns as $campaign) {
                try {
                    $processedCount++;

                    // Calculate analytics
                    $analytics = $this->analyticsRepository->calculateAndStore($campaign->id);
                    $successCount++;

                    // Create daily snapshot if needed
                    if ($this->shouldCreateSnapshot($campaign->id)) {
                        $this->createPerformanceSnapshot($campaign->id, $analytics);
                        $snapshotsCreated++;
                    }

                } catch (\Throwable $e) {
                    $errorCount++;

                    DBLog::logError(
                        "Failed to calculate analytics for campaign: {$e->getMessage()}",
                        "CalculateCampaignAnalytics",
                        $campaign->organization_id,
                        null,
                        ['campaign_id' => $campaign->id]
                    );
                }
            }

            DBLog::logInfo(
                "Campaign analytics calculation completed",
                "CalculateCampaignAnalytics",
                null,
                null,
                [
                    'processed_count' => $processedCount,
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'snapshots_created' => $snapshotsCreated
                ]
            );

        } catch (\Throwable $e) {
            DBLog::logError(
                "Campaign analytics calculation job failed: {$e->getMessage()}",
                "CalculateCampaignAnalytics",
                null,
                null
            );
            throw $e;
        }
    }

    private function getCampaignsNeedingAnalytics(): array
    {
        return [];
        // TODO: implement this
        // Get campaigns that:
        // 1. Have messages
        // 2. Don't have recent analytics (older than 1 hour) or no analytics at all
        // 3. Are not in draft status

        $campaigns = \App\Models\Campaign::whereHas('messages')
                                        ->where('status', '!=', \App\Enums\CampaignStatus::DRAFT)
                                        ->whereDoesntHave('analytics', function($query) {
                                            $query->where('calculated_at', '>', now()->subHour());
                                        })
                                        ->limit(50) // Process in batches
                                        ->get();

        return $campaigns->map(function($campaign) {
            return $this->campaignRepository->fetchById($campaign->id);
        })->toArray();
    }

    private function shouldCreateSnapshot(int $campaign_id): bool
    {
        // Check if we already have a snapshot for today
        return !CampaignPerformanceSnapshot::where('campaign_id', $campaign_id)
                                           ->where('snapshot_date', today())
                                           ->exists();
    }

    private function createPerformanceSnapshot(int $campaign_id, $analytics): void
    {
        CampaignPerformanceSnapshot::create([
            'campaign_id' => $campaign_id,
            'snapshot_date' => today(),
            'metrics_json' => $analytics->toArray(),
            'total_messages_at_date' => $analytics->total_messages,
            'sent_count_at_date' => $analytics->sent_count,
            'delivered_count_at_date' => $analytics->delivered_count,
            'failed_count_at_date' => $analytics->failed_count,
            'read_count_at_date' => $analytics->read_count,
            'response_count_at_date' => $analytics->response_count,
            'delivery_rate_at_date' => $analytics->delivery_rate,
            'read_rate_at_date' => $analytics->read_rate,
            'response_rate_at_date' => $analytics->response_rate,
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['analytics', 'campaign-analytics'];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [300, 900, 1800]; // 5 minutes, 15 minutes, 30 minutes
    }

    /**
     * Determine if the job should be retried based on the exception.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(6);
    }
}
