<?php

namespace App\Jobs;

use App\Repositories\MessageRepository;
use App\Repositories\MessageDeliveryAttemptRepository;
use App\Enums\MessageStatus;
use App\Helpers\DBLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessMessageRetries implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 600; // 10 minutes
    public int $tries = 3;

    private MessageRepository $messageRepository;
    private MessageDeliveryAttemptRepository $deliveryAttemptRepository;

    public function __construct()
    {
        $this->messageRepository = app(MessageRepository::class);
        $this->deliveryAttemptRepository = app(MessageDeliveryAttemptRepository::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $processedCount = 0;
            $successCount = 0;
            $failedCount = 0;

            // Get messages ready for retry
            $messagesForRetry = $this->messageRepository->getMessagesReadyForRetry(100);

            if (empty($messagesForRetry)) {
                DBLog::logInfo(
                    "No messages ready for retry",
                    "ProcessMessageRetries",
                    null,
                    null
                );
                return;
            }

            foreach ($messagesForRetry as $message) {
                try {
                    $processedCount++;
                    
                    // Simulate message sending (this would be replaced with actual WhatsApp API call)
                    $result = $this->attemptMessageDelivery($message);
                    
                    if ($result['success']) {
                        $successCount++;
                        $this->recordSuccessfulDelivery($message, $result);
                    } else {
                        $failedCount++;
                        $this->recordFailedDelivery($message, $result);
                    }

                } catch (\Throwable $e) {
                    $failedCount++;
                    $this->recordFailedDelivery($message, [
                        'success' => false,
                        'error' => $e->getMessage(),
                        'whatsapp_response' => null
                    ]);

                    DBLog::logError(
                        "Failed to process message retry: {$e->getMessage()}",
                        "ProcessMessageRetries",
                        $message->organization_id,
                        null,
                        ['message_id' => $message->id]
                    );
                }
            }

            DBLog::logInfo(
                "Message retry processing completed",
                "ProcessMessageRetries",
                null,
                null,
                [
                    'processed_count' => $processedCount,
                    'success_count' => $successCount,
                    'failed_count' => $failedCount
                ]
            );

        } catch (\Throwable $e) {
            DBLog::logError(
                "Message retry job failed: {$e->getMessage()}",
                "ProcessMessageRetries",
                null,
                null
            );
            throw $e;
        }
    }

    private function attemptMessageDelivery($message): array
    {
        // This is a mock implementation
        // In a real scenario, this would call the WhatsApp API
        
        // Simulate different outcomes based on attempt number
        $successRate = match($message->delivery_attempts ?? 0) {
            0 => 0.7, // 70% success on first retry
            1 => 0.5, // 50% success on second retry
            2 => 0.3, // 30% success on third retry
            default => 0.1 // 10% success on subsequent retries
        };

        $isSuccess = (rand(1, 100) / 100) <= $successRate;

        if ($isSuccess) {
            return [
                'success' => true,
                'whatsapp_response' => [
                    'message_id' => 'wamid_' . uniqid(),
                    'status' => 'sent',
                    'timestamp' => now()->timestamp
                ]
            ];
        } else {
            $errors = [
                'Rate limit exceeded',
                'Invalid phone number',
                'Template not approved',
                'Network timeout',
                'Service temporarily unavailable'
            ];

            return [
                'success' => false,
                'error' => $errors[array_rand($errors)],
                'whatsapp_response' => [
                    'error_code' => rand(400, 500),
                    'error_message' => 'Delivery failed',
                    'timestamp' => now()->timestamp
                ]
            ];
        }
    }

    private function recordSuccessfulDelivery($message, array $result): void
    {
        // Update message in database
        \App\Models\Message::where('id', $message->id)
                          ->update([
                              'status' => MessageStatus::is_sent->value,
                              'delivery_attempts' => ($message->delivery_attempts ?? 0) + 1,
                              'last_attempt_at' => now(),
                              'next_retry_at' => null,
                              'last_error_message' => null,
                              'sent_at' => now(),
                              'is_sent' => true,
                              'is_fail' => false,
                          ]);

        // Create delivery attempt record
        $this->deliveryAttemptRepository->store(
            \App\Domains\ChatBot\MessageDeliveryAttempt::create(
                $message->id,
                ($message->delivery_attempts ?? 0) + 1,
                MessageStatus::is_sent,
                null,
                $result['whatsapp_response']
            )
        );
    }

    private function recordFailedDelivery($message, array $result): void
    {
        $newAttemptCount = ($message->delivery_attempts ?? 0) + 1;
        $maxRetries = $message->max_retries ?? 3;

        // Calculate next retry time if not at max retries
        $nextRetryAt = null;
        if ($newAttemptCount < $maxRetries) {
            $backoffMinutes = match($newAttemptCount) {
                1 => 5,   // 5 minutes after first failure
                2 => 15,  // 15 minutes after second failure
                3 => 60,  // 1 hour after third failure
                default => 240 // 4 hours for subsequent failures
            };
            $nextRetryAt = now()->addMinutes($backoffMinutes);
        }

        // Update message in database
        \App\Models\Message::where('id', $message->id)
                          ->update([
                              'status' => MessageStatus::is_failed->value,
                              'delivery_attempts' => $newAttemptCount,
                              'last_attempt_at' => now(),
                              'next_retry_at' => $nextRetryAt,
                              'last_error_message' => $result['error'] ?? 'Unknown error',
                              'is_fail' => true,
                          ]);

        // Create delivery attempt record
        $this->deliveryAttemptRepository->store(
            \App\Domains\ChatBot\MessageDeliveryAttempt::create(
                $message->id,
                $newAttemptCount,
                MessageStatus::is_failed,
                $result['error'] ?? 'Unknown error',
                $result['whatsapp_response']
            )
        );
    }
}
