<?php

namespace App\Jobs;

use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\UseCases\ChatBot\WhatsApp\SyncMessageStatus;
use App\Repositories\WhatsAppSyncLogRepository;
use App\Enums\SyncType;
use App\Enums\SyncStatus;
use App\Helpers\DBLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProactiveWhatsAppSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 900; // 15 minutes
    public int $tries = 3;

    private WhatsAppMessageRepository $whatsappMessageRepository;
    private SyncMessageStatus $syncMessageStatusUseCase;
    private WhatsAppSyncLogRepository $syncLogRepository;

    public function __construct()
    {
        $this->whatsappMessageRepository = app(WhatsAppMessageRepository::class);
        $this->syncMessageStatusUseCase = app(SyncMessageStatus::class);
        $this->syncLogRepository = app(WhatsAppSyncLogRepository::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $processedCount = 0;
            $successCount = 0;
            $failedCount = 0;
            $updatedCount = 0;

            // Get WhatsApp messages that need status check
            $messagesToSync = $this->getMessagesNeedingStatusCheck();

            if (empty($messagesToSync)) {
                DBLog::logInfo(
                    "No WhatsApp messages need status check",
                    "ProactiveWhatsAppSync",
                    null,
                    null
                );
                return;
            }

            foreach ($messagesToSync as $whatsappMessage) {
                try {
                    $processedCount++;
                    
                    // Sync message status
                    $result = $this->syncMessageStatusUseCase->execute(
                        $whatsappMessage->message_id,
                        $whatsappMessage->message->organization_id ?? 1 // Default org for now
                    );
                    
                    if ($result['sync_status'] === SyncStatus::SUCCESS->value) {
                        $successCount++;
                        if ($result['messages_updated'] > 0) {
                            $updatedCount++;
                        }
                    } else {
                        $failedCount++;
                    }

                    // Rate limiting - small delay between API calls
                    if ($processedCount % 10 === 0) {
                        sleep(1); // 1 second delay every 10 messages
                    }

                } catch (\Throwable $e) {
                    $failedCount++;
                    
                    DBLog::logError(
                        "Failed to sync WhatsApp message: {$e->getMessage()}",
                        "ProactiveWhatsAppSync",
                        $whatsappMessage->message->organization_id ?? null,
                        null,
                        [
                            'whatsapp_message_id' => $whatsappMessage->id,
                            'message_id' => $whatsappMessage->message_id
                        ]
                    );
                }
            }

            // Create overall sync log
            $overallStatus = $this->determineOverallStatus($successCount, $failedCount, $processedCount);
            
            $this->syncLogRepository->createSyncLog(
                SyncType::MESSAGE,
                0, // No specific entity for batch sync
                $overallStatus,
                [
                    'batch_sync' => true,
                    'processed_count' => $processedCount,
                    'success_count' => $successCount,
                    'failed_count' => $failedCount,
                    'updated_count' => $updatedCount
                ],
                $failedCount > 0 ? "Batch sync completed with {$failedCount} failures" : null,
                $processedCount,
                $updatedCount
            );

            DBLog::logInfo(
                "Proactive WhatsApp sync completed",
                "ProactiveWhatsAppSync",
                null,
                null,
                [
                    'processed_count' => $processedCount,
                    'success_count' => $successCount,
                    'failed_count' => $failedCount,
                    'updated_count' => $updatedCount,
                    'overall_status' => $overallStatus->value
                ]
            );

        } catch (\Throwable $e) {
            DBLog::logError(
                "Proactive WhatsApp sync job failed: {$e->getMessage()}",
                "ProactiveWhatsAppSync",
                null,
                null
            );
            throw $e;
        }
    }

    private function getMessagesNeedingStatusCheck(): array
    {
        // This would need to be implemented in WhatsAppMessageRepository
        // For now, return a mock implementation
        
        // In a real implementation, this would query:
        // - Messages sent in the last 24 hours that haven't been confirmed
        // - Messages that haven't been checked in the last 2 hours
        // - Messages marked as needing status check
        
        return [];
    }

    private function determineOverallStatus(int $success, int $failed, int $total): SyncStatus
    {
        if ($failed === 0) {
            return SyncStatus::SUCCESS;
        }
        
        if ($success === 0) {
            return SyncStatus::FAILED;
        }
        
        return SyncStatus::PARTIAL;
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['whatsapp-sync', 'proactive-sync'];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [60, 300, 900]; // 1 minute, 5 minutes, 15 minutes
    }

    /**
     * Determine if the job should be retried based on the exception.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(2);
    }
}
