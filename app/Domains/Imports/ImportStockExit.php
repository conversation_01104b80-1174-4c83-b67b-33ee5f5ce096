<?php

namespace App\Domains\Imports;

use App\Models\StockExit;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\Importable;

class ImportStockExit extends Core implements ToCollection
{
    use Importable;

    public bool $skipHeader = false;
    public array $columns_sql_to_fill = [
        'user_id',
        'brand_id',
        'product_id',
        'client_id',
        'project_id',
        'quantity',
        'value',
        'description',
    ];

    public function collection(Collection $collection) : void {
        $rows = $this->skipHeader ? $collection->skip(1) : $collection;
        foreach ($rows as $row) {
            StockExit::create([
                'organization_id' => $this->organization_id,
                'user_id' => $this->getUserIdByUser($row),
                'brand_id' => $this->getOrCreateBrandIdByBrand($row),
                'product_id' => $this->getProductIdByProduct($row),
                'client_id' => $this->getClientIdByClient($row),
                'project_id' => $this->getProjectIdByProject($row),
                'quantity' => $this->getRowIndexFromMap($row, 'quantity'),
                'value' => $this->getRowIndexFromMap($row, 'value'),
                'description' => $this->getRowIndexFromMap($row, 'description'),
            ]);
        }
    }
}
