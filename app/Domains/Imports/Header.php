<?php

namespace App\Domains\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class Header implements ToCollection
{
    public array $columns = [];

    public function collection(Collection $collection) {

        foreach ($collection as $row)  {
            for ($i=0; $i < count($row); $i++){
                $this->columns[$i] = $row[$i];
            }
            break;
        }
    }

    public function toJson() : string {
        return json_encode($this->columns);
    }
}
