<?php

namespace App\Domains\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\Importable;
use App\Models\Brand;

class ImportBrand extends Core implements ToCollection
{
    use Importable;

    public bool $skipHeader = false;
    public array $columns_sql_to_fill = [
        'name',
        'description',
    ];

    public function collection(Collection $collection) : void {
        $rows = $this->skipHeader ? $collection->skip(1) : $collection;
        foreach ($rows as $row) {
            Brand::create([
                'organization_id' => $this->organization_id,
                'name' => $this->getRowIndexFromMap($row, 'name'),
                'description' => $this->getRowIndexFromMap($row, 'description'),
            ]);
        }
    }
}
