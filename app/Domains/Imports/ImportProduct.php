<?php

namespace App\Domains\Imports;

use App\Helpers\DBLog;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\Importable;
use App\Models\Product;

class ImportProduct extends Core implements ToCollection
{
    use Importable;

    public bool $skipHeader = false;
    public array $columns_sql_to_fill = [
        'brand_id',
        'name',
        'description',
        'price',
        'unity',
        'last_priced_at'
    ];

    public function collection(Collection $collection) : void {
        $rows = $this->skipHeader ? $collection->skip(1) : $collection;
        foreach ($rows as $row) {
            $product = [
                'organization_id' => $this->organization_id,
                'brand_id' => $this->getOrCreateBrandIdByBrand($row),
                'name' => $this->getRowIndexFromMap($row, 'name'),
                'description' => $this->getRowIndexFromMap($row, 'description'),
                'price' => $this->getRowIndexFromMap($row, 'price'),
                'unity' => $this->getRowIndexFromMap($row, 'unity'),
                'last_priced_at' => $this->getDateRowIndexFromMap($row, 'last_priced_at'),
            ];

            DBLog::log(
                "[IMPORTING-PRODUCT]", "import", request()->user()->organization_id, request()->user()->id, $product
            );

            Product::create($product);
        }
    }
}
