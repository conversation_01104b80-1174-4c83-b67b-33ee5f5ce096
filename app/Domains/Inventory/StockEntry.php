<?php

namespace App\Domains\Inventory;

use App\Domains\User;
use Carbon\Carbon;

class StockEntry
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $shop_id;
    public ?int $user_id;
    public ?int $brand_id;
    public ?int $product_id;
    public ?int $batch_id;
    public ?int $client_id;
    public ?int $project_id;
    public ?int $quantity;
    public ?float $value;
    public ?string $description;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Product $product;
    public ?Project $project;
    public ?Batch $batch;
    public ?Shop $shop;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $shop_id,
        ?int $user_id,
        ?int $brand_id,
        ?int $product_id,
        ?int $batch_id,
        ?int $client_id,
        ?int $project_id,
        ?int $quantity,
        ?float $value,
        ?string $description,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Product $product = null,
        ?Project $project = null,
        ?Batch $batch = null,
        ?Shop $shop = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->shop_id = $shop_id;
        $this->user_id = $user_id;
        $this->brand_id = $brand_id;
        $this->product_id = $product_id;
        $this->batch_id = $batch_id;
        $this->client_id = $client_id;
        $this->project_id = $project_id;
        $this->quantity = $quantity;
        $this->value = $value;
        $this->description = $description;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->product = $product;
        $this->project = $project;
        $this->batch = $batch;
        $this->shop = $shop;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "shop_id" => $this->shop_id,
            "user_id" => $this->user_id,
            "brand_id" => $this->brand_id,
            "product_id" => $this->product_id,
            "batch_id" => $this->batch_id,
            "client_id" => $this->client_id,
            "project_id" => $this->project_id,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => ($this->user) ? $this->user->toArray() : null,
            "product" => ($this->product) ? $this->product->toArray() : null,
            "project" => ($this->project) ? $this->project->toArray() : null,
            "batch" => ($this->batch) ? $this->batch->toArray() : null,
            "shop" => ($this->shop) ? $this->shop->toArray() : null
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "shop_id" => $this->shop_id,
            "user_id" => $this->user_id,
            "brand_id" => $this->brand_id,
            "product_id" => $this->product_id,
            "batch_id" => $this->batch_id,
            "client_id" => $this->client_id,
            "project_id" => $this->project_id,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "brand_id" => $this->brand_id,
            "product_id" => $this->product_id,
            "batch_id" => $this->batch_id,
            "client_id" => $this->client_id,
            "project_id" => $this->project_id,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
        ];
    }

    public function calculateValue() : void {
        $this->value = $this->product->price * $this->quantity;
    }
}
