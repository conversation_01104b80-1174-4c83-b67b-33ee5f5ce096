<?php

namespace App\Domains\Inventory\Report;

class StockEntryRow
{
    public ?string $user;
    public ?string $product;
    public ?int $quantity;
    public ?float $value;
    public ?string $created_at;


    public function __construct(
        ?string $user,
        ?string $product,
        ?int $quantity,
        ?float $value,
        ?string $created_at
    ){
        $this->user = $user;
        $this->product = $product;
        $this->quantity = $quantity;
        $this->value = $value;
        $this->created_at = $created_at;
    }

    public function toArray(): array
    {
        return [
            "user" => $this->user,
            "product" => $this->product,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "created_at" => $this->created_at,
        ];
    }
}
