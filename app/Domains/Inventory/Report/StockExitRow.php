<?php

namespace App\Domains\Inventory\Report;

class StockExitRow
{
    public ?string $user;
    public ?string $product;
    public ?string $client;
    public ?string $project;
    public ?int $quantity;
    public ?float $value;
    public ?string $created_at;


    public function __construct(
        ?string $user,
        ?string $product,
        ?string $client,
        ?string $project,
        ?int $quantity,
        ?float $value,
        ?string $created_at
    ){
        $this->user = $user;
        $this->product = $product;
        $this->client = $client;
        $this->project = $project;
        $this->quantity = $quantity;
        $this->value = $value;
        $this->created_at = $created_at;
    }

    public function toArray(): array
    {
        return [
            "user" => $this->user,
            "product" => $this->product,
            "client" => $this->client,
            "project" => $this->project,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "created_at" => $this->created_at,
        ];
    }
}
