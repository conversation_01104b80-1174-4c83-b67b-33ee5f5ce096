<?php

namespace App\Domains\Inventory;

use App\Domains\User;
use Carbon\Carbon;

class Department
{
    public ?int $id;
    public ?int $organization_id;
    public ?string $name;
    public ?bool $is_active;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?string $name,
        ?bool $is_active,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->name = $name;
        $this->is_active = $is_active;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "is_active" => $this->is_active,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "is_active" => $this->is_active,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "name" => $this->name,
            "is_active" => $this->is_active,
        ];
    }
}
