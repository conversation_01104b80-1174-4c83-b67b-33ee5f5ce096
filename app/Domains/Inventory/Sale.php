<?php

namespace App\Domains\Inventory;

use App\Domains\Organization;
use App\Services\ASAAS\Domains\AsaasSale;
use App\Domains\User;
use Carbon\Carbon;

class Sale
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $shop_id;
    public ?int $client_id;
    public ?float $total_value;
    public ?string $description;
    public ?string $billing_type;
    public ?Carbon $due_date;
    public ?int $installment_count;
    public ?float $installment_value;
    public ?int $installment_number;
    public ?array $discount_config;
    public ?array $fine_config;
    public ?array $interest_config;
    public ?array $split_config;
    public ?array $credit_card_data;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Shop $shop;
    public ?Client $client;
    public ?Organization $organization;

    /** @var Item[] $items */
    public ?array $items;

    public ?AsaasSale $asaas;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $user_id,
        ?int $shop_id,
        ?int $client_id,
        ?float $total_value,
        ?string $description = null,
        ?string $billing_type = null,
        ?Carbon $due_date = null,
        ?int $installment_count = null,
        ?float $installment_value = null,
        ?int $installment_number = null,
        ?array $discount_config = null,
        ?array $fine_config = null,
        ?array $interest_config = null,
        ?array $split_config = null,
        ?array $credit_card_data = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Shop $shop = null,
        ?Client $client = null,
        ?Organization $organization = null,
        ?array $items = null,
        ?AsaasSale $asaas = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->shop_id = $shop_id;
        $this->client_id = $client_id;
        $this->total_value = $total_value;
        $this->description = $description;
        $this->billing_type = $billing_type;
        $this->due_date = $due_date;
        $this->installment_count = $installment_count;
        $this->installment_value = $installment_value;
        $this->installment_number = $installment_number;
        $this->discount_config = $discount_config;
        $this->fine_config = $fine_config;
        $this->interest_config = $interest_config;
        $this->split_config = $split_config;
        $this->credit_card_data = $credit_card_data;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->shop = $shop;
        $this->client = $client;
        $this->organization = $organization;
        $this->items = $items;
        $this->asaas = $asaas;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "shop_id" => $this->shop_id,
            "client_id" => $this->client_id,
            "total_value" => $this->total_value,
            "description" => $this->description,
            "billing_type" => $this->billing_type,
            "due_date" => $this->due_date?->format("Y-m-d"),
            "installment_count" => $this->installment_count,
            "installment_value" => $this->installment_value,
            "installment_number" => $this->installment_number,
            "discount_config" => $this->discount_config,
            "fine_config" => $this->fine_config,
            "interest_config" => $this->interest_config,
            "split_config" => $this->split_config,
            "credit_card_data" => $this->credit_card_data,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => ($this->user) ? $this->user->toArray() : null,
            "shop" => ($this->shop) ? $this->shop->toArray() : null,
            "client" => ($this->client) ? $this->client->toArray() : null,
            "asaas" => ($this->asaas) ? $this->asaas->toArray() : null,
            "organization" => ($this->organization) ? $this->organization->toArray() : null,
            "items" => $this->items,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "shop_id" => $this->shop_id,
            "client_id" => $this->client_id,
            "total_value" => $this->total_value,
            "description" => $this->description,
            "billing_type" => $this->billing_type,
            "due_date" => $this->due_date,
            "installment_count" => $this->installment_count,
            "installment_value" => $this->installment_value,
            "installment_number" => $this->installment_number,
            "discount_config" => $this->discount_config,
            "fine_config" => $this->fine_config,
            "interest_config" => $this->interest_config,
            "split_config" => $this->split_config,
            "credit_card_data" => $this->credit_card_data,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "user_id" => $this->user_id,
            "shop_id" => $this->shop_id,
            "client_id" => $this->client_id,
            "total_value" => $this->total_value,
        ];
    }

    // ========== ASAAS INTEGRATION METHODS ==========

    /**
     * Check if has ASAAS payment
     */
    public function hasAsaasPayment(): bool
    {
        return $this->asaas !== null && $this->asaas->hasAsaasIntegration();
    }

    /**
     * Check if payment is paid
     */
    public function isPaid(): bool
    {
        return $this->asaas?->isPaid() ?? false;
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->asaas?->isPending() ?? false;
    }

    /**
     * Check if payment is overdue
     */
    public function isOverdue(): bool
    {
        return $this->asaas?->isOverdue() ?? false;
    }

    /**
     * Get ASAAS payment ID
     */
    public function getAsaasPaymentId(): ?string
    {
        return $this->asaas?->asaas_payment_id;
    }

    public function toAsaasPayload(): array
    {
        $payload = [
            'customer'        => $this->client->asaas->asaas_customer_id,
            'billingType'     => $this->billing_type,
            'value'           => $this->total_value,
            'dueDate'         => $this->due_date->format('Y-m-d'),
            'description'     => $this->description ?? "Venda #{$this->id}",
            'externalReference' => $this->id,
        ];
        if ($this->installment_count && $this->installment_value) {
            $payload['installmentCount'] = $this->installment_count;
            $payload['installmentValue'] = $this->installment_value;
        }
        if (!empty($this->credit_card_data)) {
            $payload['creditCard'] = $this->credit_card_data['creditCard'] ?? [];
            $payload['creditCardHolderInfo'] = $this->credit_card_data['creditCardHolderInfo'] ?? [];
        }

        if (!empty($this->discount_config)) { $payload['discount'] = $this->discount_config; }
        if (!empty($this->fine_config)) { $payload['fine'] = $this->fine_config; }
        if (!empty($this->interest_config)) { $payload['interest'] = $this->interest_config; }
        if (!empty($this->split_config)) { $payload['split'] = $this->split_config; }

        return $payload;
    }
}
