<?php

namespace App\Domains\Inventory;

use Carbon\Carbon;
use Exception;

class Stock
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $shop_id;
    public ?int $brand_id;
    public ?int $product_id;
    public ?int $quantity;
    public ?float $value;
    public ?string $description;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?Product $product;
    public ?Shop $shop;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $shop_id,
        ?int $brand_id,
        ?int $product_id,
        ?int $quantity,
        ?float $value,
        ?string $description,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Product $product = null,
        ?Shop $shop = null,
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->shop_id = $shop_id;
        $this->brand_id = $brand_id;
        $this->product_id = $product_id;
        $this->quantity = $quantity;
        $this->value = $value;
        $this->description = $description;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->product = $product;
        $this->shop = $shop;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "shop_id" => $this->shop_id,
            "brand_id" => $this->brand_id,
            "product_id" => $this->product_id,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "product" => ($this->product) ? $this->product->toArray() : null,
            "shop" => ($this->shop) ? $this->shop->toArray() : null
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "shop_id" => $this->shop_id,
            "brand_id" => $this->brand_id,
            "product_id" => $this->product_id,
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "quantity" => $this->quantity,
            "value" => $this->value,
            "description" => $this->description,
        ];
    }

    public function increaseStock(int $quantity) : void {
        $this->quantity = $this->quantity + $quantity;
        $this->value = ($this->quantity) * $this->product->price;
    }

    /**
     * @throws Exception
     */
    public function decreaseStock(int $quantity) : void {
        $this->quantity = $this->quantity - $quantity;
        $this->value = ($this->quantity) * $this->product->price;
        if(($this->value < 0) || ($this->quantity < 0)){
            throw new Exception("There is not enough stock available of this product!");
        }
    }

    public function refreshFromPrice(float $new_price) : void {
        $this->value = ($this->quantity) * $new_price;
    }

}
