<?php

namespace App\Domains\ChatBot;

use App\Domains\User;
use Carbon\Carbon;
use App\Domains\ChatBot\Interaction;

class Conversation
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $client_id;
    public ?int $flow_id;
    public ?int $phone_number_id;
    public ?int $current_step_id;
    public ?string $json;
    public ?bool $is_finished;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    public ?User $user;
    public ?Flow $flow;
    public ?PhoneNumber $phone_number;
    public ?Step $current_step;

    /** @var Interaction[]|null $interactions  */
    public ?array $interactions;

    public function __construct(
        ?int $id = null,
        ?int $organization_id = null,
        ?int $user_id = null,
        ?int $client_id = null,
        ?int $flow_id = null,
        ?int $phone_number_id = null,
        ?int $current_step_id = null,
        ?string $json = null,
        ?bool $is_finished = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?User $user = null,
        ?Flow $flow = null,
        ?PhoneNumber $phone_number = null,
        ?Step $current_step = null,
        ?array $interactions = null
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->client_id = $client_id;
        $this->flow_id = $flow_id;
        $this->phone_number_id = $phone_number_id;
        $this->current_step_id = $current_step_id;
        $this->json = $json;
        $this->is_finished = $is_finished;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->user = $user;
        $this->flow = $flow;
        $this->phone_number = $phone_number;
        $this->current_step = $current_step;
        $this->interactions = $interactions;
    }

    public function toArray(): array
    {
        $interactionsArray = null;
        if ($this->interactions) {
            $interactionsArray = [];
            foreach ($this->interactions as $interaction) {
                $interactionsArray[] = $interaction->toArray();
            }
        }

        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number_id" => $this->phone_number_id,
            "current_step_id" => $this->current_step_id,
            "json" => $this->json,
            "is_finished" => $this->is_finished,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "user" => $this->user?->toArray(),
            "flow" => $this->flow?->toArray(),
            "phone_number" => $this->phone_number?->toArray(),
            "current_step" => $this->current_step?->toArray(),
            "interactions" => $interactionsArray,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number_id" => $this->phone_number_id,
            "current_step_id" => $this->current_step_id,
            "json" => $this->json,
            "is_finished" => $this->is_finished,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "client_id" => $this->client_id,
            "flow_id" => $this->flow_id,
            "phone_number_id" => $this->phone_number_id,
            "current_step_id" => $this->current_step_id,
            "json" => $this->json,
            "is_finished" => $this->is_finished,
        ];
    }
}
