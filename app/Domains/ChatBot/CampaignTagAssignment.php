<?php

namespace App\Domains\ChatBot;

use Carbon\Carbon;

class CampaignTagAssignment
{
    public ?int $id;
    public ?int $campaign_id;
    public ?int $tag_id;
    public ?Carbon $assigned_at;

    public ?Campaign $campaign;
    public ?Tag $tag;

    public function __construct(
        ?int $id = null,
        ?int $campaign_id = null,
        ?int $tag_id = null,
        ?Carbon $assigned_at = null,
        ?Campaign $campaign = null,
        ?Tag $tag = null
    ) {
        $this->id = $id;
        $this->campaign_id = $campaign_id;
        $this->tag_id = $tag_id;
        $this->assigned_at = $assigned_at ?? now();
        $this->campaign = $campaign;
        $this->tag = $tag;
    }

    public function toStoreArray(): array
    {
        return [
            'campaign_id' => $this->campaign_id,
            'tag_id' => $this->tag_id,
            'assigned_at' => $this->assigned_at,
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'campaign_id' => $this->campaign_id,
            'tag_id' => $this->tag_id,
            'assigned_at' => $this->assigned_at?->toISOString(),
            'campaign' => $this->campaign?->toArray(),
            'tag' => $this->tag?->toArray(),
        ];
    }

    /**
     * Create assignment from campaign and tag IDs
     */
    public static function create(int $campaign_id, int $tag_id): self
    {
        return new self(
            campaign_id: $campaign_id,
            tag_id: $tag_id,
            assigned_at: now()
        );
    }
}
