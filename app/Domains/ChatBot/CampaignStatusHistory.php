<?php

namespace App\Domains\ChatBot;

use App\Domains\User;
use App\Enums\CampaignStatus;
use Carbon\Carbon;

class CampaignStatusHistory
{
    public ?int $id;
    public ?int $campaign_id;
    public ?CampaignStatus $old_status;
    public ?CampaignStatus $new_status;
    public ?string $reason;
    public ?int $user_id;
    public ?array $metadata;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public ?Campaign $campaign;
    public ?User $user;

    public function __construct(
        ?int $id = null,
        ?int $campaign_id = null,
        ?CampaignStatus $old_status = null,
        ?CampaignStatus $new_status = null,
        ?string $reason = null,
        ?int $user_id = null,
        ?array $metadata = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Campaign $campaign = null,
        ?User $user = null
    ) {
        $this->id = $id;
        $this->campaign_id = $campaign_id;
        $this->old_status = $old_status;
        $this->new_status = $new_status;
        $this->reason = $reason;
        $this->user_id = $user_id;
        $this->metadata = $metadata;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->campaign = $campaign;
        $this->user = $user;
    }

    public function toStoreArray(): array
    {
        return [
            'campaign_id' => $this->campaign_id,
            'old_status' => $this->old_status?->value,
            'new_status' => $this->new_status?->value,
            'reason' => $this->reason,
            'user_id' => $this->user_id,
            'metadata' => $this->metadata,
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'campaign_id' => $this->campaign_id,
            'old_status' => $this->old_status?->value,
            'old_status_label' => $this->old_status?->label(),
            'new_status' => $this->new_status?->value,
            'new_status_label' => $this->new_status?->label(),
            'reason' => $this->reason,
            'user_id' => $this->user_id,
            'metadata' => $this->metadata,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'campaign' => $this->campaign?->toArray(),
            'user' => $this->user?->toArray(),
        ];
    }

    /**
     * Create a status change record
     */
    public static function create(
        int $campaign_id,
        ?CampaignStatus $old_status,
        CampaignStatus $new_status,
        ?string $reason = null,
        ?int $user_id = null,
        ?array $metadata = null
    ): self {
        return new self(
            campaign_id: $campaign_id,
            old_status: $old_status,
            new_status: $new_status,
            reason: $reason,
            user_id: $user_id,
            metadata: $metadata,
            created_at: now()
        );
    }

    /**
     * Check if this is an initial status (no old status)
     */
    public function isInitialStatus(): bool
    {
        return $this->old_status === null;
    }

    /**
     * Get status change direction (upgrade, downgrade, lateral)
     */
    public function getChangeDirection(): string
    {
        if ($this->isInitialStatus()) {
            return 'initial';
        }

        $statusOrder = [
            CampaignStatus::DRAFT->value => 1,
            CampaignStatus::SCHEDULED->value => 2,
            CampaignStatus::SENDING->value => 3,
            CampaignStatus::COMPLETED->value => 4,
            CampaignStatus::FAILED->value => 0, // Failed is considered a downgrade
            CampaignStatus::CANCELLED->value => 0, // Cancelled is considered a downgrade
        ];

        $oldOrder = $statusOrder[$this->old_status->value] ?? 0;
        $newOrder = $statusOrder[$this->new_status->value] ?? 0;

        if ($newOrder > $oldOrder) {
            return 'upgrade';
        } elseif ($newOrder < $oldOrder) {
            return 'downgrade';
        } else {
            return 'lateral';
        }
    }
}
