<?php

namespace App\Domains\ChatBot;

class Lead
{
    public function __construct(
        public ?int $id,
        public int $organization_id,
        public int $client_id,
        public string $source,
        public string $status,
        public string $priority,
        public ?string $title = null,
        public ?string $description = null,
        public ?string $notes = null,
        public ?float $estimated_value = null,
        public ?string $service_type = null,
        public ?string $budget_range = null,
        public ?string $timeline = null,
        public ?string $company = null,
        public ?array $custom_fields = null,
        public string $created_via = 'manual',
        public ?\DateTime $contacted_at = null,
        public ?\DateTime $qualified_at = null,
        public ?\DateTime $closed_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?\DateTime $deleted_at = null
    ) {}

    /**
     * Check if lead is active (not closed)
     */
    public function isActive(): bool
    {
        return !in_array($this->status, ['won', 'lost']);
    }

    /**
     * Check if lead is qualified
     */
    public function isQualified(): bool
    {
        return in_array($this->status, ['qualified', 'proposal', 'won']);
    }

    /**
     * Check if lead is closed
     */
    public function isClosed(): bool
    {
        return in_array($this->status, ['won', 'lost']);
    }

    /**
     * Check if lead was created via chatbot
     */
    public function isFromChatBot(): bool
    {
        return str_contains($this->source, 'chatbot') || $this->created_via === 'chatbot';
    }

    /**
     * Get priority level as number for sorting
     */
    public function getPriorityLevel(): int
    {
        return match($this->priority) {
            'urgent' => 4,
            'high' => 3,
            'medium' => 2,
            'low' => 1,
            default => 0
        };
    }

    /**
     * Mark lead as contacted
     */
    public function markAsContacted(): void
    {
        if ($this->status === 'new') {
            $this->status = 'contacted';
            $this->contacted_at = new \DateTime();
        }
    }

    /**
     * Mark lead as qualified
     */
    public function markAsQualified(): void
    {
        if (in_array($this->status, ['new', 'contacted'])) {
            $this->status = 'qualified';
            $this->qualified_at = new \DateTime();
        }
    }

    /**
     * Close lead as won
     */
    public function markAsWon(): void
    {
        $this->status = 'won';
        $this->closed_at = new \DateTime();
    }

    /**
     * Close lead as lost
     */
    public function markAsLost(): void
    {
        $this->status = 'lost';
        $this->closed_at = new \DateTime();
    }

    /**
     * Add note to existing notes
     */
    public function addNote(string $note): void
    {
        if ($this->notes) {
            $this->notes .= "\n\n" . date('Y-m-d H:i:s') . ": " . $note;
        } else {
            $this->notes = date('Y-m-d H:i:s') . ": " . $note;
        }
    }

    /**
     * Set custom field value
     */
    public function setCustomField(string $key, mixed $value): void
    {
        if (!$this->custom_fields) {
            $this->custom_fields = [];
        }
        $this->custom_fields[$key] = $value;
    }

    /**
     * Get custom field value
     */
    public function getCustomField(string $key): mixed
    {
        return $this->custom_fields[$key] ?? null;
    }

    /**
     * Convert to array for database storage
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'client_id' => $this->client_id,
            'source' => $this->source,
            'status' => $this->status,
            'priority' => $this->priority,
            'title' => $this->title,
            'description' => $this->description,
            'notes' => $this->notes,
            'estimated_value' => $this->estimated_value,
            'service_type' => $this->service_type,
            'budget_range' => $this->budget_range,
            'timeline' => $this->timeline,
            'company' => $this->company,
            'custom_fields' => $this->custom_fields,
            'created_via' => $this->created_via,
            'contacted_at' => $this->contacted_at?->format('Y-m-d H:i:s'),
            'qualified_at' => $this->qualified_at?->format('Y-m-d H:i:s'),
            'closed_at' => $this->closed_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'deleted_at' => $this->deleted_at?->format('Y-m-d H:i:s'),
        ];
    }
}
