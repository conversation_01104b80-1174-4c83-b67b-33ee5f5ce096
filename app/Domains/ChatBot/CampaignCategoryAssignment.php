<?php

namespace App\Domains\ChatBot;

use Carbon\Carbon;

class CampaignCategoryAssignment
{
    public ?int $id;
    public ?int $campaign_id;
    public ?int $category_id;
    public ?Carbon $assigned_at;

    public ?Campaign $campaign;
    public ?Category $category;

    public function __construct(
        ?int $id = null,
        ?int $campaign_id = null,
        ?int $category_id = null,
        ?Carbon $assigned_at = null,
        ?Campaign $campaign = null,
        ?Category $category = null
    ) {
        $this->id = $id;
        $this->campaign_id = $campaign_id;
        $this->category_id = $category_id;
        $this->assigned_at = $assigned_at ?? now();
        $this->campaign = $campaign;
        $this->category = $category;
    }

    public function toStoreArray(): array
    {
        return [
            'campaign_id' => $this->campaign_id,
            'category_id' => $this->category_id,
            'assigned_at' => $this->assigned_at,
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'campaign_id' => $this->campaign_id,
            'category_id' => $this->category_id,
            'assigned_at' => $this->assigned_at?->toISOString(),
            'campaign' => $this->campaign?->toArray(),
            'category' => $this->category?->toArray(),
        ];
    }

    /**
     * Create assignment from campaign and category IDs
     */
    public static function create(int $campaign_id, int $category_id): self
    {
        return new self(
            campaign_id: $campaign_id,
            category_id: $category_id,
            assigned_at: now()
        );
    }
}
