<?php

namespace App\Domains\ChatBot;

use App\Enums\PublishingService;
use Carbon\Carbon;

class TemplatePublishing
{
    public ?int $id = null;
    public ?int $template_id;

    /** @var null|PublishingService */
    public ?PublishingService $service_id;
    public bool $is_queued = false;
    public bool $is_published = false;
    public ?string $status = null;
    public ?Carbon $published_at = null;
    public ?Carbon $created_at = null;
    public ?Carbon $updated_at = null;

    public function __construct(
        ?int $id,
        ?int $template_id,
        ?PublishingService $service_id,
        ?bool $is_queued = false,
        ?bool $is_published = false,
        ?string $status = null,
        ?Carbon $published_at = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null
    ) {
        $this->id = $id;
        $this->template_id = $template_id;
        $this->service_id = $service_id;
        $this->is_queued = $is_queued;
        $this->is_published = $is_published;
        $this->status = $status;
        $this->published_at = $published_at;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
    }

    public function toStoreArray(): array {
        return [
            'template_id' => $this->template_id,
            'service_id' => $this->service_id?->value,
            'is_queued' => $this->is_queued,
            'is_published' => $this->is_published,
            'status' => $this->status,
            'published_at' => $this->published_at?->format('Y-m-d H:i:s'),
        ];
    }

    public function toUpdateArray(): array {
        return [
            'is_queued' => $this->is_queued,
            'is_published' => $this->is_published,
            'status' => $this->status,
            'published_at' => $this->published_at?->format('Y-m-d H:i:s'),
        ];
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'template_id' => $this->template_id,
            'service_id' => $this->service_id?->value,
            'service' => $this->service_id?->name,
            'is_queued' => $this->is_queued,
            'is_published' => $this->is_published,
            'status' => $this->status,
            'published_at' => $this->published_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    public function publish(): void {
        $this->is_published = true;
        $this->is_queued = false;
        $this->status = "published";
        $this->published_at = Carbon::now();
    }

    public function fail(): void {
        $this->is_published = false;
        $this->is_queued = false;
        $this->status = "failed";
        $this->published_at = null;
    }

    public function queue(): void {
        $this->is_published = false;
        $this->is_queued = true;
        $this->status = "to_publish";
        $this->published_at = null;
    }
}
