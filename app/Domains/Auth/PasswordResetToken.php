<?php

namespace App\Domains\Auth;

use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class PasswordResetToken
{
    public ?int $id;
    public string $email;
    public string $token;           // Token em texto plano (para envio)
    public string $hashedToken;     // Token hasheado (para armazenamento)
    public ?int $organization_id;
    public Carbon $expires_at;
    public ?Carbon $used_at;
    public string $ip_address;
    public ?string $user_agent;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id,
        string $email,
        string $token,
        string $hashedToken,
        ?int $organization_id,
        Carbon $expires_at,
        ?Carbon $used_at,
        string $ip_address,
        ?string $user_agent,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null
    ) {
        $this->id = $id;
        $this->email = $email;
        $this->token = $token;
        $this->hashedToken = $hashedToken;
        $this->organization_id = $organization_id;
        $this->expires_at = $expires_at;
        $this->used_at = $used_at;
        $this->ip_address = $ip_address;
        $this->user_agent = $user_agent;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
    }

    /**
     * Generate a secure token
     */
    public static function generateToken(): string
    {
        return Str::random(64);
    }

    /**
     * Hash a token for secure storage
     */
    public static function hashToken(string $token): string
    {
        return Hash::make($token);
    }

    /**
     * Verify if a plain token matches the hashed token
     */
    public function verifyToken(string $plainToken): bool
    {
        return Hash::check($plainToken, $this->hashedToken);
    }

    /**
     * Check if the token has expired
     */
    public function isExpired(): bool
    {
        return Carbon::now()->isAfter($this->expires_at);
    }

    /**
     * Check if the token has been used
     */
    public function isUsed(): bool
    {
        return $this->used_at !== null;
    }

    /**
     * Mark the token as used
     */
    public function markAsUsed(): void
    {
        $this->used_at = Carbon::now();
    }

    /**
     * Check if the token is valid (not expired and not used)
     */
    public function isValid(): bool
    {
        return !$this->isExpired() && !$this->isUsed();
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'token' => $this->token,
            'hashed_token' => $this->hashedToken,
            'organization_id' => $this->organization_id,
            'expires_at' => $this->expires_at?->format('Y-m-d H:i:s'),
            'used_at' => $this->used_at?->format('Y-m-d H:i:s'),
            'ip_address' => $this->ip_address,
            'user_agent' => $this->user_agent,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            'email' => $this->email,
            'token' => $this->token,
            'hashed_token' => $this->hashedToken,
            'organization_id' => $this->organization_id,
            'expires_at' => $this->expires_at,
            'used_at' => $this->used_at,
            'ip_address' => $this->ip_address,
            'user_agent' => $this->user_agent,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'used_at' => $this->used_at,
        ];
    }
}
