<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockEntryFilters;
use App\Domains\Filters\StockEntryReportFilters;
use App\Domains\Inventory\Report\StockEntryReport;
use App\Domains\Inventory\StockEntry as StockEntryDomain;
use App\Factories\Inventory\StockEntryFactory;
use App\Factories\Report\StockEntryReportFactory;
use App\Models\StockEntry;
use EloquentBuilder;

class StockEntryRepository
{
    private StockEntryFactory $stockEntryFactory;
    private StockEntryReportFactory $stockEntryReportFactory;

    public function __construct(
        StockEntryFactory $stockEntryFactory,
        StockEntryReportFactory $stockEntryReportFactory
    ){
        $this->stockEntryFactory = $stockEntryFactory;
        $this->stockEntryReportFactory = $stockEntryReportFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(StockEntryFilters $filters, OrderBy $orderBy) : array {
        $stockEntries = [];

        $models = EloquentBuilder::to(StockEntry::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $stockEntries[] = $this->stockEntryFactory->buildFromModel($model);
        }

        return [
            'data' => $stockEntries,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization(int $organization_id, StockEntryFilters $filters, OrderBy $orderBy) : array {
        $stockEntries = [];

        $models =  EloquentBuilder::to(StockEntry::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $stockEntries[] = $this->stockEntryFactory->buildFromModel($model);
        }

        return [
            'data' => $stockEntries,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, StockEntryFilters $filters): int {
        return EloquentBuilder::to(StockEntry::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, StockEntryFilters $filters, string $sum): float|int {
        return EloquentBuilder::to(StockEntry::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($sum);
    }

    public function store(StockEntryDomain $stockEntry) : StockEntryDomain {
        $savedStockEntry = StockEntry::create($stockEntry->toStoreArray());

        $stockEntry->id = $savedStockEntry->id;

        return $stockEntry;
    }

    public function update(StockEntryDomain $stockEntry, int $organization_id) : StockEntryDomain {
        StockEntry::where('id', $stockEntry->id)
            ->where('organization_id', $organization_id)
            ->update($stockEntry->toUpdateArray());

        return $stockEntry;
    }

    public function fetchById(int $id) : StockEntryDomain {
        return $this->stockEntryFactory->buildFromModel(
            StockEntry::findOrFail($id)
        );
    }

    public function delete(StockEntryDomain $stockEntry) : bool {
        return StockEntry::find($stockEntry->id)->delete();
    }

    /**
     * @param int $organization_id
     * @param StockEntryReportFilters $filters
     * @param OrderBy $orderBy
     * @return StockEntryReport
     */
    public function fetchReport(
        int $organization_id,
        StockEntryReportFilters $filters,
        OrderBy $orderBy,
        ?string $grouped_by
    ) : StockEntryReport {
        $models = EloquentBuilder::to(StockEntry::class, $filters->filters)
            ->with("product")
            ->with("user")
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->get();

        return $this->stockEntryReportFactory->buildFromModels($models, $grouped_by);
    }
}
