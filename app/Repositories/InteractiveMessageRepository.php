<?php

namespace App\Repositories;

use App\Domains\ChatBot\InteractiveMessage as InteractiveMessageDomain;
use App\Domains\ChatBot\ListSection as ListSectionDomain;
use App\Domains\ChatBot\ListRow as ListRowDomain;
use App\Domains\Filters\InteractiveMessageFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\InteractiveMessageFactory;
use App\Models\InteractiveMessage;
use App\Models\ListSection;
use App\Models\ListRow;
use EloquentBuilder;
use Illuminate\Support\Facades\DB;

class InteractiveMessageRepository
{
    private InteractiveMessageFactory $interactiveMessageFactory;

    public function __construct(InteractiveMessageFactory $interactiveMessageFactory)
    {
        $this->interactiveMessageFactory = $interactiveMessageFactory;
    }

    /**
     * Fetch all interactive messages with pagination
     */
    public function fetchAll(InteractiveMessageFilters $filters, OrderBy $orderBy): array
    {
        $interactiveMessages = [];

        $models = EloquentBuilder::to(InteractiveMessage::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $interactiveMessages[] = $this->interactiveMessageFactory->buildFromModel($model, true);
        }

        return [
            'data' => $interactiveMessages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * Fetch interactive messages from organization
     */
    public function fetchFromOrganization(int $organization_id, InteractiveMessageFilters $filters, OrderBy $orderBy): array
    {
        $interactiveMessages = [];

        $models = EloquentBuilder::to(InteractiveMessage::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $interactiveMessages[] = $this->interactiveMessageFactory->buildFromModel($model, true);
        }

        return [
            'data' => $interactiveMessages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * Count interactive messages
     */
    public function count(int $organization_id, InteractiveMessageFilters $filters): int
    {
        return EloquentBuilder::to(InteractiveMessage::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    /**
     * Store interactive message with relationships
     */
    public function store(InteractiveMessageDomain $interactiveMessage): InteractiveMessageDomain
    {
        return DB::transaction(function () use ($interactiveMessage) {
            // Store the main interactive message
            $savedMessage = InteractiveMessage::create($interactiveMessage->toStoreArray());
            $interactiveMessage->id = $savedMessage->id;

            // Store buttons for BUTTON type
            if ($interactiveMessage->type === \App\Enums\ChatBot\InteractiveType::BUTTON && $interactiveMessage->buttons) {
                foreach ($interactiveMessage->buttons as $button) {
                    $button->organization_id = $interactiveMessage->organization_id;
                    $buttonModel = \App\Models\Button::create($button->toStoreArray());
                    $button->id = $buttonModel->id;
                    
                    // Attach button to interactive message
                    $savedMessage->buttons()->attach($button->id);
                }
            }

            // Store sections for LIST type
            if ($interactiveMessage->type === \App\Enums\ChatBot\InteractiveType::LIST && $interactiveMessage->sections) {
                foreach ($interactiveMessage->sections as $section) {
                    $section->interactive_message_id = $interactiveMessage->id;
                    $sectionModel = ListSection::create($section->toStoreArray());
                    $section->id = $sectionModel->id;

                    // Store rows for each section
                    if ($section->rows) {
                        foreach ($section->rows as $row) {
                            $row->list_section_id = $section->id;
                            $rowModel = ListRow::create($row->toStoreArray());
                            $row->id = $rowModel->id;
                        }
                    }
                }
            }

            return $interactiveMessage;
        });
    }

    /**
     * Update interactive message
     */
    public function update(InteractiveMessageDomain $interactiveMessage, int $organization_id): InteractiveMessageDomain
    {
        return DB::transaction(function () use ($interactiveMessage, $organization_id) {
            // Update main interactive message
            InteractiveMessage::where('id', $interactiveMessage->id)
                ->where('organization_id', $organization_id)
                ->update($interactiveMessage->toUpdateArray());

            // Handle buttons for BUTTON type
            if ($interactiveMessage->type === \App\Enums\ChatBot\InteractiveType::BUTTON) {
                $this->updateButtons($interactiveMessage);
            }

            // Handle sections for LIST type
            if ($interactiveMessage->type === \App\Enums\ChatBot\InteractiveType::LIST) {
                $this->updateSections($interactiveMessage);
            }

            return $interactiveMessage;
        });
    }

    /**
     * Update buttons for interactive message
     */
    private function updateButtons(InteractiveMessageDomain $interactiveMessage): void
    {
        $messageModel = InteractiveMessage::find($interactiveMessage->id);
        
        if ($interactiveMessage->buttons) {
            $buttonIds = [];
            foreach ($interactiveMessage->buttons as $button) {
                if ($button->id) {
                    // Update existing button
                    \App\Models\Button::where('id', $button->id)->update($button->toUpdateArray());
                    $buttonIds[] = $button->id;
                } else {
                    // Create new button
                    $button->organization_id = $interactiveMessage->organization_id;
                    $buttonModel = \App\Models\Button::create($button->toStoreArray());
                    $button->id = $buttonModel->id;
                    $buttonIds[] = $button->id;
                }
            }
            
            // Sync buttons with interactive message
            $messageModel->buttons()->sync($buttonIds);
        } else {
            // Remove all buttons
            $messageModel->buttons()->detach();
        }
    }

    /**
     * Update sections for interactive message
     */
    private function updateSections(InteractiveMessageDomain $interactiveMessage): void
    {
        // Delete existing sections and rows
        $existingSections = ListSection::where('interactive_message_id', $interactiveMessage->id)->get();
        foreach ($existingSections as $section) {
            ListRow::where('list_section_id', $section->id)->delete();
        }
        ListSection::where('interactive_message_id', $interactiveMessage->id)->delete();

        // Create new sections and rows
        if ($interactiveMessage->sections) {
            foreach ($interactiveMessage->sections as $section) {
                $section->interactive_message_id = $interactiveMessage->id;
                $sectionModel = ListSection::create($section->toStoreArray());
                $section->id = $sectionModel->id;

                if ($section->rows) {
                    foreach ($section->rows as $row) {
                        $row->list_section_id = $section->id;
                        $rowModel = ListRow::create($row->toStoreArray());
                        $row->id = $rowModel->id;
                    }
                }
            }
        }
    }

    /**
     * Save (store or update) interactive message
     */
    public function save(InteractiveMessageDomain $interactiveMessage, int $organization_id): InteractiveMessageDomain
    {
        if ($interactiveMessage->id) {
            return $this->update($interactiveMessage, $organization_id);
        }
        return $this->store($interactiveMessage);
    }

    /**
     * Fetch interactive message by ID
     */
    public function fetchById(int $id): InteractiveMessageDomain
    {
        $model = InteractiveMessage::findOrFail($id);
        return $this->interactiveMessageFactory->buildFromModel($model, true);
    }

    /**
     * Fetch interactive message by ID with organization check
     */
    public function fetchByIdFromOrganization(int $id, int $organization_id): InteractiveMessageDomain
    {
        $model = InteractiveMessage::where('id', $id)
            ->where('organization_id', $organization_id)
            ->firstOrFail();
        return $this->interactiveMessageFactory->buildFromModel($model, true);
    }

    /**
     * Delete interactive message
     */
    public function delete(InteractiveMessageDomain $interactiveMessage): bool
    {
        return DB::transaction(function () use ($interactiveMessage) {
            // Delete related data first
            $existingSections = ListSection::where('interactive_message_id', $interactiveMessage->id)->get();
            foreach ($existingSections as $section) {
                ListRow::where('list_section_id', $section->id)->delete();
            }
            ListSection::where('interactive_message_id', $interactiveMessage->id)->delete();

            // Detach buttons
            $messageModel = InteractiveMessage::find($interactiveMessage->id);
            if ($messageModel) {
                $messageModel->buttons()->detach();
                return $messageModel->delete();
            }

            return false;
        });
    }

    /**
     * Fetch interactive messages by type
     */
    public function fetchByType(string $type, int $organization_id, ?int $limit = null): array
    {
        $query = InteractiveMessage::where('type', $type)
            ->where('organization_id', $organization_id)
            ->orderBy('created_at', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        $models = $query->get();
        $interactiveMessages = [];

        foreach ($models as $model) {
            $interactiveMessages[] = $this->interactiveMessageFactory->buildFromModel($model, true);
        }

        return $interactiveMessages;
    }
}
