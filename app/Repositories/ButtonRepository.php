<?php

namespace App\Repositories;

use App\Domains\ChatBot\Button as ButtonDomain;
use App\Domains\Filters\ButtonFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\ButtonFactory;
use App\Models\Button;
use EloquentBuilder;

class ButtonRepository
{
    private ButtonFactory $buttonFactory;

    public function __construct(ButtonFactory $buttonFactory){
        $this->buttonFactory = $buttonFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(ButtonFilters $filters, OrderBy $orderBy) : array {
        $buttons = [];

        $models = EloquentBuilder::to(Button::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $buttons[] = $this->buttonFactory->buildFromModel($model);
        }

        return [
            'data' => $buttons,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, ButtonFilters $filters, OrderBy $orderBy) : array {
        $buttons = [];

        $models = EloquentBuilder::to(Button::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $buttons[] = $this->buttonFactory->buildFromModel($model);
        }

        return [
            'data' => $buttons,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, ButtonFilters $filters): int {
        return EloquentBuilder::to(Button::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, ButtonFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Button::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(ButtonDomain $button) : ButtonDomain {
        $savedButton = Button::create($button->toStoreArray());

        $button->id = $savedButton->id;

        return $button;
    }

    public function update(ButtonDomain $button, int $organization_id) : ButtonDomain {
        Button::where('id', $button->id)
            ->where('organization_id', $organization_id)
            ->update($button->toUpdateArray());

        return $button;
    }

    public function save(ButtonDomain $button, int $organization_id) : ButtonDomain {
        if($button->id){
            return $this->update($button, $organization_id);
        }
        return $this->store($button);
    }

    public function fetchById(int $id) : ButtonDomain {
        return $this->buttonFactory->buildFromModel(
            Button::findOrFail($id)
        );
    }

    public function fetchByBot(string $bot) : ButtonDomain {
        return $this->buttonFactory->buildFromModel(
            Button::where('bot', $bot)->first()
        );
    }

    public function delete(ButtonDomain $button) : bool {
        return Button::find($button->id)->delete();
    }

    public function attachComponent(ButtonDomain $button, int $component_id) : void {
        Button::findOrFail($button->id)->components()->syncWithoutDetaching([$component_id]);
    }
}
