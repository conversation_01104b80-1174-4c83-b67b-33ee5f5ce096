<?php

namespace App\Repositories;

use App\Domains\ChatBot\CampaignCategoryAssignment as CampaignCategoryAssignmentDomain;
use App\Factories\ChatBot\CampaignCategoryAssignmentFactory;
use App\Models\CampaignCategoryAssignment;

class CampaignCategoryAssignmentRepository
{
    private CampaignCategoryAssignmentFactory $factory;

    public function __construct(CampaignCategoryAssignmentFactory $factory)
    {
        $this->factory = $factory;
    }

    public function store(CampaignCategoryAssignmentDomain $assignment): CampaignCategoryAssignmentDomain
    {
        $model = CampaignCategoryAssignment::create($assignment->toStoreArray());
        $assignment->id = $model->id;

        return $assignment;
    }

    public function delete(int $campaign_id, int $category_id): bool
    {
        return CampaignCategoryAssignment::where('campaign_id', $campaign_id)
                                        ->where('category_id', $category_id)
                                        ->delete() > 0;
    }

    public function deleteByCampaign(int $campaign_id): bool
    {
        return CampaignCategoryAssignment::where('campaign_id', $campaign_id)->delete() > 0;
    }

    public function deleteByCategory(int $category_id): bool
    {
        return CampaignCategoryAssignment::where('category_id', $category_id)->delete() > 0;
    }

    public function exists(int $campaign_id, int $category_id): bool
    {
        return CampaignCategoryAssignment::where('campaign_id', $campaign_id)
                                        ->where('category_id', $category_id)
                                        ->exists();
    }

    public function fetchByCampaign(int $campaign_id): array
    {
        $models = CampaignCategoryAssignment::where('campaign_id', $campaign_id)
                                           ->with(['category'])
                                           ->get();

        return $this->factory->buildCollection($models, false, true);
    }

    public function fetchByCategory(int $category_id): array
    {
        $models = CampaignCategoryAssignment::where('category_id', $category_id)
                                           ->with(['campaign'])
                                           ->get();

        return $this->factory->buildCollection($models, true, false);
    }

    public function assignCategoriesToCampaign(int $campaign_id, array $category_ids): array
    {
        $assignments = [];
        
        foreach ($category_ids as $category_id) {
            if (!$this->exists($campaign_id, $category_id)) {
                $assignment = CampaignCategoryAssignmentDomain::create($campaign_id, $category_id);
                $assignments[] = $this->store($assignment);
            }
        }

        return $assignments;
    }

    public function syncCampaignCategories(int $campaign_id, array $category_ids): void
    {
        // Remove existing assignments
        $this->deleteByCampaign($campaign_id);

        // Add new assignments
        if (!empty($category_ids)) {
            $this->assignCategoriesToCampaign($campaign_id, $category_ids);
        }
    }
}
