<?php

namespace App\Repositories;

use App\Domains\ChatBot\Lead;
use App\Factories\ChatBot\LeadFactory;
use App\Models\Lead as LeadModel;

class LeadRepository
{
    protected LeadFactory $leadFactory;

    public function __construct(LeadFactory $leadFactory)
    {
        $this->leadFactory = $leadFactory;
    }

    /**
     * Find lead by ID
     */
    public function findById(int $id): ?Lead
    {
        $leadModel = LeadModel::find($id);

        if (!$leadModel) {
            return null;
        }

        return $this->leadFactory->buildFromModel($leadModel);
    }

    /**
     * Find leads by organization ID
     */
    public function findByOrganizationId(int $organizationId): array
    {
        $leadModels = LeadModel::where('organization_id', $organizationId)
            ->orderBy('created_at', 'desc')
            ->get();

        return $leadModels->map(function ($leadModel) {
            return $this->leadFactory->buildFromModel($leadModel);
        })->toArray();
    }

    /**
     * Find leads by client ID
     */
    public function findByClientId(int $clientId): array
    {
        $leadModels = LeadModel::where('client_id', $clientId)
            ->orderBy('created_at', 'desc')
            ->get();

        return $leadModels->map(function ($leadModel) {
            return $this->leadFactory->buildFromModel($leadModel);
        })->toArray();
    }

    /**
     * Find leads by status
     */
    public function findByStatus(int $organizationId, string $status): array
    {
        $leadModels = LeadModel::where('organization_id', $organizationId)
            ->where('status', $status)
            ->orderBy('created_at', 'desc')
            ->get();

        return $leadModels->map(function ($leadModel) {
            return $this->leadFactory->buildFromModel($leadModel);
        })->toArray();
    }

    /**
     * Find leads by source
     */
    public function findBySource(int $organizationId, string $source): array
    {
        $leadModels = LeadModel::where('organization_id', $organizationId)
            ->where('source', $source)
            ->orderBy('created_at', 'desc')
            ->get();

        return $leadModels->map(function ($leadModel) {
            return $this->leadFactory->buildFromModel($leadModel);
        })->toArray();
    }

    /**
     * Find leads created via chatbot
     */
    public function findChatBotLeads(int $organizationId): array
    {
        $leadModels = LeadModel::where('organization_id', $organizationId)
            ->where(function ($query) {
                $query->where('source', 'like', '%chatbot%')
                      ->orWhere('created_via', 'chatbot');
            })
            ->orderBy('created_at', 'desc')
            ->get();

        return $leadModels->map(function ($leadModel) {
            return $this->leadFactory->buildFromModel($leadModel);
        })->toArray();
    }

    /**
     * Save lead
     */
    public function save(Lead $lead): Lead
    {
        $leadData = $lead->toArray();

        // Remove null values and format dates
        $leadData = array_filter($leadData, function ($value) {
            return $value !== null;
        });

        if ($lead->id) {
            // Update existing lead
            $leadModel = LeadModel::findOrFail($lead->id);
            $leadModel->update($leadData);
        } else {
            // Create new lead
            $leadModel = LeadModel::create($leadData);
        }

        return $this->leadFactory->buildFromModel($leadModel);
    }

    /**
     * Delete lead
     */
    public function delete(int $id): bool
    {
        $leadModel = LeadModel::find($id);

        if (!$leadModel) {
            return false;
        }

        return $leadModel->delete();
    }

    /**
     * Get leads with pagination
     */
    public function getPaginated(int $organizationId, int $perPage = 15, array $filters = []): array
    {
        $query = LeadModel::where('organization_id', $organizationId);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }

        if (isset($filters['source'])) {
            $query->where('source', $filters['source']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhere('company', 'like', "%{$search}%");
            });
        }

        $leadModels = $query->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return [
            'data' => $leadModels->items(),
            'total' => $leadModels->total(),
            'per_page' => $leadModels->perPage(),
            'current_page' => $leadModels->currentPage(),
            'last_page' => $leadModels->lastPage(),
        ];
    }

    /**
     * Get lead statistics for organization
     */
    public function getStatistics(int $organizationId): array
    {
        $total = LeadModel::where('organization_id', $organizationId)->count();
        $new = LeadModel::where('organization_id', $organizationId)->where('status', 'new')->count();
        $qualified = LeadModel::where('organization_id', $organizationId)->where('status', 'qualified')->count();
        $won = LeadModel::where('organization_id', $organizationId)->where('status', 'won')->count();
        $lost = LeadModel::where('organization_id', $organizationId)->where('status', 'lost')->count();
        $chatbot = LeadModel::where('organization_id', $organizationId)
            ->where(function ($query) {
                $query->where('source', 'like', '%chatbot%')
                      ->orWhere('created_via', 'chatbot');
            })->count();

        return [
            'total' => $total,
            'new' => $new,
            'qualified' => $qualified,
            'won' => $won,
            'lost' => $lost,
            'chatbot' => $chatbot,
            'conversion_rate' => $total > 0 ? round(($won / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Update lead status
     */
    public function updateStatus(int $id, string $status): bool
    {
        $leadModel = LeadModel::find($id);

        if (!$leadModel) {
            return false;
        }

        $updateData = ['status' => $status];

        // Set timestamps based on status
        if ($status === 'contacted' && !$leadModel->contacted_at) {
            $updateData['contacted_at'] = now();
        } elseif ($status === 'qualified' && !$leadModel->qualified_at) {
            $updateData['qualified_at'] = now();
        } elseif (in_array($status, ['won', 'lost']) && !$leadModel->closed_at) {
            $updateData['closed_at'] = now();
        }

        return $leadModel->update($updateData);
    }
}
