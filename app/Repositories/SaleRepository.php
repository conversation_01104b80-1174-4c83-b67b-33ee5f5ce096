<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\SaleFilters;
use App\Domains\Inventory\Sale as SaleDomain;
use App\Factories\Inventory\SaleFactory;
use App\Models\Sale;
use EloquentBuilder;

class SaleRepository
{
    private SaleFactory $saleFactory;

    public function __construct(
        SaleFactory $saleFactory
    ) {
        $this->saleFactory = $saleFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(SaleFilters $filters, OrderBy $orderBy) : array {
        $sales = [];

        $models = EloquentBuilder::to(Sale::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $sales[] = $this->saleFactory->buildFromModel($model);
        }

        return [
            'data' => $sales,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, SaleFilters $filters, OrderBy $orderBy) : array {
        $sales = [];

        $models = EloquentBuilder::to(Sale::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $sales[] = $this->saleFactory->buildFromModel($model);
        }

        return [
            'data' => $sales,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, SaleFilters $filters): int {
        return EloquentBuilder::to(Sale::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, SaleFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Sale::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(SaleDomain $sale) : SaleDomain {
        $savedSale = Sale::create($sale->toStoreArray());

        $sale->id = $savedSale->id;

        return $sale;
    }

    public function update(SaleDomain $sale, int $organization_id) : SaleDomain {
        Sale::where('id', $sale->id)
            ->where('organization_id', $organization_id)
            ->update($sale->toUpdateArray());

        return $sale;
    }

    public function fetchById(int $id) : SaleDomain {
        return $this->saleFactory->buildFromModel(
            Sale::with('items')
                ->with('user')
                ->with('shop')
                ->with('client')
                ->findOrFail($id)
        );
    }

    public function delete(SaleDomain $sale) : bool {
        return Sale::find($sale->id)->delete();
    }


}
