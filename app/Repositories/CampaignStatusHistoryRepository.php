<?php

namespace App\Repositories;

use App\Domains\ChatBot\CampaignStatusHistory as CampaignStatusHistoryDomain;
use App\Factories\ChatBot\CampaignStatusHistoryFactory;
use App\Models\CampaignStatusHistory;
use App\Enums\CampaignStatus;

class CampaignStatusHistoryRepository
{
    private CampaignStatusHistoryFactory $factory;

    public function __construct(CampaignStatusHistoryFactory $factory)
    {
        $this->factory = $factory;
    }

    public function store(CampaignStatusHistoryDomain $history): CampaignStatusHistoryDomain
    {
        $model = CampaignStatusHistory::create($history->toStoreArray());
        $history->id = $model->id;
        $history->created_at = $model->created_at;
        $history->updated_at = $model->updated_at;

        return $history;
    }

    public function fetchByCampaign(int $campaign_id, int $limit = 50): array
    {
        $models = CampaignStatusHistory::where('campaign_id', $campaign_id)
                                      ->with(['user'])
                                      ->orderBy('created_at', 'desc')
                                      ->limit($limit)
                                      ->get();

        return $this->factory->buildCollection($models, false, true);
    }

    public function fetchById(int $id): CampaignStatusHistoryDomain
    {
        $model = CampaignStatusHistory::with(['campaign', 'user'])->findOrFail($id);
        return $this->factory->buildFromModel($model, true, true);
    }

    public function getLatestStatusChange(int $campaign_id): ?CampaignStatusHistoryDomain
    {
        $model = CampaignStatusHistory::where('campaign_id', $campaign_id)
                                     ->orderBy('created_at', 'desc')
                                     ->first();

        return $model ? $this->factory->buildFromModel($model) : null;
    }

    public function getStatusChangesCount(int $campaign_id): int
    {
        return CampaignStatusHistory::where('campaign_id', $campaign_id)->count();
    }

    public function getStatusChangesByStatus(int $campaign_id, CampaignStatus $status): array
    {
        $models = CampaignStatusHistory::where('campaign_id', $campaign_id)
                                      ->where('new_status', $status->value)
                                      ->with(['user'])
                                      ->orderBy('created_at', 'desc')
                                      ->get();

        return $this->factory->buildCollection($models, false, true);
    }

    public function createStatusChange(
        int $campaign_id,
        ?CampaignStatus $old_status,
        CampaignStatus $new_status,
        ?string $reason = null,
        ?int $user_id = null,
        ?array $metadata = null
    ): CampaignStatusHistoryDomain {
        $history = CampaignStatusHistoryDomain::create(
            $campaign_id,
            $old_status,
            $new_status,
            $reason,
            $user_id,
            $metadata
        );

        return $this->store($history);
    }

    public function getStatusTimeline(int $campaign_id): array
    {
        $models = CampaignStatusHistory::where('campaign_id', $campaign_id)
                                      ->with(['user'])
                                      ->orderBy('created_at', 'asc')
                                      ->get();

        return $this->factory->buildCollection($models, false, true);
    }

    public function deleteOldHistory(int $days = 365): int
    {
        return CampaignStatusHistory::where('created_at', '<', now()->subDays($days))->delete();
    }
}
