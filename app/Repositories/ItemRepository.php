<?php

namespace App\Repositories;

use App\Domains\Inventory\Item as ItemDomain;
use App\Factories\Inventory\ItemFactory;
use App\Models\Item;

class ItemRepository
{
    private ItemFactory $itemFactory;

    public function __construct(ItemFactory $itemFactory){
        $this->itemFactory = $itemFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $items = [];

        $models = Item::with('sale')->with('product')->paginate(30);

        foreach ($models as $model){
            $items[] = $this->itemFactory->buildFromModel($model);
        }

        return [
            'data' => $items,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id) : array {
        $items = [];

        $models = Item::where("organization_id", $organization_id)->with('sale')->with('product')->paginate(30);

        foreach ($models as $model){
            $items[] = $this->itemFactory->buildFromModel($model);
        }

        return [
            'data' => $items,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return ItemDomain[]
     */
    public function fetchFromSale(int $sale_id) : array {
        $items = [];

        $models = Item::with('sale')
            ->with('product')
            ->where('sale_id', $sale_id)
            ->get();

        foreach ($models as $model){
            $items[] = $this->itemFactory->buildFromModel($model);
        }

        return $items;
    }

    public function store(ItemDomain $item) : ItemDomain {
        $savedItem = Item::create($item->toStoreArray());

        $item->id = $savedItem->id;

        return $item;
    }

    public function update(ItemDomain $item, int $organization_id) : ItemDomain {
        Item::where('id', $item->id)
            ->where('organization_id', $organization_id)
            ->update($item->toUpdateArray());

        return $item;
    }

    public function fetchById(int $id) : ItemDomain {
        return $this->itemFactory->buildFromModel(
            Item::with('sale')
                ->with('product')
                ->findOrFail($id)
        );
    }

    public function delete(ItemDomain $item) : bool {
        return Item::find($item->id)->delete();
    }
}
