<?php

namespace App\Repositories;

use App\Domains\ChatBot\Component as ComponentDomain;
use App\Domains\Filters\ComponentFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\ComponentFactory;
use App\Models\Component;
use EloquentBuilder;

class ComponentRepository
{
    private ComponentFactory $componentFactory;

    public function __construct(ComponentFactory $componentFactory){
        $this->componentFactory = $componentFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(ComponentFilters $filters, OrderBy $orderBy) : array {
        $components = [];

        $models = EloquentBuilder::to(Component::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $components[] = $this->componentFactory->buildFromModel($model);
        }

        return [
            'data' => $components,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, ComponentFilters $filters, OrderBy $orderBy) : array {
        $components = [];

        $models = EloquentBuilder::to(Component::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $components[] = $this->componentFactory->buildFromModel($model);
        }

        return [
            'data' => $components,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, ComponentFilters $filters): int {
        return EloquentBuilder::to(Component::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, ComponentFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Component::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(ComponentDomain $component) : ComponentDomain {
        $savedComponent = Component::create($component->toStoreArray());

        $component->id = $savedComponent->id;

        return $component;
    }

    public function update(ComponentDomain $component, int $organization_id) : ComponentDomain {
        Component::where('id', $component->id)
            ->where('organization_id', $organization_id)
            ->update($component->toUpdateArray());

        return $component;
    }

    public function save(ComponentDomain $component, int $organization_id) : ComponentDomain {
        if ($component->id){
            $this->update($component, $organization_id);
        }
        return $this->store($component);
    }

    public function fetchById(int $id) : ComponentDomain {
        return $this->componentFactory->buildFromModel(
            Component::findOrFail($id)
        );
    }

    public function fetchByBot(string $bot) : ComponentDomain {
        return $this->componentFactory->buildFromModel(
            Component::where('bot', $bot)->first()
        );
    }

    public function delete(ComponentDomain $component) : bool {
        return Component::find($component->id)->delete();
    }
}
