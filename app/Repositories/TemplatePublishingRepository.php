<?php

namespace App\Repositories;

use App\Enums\PublishingService;
use App\Factories\ChatBot\TemplatePublishingFactory;
use App\Models\TemplatePublishing;
use App\Domains\ChatBot\TemplatePublishing as TemplatePublishingDomain;

class TemplatePublishingRepository
{
    private TemplatePublishingFactory $templatePublishingFactory;
    public function __construct(TemplatePublishingFactory $templatePublishingFactory){
        $this->templatePublishingFactory = $templatePublishingFactory;
    }

    public function store(TemplatePublishingDomain $template) : TemplatePublishingDomain {
        $saved = TemplatePublishing::create(
            $template->toStoreArray()
        );

        $template->id = $saved->id;

        return $template;
    }

    public function update(TemplatePublishingDomain $publish, int $template_id) : TemplatePublishingDomain {
        TemplatePublishing::where('id', $publish->id)
            ->where('template_id', $template_id)
            ->update($publish->toUpdateArray());

        return $publish;
    }

    public function fetchById(int $id) : TemplatePublishingDomain {
        return $this->templatePublishingFactory->buildFromModel(
            TemplatePublishing::findOrFail($id)
        );
    }

    public function fetchByTemplateId(int $template_id) : ?TemplatePublishingDomain {
        return $this->templatePublishingFactory->buildFromModel(
            TemplatePublishing::where("template_id", $template_id)->first()
        );
    }

    public function fetchByPublishedTemplateId(int $template_id) : ?TemplatePublishingDomain {
        return $this->templatePublishingFactory->buildFromModel(
            TemplatePublishing::where("template_id", $template_id)
                ->where("is_published", true)
                ->first()
        );
    }

    public function fetchByQueuedTemplateId(int $template_id) : ?TemplatePublishingDomain {
        return $this->templatePublishingFactory->buildFromModel(
            TemplatePublishing::where("template_id", $template_id)
                ->where("is_queued", true)
                ->first()
        );
    }

    public function fetchToPublish(PublishingService $service): array
    {
        $models = TemplatePublishing::where('service_id', $service)
            ->where('is_queued', true)
            ->where('is_published', false)
            ->get();

        return $models->map(function ($model) {
            return $this->templatePublishingFactory->buildFromModel($model);
        })->toArray();
    }
}
