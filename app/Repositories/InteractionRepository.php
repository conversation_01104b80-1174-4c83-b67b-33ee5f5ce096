<?php

namespace App\Repositories;

use App\Domains\ChatBot\Interaction as InteractionDomain;
use App\Domains\Filters\InteractionFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\InteractionFactory;
use App\Models\Interaction;
use EloquentBuilder;

class InteractionRepository
{
    private InteractionFactory $interactionFactory;

    public function __construct(InteractionFactory $interactionFactory){
        $this->interactionFactory = $interactionFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(InteractionFilters $filters, OrderBy $orderBy) : array {
        $interactions = [];

        $models = EloquentBuilder::to(Interaction::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $interactions[] = $this->interactionFactory->buildFromModel($model);
        }

        return [
            'data' => $interactions,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, InteractionFilters $filters, OrderBy $orderBy) : array {
        $interactions = [];

        $models = EloquentBuilder::to(Interaction::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $interactions[] = $this->interactionFactory->buildFromModel($model);
        }

        return [
            'data' => $interactions,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, InteractionFilters $filters): int {
        return EloquentBuilder::to(Interaction::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, InteractionFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Interaction::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(InteractionDomain $interaction) : InteractionDomain {
        $savedInteraction = Interaction::create($interaction->toStoreArray());

        $interaction->id = $savedInteraction->id;

        return $interaction;
    }

    public function update(InteractionDomain $interaction, int $organization_id) : InteractionDomain {
        Interaction::where('id', $interaction->id)
            ->where('organization_id', $organization_id)
            ->update($interaction->toUpdateArray());

        return $interaction;
    }

    public function save(InteractionDomain $interaction, int $organization_id) : InteractionDomain {
        if ($interaction->id){
            $this->update($interaction, $organization_id);
        }
        return $this->store($interaction);
    }

    public function fetchById(int $id) : InteractionDomain {
        return $this->interactionFactory->buildFromModel(
            Interaction::findOrFail($id)
        );
    }

    public function delete(InteractionDomain $interaction) : bool {
        return Interaction::find($interaction->id)->delete();
    }
}
