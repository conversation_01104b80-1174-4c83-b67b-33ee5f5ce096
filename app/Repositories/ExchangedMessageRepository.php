<?php

namespace App\Repositories;

use App\Domains\ChatBot\ExchangedMessage as ExchangedMessageDomain;
use App\Domains\Filters\ExchangedMessageFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Models\ExchangedMessage;
use Fouladgar\EloquentBuilder\EloquentBuilder;

class ExchangedMessageRepository
{
    private ExchangedMessageFactory $exchangedMessageFactory;

    public function __construct(ExchangedMessageFactory $exchangedMessageFactory)
    {
        $this->exchangedMessageFactory = $exchangedMessageFactory;
    }

    /**
     * @return array
     */
    public function fetchFromOrganization(
        int $organization_id,
        ExchangedMessageFilters $filters,
        OrderBy $orderBy,
        bool $with_client = false,
        bool $with_phone_number = false,
        bool $with_conversation = false,
        bool $with_message = false
    ): array {
        $exchangedMessages = [];

        // Using direct Eloquent query due to EloquentBuilder compatibility issues
        $query = ExchangedMessage::where("organization_id", $organization_id);

        if ($with_client) {
            $query->with('client');
        }
        if ($with_phone_number) {
            $query->with('phoneNumber');
        }
        if ($with_conversation) {
            $query->with('conversation');
        }
        if ($with_message) {
            $query->with('message');
        }

        $models = $query->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $exchangedMessages[] = $this->exchangedMessageFactory->buildFromModel(
                $model,
                $with_client,
                $with_phone_number,
                $with_conversation,
                $with_message
            );
        }

        return [
            'data' => $exchangedMessages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count(int $organization_id, ExchangedMessageFilters $filters): int
    {
        // Using direct Eloquent query due to EloquentBuilder compatibility issues
        return ExchangedMessage::where("organization_id", $organization_id)->count();
    }

    public function sum(int $organization_id, ExchangedMessageFilters $filters, string $column): float|int
    {
        // Using direct Eloquent query due to EloquentBuilder compatibility issues
        return ExchangedMessage::where("organization_id", $organization_id)->sum($column);
    }

    public function store(ExchangedMessageDomain $exchangedMessage): ExchangedMessageDomain
    {
        $savedExchangedMessage = ExchangedMessage::create($exchangedMessage->toStoreArray());

        $exchangedMessage->id = $savedExchangedMessage->id;

        return $exchangedMessage;
    }

    public function update(ExchangedMessageDomain $exchangedMessage, int $organization_id): ExchangedMessageDomain
    {
        ExchangedMessage::where('id', $exchangedMessage->id)
            ->where('organization_id', $organization_id)
            ->update($exchangedMessage->toUpdateArray());

        return $exchangedMessage;
    }

    public function fetchById(int $id, int $organization_id): ExchangedMessageDomain
    {
        return $this->exchangedMessageFactory->buildFromModel(
            ExchangedMessage::with('client')
                ->with('phoneNumber')
                ->with('conversation')
                ->with('message')
                ->where('id', $id)
                ->where('organization_id', $organization_id)
                ->firstOrFail(),
            true,
            true,
            true,
            true
        );
    }

    public function delete(ExchangedMessageDomain $exchangedMessage): bool
    {
        return ExchangedMessage::find($exchangedMessage->id)->delete();
    }

    /**
     * Fetch messages by conversation
     */
    public function fetchByConversation(
        int $conversation_id,
        int $organization_id,
        OrderBy $orderBy,
        bool $with_client = false,
        bool $with_phone_number = false,
        bool $with_message = false
    ): array {
        $exchangedMessages = [];

        $query = ExchangedMessage::where('conversation_id', $conversation_id)
            ->where('organization_id', $organization_id);

        if ($with_client) {
            $query->with('client');
        }
        if ($with_phone_number) {
            $query->with('phoneNumber');
        }
        if ($with_message) {
            $query->with('message');
        }

        $models = $query->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $exchangedMessages[] = $this->exchangedMessageFactory->buildFromModel(
                $model,
                $with_client,
                $with_phone_number,
                false,
                $with_message
            );
        }

        return [
            'data' => $exchangedMessages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * Fetch messages by client
     */
    public function fetchByClient(
        int $client_id,
        int $organization_id,
        OrderBy $orderBy,
        bool $with_phone_number = false,
        bool $with_conversation = false,
        bool $with_message = false
    ): array {
        $exchangedMessages = [];

        $query = ExchangedMessage::where('client_id', $client_id)
            ->where('organization_id', $organization_id);

        if ($with_phone_number) {
            $query->with('phoneNumber');
        }
        if ($with_conversation) {
            $query->with('conversation');
        }
        if ($with_message) {
            $query->with('message');
        }

        $models = $query->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model) {
            $exchangedMessages[] = $this->exchangedMessageFactory->buildFromModel(
                $model,
                false,
                $with_phone_number,
                $with_conversation,
                $with_message
            );
        }

        return [
            'data' => $exchangedMessages,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function fetchByMessageId(int $message_id, int $organization_id): ?\App\Domains\ChatBot\ExchangedMessage
    {
        $model = ExchangedMessage::where('message_id', $message_id)
            ->where('organization_id', $organization_id)
            ->first();

        if (!$model) {
            return null;
        }

        return $this->exchangedMessageFactory->buildFromModel($model);
    }

    public function fetchChatByClient(int $client_id, int $organization_id, int $limit = 50): array
    {
        $exchangedMessages = [];

        // Buscar as últimas mensagens ordenadas por sent_at DESC (mais recentes primeiro)
        $models = ExchangedMessage::where('client_id', $client_id)
            ->where('organization_id', $organization_id)
            ->whereNotNull('sent_at')
            ->orderBy('sent_at', 'DESC')
            ->limit($limit)
            ->get();

        // Converter para domínios e inverter a ordem (mais antigas primeiro)
        foreach ($models->reverse() as $model) {
            $exchangedMessages[] = $this->exchangedMessageFactory->buildFromModel($model);
        }

        return $exchangedMessages;
    }
}
