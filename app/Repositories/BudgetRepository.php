<?php

namespace App\Repositories;

use App\Domains\Filters\BudgetFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Budget as BudgetDomain;
use App\Domains\Inventory\ProductsAttachs\AttachCustomDomain;
use App\Domains\Inventory\ProductsAttachs\AttachProductsDomain;
use App\Factories\Inventory\BudgetFactory;
use App\Models\Budget;
use EloquentBuilder;

class BudgetRepository
{
    private BudgetFactory $budgetFactory;

    public function __construct(BudgetFactory $budgetFactory){
        $this->budgetFactory = $budgetFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(BudgetFilters $filters, OrderBy $orderBy, bool $with_client = true, bool $with_products = true, bool $with_custom_products = true, bool $with_projects = true) : array {
        $budgets = [];

        $query = EloquentBuilder::to(Budget::class, $filters->filters);

        if ($with_client) {
            $query->with('client');
        }
        if ($with_products) {
            $query->with('products');
        }
        if ($with_custom_products) {
            $query->with('custom_products');
        }
        if ($with_projects) {
            $query->with('projects');
        }

        $models = $query->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $budgets[] = $this->budgetFactory->buildFromModel($model, $with_client, $with_products, $with_custom_products, $with_projects);
        }

        return [
            'data' => $budgets,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, BudgetFilters $filters, OrderBy $orderBy) : array {
        $budgets = [];

        $models = EloquentBuilder::to(Budget::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $budgets[] = $this->budgetFactory->buildFromModel($model);
        }

        return [
            'data' => $budgets,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, BudgetFilters $filters): int {
        return EloquentBuilder::to(Budget::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, BudgetFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Budget::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(BudgetDomain $budget) : BudgetDomain {
        $savedBudget = Budget::create($budget->toStoreArray());

        $budget->id = $savedBudget->id;

        return $budget;
    }

    public function update(BudgetDomain $budget, int $organization_id) : BudgetDomain {
        Budget::where('id', $budget->id)
            ->where('organization_id', $organization_id)
            ->update($budget->toUpdateArray());

        return $budget;
    }

    public function fetchById(int $id) : BudgetDomain {
        return $this->budgetFactory->buildFromModel(
            Budget::with('products')
                ->with('client')
                ->findOrFail($id)
        );
    }

    public function delete(BudgetDomain $budget) : bool {
        return Budget::find($budget->id)->delete();
    }
    public function attachProducts(BudgetDomain $budget, AttachProductsDomain $product_ids) {
        Budget::find($budget->id)->products()->attach($product_ids->products);
    }
    public function attachCustoms(BudgetDomain $budget, AttachCustomDomain $custom_ids) {
        Budget::find($budget->id)->custom_products()->saveMany($custom_ids->products);
    }
    public function clearProducts(BudgetDomain $budget) {
        Budget::find($budget->id)->products()->detach();
    }
    public function clearCustoms(BudgetDomain $budget) {
        Budget::find($budget->id)->custom_products()->delete();
    }

    public function addProductToBudget(int $budget_id, int $product_id, int $quantity, float $value): bool {
        try {
            Budget::find($budget_id)->products()->attach($product_id, [
                'quantity' => $quantity,
                'value' => $value
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function removeProductFromBudget(int $budget_id, int $product_id): bool {
        try {
            Budget::find($budget_id)->products()->detach($product_id);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

}
