<?php

namespace App\Services\GoogleVision;

use App\Helpers\DBLog;
use Google\Cloud\Vision\V1\Client\ImageAnnotatorClient;
use Google\Cloud\Vision\V1\Feature\Type;
use Google\Cloud\Vision\V1\Image;
use Google\Cloud\Vision\V1\Feature;
use Google\Cloud\Vision\V1\AnnotateImageRequest;
use Google\Cloud\Vision\V1\BatchAnnotateImagesRequest; // <--- NEW IMPORT NEEDED!

class GoogleVisionOCR
{
    protected ImageAnnotatorClient $vision;

    public function __construct()
    {
        $this->vision = new ImageAnnotatorClient([
            'credentials' => config('services.google_cloud.key_file'),
        ]);
    }

    public function extractText(string $imagePath): ?string
    {
        DBLog::log(
            "ImageAnnotatorClient::extractText",
            "Telegram",
            null,
            null,
            ["callback_data" => $imagePath]
        );

        if (!file_exists(public_path('storage/' . $imagePath))) {
            //\Log::error("Image file not found at: " . $imagePath);
            throw new \Exception("Image file not found at: " . $imagePath);
        }

        $imageContent = file_get_contents(public_path('storage/' . $imagePath));
        if ($imageContent === false) {
            //\Log::error("Failed to read image file: " . $imagePath);
            throw new \Exception("Failed to read image file: " . $imagePath);
        }

        try {
            // Create a Google\Cloud\Vision\V1\Image object from the content
            $image = (new Image())->setContent($imageContent);

            // Create a Feature object specifying TEXT_DETECTION
            $feature = (new Feature())->setType(Type::TEXT_DETECTION);

            // Create an AnnotateImageRequest for a single image
            $annotateImageRequest = (new AnnotateImageRequest())
                ->setImage($image)
                ->setFeatures([$feature]);

            // <--- THE CRITICAL CHANGE IS HERE --->
            // Create a BatchAnnotateImagesRequest and set the individual image requests into it
            $batchRequest = (new BatchAnnotateImagesRequest())
                ->setRequests([$annotateImageRequest]); // Pass an array of AnnotateImageRequest objects

            // Call batchAnnotateImages with the BatchAnnotateImagesRequest object
            $response = $this->vision->batchAnnotateImages($batchRequest);

            // Get the responses for each image in the batch (in this case, just one)
            $imageResponses = $response->getResponses();

            if (empty($imageResponses)) {
                return null; // No responses received
            }

            $firstImageResponse = $imageResponses[0];

            if ($firstImageResponse->getError() && $firstImageResponse->getError()->getMessage()) {
                $errorMessage = 'Vision API Error: ' . $firstImageResponse->getError()->getMessage();
                //\Log::error($errorMessage);
                throw new \Exception($errorMessage);
            }

            // Get the text annotations from the response of the first image
            $texts = $firstImageResponse->getTextAnnotations();

            // The first text annotation usually contains the full text of the image
            return $texts[0]?->getDescription() ?? null;

        } catch (\Google\ApiCore\ApiException $e) {
            $errorMessage = 'Google Vision API communication error: ' . $e->getMessage();
            //\Log::error($errorMessage, ['code' => $e->getCode(), 'details' => $e->getMetadata()]);
            throw new \Exception($errorMessage, $e->getCode(), $e);
        } catch (\Exception $e) {
            $errorMessage = 'An unexpected error occurred: ' . $e->getMessage();
            //\Log::error($errorMessage, ['trace' => $e->getTraceAsString()]);
            throw new \Exception($errorMessage, $e->getCode(), $e);
        } finally {
            // Close the client when it is no longer needed
            $this->vision->close();
        }
    }
}
