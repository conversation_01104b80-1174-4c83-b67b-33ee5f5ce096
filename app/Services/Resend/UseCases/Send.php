<?php

namespace App\Services\Resend\UseCases;

use App\Helpers\DBLog;
use App\Services\Resend\Contracts\EmailInterface;
use App\Services\Resend\Exceptions\ResendException;
use App\Services\Resend\ResendService;

class Send
{
    private ResendService $resendService;

    public function __construct(ResendService $resendService)
    {
        $this->resendService = $resendService;
    }

    /**
     * Send email using any EmailInterface implementation
     * 
     * @param EmailInterface $email
     * @return array
     * @throws ResendException
     */
    public function perform(EmailInterface $email): array
    {
        $this->validateEmail($email);
        
        try {
            $result = $this->resendService->send($email);
            
            $this->logSuccess($email, $result);
            
            return $result;
        } catch (ResendException $e) {
            $this->logError($email, $e);
            throw $e;
        }
    }

    /**
     * Validate email data before sending
     * 
     * @param EmailInterface $email
     * @throws ResendException
     */
    private function validateEmail(EmailInterface $email): void
    {
        $to = $email->getTo();
        if (empty($to['email'])) {
            throw ResendException::badRequest('Recipient email is required');
        }

        if (!filter_var($to['email'], FILTER_VALIDATE_EMAIL)) {
            throw ResendException::badRequest('Invalid recipient email format');
        }

        if (empty($email->getSubject())) {
            throw ResendException::badRequest('Email subject is required');
        }

        if (empty($email->getTemplatePath())) {
            throw ResendException::badRequest('Email template path is required');
        }

        // Validate template exists
        if (!view()->exists($email->getTemplatePath())) {
            throw ResendException::badRequest('Email template does not exist: ' . $email->getTemplatePath());
        }
    }

    /**
     * Log successful email sending
     */
    private function logSuccess(EmailInterface $email, array $result): void
    {
        DBLog::log(
            "Email sent successfully via Send UseCase",
            "Resend\\Send",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            [
                'email_type' => get_class($email),
                'to' => $email->getTo(),
                'subject' => $email->getSubject(),
                'resend_id' => $result['id'] ?? null,
                'tags' => $email->getTags()
            ]
        );
    }

    /**
     * Log email sending error
     */
    private function logError(EmailInterface $email, ResendException $exception): void
    {
        DBLog::logError(
            "Email sending failed via Send UseCase: " . $exception->getMessage(),
            "Resend\\Send",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            [
                'email_type' => get_class($email),
                'to' => $email->getTo(),
                'subject' => $email->getSubject(),
                'error_code' => $exception->getCode(),
                'context' => $exception->getContext()
            ]
        );
    }
}
