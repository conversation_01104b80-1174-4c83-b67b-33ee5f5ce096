<?php

namespace App\Services\Resend\Exceptions;

use Exception;

class ResendException extends Exception
{
    protected array $context;

    public function __construct(
        string $message = "",
        int $code = 0,
        ?Exception $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public static function authenticationFailed(array $context = []): self
    {
        return new self(
            'Resend authentication failed. Check your API key.',
            401,
            null,
            $context
        );
    }

    public static function badRequest(string $message, array $context = []): self
    {
        return new self(
            "Resend API bad request: {$message}",
            400,
            null,
            $context
        );
    }

    public static function rateLimitExceeded(array $context = []): self
    {
        return new self(
            'Resend API rate limit exceeded.',
            429,
            null,
            $context
        );
    }

    public static function serverError(string $message, array $context = []): self
    {
        return new self(
            "Resend server error: {$message}",
            500,
            null,
            $context
        );
    }

    public static function networkError(string $message, array $context = []): self
    {
        return new self(
            "Network error communicating with Resend: {$message}",
            0,
            null,
            $context
        );
    }
}
