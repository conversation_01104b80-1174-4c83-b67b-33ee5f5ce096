<?php

namespace App\Services\Resend\Domains;

use App\Services\Resend\Contracts\EmailInterface;
use App\Domains\User;
use App\Domains\Organization;

class WelcomeEmail implements EmailInterface
{
    private User $user;
    private Organization $organization;
    private array $nextSteps;
    private string $supportEmail;
    private string $gettingStartedUrl;

    public function __construct(
        User $user,
        Organization $organization,
        array $nextSteps = [],
        ?string $supportEmail = null,
        ?string $gettingStartedUrl = null
    ) {
        $this->user = $user;
        $this->organization = $organization;
        $this->nextSteps = $nextSteps ?: $this->getDefaultNextSteps();
        $this->supportEmail = $supportEmail ?? config('resend.support_email', '<EMAIL>');
        $this->gettingStartedUrl = $gettingStartedUrl ?? url('/getting-started');
    }

    public function getTo(): array
    {
        return [
            'email' => $this->user->email,
            'name' => $this->user->name
        ];
    }

    public function getFrom(): string
    {
        return config('resend.from_email', '<EMAIL>');
    }

    public function getSubject(): string
    {
        return 'Bem-vindo ao ' . config('app.name') . '!';
    }

    public function getTemplatePath(): string
    {
        return 'emails.welcome';
    }

    public function getTemplateData(): array
    {
        return [
            'user' => $this->user,
            'organization' => $this->organization,
            'next_steps' => $this->nextSteps,
            'support_email' => $this->supportEmail,
            'getting_started_url' => $this->gettingStartedUrl
        ];
    }

    public function getTags(): array
    {
        return [
            'type' => 'onboarding',
            'priority' => 'medium',
            'user_id' => $this->user->id,
            'organization_id' => $this->organization->id
        ];
    }

    public function getReplyTo(): ?string
    {
        return $this->supportEmail;
    }

    public function toResendPayload(): array
    {
        $templateData = $this->getTemplateData();
        $htmlContent = view($this->getTemplatePath(), $templateData)->render();

        return [
            'from' => $this->getFrom(),
            'to' => [$this->getTo()],
            'subject' => $this->getSubject(),
            'html' => $htmlContent,
            'tags' => $this->getTags(),
            'reply_to' => $this->getReplyTo()
        ];
    }

    private function getDefaultNextSteps(): array
    {
        return [
            'Configurar seu perfil pessoal',
            'Explorar o dashboard principal',
            'Criar sua primeira campanha',
            'Configurar templates de mensagem',
            'Convidar membros da equipe',
            'Revisar configurações da organização'
        ];
    }
}
