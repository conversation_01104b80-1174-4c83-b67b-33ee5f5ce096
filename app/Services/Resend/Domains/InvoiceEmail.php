<?php

namespace App\Services\Resend\Domains;

use App\Services\Resend\Contracts\EmailInterface;
use App\Domains\Organization;
use Carbon\Carbon;

class InvoiceEmail implements EmailInterface
{
    private $invoice;
    private $client;
    private Carbon $dueDate;
    private string $paymentLink;
    private array $items;
    private float $totalAmount;
    private Organization $organization;

    public function __construct(
        $invoice,
        $client,
        Carbon $dueDate,
        string $paymentLink,
        array $items = [],
        float $totalAmount = 0.0,
        ?Organization $organization = null
    ) {
        $this->invoice = $invoice;
        $this->client = $client;
        $this->dueDate = $dueDate;
        $this->paymentLink = $paymentLink;
        $this->items = $items;
        $this->totalAmount = $totalAmount;
        $this->organization = $organization;
    }

    public function getTo(): array
    {
        $email = is_object($this->client) ? $this->client->email : $this->client['email'];
        $name = is_object($this->client) ? $this->client->name : $this->client['name'];

        return [
            'email' => $email,
            'name' => $name
        ];
    }

    public function getFrom(): string
    {
        return config('resend.from_email', '<EMAIL>');
    }

    public function getSubject(): string
    {
        $invoiceId = is_object($this->invoice) ? $this->invoice->id : $this->invoice['id'];
        return 'Fatura #' . $invoiceId . ' - ' . config('app.name');
    }

    public function getTemplatePath(): string
    {
        return 'emails.invoice';
    }

    public function getTemplateData(): array
    {
        return [
            'invoice' => $this->invoice,
            'client' => $this->client,
            'due_date' => $this->dueDate,
            'payment_link' => $this->paymentLink,
            'items' => $this->items,
            'total_amount' => $this->totalAmount,
            'organization' => $this->organization
        ];
    }

    public function getTags(): array
    {
        $invoiceId = is_object($this->invoice) ? $this->invoice->id : $this->invoice['id'];
        $organizationId = $this->organization?->id;

        return [
            'type' => 'billing',
            'priority' => 'high',
            'invoice_id' => $invoiceId,
            'organization_id' => $organizationId
        ];
    }

    public function getReplyTo(): ?string
    {
        return config('resend.support_email', '<EMAIL>');
    }

    public function toResendPayload(): array
    {
        $templateData = $this->getTemplateData();
        $htmlContent = view($this->getTemplatePath(), $templateData)->render();

        return [
            'from' => $this->getFrom(),
            'to' => [$this->getTo()],
            'subject' => $this->getSubject(),
            'html' => $htmlContent,
            'tags' => $this->getTags(),
            'reply_to' => $this->getReplyTo()
        ];
    }
}
