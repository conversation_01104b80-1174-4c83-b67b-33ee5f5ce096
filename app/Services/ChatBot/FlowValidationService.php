<?php

namespace App\Services\ChatBot;

use App\Enums\ChatBot\StepType;
use Illuminate\Support\Collection;

class FlowValidationService
{
    /**
     * Validate complete flow structure and relationships
     */
    public function validateFlowStructure(array $flowData): array
    {
        $errors = [];
        $warnings = [];

        $flow = $flowData['flow'] ?? [];
        $steps = $flowData['steps'] ?? [];

        // Validate basic structure
        $structureValidation = $this->validateBasicStructure($flow, $steps);
        $errors = array_merge($errors, $structureValidation['errors']);
        $warnings = array_merge($warnings, $structureValidation['warnings']);

        // Validate step relationships
        $relationshipValidation = $this->validateStepRelationships($steps);
        $errors = array_merge($errors, $relationshipValidation['errors']);
        $warnings = array_merge($warnings, $relationshipValidation['warnings']);

        // Validate navigation flow
        $navigationValidation = $this->validateNavigationFlow($steps);
        $errors = array_merge($errors, $navigationValidation['errors']);
        $warnings = array_merge($warnings, $navigationValidation['warnings']);

        // Validate component relationships
        $componentValidation = $this->validateComponentRelationships($steps);
        $errors = array_merge($errors, $componentValidation['errors']);
        $warnings = array_merge($warnings, $componentValidation['warnings']);

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * Validate basic flow structure
     */
    private function validateBasicStructure(array $flow, array $steps): array
    {
        $errors = [];
        $warnings = [];

        // Check if flow has required fields
        if (empty($flow['name'])) {
            $errors[] = 'Flow deve ter um nome.';
        }

        // Check if there are steps
        if (empty($steps)) {
            $errors[] = 'Flow deve ter pelo menos um step.';
        }

        // Check for initial and ending steps
        $hasInitial = false;
        $hasEnding = false;

        foreach ($steps as $step) {
            if ($step['is_initial_step'] ?? false) {
                $hasInitial = true;
            }
            if ($step['is_ending_step'] ?? false) {
                $hasEnding = true;
            }
        }

        if (!$hasInitial) {
            $errors[] = 'Flow deve ter pelo menos um step inicial.';
        }

        if (!$hasEnding) {
            $warnings[] = 'Flow não tem step final definido. Considere adicionar um.';
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate step relationships and references
     */
    private function validateStepRelationships(array $steps): array
    {
        $errors = [];
        $warnings = [];

        $stepPositions = array_column($steps, 'position');
        $stepIds = array_filter(array_column($steps, 'id'));
        $stepIdentifiers = array_column($steps, 'step');

        foreach ($steps as $index => $step) {
            $stepId = $step['step'] ?? "step_{$index}";

            // Validate position uniqueness
            $position = $step['position'] ?? $index;
            $positionCount = array_count_values($stepPositions)[$position] ?? 0;
            if ($positionCount > 1) {
                $errors[] = "Step '{$stepId}' tem posição duplicada: {$position}";
            }

            // Validate step identifier uniqueness
            $identifier = $step['step'] ?? null;
            if ($identifier) {
                $identifierCount = array_count_values($stepIdentifiers)[$identifier] ?? 0;
                if ($identifierCount > 1) {
                    $errors[] = "Identificador de step duplicado: '{$identifier}'";
                }
            }

            // Validate next_step references
            $nextStep = $step['next_step'] ?? null;
            if ($nextStep !== null) {
                if (!in_array($nextStep, $stepPositions)) {
                    $errors[] = "Step '{$stepId}' referencia next_step inválido: {$nextStep}";
                }
            }

            // Validate earlier_step references
            $earlierStep = $step['earlier_step'] ?? null;
            if ($earlierStep !== null) {
                if (!in_array($earlierStep, $stepPositions)) {
                    $errors[] = "Step '{$stepId}' referencia earlier_step inválido: {$earlierStep}";
                }
            }

            // Validate step type consistency
            $stepType = $step['step_type'] ?? $step['type'] ?? null;
            if ($stepType && !StepType::isValid($stepType)) {
                $errors[] = "Step '{$stepId}' tem tipo inválido: {$stepType}";
            }
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate navigation flow logic
     */
    private function validateNavigationFlow(array $steps): array
    {
        $errors = [];
        $warnings = [];

        // Build navigation graph
        $navigationGraph = $this->buildNavigationGraph($steps);

        // Check for orphaned steps
        $orphanedSteps = $this->findOrphanedSteps($steps, $navigationGraph);
        foreach ($orphanedSteps as $orphan) {
            $warnings[] = "Step '{$orphan}' pode estar órfão (sem navegação de entrada).";
        }

        // Check for infinite loops
        $infiniteLoops = $this->detectInfiniteLoops($navigationGraph);
        foreach ($infiniteLoops as $loop) {
            $errors[] = "Loop infinito detectado: " . implode(' → ', $loop);
        }

        // Check for unreachable steps
        $unreachableSteps = $this->findUnreachableSteps($steps, $navigationGraph);
        foreach ($unreachableSteps as $unreachable) {
            $warnings[] = "Step '{$unreachable}' pode ser inalcançável.";
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate component relationships
     */
    private function validateComponentRelationships(array $steps): array
    {
        $errors = [];
        $warnings = [];

        foreach ($steps as $index => $step) {
            $stepId = $step['step'] ?? "step_{$index}";
            $component = $step['component'] ?? [];

            if (empty($component)) {
                $warnings[] = "Step '{$stepId}' não tem componente definido.";
                continue;
            }

            // Validate buttons
            $buttons = $component['buttons'] ?? [];
            $actionButtons = $component['action']['buttons'] ?? [];
            $allButtons = array_merge($buttons, $actionButtons);

            if (count($allButtons) > 3) {
                $errors[] = "Step '{$stepId}' tem mais de 3 botões (limite do WhatsApp).";
            }

            // Validate button text length
            foreach ($allButtons as $buttonIndex => $button) {
                $buttonText = $button['text'] ?? '';
                if (strlen($buttonText) > 20) {
                    $errors[] = "Step '{$stepId}' botão {$buttonIndex}: texto muito longo para WhatsApp (máximo 20 caracteres).";
                }
            }

            // Validate component text length for WhatsApp
            $componentText = $component['text'] ?? '';
            if (strlen($componentText) > 4096) {
                $warnings[] = "Step '{$stepId}' componente: texto muito longo (máximo recomendado 4096 caracteres).";
            }

            // Validate parameters
            $parameters = $component['parameters'] ?? [];
            foreach ($parameters as $paramIndex => $parameter) {
                if (empty($parameter['type'])) {
                    $warnings[] = "Step '{$stepId}' parâmetro {$paramIndex}: tipo não definido.";
                }
            }
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Build navigation graph from steps
     */
    private function buildNavigationGraph(array $steps): array
    {
        $graph = [];

        foreach ($steps as $step) {
            $position = $step['position'] ?? 0;
            $nextStep = $step['next_step'] ?? null;

            $graph[$position] = [
                'next' => $nextStep,
                'step_id' => $step['step'] ?? "step_{$position}",
                'type' => $step['step_type'] ?? $step['type'] ?? 'message'
            ];
        }

        return $graph;
    }

    /**
     * Find orphaned steps (steps with no incoming navigation)
     */
    private function findOrphanedSteps(array $steps, array $navigationGraph): array
    {
        $allPositions = array_column($steps, 'position');
        $referencedPositions = [];

        // Collect all referenced positions
        foreach ($navigationGraph as $node) {
            if ($node['next'] !== null) {
                $referencedPositions[] = $node['next'];
            }
        }

        // Find initial steps (they're not orphaned)
        $initialSteps = [];
        foreach ($steps as $step) {
            if ($step['is_initial_step'] ?? false) {
                $initialSteps[] = $step['position'] ?? 0;
            }
        }

        // Find orphaned steps
        $orphaned = [];
        foreach ($allPositions as $position) {
            if (!in_array($position, $referencedPositions) && !in_array($position, $initialSteps)) {
                $stepId = $navigationGraph[$position]['step_id'] ?? "step_{$position}";
                $orphaned[] = $stepId;
            }
        }

        return $orphaned;
    }

    /**
     * Detect infinite loops in navigation
     */
    private function detectInfiniteLoops(array $navigationGraph): array
    {
        $loops = [];
        $visited = [];
        $recursionStack = [];

        foreach ($navigationGraph as $position => $node) {
            if (!isset($visited[$position])) {
                $path = $this->detectLoopDFS($position, $navigationGraph, $visited, $recursionStack, []);
                if (!empty($path)) {
                    $loops[] = $path;
                }
            }
        }

        return $loops;
    }

    /**
     * DFS helper for loop detection
     */
    private function detectLoopDFS(int $position, array $graph, array &$visited, array &$recursionStack, array $path): array
    {
        $visited[$position] = true;
        $recursionStack[$position] = true;
        $path[] = $graph[$position]['step_id'] ?? "step_{$position}";

        $nextPosition = $graph[$position]['next'] ?? null;

        if ($nextPosition !== null && isset($graph[$nextPosition])) {
            if (!isset($visited[$nextPosition])) {
                $result = $this->detectLoopDFS($nextPosition, $graph, $visited, $recursionStack, $path);
                if (!empty($result)) {
                    return $result;
                }
            } elseif (isset($recursionStack[$nextPosition]) && $recursionStack[$nextPosition]) {
                // Found a loop
                $loopStart = array_search($graph[$nextPosition]['step_id'], $path);
                return array_slice($path, $loopStart);
            }
        }

        $recursionStack[$position] = false;
        return [];
    }

    /**
     * Find unreachable steps
     */
    private function findUnreachableSteps(array $steps, array $navigationGraph): array
    {
        $reachable = [];
        $visited = [];

        // Find initial steps
        $initialSteps = [];
        foreach ($steps as $step) {
            if ($step['is_initial_step'] ?? false) {
                $initialSteps[] = $step['position'] ?? 0;
            }
        }

        // If no initial steps, consider position 0 as initial
        if (empty($initialSteps)) {
            $initialSteps = [0];
        }

        // DFS from each initial step
        foreach ($initialSteps as $initial) {
            $this->markReachableDFS($initial, $navigationGraph, $visited);
        }

        // Find unreachable steps
        $unreachable = [];
        foreach ($navigationGraph as $position => $node) {
            if (!isset($visited[$position])) {
                $unreachable[] = $node['step_id'];
            }
        }

        return $unreachable;
    }

    /**
     * DFS helper for marking reachable steps
     */
    private function markReachableDFS(int $position, array $graph, array &$visited): void
    {
        if (isset($visited[$position]) || !isset($graph[$position])) {
            return;
        }

        $visited[$position] = true;
        $nextPosition = $graph[$position]['next'] ?? null;

        if ($nextPosition !== null) {
            $this->markReachableDFS($nextPosition, $graph, $visited);
        }
    }
}
