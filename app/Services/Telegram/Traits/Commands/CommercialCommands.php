<?php

namespace App\Services\Telegram\Traits\Commands;

use App\Services\Telegram\UseCases\Commands\Commercial\CommercialMenu;
use App\Services\Telegram\UseCases\Commands\Inventory\InventoryMenu;

trait CommercialCommands
{
    protected const array TELEGRAM_COMMERCIAL_COMMANDS = [
        "/sale",
        "/commercial_data",
    ];

    protected function runCommercialCommands() : void {
        switch ($this->command) {
            case "/inventory":
                /** @var InventoryMenu $useCase */
                $useCase = app()->make(InventoryMenu::class);
                $useCase->perform($this->service);
                break;
            case "/commercial":
                /** @var CommercialMenu $useCase */
                $useCase = app()->make(CommercialMenu::class);
                $useCase->perform($this->service);
                break;
            case "/image_reader":
                break;
        }
    }
}
