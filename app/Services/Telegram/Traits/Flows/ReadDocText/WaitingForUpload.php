<?php

namespace App\Services\Telegram\Traits\Flows\ReadDocText;

use App\Services\GoogleVision\GoogleVisionOCR;
use App\Services\OpenAI\OpenAIService;
use App\Services\Telegram\Domains\Messages\DocumentReader\IsDataCorrect;
use App\Services\Telegram\UseCases\Flows\FinishFlow;
use App\Services\Telegram\UseCases\Flows\ReadDocText\MessageToDataArray;
use App\Services\Telegram\UseCases\TelegramChat\UpdateStatus;
use Illuminate\Support\Facades\Log;

trait WaitingForUpload
{
    private const string NEXT_FLOW_STATUS = "waiting_for_approval";

    public function waitingForUpload()
    {
        Log::info("WaitingForUpload::waitingForUpload");
        if (!$this->telegram->telegramFile) {
            $this->telegram->sendMessage("Ops! Não encontramos o seu arquivo, houve um erro no fluxo, tente novamente");
            return null;
        }

        $data = null;
        $extractedText = null;

        try {
            // First try: ChatGPT Vision Analysis
            $this->telegram->sendMessage("🤖 Analisando documento com IA...");

            $openAIService = new OpenAIService();
            $imagePath = public_path('storage/' . $this->telegram->telegramFile->file);

            $aiResult = $openAIService->analyzeImage($imagePath);

            if ($aiResult['status'] === 'success' && !empty($aiResult['data'])) {
                $data = $aiResult;
                $extractedText = $aiResult['raw_response'] ?? 'Dados extraídos via IA';

                Log::info("WaitingForUpload::ChatGPT Success", ['data' => $data]);
            } else {
                throw new \Exception("ChatGPT analysis failed or returned empty data");
            }

        } catch (\Exception $e) {
            Log::warning("WaitingForUpload::ChatGPT Failed, trying Google Vision", [
                'error' => $e->getMessage()
            ]);

            try {
                // Fallback: Google Vision OCR + Traditional Parsing
                $this->telegram->sendMessage("🔍 Tentando com OCR tradicional...");

                $vision = new GoogleVisionOCR();
                $extractedText = $vision->extractText($this->telegram->telegramFile->file);

                if ($extractedText && $extractedText != "") {
                    /** @var MessageToDataArray $useCase */
                    $useCase = app()->makeWith(MessageToDataArray::class, ["telegram" => $this->telegram]);
                    $data = $useCase->perform($extractedText);

                    Log::info("WaitingForUpload::GoogleVision Success", ['data' => $data]);
                } else {
                    throw new \Exception("Google Vision OCR failed to extract text");
                }

            } catch (\Exception $fallbackError) {
                Log::error("WaitingForUpload::Both methods failed", [
                    'chatgpt_error' => $e->getMessage(),
                    'vision_error' => $fallbackError->getMessage()
                ]);

                $this->telegram->sendMessage("❌ Não conseguimos processar o documento. Tente novamente com uma imagem mais clara.");

                /** @var FinishFlow $useCase */
                $useCase = app()->makeWith(FinishFlow::class, ["telegram" => $this->telegram]);
                $useCase->perform();
                return;
            }
        }

        if ($data && !empty($data)) {
            $this->telegram->sendMessage("✅ Dados extraídos com sucesso:");

            // Send data in separate messages to avoid Telegram length limit
            $this->sendDataInSeparateMessages($data);

            new IsDataCorrect($this->telegram);

            /** @var UpdateStatus $useCase */
            $useCase = app()->make(UpdateStatus::class);
            $useCase->perform($this->telegram->telegramChat, self::NEXT_FLOW_STATUS, $extractedText, json_encode($data));
        } else {
            $this->telegram->sendMessage("❌ Não conseguimos extrair dados válidos do documento!");

            /** @var FinishFlow $useCase */
            $useCase = app()->makeWith(FinishFlow::class, ["telegram" => $this->telegram]);
            $useCase->perform();
        }
    }

    /**
     * Send extracted data in separate messages to avoid Telegram length limit
     */
    private function sendDataInSeparateMessages(array $data): void
    {
        // Check if data has the new structured format (with entities)
        if (isset($data['data'])) {
            $structuredData = $data['data'];

            // Send Invoice data
            if (isset($structuredData['invoice']) && !empty($structuredData['invoice'])) {
                $this->telegram->sendMessage("🧾 **NOTA FISCAL/FATURAMENTO:**");
                $invoiceMessage = $this->formatEntityMessage($structuredData['invoice']);
                $this->telegram->sendMessage($invoiceMessage);
            }

            // Send Client data
            if (isset($structuredData['client']) && !empty($structuredData['client'])) {
                $this->telegram->sendMessage("👤 **CLIENTE/PACIENTE:**");
                $clientMessage = $this->formatEntityMessage($structuredData['client']);
                $this->telegram->sendMessage($clientMessage);
            }

            // Send Agreement data
            if (isset($structuredData['agreement']) && !empty($structuredData['agreement'])) {
                $this->telegram->sendMessage("🏥 **CONVÊNIO/PLANO:**");
                $agreementMessage = $this->formatEntityMessage($structuredData['agreement']);
                $this->telegram->sendMessage($agreementMessage);
            }

            // Send Proceeding data
            if (isset($structuredData['proceeding']) && !empty($structuredData['proceeding'])) {
                $this->telegram->sendMessage("⚕️ **PROCEDIMENTO:**");
                $proceedingMessage = $this->formatEntityMessage($structuredData['proceeding']);
                $this->telegram->sendMessage($proceedingMessage);
            }

            // Send Raw Information (for compatibility)
            if (isset($structuredData['raw_information']) && !empty($structuredData['raw_information'])) {
                $this->telegram->sendMessage("📄 **INFORMAÇÕES ORIGINAIS:**");
                $rawMessage = $this->formatEntityMessage($structuredData['raw_information']);
                $this->telegram->sendMessage($rawMessage);
            }

        } else {
            // Fallback for old format (from Google Vision OCR)
            $this->telegram->sendMessage("📋 **DADOS EXTRAÍDOS:**");
            $message = $this->jsonPrettyString($data);
            $this->telegram->sendMessage($message);
        }
    }

    /**
     * Format entity data into readable message
     */
    private function formatEntityMessage(array $entityData): string
    {
        $message = "";

        foreach ($entityData as $key => $value) {
            if ($value === null || $value === "") {
                continue; // Skip null/empty values
            }

            // Handle nested objects (subjsons)
            if (is_array($value)) {
                $nestedInfo = [];
                foreach ($value as $nestedKey => $nestedValue) {
                    if ($nestedValue !== null && $nestedValue !== "") {
                        $nestedInfo[] = "$nestedKey: $nestedValue";
                    }
                }
                if (!empty($nestedInfo)) {
                    $message .= "• " . ucfirst(str_replace('_', ' ', $key)) . ": " . implode(', ', $nestedInfo) . "\n";
                }
            } else {
                // Handle simple values
                $formattedKey = ucfirst(str_replace('_', ' ', $key));
                $message .= "• $formattedKey: $value\n";
            }
        }

        return $message ?: "Nenhum dado disponível.";
    }

    function jsonPrettyString(array $data): string
    {
        // Convert to JSON with pretty print and unescaped unicode/slashes
        return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
}
