<?php

namespace App\Services\Telegram\Domains;

use App\Domains\User;
use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use Carbon\Carbon;

class TelegramChat
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $user_id;
    public ?int $telegram_user_id;
    public ?int $bot_id;
    public ?int $chat_id;
    public ?int $from_id;
    public ?bool $has_active_flow;
    public ?bool $has_broken_flow;
    public ?string $current_flow;
    public ?int $current_flow_id;
    public ?string $current_flow_status;
    public ?int $current_step_id;
    public ?string $ocr_raw;
    public ?string $ocr_data;

    public ?Carbon $created_at;

    public ?User $user;
    public ?TelegramUser $telegramUser;
    public ?Flow $flow;
    public ?Step $step;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $user_id,
        ?int $telegram_user_id,
        ?int $bot_id,
        ?int $chat_id,
        ?int $from_id,
        ?bool $has_active_flow = false,
        ?bool $has_broken_flow = false,
        ?string $current_flow = null,
        ?int $current_flow_id = null,
        ?string $current_flow_status = null,
        ?int $current_step_id = null,
        ?string $ocr_raw = null,
        ?string $ocr_data = null,
        ?Carbon $created_at = null,
        ?User $user = null,
        ?TelegramUser $telegramUser = null,
        ?Flow $flow = null,
        ?Step $step = null
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->user_id = $user_id;
        $this->telegram_user_id = $telegram_user_id;
        $this->bot_id = $bot_id;
        $this->chat_id = $chat_id;
        $this->from_id = $from_id;
        $this->has_active_flow = $has_active_flow;
        $this->has_broken_flow = $has_broken_flow;
        $this->current_flow = $current_flow;
        $this->current_flow_id = $current_flow_id;
        $this->current_flow_status = $current_flow_status;
        $this->current_step_id = $current_step_id;
        $this->ocr_raw = $ocr_raw;
        $this->ocr_data = $ocr_data;
        $this->created_at = $created_at;
        $this->user = $user;
        $this->telegramUser = $telegramUser;
        $this->flow = $flow;
        $this->step = $step;
    }

    public function toArray() : array {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "user_id" => $this->user_id,
            "telegram_user_id" => $this->telegram_user_id,
            "bot_id" => $this->bot_id,
            "chat_id" => $this->chat_id,
            "from_id" => $this->from_id,
            "has_active_flow" => $this->has_active_flow,
            "has_broken_flow" => $this->has_broken_flow,
            "current_flow" => $this->current_flow,
            "current_flow_id" => $this->current_flow_id,
            "current_flow_status" => $this->current_flow_status,
            "current_step_id" => $this->current_step_id,
            "ocr_raw" => $this->ocr_raw,
            "ocr_data" => $this->ocr_data,
            "created_at" => $this->created_at,
            "user" => $this->user,
            "telegramUser" => $this->telegramUser,
            "flow" => $this->flow,
            "step" => $this->step,
        ];
    }

    public function toStoreArray() : array {
        return [
            "organization_id" => $this->organization_id,
            "bot_id" => $this->bot_id,
            "user_id" => $this->user_id,
            "telegram_user_id" => $this->telegram_user_id,
            "chat_id" => $this->chat_id,
            "from_id" => $this->from_id,
            "has_active_flow" => $this->has_active_flow,
            "has_broken_flow" => $this->has_broken_flow,
            "current_flow" => $this->current_flow,
            "current_flow_id" => $this->current_flow_id,
            "current_flow_status" => $this->current_flow_status,
            "current_step_id" => $this->current_step_id,
            "ocr_raw" => $this->ocr_raw,
            "ocr_data" => $this->ocr_data,
        ];
    }

    public function toUpdateArray() : array {
        return [
            "telegram_user_id" => $this->telegram_user_id,
            "has_active_flow" => $this->has_active_flow,
            "has_broken_flow" => $this->has_broken_flow,
            "current_flow" => $this->current_flow,
            "current_flow_id" => $this->current_flow_id,
            "current_flow_status" => $this->current_flow_status,
            "current_step_id" => $this->current_step_id,
            "ocr_raw" => $this->ocr_raw,
            "ocr_data" => $this->ocr_data,
        ];
    }
}
