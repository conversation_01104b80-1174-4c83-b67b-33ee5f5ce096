<?php

namespace App\Services\Telegram\Domains\Messages;

use App\Services\Telegram\Telegram;
use Telegram\Bot\Keyboard\Keyboard;

class Begin
{
    public const SEARCH_ARRAY = [
        "{{username}}"
    ];

    public const MESSAGE = "<PERSON><PERSON><PERSON> {{username}}! Seja bem vindo(a) ao FlechAI o bot inteligente da Flecha! 🏹".
                           "\nSelecione uma opção no menu\n";

    public function __construct(Telegram $service) {
        $message = self::MESSAGE;

        $keyboard = Keyboard::make()
            ->inline()
            ->row([Keyboard::inlineButton(['text' => '📦 Inventário', 'callback_data' => 'inventory'])])
            ->row([Keyboard::inlineButton(['text' => '💵 Comercial', 'callback_data' => 'commercial'])])
            ->row([Keyboard::inlineButton(['text' => '🖨️ Leitor de Documentos', 'callback_data' => 'image_reader'])]);

        $service->sendMessage(str_replace(
            self::SEARCH_ARRAY,
            [$service->from->firstName],
            $message,
        ), $keyboard ?? null);
    }
}
