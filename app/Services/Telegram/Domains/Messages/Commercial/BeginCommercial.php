<?php

namespace App\Services\Telegram\Domains\Messages\Commercial;

use App\Services\Telegram\Telegram;
use Telegram\Bot\Keyboard\Keyboard;

class BeginCommercial
{
    public const MESSAGE = "Bem vindo(a) ao módulo comercial!\n".
                           "Selecione uma opção no menu\n";

    public function __construct(Telegram $service) {
        $message = self::MESSAGE;

        $keyboard = Keyboard::make()
            ->inline()
            ->row([Keyboard::inlineButton(['text' => 'Registrar Venda', 'callback_data' => 'sale'])])
            ->row([Keyboard::inlineButton(['text' => '📈 Dados e Relatórios', 'callback_data' => 'commercial_data'])])
            ->row([Keyboard::inlineButton(['text' => 'Voltar', 'callback_data' => 'back_to_begin'])]);


        $service->sendMessage($message, $keyboard ?? null);
    }
}
