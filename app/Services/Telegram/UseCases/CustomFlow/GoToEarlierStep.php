<?php

namespace App\Services\Telegram\UseCases\CustomFlow;

use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Telegram;
use App\Services\Telegram\UseCases\UpdateChat;

class GoToEarlierStep
{
    public Telegram $telegram;
    public TelegramChat $telegramChat;
    public ?Flow $currentFlow;
    public ?Step $currentStep;
    public ?int $earlierStep;

    public function __construct(Telegram $telegram)
    {
        $this->telegram = $telegram;
        $this->telegramChat = $telegram->telegramChat;
        $this->currentFlow = $telegram->customCurrentFlow;
        $this->currentStep = $telegram->currentStep;
        $this->earlierStep = $telegram->currentStep->earlier_step;
    }

    /**
     * Updates the TelegramChat to the earlier step
     */
    public function perform(): void
    {
        if (!$this->currentStep || $this->currentStep->is_initial_step || !$this->earlierStep) {
            return;
        }

        $earlierStep = $this->currentFlow->getStepByPosition($this->earlierStep);

        $this->telegramChat->current_step_id = $earlierStep->id;

        /** @var UpdateChat $updateChat */
        $updateChat = app()->make(UpdateChat::class);
        $updateChat->perform($this->telegramChat);
    }
}
