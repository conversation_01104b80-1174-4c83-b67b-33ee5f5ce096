<?php

namespace App\Services\Telegram\UseCases\CustomFlow;

use App\Domains\ChatBot\Step;
use App\Domains\Inventory\Client;
use App\Services\Telegram\Telegram;
use App\Helpers\Functions;

class ReceiveInput
{
    public Telegram $telegram;

    public string $input;
    public string $field;
    public string $domain;
    public string $value;

    public Client $client;


    public function __construct(Telegram $telegram)
    {
        $this->telegram = $telegram;
        $this->value = $this->telegram->message->text;
        $this->client = $this->telegram->telegramUser->client;
    }

    /**
     * Updates the TelegramChat to the next step
     */
    public function perform(Step $step): void
    {
        $field = $step->getInputField();
        $this->domain = $step->getInputDomain();


        $repository = app()->make(Functions::REPOSITORIES[$this->domain]);

        if($this->domain == 'client'){
            $this->client->$field = $this->value;

            $repository->update($this->client, $this->telegram->organization->id);
        }
    }
}
