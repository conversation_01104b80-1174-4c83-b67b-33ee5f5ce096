<?php

namespace App\Services\Telegram\UseCases\TelegramUser;

use App\Services\Telegram\Repositories\TelegramUserRepository;
use Exception;

class Delete
{
    private TelegramUserRepository $telegramUserRepository;

    public function __construct(TelegramUserRepository $telegramUserRepository) {
        $this->telegramUserRepository = $telegramUserRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $telegramUser = $this->telegramUserRepository->fetchById($id);

        if($telegramUser->organization_id !== $organization_id){
            throw new Exception(
                "This telegramUser don't belong to this organization." ,
                403
            );
        }

        return $this->telegramUserRepository->delete($telegramUser);
    }
}
