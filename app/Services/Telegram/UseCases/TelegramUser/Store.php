<?php

namespace App\Services\Telegram\UseCases\TelegramUser;

use App\Services\Telegram\Domains\TelegramUser;
use App\Services\Telegram\Factories\TelegramUserFactory;
use App\Http\Requests\TelegramUser\StoreRequest;
use App\Services\Telegram\Repositories\TelegramUserRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private TelegramUserRepository $telegramUserRepository;
    private TelegramUserFactory $telegramUserFactory;

    public function __construct(TelegramUserRepository $telegramUserRepository, TelegramUserFactory $telegramUserFactory) {
        $this->telegramUserRepository = $telegramUserRepository;
        $this->telegramUserFactory = $telegramUserFactory;
    }

    /**
     * @param StoreRequest $request
     * @return TelegramUser
     */
    public function perform(StoreRequest $request) : TelegramUser {
        DB::beginTransaction();

        $domain = $this->telegramUserFactory->buildFromStoreRequest($request);
        $domain->user_id = request()->user()->id;

        $telegramUser = $this->telegramUserRepository->store($domain);

        DB::commit();

        return $telegramUser;
    }
}
