<?php

namespace App\Services\Telegram\UseCases\TelegramUser;

use App\Services\Telegram\Domains\TelegramUser;
use App\Services\Telegram\Factories\TelegramUserFactory;
use App\Http\Requests\TelegramUser\UpdateRequest;
use App\Services\Telegram\Repositories\TelegramUserRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private TelegramUserRepository $telegramUserRepository;
    private TelegramUserFactory $telegramUserFactory;

    public function __construct(TelegramUserRepository $telegramUserRepository, TelegramUserFactory $telegramUserFactory) {
        $this->telegramUserRepository = $telegramUserRepository;
        $this->telegramUserFactory = $telegramUserFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return TelegramUser
     */
    public function perform(UpdateRequest $request, int $id) : TelegramUser {
        DB::beginTransaction();

        $domain = $this->telegramUserFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $telegramUser = $this->telegramUserRepository->update(
            $domain,
            request()->user()->id
        );

        DB::commit();

        return $telegramUser;
    }
}
