<?php

namespace App\Services\Telegram\UseCases\Commands\Commercial;

use App\Services\Telegram\Domains\Messages\Commercial\BeginCommercial;
use App\Services\Telegram\Telegram;

class CommercialMenu {

    public function __construct() {}

    /**
     * @param Telegram $telegram
     * @return void
     */
    public function perform(Telegram $telegram) : void {
        $telegram->log("[Command::Inventory::CommercialMenu::perform]", [
            "user_id" => $telegram->user?->id,
            "user_name" => $telegram->user?->first_name . " ". $telegram->user?->last_name
        ]);

        new BeginCommercial($telegram);
    }
}
