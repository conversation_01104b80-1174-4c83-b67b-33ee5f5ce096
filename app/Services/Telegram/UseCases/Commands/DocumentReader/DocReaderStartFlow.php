<?php

namespace App\Services\Telegram\UseCases\Commands\DocumentReader;

use App\Services\Telegram\Domains\Messages\DocumentReader\UploadImageToRead;
use App\Services\Telegram\Repositories\TelegramChatRepository;
use App\Services\Telegram\Telegram;

class DocReaderStartFlow {

    public const string CURRENT_FLOW = "read_doc_text";
    public const string CURRENT_FLOW_STATUS = "waiting_for_upload";

    private TelegramChatRepository $chatRepository;
    public function __construct(TelegramChatRepository $chatRepository) {
        $this->chatRepository = $chatRepository;
    }

    /**
     * @param Telegram $telegram
     * @return void
     */
    public function perform(Telegram $telegram) : void {
        $telegram->log("[Command::Inventory::InventoryMenu::perform]", [
            "user_id" => $telegram->user?->id,
            "user_name" => $telegram->user?->first_name . " ". $telegram->user?->last_name
        ]);

        $telegram->telegramChat->has_active_flow = true;
        $telegram->telegramChat->has_broken_flow = false;
        $telegram->telegramChat->current_flow = self::CURRENT_FLOW;
        $telegram->telegramChat->current_flow_status = self::CURRENT_FLOW_STATUS;

        $this->chatRepository->update($telegram->telegramChat);

        new UploadImageToRead($telegram);
    }
}
