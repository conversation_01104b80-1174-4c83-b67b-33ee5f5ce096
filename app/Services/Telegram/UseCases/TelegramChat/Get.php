<?php

namespace App\Services\Telegram\UseCases\TelegramChat;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Repositories\TelegramChatRepository;
use Exception;

class Get
{
    private TelegramChatRepository $telegramChatRepository;

    public function __construct(TelegramChatRepository $telegramChatRepository) {
        $this->telegramChatRepository = $telegramChatRepository;
    }

    /**
     * @param int $id
     * @return TelegramChat
     * @throws Exception
     */
    public function perform(int $id) : TelegramChat {
        $organization_id = request()->user()->organization_id;

        $telegramChat = $this->telegramChatRepository->fetchById($id);

        if($telegramChat->organization_id !== $organization_id){
            throw new Exception(
                "This telegramChat don't belong to this organization." ,
                403
            );
        }
        return $telegramChat;
    }
}
