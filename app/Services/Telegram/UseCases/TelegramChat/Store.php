<?php

namespace App\Services\Telegram\UseCases\TelegramChat;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Factories\TelegramChatFactory;
use App\Http\Requests\TelegramChat\StoreRequest;
use App\Services\Telegram\Repositories\TelegramChatRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private TelegramChatRepository $telegramChatRepository;
    private TelegramChatFactory $telegramChatFactory;

    public function __construct(TelegramChatRepository $telegramChatRepository, TelegramChatFactory $telegramChatFactory) {
        $this->telegramChatRepository = $telegramChatRepository;
        $this->telegramChatFactory = $telegramChatFactory;
    }

    /**
     * @param StoreRequest $request
     * @return TelegramChat
     */
    public function perform(StoreRequest $request) : TelegramChat {
        DB::beginTransaction();

        $domain = $this->telegramChatFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $telegramChat = $this->telegramChatRepository->store($domain);

        DB::commit();

        return $telegramChat;
    }
}
