<?php

namespace App\Services\Telegram\UseCases\TelegramBot;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TelegramBotFilters;
use App\Services\Telegram\Repositories\TelegramBotRepository;

class GetAll
{
    private TelegramBotRepository $telegramBotRepository;

    public function __construct(TelegramBotRepository $telegramBotRepository) {
        $this->telegramBotRepository = $telegramBotRepository;
    }

    /**
     * @return array
     */
    public function perform(TelegramBotFilters $filters, OrderBy $orderBy) : array {
        return $this->telegramBotRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
