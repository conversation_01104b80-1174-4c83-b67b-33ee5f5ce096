<?php

namespace App\Services\Telegram\UseCases\TelegramBot;

use App\Services\Telegram\Domains\TelegramBot;
use App\Services\Telegram\Repositories\TelegramBotRepository;
use Exception;

class Get
{
    private TelegramBotRepository $telegramBotRepository;

    public function __construct(TelegramBotRepository $telegramBotRepository) {
        $this->telegramBotRepository = $telegramBotRepository;
    }

    /**
     * @param int $id
     * @return TelegramBot
     * @throws Exception
     */
    public function perform(int $id) : TelegramBot {
        $organization_id = request()->user()->organization_id;

        $telegramBot = $this->telegramBotRepository->fetchById($id);

        if($telegramBot->organization_id !== $organization_id){
            throw new Exception(
                "This telegramBot don't belong to this organization." ,
                403
            );
        }
        return $telegramBot;
    }
}
