<?php

namespace App\Services\Telegram\UseCases\TelegramBot;

use App\Services\Telegram\Domains\TelegramBot;
use App\Services\Telegram\Factories\TelegramBotFactory;
use App\Http\Requests\TelegramBot\UpdateRequest;
use App\Services\Telegram\Repositories\TelegramBotRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private TelegramBotRepository $telegramBotRepository;
    private TelegramBotFactory $telegramBotFactory;

    public function __construct(TelegramBotRepository $telegramBotRepository, TelegramBotFactory $telegramBotFactory) {
        $this->telegramBotRepository = $telegramBotRepository;
        $this->telegramBotFactory = $telegramBotFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return TelegramBot
     */
    public function perform(UpdateRequest $request, int $id) : TelegramBot {
        DB::beginTransaction();

        $domain = $this->telegramBotFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $telegramBot = $this->telegramBotRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $telegramBot;
    }
}
