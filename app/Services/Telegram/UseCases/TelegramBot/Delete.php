<?php

namespace App\Services\Telegram\UseCases\TelegramBot;

use App\Services\Telegram\Repositories\TelegramBotRepository;
use Exception;

class Delete
{
    private TelegramBotRepository $telegramBotRepository;

    public function __construct(TelegramBotRepository $telegramBotRepository) {
        $this->telegramBotRepository = $telegramBotRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $telegramBot = $this->telegramBotRepository->fetchById($id);

        if($telegramBot->organization_id !== $organization_id){
            throw new Exception(
                "This telegramBot don't belong to this organization." ,
                403
            );
        }

        return $this->telegramBotRepository->delete($telegramBot);
    }
}
