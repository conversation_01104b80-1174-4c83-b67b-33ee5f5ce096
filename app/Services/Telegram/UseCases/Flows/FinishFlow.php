<?php

namespace App\Services\Telegram\UseCases\Flows;

use App\Services\Telegram\Repositories\TelegramChatRepository;
use App\Services\Telegram\Telegram;

class FinishFlow
{

    public Telegram $telegram;
    public TelegramChatRepository $telegramChatRepository;

    public function __construct(Telegram $telegram, TelegramChatRepository $telegramChatRepository){
        $this->telegram = $telegram;
        $this->telegramChatRepository = $telegramChatRepository;
    }

    public function perform(bool $send = true): void {
        $this->telegram->telegramChat->has_active_flow = false;
        $this->telegram->telegramChat->has_broken_flow = false;
        $this->telegram->telegramChat->current_flow = null;
        $this->telegram->telegramChat->current_flow_status = null;
        $this->telegram->telegramChat->current_flow_id = null;
        $this->telegram->telegramChat->current_step_id = null;
        $this->telegram->telegramChat->ocr_raw = null;
        $this->telegram->telegramChat->ocr_data = null;

        $this->telegramChatRepository->update($this->telegram->telegramChat);

        if ($send) {
            $this->telegram->sendMessage("Fluxo finalizado com sucesso!");
        }
    }

}
