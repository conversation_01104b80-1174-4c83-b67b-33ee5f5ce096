<?php

namespace App\Services\Telegram\Factories;

use App\Services\Telegram\Domains\TelegramChat;
use App\Services\Telegram\Domains\TelegramMessage;
use Telegram\Bot\Objects\Update;
use App\Services\Telegram\Models\TelegramMessage as TelegramMessageModel;

class TelegramMessageFactory
{

    public function __construct(){}

    public function buildFromUpdate(Update $update, ?TelegramChat $chat) : TelegramMessage {
        return new TelegramMessage(
            null,
            $chat->id ?? null,
            $update->message->text ?? ($update->callbackQuery->data ?? "undefined"),
            ((bool) $update->callbackQuery) ?? false,
        );
    }

    public function buildFromModel(?TelegramMessageModel $telegramMessage) : ?TelegramMessage {
        if(!$telegramMessage){
            return null;
        }

        return new TelegramMessage(
            $telegramMessage->id ?? null,
            $telegramMessage->telegram_chat_id ?? null,
            $telegramMessage->text ?? "callbackQuery",
            ((bool) $telegramMessage->is_callback_data) ?? false
        );
    }
}
