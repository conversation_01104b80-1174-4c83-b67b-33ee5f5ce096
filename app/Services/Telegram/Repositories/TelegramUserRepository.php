<?php

namespace App\Services\Telegram\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TelegramUserFilters;
use App\Services\Telegram\Domains\TelegramUser as TelegramUserDomain;
use App\Services\Telegram\Factories\TelegramUserFactory;
use App\Services\Telegram\Models\TelegramUser;
use EloquentBuilder;

class TelegramUserRepository
{
    private TelegramUserFactory $telegramUserFactory;

    public function __construct(TelegramUserFactory $telegramUserFactory){
        $this->telegramUserFactory = $telegramUserFactory;
    }

    /**
     * @return array
     */
        public function fetchAll(TelegramUserFilters $filters, OrderBy $orderBy) : array {
        $telegramUsers = [];

        $models = EloquentBuilder::to(TelegramUser::class, $filters->filters)
            ->with('user')
            ->with('user.organization')
            ->with('client')
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $telegramUsers[] = $this->telegramUserFactory->buildFromModel($model);
        }

        return [
            'data' => $telegramUsers,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromUser($user_id, TelegramUserFilters $filters, OrderBy $orderBy) : array {
        $telegramUsers = [];

        $models = EloquentBuilder::to(TelegramUser::class, $filters->filters)
            ->where("user_id", $user_id)
            ->with('user')
            ->with('user.organization')
            ->with('client')
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $telegramUsers[] = $this->telegramUserFactory->buildFromModel($model);
        }

        return [
            'data' => $telegramUsers,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromClient($client_id, TelegramUserFilters $filters, OrderBy $orderBy) : array {
        $telegramUsers = [];

        $models = EloquentBuilder::to(TelegramUser::class, $filters->filters)
            ->where("client_id", $client_id)
            ->with('user')
            ->with('user.organization')
            ->with('client')
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $telegramUsers[] = $this->telegramUserFactory->buildFromModel($model);
        }

        return [
            'data' => $telegramUsers,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($id, TelegramUserFilters $filters, $field = 'user_id'): int {
        return EloquentBuilder::to(TelegramUser::class, $filters->filters)
            ->where($field, $id)
            ->count();
    }

    public function sum($id, TelegramUserFilters $filters, string $column, $field = 'user_id'): float|int {
        return EloquentBuilder::to(TelegramUser::class, $filters->filters)
            ->where($field, $id)
            ->sum($column);
    }

    public function store(TelegramUserDomain $telegramUser) : TelegramUserDomain {
        $savedTelegramUser = TelegramUser::create($telegramUser->toStoreArray());

        $telegramUser->id = $savedTelegramUser->id;

        return $telegramUser;
    }

    public function update(TelegramUserDomain $telegramUser, int $id, $field = 'user_id') : TelegramUserDomain {
        $query = TelegramUser::where('id', $telegramUser->id);

        if ($field === 'user_id' && $telegramUser->user_id) {
            $query->where('user_id', $id);
        } elseif ($field === 'client_id' && $telegramUser->client_id) {
            $query->where('client_id', $id);
        }

        $query->update($telegramUser->toUpdateArray());

        return $telegramUser;
    }

    public function fetchByTelegramId(int $telegram_id) : ?TelegramUserDomain {
        return $this->telegramUserFactory->buildFromModel(
            TelegramUser::with('user')
                ->with('user.organization')
                ->with('client')
                ->where('telegram_id', $telegram_id)
                ->first()
        );
    }

    public function fetchById(int $id) : TelegramUserDomain {
        return $this->telegramUserFactory->buildFromModel(
            TelegramUser::with('user')
                ->with('user.organization')
                ->with('client')
                ->findOrFail($id)
        );
    }

    public function delete(TelegramUserDomain $telegramUser) : bool {
        return TelegramUser::find($telegramUser->id)->delete();
    }
}
