<?php

namespace App\Services\Telegram\Models;

use Illuminate\Database\Eloquent\Model;

class TelegramMessage extends Model
{
    protected $table = "telegram_messages";

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'telegram_chat_id',
        'text',
        'is_callback_data'
    ];

    public function chat() {
        return $this->belongsTo(TelegramChat::class, 'telegram_chat_id');
    }
}
