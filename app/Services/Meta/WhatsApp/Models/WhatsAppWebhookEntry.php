<?php

namespace App\Services\Meta\WhatsApp\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WhatsAppWebhookEntry extends Model
{
    use SoftDeletes;

    protected $table = 'whatsapp_webhook_entries';

    protected $fillable = [
        'whatsapp_message_id',
        'external_wam_id',
        'status',
        'timestamp',
        'recipient_id',
        'conversation_id',
        'conversation_origin_type',
        'json',
    ];

    public function whatsappMessage()
    {
        return $this->belongsTo(WhatsAppMessage::class, 'whatsapp_message_id');
    }
}
