<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Repositories;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppConversationFactory;
use App\Models\Conversation as ConversationModel;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;

class WhatsAppConversationRepository
{
    protected WhatsAppConversationFactory $factory;

    public function __construct(WhatsAppConversationFactory $factory)
    {
        $this->factory = $factory;
    }

    /**
     * Find active conversation for client and phone number
     */
    public function findActiveConversation(Client $client, PhoneNumber $phoneNumber): ?WhatsAppConversation
    {
        $conversation = ConversationModel::where('client_id', $client->id)
            ->where('phone_number_id', $phoneNumber->id)
            ->where('is_finished', false)
            ->with(['flow', 'currentStep', 'phoneNumber', 'client'])
            ->first();

        return $this->factory->buildFromModel($conversation);
    }

    /**
     * Find conversation by ID
     */
    public function findById(int $id): ?WhatsAppConversation
    {
        $conversation = ConversationModel::with(['flow', 'currentStep', 'phoneNumber', 'client'])
            ->find($id);

        return $this->factory->buildFromModel($conversation);
    }

    /**
     * Save conversation
     */
    public function save(WhatsAppConversation $conversation): WhatsAppConversation
    {
        if ($conversation->id) {
            return $this->update($conversation);
        }

        return $this->store($conversation);
    }

    /**
     * Store new conversation
     */
    public function store(WhatsAppConversation $conversation): WhatsAppConversation
    {
        $model = ConversationModel::create($conversation->toStoreArray());

        $conversation->id = $model->id;
        $conversation->created_at = $model->created_at;
        $conversation->updated_at = $model->updated_at;

        return $conversation;
    }

    /**
     * Update existing conversation
     */
    public function update(WhatsAppConversation $conversation): WhatsAppConversation
    {
        ConversationModel::where('id', $conversation->id)
            ->update($conversation->toUpdateArray());

        $conversation->updated_at = now();

        return $conversation;
    }

    /**
     * Get recent conversations for organization
     */
    public function getRecentConversations(int $organizationId, int $limit = 50): array
    {
        $conversations = ConversationModel::where('organization_id', $organizationId)
            ->with(['flow', 'currentStep', 'phoneNumber', 'client'])
            ->orderBy('updated_at', 'desc')
            ->limit($limit)
            ->get();

        return $conversations->map(function ($conversation) {
            return $this->factory->buildFromModel($conversation);
        })->filter()->toArray();
    }

    /**
     * Mark conversation as finished
     */
    public function markAsFinished(WhatsAppConversation $conversation): WhatsAppConversation
    {
        $conversation->is_finished = true;

        return $this->save($conversation);
    }

    /**
     * Reset conversation to start
     */
    public function resetConversation(WhatsAppConversation $conversation): WhatsAppConversation
    {
        $conversation->resetToStart();

        return $this->save($conversation);
    }
}
