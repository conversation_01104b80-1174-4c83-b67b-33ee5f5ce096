<?php

namespace App\Services\Meta\WhatsApp\ChatBot;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Step;
use App\Domains\Inventory\Client;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessWebhookMessage;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateClient;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendWhatsAppResponse;
use Exception;

class ChatBotService
{
    protected ProcessWebhookMessage $processWebhookMessage;
    protected FindOrCreateClient $findOrCreateClient;
    protected FindOrCreateConversation $findOrCreateConversation;
    protected ProcessFlowStep $processFlowStep;
    protected SendWhatsAppResponse $sendWhatsAppResponse;

    public function __construct(
        ProcessWebhookMessage $processWebhookMessage,
        FindOrCreateClient $findOrCreateClient,
        FindOrCreateConversation $findOrCreateConversation,
        ProcessFlowStep $processFlowStep,
        SendWhatsAppResponse $sendWhatsAppResponse
    ) {
        $this->processWebhookMessage = $processWebhookMessage;
        $this->findOrCreateClient = $findOrCreateClient;
        $this->findOrCreateConversation = $findOrCreateConversation;
        $this->processFlowStep = $processFlowStep;
        $this->sendWhatsAppResponse = $sendWhatsAppResponse;
    }

    /**
     * Main entry point for processing WhatsApp webhook messages
     *
     * @param array $webhookData
     * @return array
     * @throws Exception
     */
    public function processWebhook(array $webhookData): array
    {
        try {
            // Step 1: Process and validate webhook message
            $messageData = $this->processWebhookMessage->perform($webhookData);
            
            // Step 2: Find or create client from WhatsApp data
            $client = $this->findOrCreateClient->perform($messageData);
            
            // Step 3: Find or create conversation
            $conversation = $this->findOrCreateConversation->perform($messageData, $client);
            
            // Step 4: Process current flow step
            $stepResult = $this->processFlowStep->perform($conversation, $messageData);
            
            // Step 5: Send WhatsApp response
            $response = $this->sendWhatsAppResponse->perform($stepResult, $conversation);
            
            return [
                'success' => true,
                'conversation_id' => $conversation->id,
                'client_id' => $client->id,
                'step_result' => $stepResult,
                'response' => $response
            ];
            
        } catch (Exception $e) {
            // Log error and return error response
            \Log::error('ChatBot processing error: ' . $e->getMessage(), [
                'webhook_data' => $webhookData,
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Get flow for phone number (with fallback to organization default)
     *
     * @param PhoneNumber $phoneNumber
     * @return Flow|null
     */
    public function getFlowForPhoneNumber(PhoneNumber $phoneNumber): ?Flow
    {
        // First try phone number's assigned flow
        if ($phoneNumber->flow) {
            return $phoneNumber->flow;
        }
        
        // Fallback to organization's default flow
        if ($phoneNumber->organization && $phoneNumber->organization->default_flow_id) {
            // TODO: Load organization's default flow
            return null; // Will implement this when we have flow repository
        }
        
        return null;
    }
}
