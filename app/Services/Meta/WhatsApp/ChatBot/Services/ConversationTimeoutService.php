<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Services;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Helpers\DBLog;
use Carbon\Carbon;

class ConversationTimeoutService
{
    protected WhatsAppConversationRepository $conversationRepository;

    public function __construct(WhatsAppConversationRepository $conversationRepository)
    {
        $this->conversationRepository = $conversationRepository;
    }

    /**
     * Check if conversation has timed out
     *
     * @param WhatsAppConversation $conversation
     * @return bool
     */
    public function hasTimedOut(WhatsAppConversation $conversation): bool
    {
        if ($conversation->is_finished) {
            return false;
        }

        $timeouts = config('chatbot.timeouts', []);
        $conversationTimeout = $timeouts['conversation'] ?? 3600; // 1 hour default

        $lastActivity = $conversation->updated_at ?? $conversation->created_at;

        if (!$lastActivity) {
            return false;
        }

        return $lastActivity->addSeconds($conversationTimeout)->isPast();
    }

    /**
     * Check if conversation is inactive
     *
     * @param WhatsAppConversation $conversation
     * @return bool
     */
    public function isInactive(WhatsAppConversation $conversation): bool
    {
        if ($conversation->is_finished) {
            return true;
        }

        $timeouts = config('chatbot.timeouts', []);
        $inactiveTimeout = $timeouts['inactive_after'] ?? 86400; // 24 hours default

        $lastActivity = $conversation->updated_at ?? $conversation->created_at;

        if (!$lastActivity) {
            return false;
        }

        return $lastActivity->addSeconds($inactiveTimeout)->isPast();
    }

    /**
     * Get timeout remaining for conversation
     *
     * @param WhatsAppConversation $conversation
     * @return int Seconds remaining, 0 if timed out
     */
    public function getTimeoutRemaining(WhatsAppConversation $conversation): int
    {
        if ($conversation->is_finished) {
            return 0;
        }

        $timeouts = config('chatbot.timeouts', []);
        $conversationTimeout = $timeouts['conversation'] ?? 3600;

        $lastActivity = $conversation->updated_at ?? $conversation->created_at;

        if (!$lastActivity) {
            return $conversationTimeout;
        }

        $timeoutAt = $lastActivity->addSeconds($conversationTimeout);

        if ($timeoutAt->isPast()) {
            return 0;
        }

        return $timeoutAt->diffInSeconds(now());
    }

    /**
     * Handle conversation timeout
     *
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function handleTimeout(WhatsAppConversation $conversation): array
    {
        // Mark conversation as finished
        $conversation->is_finished = true;
        $conversation->updated_at = now();

        // Add timeout information to conversation data
        $conversationData = $conversation->json ? json_decode($conversation->json, true) : [];
        $conversationData['timeout_info'] = [
            'timed_out_at' => now()->toISOString(),
            'reason' => 'conversation_timeout',
            'last_activity' => $conversation->updated_at?->toISOString(),
        ];
        $conversation->json = json_encode($conversationData);

        // Save conversation
        $this->conversationRepository->save($conversation);

        DBLog::logInfo(
            'Conversation timed out',
            'ConversationTimeoutService',
            $conversation->organization_id ?? null,
            null,
            [
                'conversation_id' => $conversation->id,
                'client_id' => $conversation->client_id,
                'flow_id' => $conversation->flow_id,
                'last_activity' => $conversation->updated_at?->toISOString(),
            ]
        );

        return [
            'action' => 'timeout',
            'conversation_id' => $conversation->id,
            'message' => 'Conversa encerrada por inatividade.',
            'timeout_at' => now()->toISOString(),
        ];
    }

    /**
     * Find all timed out conversations
     *
     * @return array
     */
    public function findTimedOutConversations(): array
    {
        $timeouts = config('chatbot.timeouts', []);
        $conversationTimeout = $timeouts['conversation'] ?? 3600;

        $cutoffTime = now()->subSeconds($conversationTimeout);

        // This would need to be implemented in the repository
        // For now, return empty array as placeholder
        return [];
    }

    /**
     * Find all inactive conversations
     *
     * @return array
     */
    public function findInactiveConversations(): array
    {
        $timeouts = config('chatbot.timeouts', []);
        $inactiveTimeout = $timeouts['inactive_after'] ?? 86400;

        $cutoffTime = now()->subSeconds($inactiveTimeout);

        // This would need to be implemented in the repository
        // For now, return empty array as placeholder
        return [];
    }

    /**
     * Cleanup inactive conversations
     *
     * @param bool $dryRun
     * @return array
     */
    public function cleanupInactiveConversations(bool $dryRun = false): array
    {
        $inactiveConversations = $this->findInactiveConversations();
        $cleanupConfig = config('chatbot.cleanup', []);
        $softDelete = $cleanupConfig['soft_delete'] ?? true;

        $cleaned = 0;
        $errors = 0;

        foreach ($inactiveConversations as $conversation) {
            try {
                if (!$dryRun) {
                    if ($softDelete) {
                        // Mark as finished instead of deleting
                        $conversation->is_finished = true;
                        $conversation->updated_at = now();

                        $conversationData = $conversation->json ? json_decode($conversation->json, true) : [];
                        $conversationData['cleanup_info'] = [
                            'cleaned_up_at' => now()->toISOString(),
                            'reason' => 'inactive_cleanup',
                            'soft_delete' => true,
                        ];
                        $conversation->json = json_encode($conversationData);

                        $this->conversationRepository->save($conversation);
                    } else {
                        // Hard delete (if repository supports it)
                        // $this->conversationRepository->delete($conversation->id);
                    }
                }

                $cleaned++;
            } catch (\Exception $e) {
                $errors++;
                DBLog::logError(
                    'Failed to cleanup conversation',
                    'ConversationTimeoutService',
                    $conversation->organization_id ?? null,
                    null,
                    [
                        'conversation_id' => $conversation->id,
                        'error' => $e->getMessage(),
                    ]
                );
            }
        }

        DBLog::logInfo(
            'Conversation cleanup completed',
            'ConversationTimeoutService',
            null, // No specific organization for cleanup summary
            null,
            [
                'total_found' => count($inactiveConversations),
                'cleaned' => $cleaned,
                'errors' => $errors,
                'dry_run' => $dryRun,
                'soft_delete' => $softDelete,
            ]
        );

        return [
            'total_found' => count($inactiveConversations),
            'cleaned' => $cleaned,
            'errors' => $errors,
            'dry_run' => $dryRun,
        ];
    }

    /**
     * Send timeout warning to user
     *
     * @param WhatsAppConversation $conversation
     * @param int $minutesRemaining
     * @return array
     */
    public function sendTimeoutWarning(WhatsAppConversation $conversation, int $minutesRemaining): array
    {
        $warningMessage = "⏰ Sua conversa será encerrada em {$minutesRemaining} minutos por inatividade. " .
                         "Envie qualquer mensagem para continuar.";

        // TODO: Integrate with WhatsApp message sending service

        DBLog::logInfo(
            'Timeout warning sent',
            'ConversationTimeoutService',
            $conversation->organization_id ?? null,
            null,
            [
                'conversation_id' => $conversation->id,
                'client_id' => $conversation->client_id,
                'minutes_remaining' => $minutesRemaining,
            ]
        );

        return [
            'action' => 'timeout_warning',
            'conversation_id' => $conversation->id,
            'message' => $warningMessage,
            'minutes_remaining' => $minutesRemaining,
        ];
    }

    /**
     * Extend conversation timeout
     *
     * @param WhatsAppConversation $conversation
     * @param int $additionalSeconds
     * @return array
     */
    public function extendTimeout(WhatsAppConversation $conversation, int $additionalSeconds = 0): array
    {
        $conversation->updated_at = now();

        if ($additionalSeconds > 0) {
            $conversationData = $conversation->json ? json_decode($conversation->json, true) : [];
            $conversationData['timeout_extensions'] = $conversationData['timeout_extensions'] ?? [];
            $conversationData['timeout_extensions'][] = [
                'extended_at' => now()->toISOString(),
                'additional_seconds' => $additionalSeconds,
            ];
            $conversation->json = json_encode($conversationData);
        }

        $this->conversationRepository->save($conversation);

        return [
            'action' => 'timeout_extended',
            'conversation_id' => $conversation->id,
            'extended_at' => now()->toISOString(),
            'additional_seconds' => $additionalSeconds,
        ];
    }
}
