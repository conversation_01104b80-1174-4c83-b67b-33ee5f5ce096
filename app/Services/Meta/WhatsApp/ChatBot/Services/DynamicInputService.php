<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Services;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Repositories\ClientRepository;
use App\Repositories\PhoneNumberRepository;
use App\Repositories\OrganizationRepository;
use App\Domains\ChatBot\Step;
use App\Domains\Inventory\Client;
use Exception;

class DynamicInputService
{
    protected ClientRepository $clientRepository;
    protected PhoneNumberRepository $phoneNumberRepository;
    protected OrganizationRepository $organizationRepository;

    public function __construct(
        ClientRepository $clientRepository,
        PhoneNumberRepository $phoneNumberRepository,
        OrganizationRepository $organizationRepository
    ) {
        $this->clientRepository = $clientRepository;
        $this->phoneNumberRepository = $phoneNumberRepository;
        $this->organizationRepository = $organizationRepository;
    }

    /**
     * Process input step and update domain objects
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     * @throws Exception
     */
    public function processInputStep(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // Get input value from interaction
        $inputValue = $interaction->getTextContent();
        
        if (empty($inputValue)) {
            return [
                'success' => false,
                'error' => 'No input provided',
                'retry' => true
            ];
        }

        // Get input configuration from step
        $inputConfig = $this->getInputConfiguration($step);
        
        if (!$inputConfig) {
            return [
                'success' => false,
                'error' => 'Step input configuration not found',
                'retry' => false
            ];
        }

        // Validate input
        $validation = $this->validateInput($inputValue, $inputConfig);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'error' => $validation['error'],
                'retry' => true
            ];
        }

        // Process input and update domain objects
        try {
            $updateResult = $this->updateDomainObject($inputValue, $inputConfig, $conversation);
            
            return [
                'success' => true,
                'updated_field' => $inputConfig['field'],
                'updated_value' => $inputValue,
                'update_result' => $updateResult
            ];
            
        } catch (Exception $e) {
            \Log::error('Failed to update domain object from input', [
                'error' => $e->getMessage(),
                'step_id' => $step->id,
                'conversation_id' => $conversation->id,
                'input_config' => $inputConfig,
                'input_value' => $inputValue
            ]);

            return [
                'success' => false,
                'error' => 'Failed to save your information. Please try again.',
                'retry' => false,
                'finish_flow' => true
            ];
        }
    }

    /**
     * Get input configuration from step JSON
     *
     * @param Step $step
     * @return array|null
     */
    protected function getInputConfiguration(Step $step): ?array
    {
        if (!$step->json) {
            return null;
        }

        $jsonData = json_decode($step->json, true);
        if (!is_array($jsonData)) {
            return null;
        }

        // Look for input_field configuration
        if (isset($jsonData['input_field'])) {
            return $this->parseInputField($jsonData['input_field'], $jsonData);
        }

        return null;
    }

    /**
     * Parse input field configuration
     *
     * @param string $inputField
     * @param array $stepConfig
     * @return array
     */
    protected function parseInputField(string $inputField, array $stepConfig): array
    {
        // Parse pattern like "client.name"
        $parts = explode('.', $inputField);
        
        if (count($parts) !== 2) {
            throw new Exception("Invalid input field pattern: {$inputField}. Expected format: 'model.field'");
        }

        return [
            'model' => $parts[0],
            'field' => $parts[1],
            'validation' => $stepConfig['validation'] ?? null,
            'required' => $stepConfig['required'] ?? true,
            'min_length' => $stepConfig['min_length'] ?? null,
            'max_length' => $stepConfig['max_length'] ?? null,
            'pattern' => $stepConfig['pattern'] ?? null,
        ];
    }

    /**
     * Validate input value
     *
     * @param string $inputValue
     * @param array $inputConfig
     * @return array
     */
    protected function validateInput(string $inputValue, array $inputConfig): array
    {
        // Required validation
        if ($inputConfig['required'] && empty(trim($inputValue))) {
            return ['valid' => false, 'error' => 'This field is required'];
        }

        // Length validation
        if ($inputConfig['min_length'] && strlen($inputValue) < $inputConfig['min_length']) {
            return ['valid' => false, 'error' => "Minimum length is {$inputConfig['min_length']} characters"];
        }

        if ($inputConfig['max_length'] && strlen($inputValue) > $inputConfig['max_length']) {
            return ['valid' => false, 'error' => "Maximum length is {$inputConfig['max_length']} characters"];
        }

        // Pattern validation
        if ($inputConfig['pattern'] && !preg_match($inputConfig['pattern'], $inputValue)) {
            return ['valid' => false, 'error' => 'Invalid format'];
        }

        // Field-specific validation
        $fieldValidation = $this->validateFieldSpecific($inputValue, $inputConfig);
        if (!$fieldValidation['valid']) {
            return $fieldValidation;
        }

        return ['valid' => true, 'error' => null];
    }

    /**
     * Field-specific validation
     *
     * @param string $inputValue
     * @param array $inputConfig
     * @return array
     */
    protected function validateFieldSpecific(string $inputValue, array $inputConfig): array
    {
        $field = $inputConfig['field'];

        switch ($field) {
            case 'email':
                if (!filter_var($inputValue, FILTER_VALIDATE_EMAIL)) {
                    return ['valid' => false, 'error' => 'Please enter a valid email address'];
                }
                break;

            case 'phone':
                // Basic phone validation
                $cleanPhone = preg_replace('/[^0-9]/', '', $inputValue);
                if (strlen($cleanPhone) < 10 || strlen($cleanPhone) > 15) {
                    return ['valid' => false, 'error' => 'Please enter a valid phone number'];
                }
                break;

            case 'cpf':
                // Basic CPF validation (11 digits)
                $cleanCpf = preg_replace('/[^0-9]/', '', $inputValue);
                if (strlen($cleanCpf) !== 11) {
                    return ['valid' => false, 'error' => 'CPF must have 11 digits'];
                }
                break;

            case 'cep':
                // Basic CEP validation
                $cleanCep = preg_replace('/[^0-9]/', '', $inputValue);
                if (strlen($cleanCep) !== 8) {
                    return ['valid' => false, 'error' => 'CEP must have 8 digits'];
                }
                break;
        }

        return ['valid' => true, 'error' => null];
    }

    /**
     * Update domain object based on input
     *
     * @param string $inputValue
     * @param array $inputConfig
     * @param WhatsAppConversation $conversation
     * @return mixed
     * @throws Exception
     */
    protected function updateDomainObject(string $inputValue, array $inputConfig, WhatsAppConversation $conversation)
    {
        $model = $inputConfig['model'];
        $field = $inputConfig['field'];

        switch ($model) {
            case 'client':
                return $this->updateClient($field, $inputValue, $conversation);

            case 'phone_number':
                return $this->updatePhoneNumber($field, $inputValue, $conversation);

            case 'organization':
                return $this->updateOrganization($field, $inputValue, $conversation);

            default:
                throw new Exception("Unsupported model for input: {$model}");
        }
    }

    /**
     * Update client field
     *
     * @param string $field
     * @param string $value
     * @param WhatsAppConversation $conversation
     * @return Client
     * @throws Exception
     */
    protected function updateClient(string $field, string $value, WhatsAppConversation $conversation): Client
    {
        if (!$conversation->client_id) {
            throw new Exception('No client associated with conversation');
        }

        $client = $this->clientRepository->fetchById($conversation->client_id);
        if (!$client) {
            throw new Exception('Client not found');
        }

        // Update the field
        switch ($field) {
            case 'name':
                $client->name = $value;
                break;
            case 'email':
                $client->email = $value;
                break;
            case 'phone':
                $client->phone = $value;
                break;
            case 'profession':
                $client->profession = $value;
                break;
            case 'cpf':
                $client->cpf = $value;
                break;
            case 'address':
                $client->address = $value;
                break;
            case 'neighborhood':
                $client->neighborhood = $value;
                break;
            case 'cep':
                $client->cep = $value;
                break;
            case 'complement':
                $client->complement = $value;
                break;
            default:
                throw new Exception("Unsupported client field: {$field}");
        }

        return $this->clientRepository->save($client);
    }

    /**
     * Update phone number field
     *
     * @param string $field
     * @param string $value
     * @param WhatsAppConversation $conversation
     * @return mixed
     * @throws Exception
     */
    protected function updatePhoneNumber(string $field, string $value, WhatsAppConversation $conversation)
    {
        if (!$conversation->phone_number_id) {
            throw new Exception('No phone number associated with conversation');
        }

        $phoneNumber = $this->phoneNumberRepository->fetchById($conversation->phone_number_id);
        if (!$phoneNumber) {
            throw new Exception('Phone number not found');
        }

        // Update the field
        switch ($field) {
            case 'name':
                $phoneNumber->name = $value;
                break;
            case 'description':
                $phoneNumber->description = $value;
                break;
            default:
                throw new Exception("Unsupported phone number field: {$field}");
        }

        return $this->phoneNumberRepository->save($phoneNumber);
    }

    /**
     * Update organization field
     *
     * @param string $field
     * @param string $value
     * @param WhatsAppConversation $conversation
     * @return mixed
     * @throws Exception
     */
    protected function updateOrganization(string $field, string $value, WhatsAppConversation $conversation)
    {
        if (!$conversation->organization_id) {
            throw new Exception('No organization associated with conversation');
        }

        // Note: Organization updates might need special permissions
        // For now, we'll throw an exception as it's typically not user-editable
        throw new Exception('Organization fields cannot be updated via user input');
    }
}
