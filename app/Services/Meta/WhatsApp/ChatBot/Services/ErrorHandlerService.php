<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Services;

use App\Enums\ChatBot\ChatBotErrorType;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Domains\ChatBot\Step;
use App\Helpers\DBLog;
use Exception;

class ErrorHandlerService
{
    /**
     * Handle error and determine recovery strategy
     *
     * @param Exception $exception
     * @param ChatBotErrorType|null $errorType
     * @param WhatsAppConversation $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     * @return array
     */
    public function handleError(
        Exception $exception,
        ?ChatBotErrorType $errorType = null,
        ?WhatsAppConversation $conversation = null,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): array {
        // Determine error type if not provided
        if (!$errorType) {
            $errorType = $this->determineErrorType($exception);
        }

        // Log error
        $this->logError($exception, $errorType, $conversation, $interaction, $currentStep);

        // Get recovery strategy
        $strategy = $this->getRecoveryStrategy($errorType, $exception);

        // Execute recovery strategy
        return $this->executeRecoveryStrategy($strategy, $errorType, $conversation, $interaction, $currentStep);
    }

    /**
     * Determine error type from exception
     *
     * @param Exception $exception
     * @return ChatBotErrorType
     */
    protected function determineErrorType(Exception $exception): ChatBotErrorType
    {
        $message = strtolower($exception->getMessage());

        if (str_contains($message, 'validation') || str_contains($message, 'invalid')) {
            return ChatBotErrorType::VALIDATION_ERROR;
        }

        if (str_contains($message, 'timeout') || str_contains($message, 'time')) {
            return ChatBotErrorType::TIMEOUT_ERROR;
        }

        if (str_contains($message, 'api') || str_contains($message, 'external') || str_contains($message, 'http')) {
            return ChatBotErrorType::EXTERNAL_API_ERROR;
        }

        if (str_contains($message, 'flow') || str_contains($message, 'step') || str_contains($message, 'navigation')) {
            return ChatBotErrorType::FLOW_ERROR;
        }

        return ChatBotErrorType::COMMAND_EXECUTION_ERROR;
    }

    /**
     * Get recovery strategy for error type
     *
     * @param ChatBotErrorType $errorType
     * @param Exception $exception
     * @return string
     */
    protected function getRecoveryStrategy(ChatBotErrorType $errorType, Exception $exception): string
    {
        $config = config('chatbot.error_handling.strategies', []);

        return match($errorType) {
            ChatBotErrorType::VALIDATION_ERROR => 'retry',
            ChatBotErrorType::COMMAND_EXECUTION_ERROR => $config['default'] ?? 'retry',
            ChatBotErrorType::FLOW_ERROR => 'fallback',
            ChatBotErrorType::EXTERNAL_API_ERROR => 'retry',
            ChatBotErrorType::TIMEOUT_ERROR => 'reset',
        };
    }

    /**
     * Execute recovery strategy
     *
     * @param string $strategy
     * @param ChatBotErrorType $errorType
     * @param WhatsAppConversation|null $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     * @return array
     */
    protected function executeRecoveryStrategy(
        string $strategy,
        ChatBotErrorType $errorType,
        ?WhatsAppConversation $conversation = null,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): array {
        return match($strategy) {
            'retry' => $this->executeRetryStrategy($errorType, $conversation, $interaction, $currentStep),
            'fallback' => $this->executeFallbackStrategy($errorType, $conversation, $interaction, $currentStep),
            'escalate' => $this->executeEscalationStrategy($errorType, $conversation, $interaction, $currentStep),
            'reset' => $this->executeResetStrategy($errorType, $conversation, $interaction, $currentStep),
            default => $this->executeDefaultStrategy($errorType, $conversation, $interaction, $currentStep)
        };
    }

    /**
     * Execute retry strategy
     *
     * @param ChatBotErrorType $errorType
     * @param WhatsAppConversation|null $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     * @return array
     */
    protected function executeRetryStrategy(
        ChatBotErrorType $errorType,
        ?WhatsAppConversation $conversation = null,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): array {
        $config = config('chatbot.error_handling.strategies.retry', []);
        $maxAttempts = $config['max_attempts'] ?? 3;
        $delay = $config['delay'] ?? 5;

        return [
            'strategy' => 'retry',
            'action' => 'retry_current_step',
            'max_attempts' => $maxAttempts,
            'delay_seconds' => $delay,
            'message' => $errorType->getDefaultMessage(),
            'stay_on_current_step' => true,
            'increment_retry_count' => true,
        ];
    }

    /**
     * Execute fallback strategy
     *
     * @param ChatBotErrorType $errorType
     * @param WhatsAppConversation|null $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     * @return array
     */
    protected function executeFallbackStrategy(
        ChatBotErrorType $errorType,
        ?WhatsAppConversation $conversation = null,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): array {
        $config = config('chatbot.error_handling.strategies.fallback', []);
        $fallbackStep = $config['fallback_step'] ?? 'error_fallback';
        $fallbackMessage = $config['fallback_message'] ?? $errorType->getDefaultMessage();

        return [
            'strategy' => 'fallback',
            'action' => 'goto_fallback_step',
            'fallback_step' => $fallbackStep,
            'message' => $fallbackMessage,
            'preserve_conversation_data' => true,
        ];
    }

    /**
     * Execute escalation strategy
     *
     * @param ChatBotErrorType $errorType
     * @param WhatsAppConversation|null $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     * @return array
     */
    protected function executeEscalationStrategy(
        ChatBotErrorType $errorType,
        ?WhatsAppConversation $conversation = null,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): array {
        $config = config('chatbot.error_handling.strategies.escalate', []);
        $escalationMessage = $config['escalation_message'] ?? $errorType->getDefaultMessage();
        $notifyTeam = $config['notify_team'] ?? true;

        if ($notifyTeam && $conversation) {
            $this->notifyTeamOfEscalation($errorType, $conversation, $interaction, $currentStep);
        }

        return [
            'strategy' => 'escalate',
            'action' => 'escalate_to_human',
            'message' => $escalationMessage,
            'mark_for_human_takeover' => true,
            'preserve_conversation_data' => true,
            'team_notified' => $notifyTeam,
        ];
    }

    /**
     * Execute reset strategy
     *
     * @param ChatBotErrorType $errorType
     * @param WhatsAppConversation|null $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     * @return array
     */
    protected function executeResetStrategy(
        ChatBotErrorType $errorType,
        ?WhatsAppConversation $conversation = null,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): array {
        $config = config('chatbot.error_handling.strategies.reset', []);
        $resetMessage = $config['reset_message'] ?? $errorType->getDefaultMessage();
        $preserveClientData = $config['preserve_client_data'] ?? true;

        return [
            'strategy' => 'reset',
            'action' => 'reset_conversation',
            'message' => $resetMessage,
            'goto_initial_step' => true,
            'preserve_client_data' => $preserveClientData,
            'clear_conversation_data' => !$preserveClientData,
        ];
    }

    /**
     * Execute default strategy
     *
     * @param ChatBotErrorType $errorType
     * @param WhatsAppConversation|null $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     * @return array
     */
    protected function executeDefaultStrategy(
        ChatBotErrorType $errorType,
        ?WhatsAppConversation $conversation = null,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): array {
        return [
            'strategy' => 'default',
            'action' => 'send_error_message',
            'message' => $errorType->getDefaultMessage(),
            'stay_on_current_step' => true,
        ];
    }

    /**
     * Log error for debugging and monitoring
     *
     * @param Exception $exception
     * @param ChatBotErrorType $errorType
     * @param WhatsAppConversation|null $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     */
    protected function logError(
        Exception $exception,
        ChatBotErrorType $errorType,
        ?WhatsAppConversation $conversation = null,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): void {
        if (!config('chatbot.error_handling.log_errors', true)) {
            return;
        }

        $logData = [
            'error_type' => $errorType->value,
            'error_message' => $exception->getMessage(),
            'error_file' => $exception->getFile(),
            'error_line' => $exception->getLine(),
            'conversation_id' => $conversation?->id,
            'interaction_id' => $interaction?->id,
            'current_step_id' => $currentStep?->id,
            'current_step_identifier' => $currentStep?->step,
            'severity' => $errorType->getSeverity(),
            'should_escalate' => $errorType->shouldEscalate(),
        ];

        DBLog::logError(
            'ChatBot Error: ' . $errorType->getDescription(),
            'ErrorHandlerService',
            $conversation?->organization_id ?? null,
            null,
            $logData
        );
    }

    /**
     * Notify team of escalation
     *
     * @param ChatBotErrorType $errorType
     * @param WhatsAppConversation $conversation
     * @param WhatsAppInteraction|null $interaction
     * @param Step|null $currentStep
     */
    protected function notifyTeamOfEscalation(
        ChatBotErrorType $errorType,
        WhatsAppConversation $conversation,
        ?WhatsAppInteraction $interaction = null,
        ?Step $currentStep = null
    ): void {
        // TODO: Implement team notification logic
        // This could send emails, Slack messages, or create tickets

        DBLog::logInfo(
            'ChatBot Escalation Notification',
            'ErrorHandlerService',
            $conversation->organization_id ?? null,
            null,
            [
                'error_type' => $errorType->value,
                'conversation_id' => $conversation->id,
                'client_id' => $conversation->client_id,
                'current_step' => $currentStep?->step,
                'escalation_reason' => $errorType->getDescription(),
            ]
        );
    }
}
