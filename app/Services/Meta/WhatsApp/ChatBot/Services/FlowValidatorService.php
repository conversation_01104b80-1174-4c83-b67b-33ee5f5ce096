<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Services;

use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use App\Repositories\StepRepository;

class FlowValidatorService
{
    protected StepRepository $stepRepository;

    public function __construct(StepRepository $stepRepository)
    {
        $this->stepRepository = $stepRepository;
    }

    /**
     * Validate flow integrity
     *
     * @param Flow $flow
     * @return array
     */
    public function validateFlow(Flow $flow): array
    {
        $validationRules = config('chatbot.flow_validation.rules', []);
        $errors = [];
        $warnings = [];

        // Get all steps for the flow
        $steps = $flow->steps ?? [];

        if (empty($steps)) {
            $errors[] = 'Flow has no steps defined';
            return [
                'valid' => false,
                'errors' => $errors,
                'warnings' => $warnings,
            ];
        }

        // Validate initial step
        if ($validationRules['require_initial_step'] ?? true) {
            $initialStepValidation = $this->validateInitialStep($steps);
            if (!$initialStepValidation['valid']) {
                $errors = array_merge($errors, $initialStepValidation['errors']);
            }
        }

        // Validate ending step
        if ($validationRules['require_ending_step'] ?? true) {
            $endingStepValidation = $this->validateEndingStep($steps);
            if (!$endingStepValidation['valid']) {
                $errors = array_merge($errors, $endingStepValidation['errors']);
            }
        }

        // Check for orphaned steps
        if ($validationRules['check_orphaned_steps'] ?? true) {
            $orphanedStepsValidation = $this->validateOrphanedSteps($steps);
            if (!$orphanedStepsValidation['valid']) {
                $warnings = array_merge($warnings, $orphanedStepsValidation['warnings']);
            }
        }

        // Check for infinite loops
        if ($validationRules['check_infinite_loops'] ?? true) {
            $infiniteLoopValidation = $this->validateInfiniteLoops($steps);
            if (!$infiniteLoopValidation['valid']) {
                $errors = array_merge($errors, $infiniteLoopValidation['errors']);
            }
        }

        // Validate conditional navigation targets
        if ($validationRules['validate_conditional_targets'] ?? true) {
            $conditionalValidation = $this->validateConditionalTargets($steps);
            if (!$conditionalValidation['valid']) {
                $errors = array_merge($errors, $conditionalValidation['errors']);
            }
        }

        // Additional validations
        $stepValidation = $this->validateStepConfiguration($steps);
        $errors = array_merge($errors, $stepValidation['errors']);
        $warnings = array_merge($warnings, $stepValidation['warnings']);

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'flow_id' => $flow->id,
            'flow_name' => $flow->name,
            'total_steps' => count($steps),
        ];
    }

    /**
     * Validate initial step exists and is unique
     *
     * @param array $steps
     * @return array
     */
    protected function validateInitialStep(array $steps): array
    {
        $initialSteps = array_filter($steps, fn($step) => $step->is_initial_step);

        if (empty($initialSteps)) {
            return [
                'valid' => false,
                'errors' => ['No initial step found in flow']
            ];
        }

        if (count($initialSteps) > 1) {
            return [
                'valid' => false,
                'errors' => ['Multiple initial steps found. Only one initial step is allowed.']
            ];
        }

        return ['valid' => true, 'errors' => []];
    }

    /**
     * Validate ending step exists
     *
     * @param array $steps
     * @return array
     */
    protected function validateEndingStep(array $steps): array
    {
        $endingSteps = array_filter($steps, fn($step) => $step->is_ending_step);

        if (empty($endingSteps)) {
            return [
                'valid' => false,
                'errors' => ['No ending step found in flow']
            ];
        }

        return ['valid' => true, 'errors' => []];
    }

    /**
     * Check for orphaned steps (steps that cannot be reached)
     *
     * @param array $steps
     * @return array
     */
    protected function validateOrphanedSteps(array $steps): array
    {
        $stepIds = array_map(fn($step) => $step->id, $steps);
        $reachableSteps = [];
        $warnings = [];

        // Find initial step
        $initialStep = array_values(array_filter($steps, fn($step) => $step->is_initial_step))[0] ?? null;

        if (!$initialStep) {
            return ['valid' => true, 'warnings' => []];
        }

        // Traverse from initial step
        $this->traverseSteps($initialStep, $steps, $reachableSteps);

        // Find orphaned steps
        $orphanedSteps = array_filter($steps, fn($step) => !in_array($step->id, $reachableSteps));

        foreach ($orphanedSteps as $orphanedStep) {
            $warnings[] = "Step '{$orphanedStep->step}' (ID: {$orphanedStep->id}) is orphaned and cannot be reached";
        }

        return [
            'valid' => empty($orphanedSteps),
            'warnings' => $warnings
        ];
    }

    /**
     * Check for infinite loops in flow
     *
     * @param array $steps
     * @return array
     */
    protected function validateInfiniteLoops(array $steps): array
    {
        $errors = [];
        $visited = [];
        $recursionStack = [];

        foreach ($steps as $step) {
            if (!isset($visited[$step->id])) {
                $loopResult = $this->detectLoop($step, $steps, $visited, $recursionStack);
                if ($loopResult['hasLoop']) {
                    $errors[] = "Infinite loop detected starting from step '{$step->step}' (ID: {$step->id})";
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate conditional navigation targets exist
     *
     * @param array $steps
     * @return array
     */
    protected function validateConditionalTargets(array $steps): array
    {
        $errors = [];
        $stepIdentifiers = array_map(fn($step) => $step->step, $steps);

        foreach ($steps as $step) {
            if (!$step->is_interactive) {
                continue;
            }

            // Check if step has components with conditional buttons
            if (!$step->component || !$step->component->buttons) {
                continue;
            }

            foreach ($step->component->buttons as $button) {
                if ($button->isConditionalButton()) {
                    $targetStep = $button->getTargetStepIdentifier();
                    if ($targetStep && !in_array($targetStep, $stepIdentifiers)) {
                        $errors[] = "Step '{$step->step}' has conditional button targeting non-existent step '{$targetStep}'";
                    }
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate step configuration
     *
     * @param array $steps
     * @return array
     */
    protected function validateStepConfiguration(array $steps): array
    {
        $errors = [];
        $warnings = [];

        foreach ($steps as $step) {
            // Validate step has required properties
            if (empty($step->step)) {
                $errors[] = "Step ID {$step->id} has no step identifier";
            }

            // Validate step type flags
            $typeFlags = [
                $step->is_message,
                $step->is_interactive,
                $step->is_command,
                $step->is_input
            ];
            $activeFlagsCount = count(array_filter($typeFlags));

            if ($activeFlagsCount === 0) {
                $warnings[] = "Step '{$step->step}' has no type flags set";
            } elseif ($activeFlagsCount > 1) {
                $warnings[] = "Step '{$step->step}' has multiple type flags set";
            }

            // Validate next_step references
            if (!$step->is_ending_step && !$step->next_step) {
                $errors[] = "Non-ending step '{$step->step}' has no next_step defined";
            }

            // Validate command steps have proper configuration
            if ($step->is_command) {
                $commandValidation = $this->validateCommandStep($step);
                $errors = array_merge($errors, $commandValidation['errors']);
                $warnings = array_merge($warnings, $commandValidation['warnings']);
            }

            // Validate input steps have proper configuration
            if ($step->is_input) {
                $inputValidation = $this->validateInputStep($step);
                $errors = array_merge($errors, $inputValidation['errors']);
                $warnings = array_merge($warnings, $inputValidation['warnings']);
            }
        }

        return [
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * Validate command step configuration
     *
     * @param Step $step
     * @return array
     */
    protected function validateCommandStep(Step $step): array
    {
        $errors = [];
        $warnings = [];

        if (!$step->json) {
            $errors[] = "Command step '{$step->step}' has no JSON configuration";
            return ['errors' => $errors, 'warnings' => $warnings];
        }

        $config = json_decode($step->json, true);
        if (!is_array($config)) {
            $errors[] = "Command step '{$step->step}' has invalid JSON configuration";
            return ['errors' => $errors, 'warnings' => $warnings];
        }

        if (!isset($config['command_type'])) {
            $errors[] = "Command step '{$step->step}' has no command_type specified";
        }

        if (!isset($config['command_config'])) {
            $warnings[] = "Command step '{$step->step}' has no command_config specified";
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Validate input step configuration
     *
     * @param Step $step
     * @return array
     */
    protected function validateInputStep(Step $step): array
    {
        $errors = [];
        $warnings = [];

        if ($step->json) {
            $config = json_decode($step->json, true);
            if (is_array($config) && isset($config['input_config'])) {
                $inputConfig = $config['input_config'];

                if (!isset($inputConfig['field_name'])) {
                    $warnings[] = "Input step '{$step->step}' has no field_name specified";
                }
            }
        }

        return ['errors' => $errors, 'warnings' => $warnings];
    }

    /**
     * Traverse steps to find reachable ones
     *
     * @param Step $step
     * @param array $allSteps
     * @param array &$reachableSteps
     */
    protected function traverseSteps(Step $step, array $allSteps, array &$reachableSteps): void
    {
        if (in_array($step->id, $reachableSteps)) {
            return;
        }

        $reachableSteps[] = $step->id;

        // Follow next_step
        if ($step->next_step) {
            $nextStep = array_values(array_filter($allSteps, fn($s) => $s->id === $step->next_step))[0] ?? null;
            if ($nextStep) {
                $this->traverseSteps($nextStep, $allSteps, $reachableSteps);
            }
        }

        // Follow conditional navigation targets
        if ($step->is_interactive && $step->component && $step->component->buttons) {
            foreach ($step->component->buttons as $button) {
                if ($button->isConditionalButton()) {
                    $targetStepIdentifier = $button->getTargetStepIdentifier();
                    if ($targetStepIdentifier) {
                        $targetStep = array_values(array_filter($allSteps, fn($s) => $s->step === $targetStepIdentifier))[0] ?? null;
                        if ($targetStep) {
                            $this->traverseSteps($targetStep, $allSteps, $reachableSteps);
                        }
                    }
                }
            }
        }
    }

    /**
     * Detect loops in flow using DFS
     *
     * @param Step $step
     * @param array $allSteps
     * @param array &$visited
     * @param array &$recursionStack
     * @return array
     */
    protected function detectLoop(Step $step, array $allSteps, array &$visited, array &$recursionStack): array
    {
        $visited[$step->id] = true;
        $recursionStack[$step->id] = true;

        // Check next_step
        if ($step->next_step) {
            if (!isset($visited[$step->next_step])) {
                $nextStep = array_values(array_filter($allSteps, fn($s) => $s->id === $step->next_step))[0] ?? null;
                if ($nextStep) {
                    $result = $this->detectLoop($nextStep, $allSteps, $visited, $recursionStack);
                    if ($result['hasLoop']) {
                        return $result;
                    }
                }
            } elseif (isset($recursionStack[$step->next_step])) {
                return ['hasLoop' => true];
            }
        }

        $recursionStack[$step->id] = false;
        return ['hasLoop' => false];
    }
}
