<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Repositories\ClientRepository;
use App\Services\Resend\ResendService;
use Exception;

class SendNotification
{
    protected ClientRepository $clientRepository;
    protected ResendService $emailService;

    public function __construct(
        ClientRepository $clientRepository,
        ResendService $emailService
    ) {
        $this->clientRepository = $clientRepository;
        $this->emailService = $emailService;
    }

    /**
     * Send notification based on command configuration
     *
     * @param array $config
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     * @throws Exception
     */
    public function perform(
        array $config,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        $notificationType = $config['type'] ?? 'email';

        return match($notificationType) {
            'email' => $this->sendEmailNotification($config, $interaction, $conversation),
            'internal' => $this->sendInternalNotification($config, $interaction, $conversation),
            default => throw new Exception("Unknown notification type: {$notificationType}")
        };
    }

    /**
     * Send email notification
     *
     * @param array $config
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     * @throws Exception
     */
    protected function sendEmailNotification(
        array $config,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // Get client data
        $client = $this->clientRepository->findById($conversation->client_id);
        if (!$client) {
            throw new Exception('Client not found for conversation');
        }

        // Prepare email data
        $emailData = $this->prepareEmailData($config, $interaction, $conversation, $client);

        // Validate email configuration
        if (!isset($config['to']) || !isset($config['subject'])) {
            throw new Exception('Email notification requires "to" and "subject" fields');
        }

        // Send email
        $emailResult = $this->emailService->sendEmail(
            $config['to'],
            $config['subject'],
            $emailData['body'],
            $emailData['template'] ?? null,
            $emailData['variables'] ?? []
        );

        if (!$emailResult['success']) {
            throw new Exception('Failed to send email notification: ' . ($emailResult['error'] ?? 'Unknown error'));
        }

        return [
            'notification_type' => 'email',
            'recipient' => $config['to'],
            'subject' => $config['subject'],
            'email_id' => $emailResult['email_id'] ?? null,
            'client_id' => $client->id,
            'conversation_id' => $conversation->id,
        ];
    }

    /**
     * Send internal notification (log, database, etc.)
     *
     * @param array $config
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    protected function sendInternalNotification(
        array $config,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        $message = $config['message'] ?? 'ChatBot notification';
        $level = $config['level'] ?? 'info';

        // Log notification
        \Log::log($level, $message, [
            'source' => 'chatbot_notification',
            'conversation_id' => $conversation->id,
            'client_id' => $conversation->client_id,
            'step_id' => $interaction->step_id,
            'interaction_id' => $interaction->id,
            'config' => $config,
        ]);

        // TODO: Add database notification storage if needed
        // TODO: Add real-time notification to admin dashboard

        return [
            'notification_type' => 'internal',
            'message' => $message,
            'level' => $level,
            'logged_at' => now()->toISOString(),
            'conversation_id' => $conversation->id,
        ];
    }

    /**
     * Prepare email data with variable substitution
     *
     * @param array $config
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @param mixed $client
     * @return array
     */
    protected function prepareEmailData(
        array $config,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation,
        $client
    ): array {
        // Get conversation data
        $conversationData = [];
        if ($conversation->json) {
            $conversationData = json_decode($conversation->json, true) ?: [];
        }

        // Prepare variables for substitution
        $variables = [
            'client' => [
                'name' => $client->name ?? 'Cliente',
                'email' => $client->email ?? '',
                'phone' => $client->phone ?? '',
                'document' => $client->document ?? '',
            ],
            'conversation' => $conversationData,
            'interaction' => [
                'message' => $interaction->message_text ?? '',
                'input' => $interaction->input_text ?? '',
                'created_at' => $interaction->created_at?->format('d/m/Y H:i') ?? '',
            ],
        ];

        // Process email body with variable substitution
        $body = $config['body'] ?? $config['message'] ?? '';
        $body = $this->substituteVariables($body, $variables);

        // Process subject with variable substitution
        $subject = $this->substituteVariables($config['subject'], $variables);

        return [
            'body' => $body,
            'subject' => $subject,
            'template' => $config['template'] ?? null,
            'variables' => $variables,
        ];
    }

    /**
     * Substitute variables in text using {{variable.field}} pattern
     *
     * @param string $text
     * @param array $variables
     * @return string
     */
    protected function substituteVariables(string $text, array $variables): string
    {
        return preg_replace_callback('/\{\{([^}]+)\}\}/', function ($matches) use ($variables) {
            $path = explode('.', $matches[1]);
            $value = $variables;

            foreach ($path as $key) {
                if (is_array($value) && isset($value[$key])) {
                    $value = $value[$key];
                } else {
                    return $matches[0]; // Return original if not found
                }
            }

            return is_string($value) || is_numeric($value) ? $value : $matches[0];
        }, $text);
    }
}
