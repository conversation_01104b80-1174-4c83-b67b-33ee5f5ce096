<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Enums\ChatBot\CommandType;
use Exception;

class ExecuteCommand
{
    protected UpdateClientData $updateClientData;
    protected CreateLead $createLead;
    protected SendNotification $sendNotification;

    public function __construct(
        UpdateClientData $updateClientData,
        CreateLead $createLead,
        SendNotification $sendNotification
    ) {
        $this->updateClientData = $updateClientData;
        $this->createLead = $createLead;
        $this->sendNotification = $sendNotification;
    }

    /**
     * Execute command based on step configuration
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     * @throws Exception
     */
    public function perform(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // Parse step configuration
        $commandConfig = $this->parseCommandConfiguration($step);

        if (!$commandConfig) {
            throw new Exception("Invalid command configuration for step: {$step->step}");
        }

        $commandType = CommandType::fromString($commandConfig['command_type']);

        if (!$commandType) {
            throw new Exception("Unknown command type: {$commandConfig['command_type']}");
        }

        // Execute specific command
        return $this->executeSpecificCommand($commandType, $commandConfig, $step, $interaction, $conversation);
    }

    /**
     * Parse command configuration from step JSON
     *
     * @param Step $step
     * @return array|null
     */
    protected function parseCommandConfiguration(Step $step): ?array
    {
        if (!$step->json) {
            return null;
        }

        $jsonData = json_decode($step->json, true);

        if (!is_array($jsonData)) {
            return null;
        }

        // Validate required fields
        if (!isset($jsonData['command_type'])) {
            return null;
        }

        return [
            'command_type' => $jsonData['command_type'],
            'command_config' => $jsonData['command_config'] ?? [],
            'success_message' => $jsonData['success_message'] ?? 'Comando executado com sucesso!',
            'error_message' => $jsonData['error_message'] ?? 'Erro ao executar comando.',
        ];
    }

    /**
     * Execute specific command based on type
     *
     * @param CommandType $commandType
     * @param array $commandConfig
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     * @throws Exception
     */
    protected function executeSpecificCommand(
        CommandType $commandType,
        array $commandConfig,
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        try {
            $result = match($commandType) {
                CommandType::UPDATE_CLIENT => $this->updateClientData->perform(
                    $commandConfig['command_config'],
                    $interaction,
                    $conversation
                ),
                CommandType::CREATE_LEAD => $this->createLead->perform(
                    $commandConfig['command_config'],
                    $interaction,
                    $conversation
                ),
                CommandType::SEND_NOTIFICATION => $this->sendNotification->perform(
                    $commandConfig['command_config'],
                    $interaction,
                    $conversation
                ),
                default => throw new Exception("Command type not implemented: {$commandType->value}")
            };

            return [
                'success' => true,
                'command_type' => $commandType->value,
                'result' => $result,
                'message' => $commandConfig['success_message'],
            ];

        } catch (Exception $e) {
            \Log::error('Command execution failed', [
                'command_type' => $commandType->value,
                'step_id' => $step->id,
                'error' => $e->getMessage(),
                'config' => $commandConfig
            ]);

            return [
                'success' => false,
                'command_type' => $commandType->value,
                'error' => $e->getMessage(),
                'message' => $commandConfig['error_message'],
            ];
        }
    }
}
