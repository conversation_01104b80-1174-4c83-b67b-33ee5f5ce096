<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Domains\ChatBot\Flow;
use App\Services\Meta\WhatsApp\ChatBot\Services\FlowValidatorService;
use App\Repositories\StepRepository;
use Illuminate\Support\Facades\Cache;

class ValidateFlow
{
    protected FlowValidatorService $flowValidatorService;
    protected StepRepository $stepRepository;

    public function __construct(
        FlowValidatorService $flowValidatorService,
        StepRepository $stepRepository
    ) {
        $this->flowValidatorService = $flowValidatorService;
        $this->stepRepository = $stepRepository;
    }

    /**
     * Validate flow integrity and structure
     *
     * @param Flow $flow
     * @param bool $useCache
     * @return array
     */
    public function perform(Flow $flow, bool $useCache = true): array
    {
        // Check if validation is enabled
        if (!config('chatbot.flow_validation.enabled', true)) {
            return [
                'valid' => true,
                'skipped' => true,
                'reason' => 'Flow validation is disabled',
                'flow_id' => $flow->id,
            ];
        }

        // Check cache if enabled
        if ($useCache && config('chatbot.flow_validation.cache_results', true)) {
            $cacheKey = $this->getCacheKey($flow);
            $cachedResult = Cache::get($cacheKey);

            if ($cachedResult) {
                $cachedResult['from_cache'] = true;
                return $cachedResult;
            }
        }

        // Load steps if not already loaded
        if (!$flow->steps) {
            $flow->steps = $this->stepRepository->findByFlowId($flow->id);
        }

        // Perform validation
        $validationResult = $this->flowValidatorService->validateFlow($flow);

        // Add metadata
        $validationResult['validated_at'] = now()->toISOString();
        $validationResult['validation_version'] = '1.0';
        $validationResult['from_cache'] = false;

        // Cache result if enabled
        if ($useCache && config('chatbot.flow_validation.cache_results', true)) {
            $cacheTtl = config('chatbot.flow_validation.cache_ttl', 3600);
            Cache::put($this->getCacheKey($flow), $validationResult, $cacheTtl);
        }

        // Log validation result
        $this->logValidationResult($flow, $validationResult);

        return $validationResult;
    }

    /**
     * Validate multiple flows
     *
     * @param array $flows
     * @param bool $useCache
     * @return array
     */
    public function validateMultiple(array $flows, bool $useCache = true): array
    {
        $results = [];
        $summary = [
            'total_flows' => count($flows),
            'valid_flows' => 0,
            'invalid_flows' => 0,
            'flows_with_warnings' => 0,
            'total_errors' => 0,
            'total_warnings' => 0,
        ];

        foreach ($flows as $flow) {
            $result = $this->perform($flow, $useCache);
            $results[$flow->id] = $result;

            // Update summary
            if ($result['valid']) {
                $summary['valid_flows']++;
            } else {
                $summary['invalid_flows']++;
            }

            if (!empty($result['warnings'])) {
                $summary['flows_with_warnings']++;
            }

            $summary['total_errors'] += count($result['errors'] ?? []);
            $summary['total_warnings'] += count($result['warnings'] ?? []);
        }

        return [
            'summary' => $summary,
            'results' => $results,
            'validated_at' => now()->toISOString(),
        ];
    }

    /**
     * Quick validation check (basic structure only)
     *
     * @param Flow $flow
     * @return array
     */
    public function quickValidate(Flow $flow): array
    {
        // Load steps if not already loaded
        if (!$flow->steps) {
            $flow->steps = $this->stepRepository->findByFlowId($flow->id);
        }

        $errors = [];
        $steps = $flow->steps ?? [];

        // Basic checks
        if (empty($steps)) {
            $errors[] = 'Flow has no steps';
        } else {
            // Check for initial step
            $initialSteps = array_filter($steps, fn($step) => $step->is_initial_step);
            if (empty($initialSteps)) {
                $errors[] = 'No initial step found';
            }

            // Check for ending step
            $endingSteps = array_filter($steps, fn($step) => $step->is_ending_step);
            if (empty($endingSteps)) {
                $errors[] = 'No ending step found';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'flow_id' => $flow->id,
            'total_steps' => count($steps),
            'validation_type' => 'quick',
            'validated_at' => now()->toISOString(),
        ];
    }

    /**
     * Validate flow before execution
     *
     * @param Flow $flow
     * @return array
     */
    public function validateForExecution(Flow $flow): array
    {
        $validationResult = $this->perform($flow);

        // Additional checks for execution readiness
        if ($validationResult['valid']) {
            $executionChecks = $this->performExecutionChecks($flow);

            if (!$executionChecks['ready']) {
                $validationResult['valid'] = false;
                $validationResult['execution_errors'] = $executionChecks['errors'];
            }
        }

        return $validationResult;
    }

    /**
     * Perform additional checks for execution readiness
     *
     * @param Flow $flow
     * @return array
     */
    protected function performExecutionChecks(Flow $flow): array
    {
        $errors = [];
        $steps = $flow->steps ?? [];

        foreach ($steps as $step) {
            // Check command steps have valid command types
            if ($step->step_type === \App\Enums\StepType::COMMAND && $step->json) {
                $config = json_decode($step->json, true);
                if (is_array($config) && isset($config['command_type'])) {
                    $commandType = $config['command_type'];
                    if (!in_array($commandType, ['update_client', 'create_lead', 'send_notification', 'send_email', 'schedule_callback', 'update_conversation_data'])) {
                        $errors[] = "Step '{$step->step}' has unsupported command type: {$commandType}";
                    }
                }
            }

            // Check interactive steps have components
            if ($step->step_type === \App\Enums\StepType::INTERACTIVE && !$step->component) {
                $errors[] = "Interactive step '{$step->step}' has no component defined";
            }
        }

        return [
            'ready' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Clear validation cache for flow
     *
     * @param Flow $flow
     * @return bool
     */
    public function clearCache(Flow $flow): bool
    {
        $cacheKey = $this->getCacheKey($flow);
        return Cache::forget($cacheKey);
    }

    /**
     * Clear all validation cache
     *
     * @return bool
     */
    public function clearAllCache(): bool
    {
        // This would need a more sophisticated cache tagging system
        // For now, just return true as placeholder
        return true;
    }

    /**
     * Get cache key for flow validation
     *
     * @param Flow $flow
     * @return string
     */
    protected function getCacheKey(Flow $flow): string
    {
        $flowHash = md5(json_encode([
            'flow_id' => $flow->id,
            'updated_at' => $flow->updated_at?->timestamp,
            'steps_count' => $flow->steps_count,
        ]));

        return "chatbot:flow_validation:{$flow->id}:{$flowHash}";
    }

    /**
     * Log validation result
     *
     * @param Flow $flow
     * @param array $validationResult
     */
    protected function logValidationResult(Flow $flow, array $validationResult): void
    {
        $logLevel = $validationResult['valid'] ? 'info' : 'warning';

        \Log::log($logLevel, 'Flow validation completed', [
            'flow_id' => $flow->id,
            'flow_name' => $flow->name,
            'valid' => $validationResult['valid'],
            'error_count' => count($validationResult['errors'] ?? []),
            'warning_count' => count($validationResult['warnings'] ?? []),
            'total_steps' => $validationResult['total_steps'] ?? 0,
            'from_cache' => $validationResult['from_cache'] ?? false,
        ]);

        // Log errors and warnings if any
        if (!empty($validationResult['errors'])) {
            \Log::error('Flow validation errors', [
                'flow_id' => $flow->id,
                'errors' => $validationResult['errors'],
            ]);
        }

        if (!empty($validationResult['warnings'])) {
            \Log::warning('Flow validation warnings', [
                'flow_id' => $flow->id,
                'warnings' => $validationResult['warnings'],
            ]);
        }
    }
}
