<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;

/**
 * Interface for step processors in the ChatBot flow
 *
 * This interface defines the contract that all step processors must implement.
 * Each processor is responsible for handling a specific type of step in the
 * conversation flow using the Strategy Pattern.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
interface StepProcessorInterface
{
    /**
     * Process a step in the conversation flow
     *
     * This method contains the core logic for processing a specific type of step.
     * It receives the current step, user interaction, and conversation context,
     * and returns a standardized result array that describes the action to take.
     *
     * @param Step $step The current step being processed
     * @param WhatsAppInteraction $interaction The user's interaction/message
     * @param WhatsAppConversation $conversation The conversation context
     * @return array Standardized result array with the following structure:
     *               - type: string - The step type being processed
     *               - step_id: int - The ID of the current step
     *               - action: string - The action to perform (e.g., 'send_message', 'show_options')
     *               - message: string|null - Message to send to user (if applicable)
     *               - next_step: int|null - ID of the next step to navigate to
     *               - move_to_next: bool - Whether to advance to the next step
     *               - Additional fields specific to the step type
     * @throws \Exception When step processing fails
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array;

    /**
     * Check if this processor can handle the given step type
     *
     * @param Step $step The step to check
     * @return bool True if this processor can handle the step type
     */
    public function canProcess(Step $step): bool;

    /**
     * Get the step type(s) that this processor handles
     *
     * @return array Array of step type strings that this processor can handle
     */
    public function getSupportedStepTypes(): array;
}
