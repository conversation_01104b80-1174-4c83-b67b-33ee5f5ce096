<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;

/**
 * Processor for CONDITION type steps
 *
 * Handles conditional steps that evaluate conditions and determine
 * the next step in the flow based on conversation state, user data,
 * or other criteria. This enables branching logic in conversations.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class ConditionStepProcessor implements StepProcessorInterface
{
    /**
     * Process a condition step
     *
     * Condition steps evaluate configured conditions and determine
     * which path to take in the conversation flow. They typically
     * don't send messages to users but control navigation.
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // Get condition configuration from step
        $conditionConfig = $this->getConditionConfiguration($step);

        if (!$conditionConfig) {
            // No condition configuration, use default next step
            return [
                'type' => 'condition',
                'step_id' => $step->id,
                'action' => 'no_condition_config',
                'next_step' => $step->next_step,
                'move_to_next' => true,
                'condition_result' => null,
            ];
        }

        // Evaluate conditions
        $conditionResult = $this->evaluateConditions($conditionConfig, $conversation, $interaction);

        // Determine next step based on condition result
        $nextStep = $this->getNextStepFromConditionResult($conditionResult, $conditionConfig, $step);

        return [
            'type' => 'condition',
            'step_id' => $step->id,
            'action' => 'evaluate_condition',
            'condition_result' => $conditionResult,
            'next_step' => $nextStep,
            'move_to_next' => true,
            'conditions_evaluated' => $conditionConfig['conditions'] ?? [],
        ];
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::CONDITION;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::CONDITION->value];
    }

    /**
     * Get condition configuration from step
     *
     * @param Step $step
     * @return array|null
     */
    protected function getConditionConfiguration(Step $step): ?array
    {
        if (!$step->json) {
            return null;
        }

        $jsonData = json_decode($step->json, true);
        if (!is_array($jsonData) || !isset($jsonData['conditions'])) {
            return null;
        }

        return $jsonData;
    }

    /**
     * Evaluate conditions based on conversation state
     *
     * @param array $conditionConfig
     * @param WhatsAppConversation $conversation
     * @param WhatsAppInteraction $interaction
     * @return array
     */
    protected function evaluateConditions(
        array $conditionConfig,
        WhatsAppConversation $conversation,
        WhatsAppInteraction $interaction
    ): array {
        $results = [];
        $conditions = $conditionConfig['conditions'] ?? [];

        foreach ($conditions as $condition) {
            $result = $this->evaluateSingleCondition($condition, $conversation, $interaction);
            $results[] = [
                'condition' => $condition,
                'result' => $result,
            ];
        }

        return $results;
    }

    /**
     * Evaluate a single condition
     *
     * @param array $condition
     * @param WhatsAppConversation $conversation
     * @param WhatsAppInteraction $interaction
     * @return bool
     */
    protected function evaluateSingleCondition(
        array $condition,
        WhatsAppConversation $conversation,
        WhatsAppInteraction $interaction
    ): bool {
        // TODO: Implement actual condition evaluation logic
        // This would check things like:
        // - User data (client.name exists, client.email is valid, etc.)
        // - Conversation state (step count, time elapsed, etc.)
        // - Interaction type (button clicked, text received, etc.)
        // - External data (API responses, database queries, etc.)

        // For now, return true as placeholder
        return true;
    }

    /**
     * Get next step based on condition evaluation results
     *
     * @param array $conditionResults
     * @param array $conditionConfig
     * @param Step $step
     * @return int|null
     */
    protected function getNextStepFromConditionResult(
        array $conditionResults,
        array $conditionConfig,
        Step $step
    ): ?int {
        // TODO: Implement logic to determine next step based on condition results
        // This would:
        // 1. Check which conditions passed/failed
        // 2. Apply configured rules to determine the target step
        // 3. Fall back to default path if no conditions match

        // For now, return the default next step
        return $conditionConfig['default_path'] ?? $step->next_step;
    }
}
