<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;

/**
 * Processor for MESSAGE type steps
 *
 * Handles simple message steps that display content to the user
 * and automatically advance to the next step without requiring
 * user interaction.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class MessageStepProcessor implements StepProcessorInterface
{
    /**
     * Process a message step
     *
     * Message steps are the simplest type - they just display a message
     * to the user and automatically move to the next step.
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        return [
            'type' => 'message',
            'step_id' => $step->id,
            'action' => 'send_message',
            'message' => $this->getStepMessage($step),
            'next_step' => $step->next_step,
            'move_to_next' => true,
        ];
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::MESSAGE;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::MESSAGE->value];
    }

    /**
     * Get message content for step
     *
     * Extracts the message content from the step configuration,
     * falling back to the step name if no message is configured.
     *
     * @param Step $step
     * @return string
     */
    protected function getStepMessage(Step $step): string
    {
        // First try to get message from JSON configuration
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData) && isset($jsonData['message'])) {
                return $jsonData['message'];
            }
            // Also check for 'text' field as alternative
            if (is_array($jsonData) && isset($jsonData['text'])) {
                return $jsonData['text'];
            }
        }

        // Fallback to step name
        return $step->step ?? 'Step message not configured';
    }
}
