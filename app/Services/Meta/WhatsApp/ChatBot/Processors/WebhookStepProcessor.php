<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use App\Helpers\DBLog;

/**
 * Processor for WEBHOOK type steps
 *
 * Handles webhook steps that make calls to external APIs or services.
 * This enables integration with third-party systems and services
 * during the conversation flow.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class WebhookStepProcessor implements StepProcessorInterface
{
    /**
     * Process a webhook step
     *
     * Webhook steps make HTTP calls to external endpoints and can
     * process the responses to determine next actions or update
     * conversation state.
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // Get webhook configuration from step
        $webhookConfig = $this->getWebhookConfiguration($step);

        if (!$webhookConfig) {
            return [
                'type' => 'webhook',
                'step_id' => $step->id,
                'action' => 'webhook_config_missing',
                'error' => 'Webhook configuration not found',
                'next_step' => $step->next_step,
                'move_to_next' => false,
                'success' => false,
            ];
        }

        try {
            // Execute webhook call
            $webhookResult = $this->executeWebhookCall($webhookConfig, $conversation, $interaction);

            return [
                'type' => 'webhook',
                'step_id' => $step->id,
                'action' => 'webhook_executed',
                'webhook_result' => $webhookResult,
                'next_step' => $step->next_step,
                'move_to_next' => true,
                'success' => $webhookResult['success'] ?? true,
                'response_data' => $webhookResult['data'] ?? null,
            ];
        } catch (\Exception $e) {
            DBLog::logError(
                'Webhook step execution failed',
                'WebhookStepProcessor',
                $conversation->organization_id ?? null,
                null,
                [
                    'step_id' => $step->id,
                    'step_identifier' => $step->step,
                    'webhook_url' => $webhookConfig['url'] ?? 'unknown',
                    'error' => $e->getMessage(),
                    'conversation_id' => $conversation->id
                ]
            );

            return [
                'type' => 'webhook',
                'step_id' => $step->id,
                'action' => 'webhook_failed',
                'error' => $e->getMessage(),
                'next_step' => $step->next_step,
                'move_to_next' => false, // Don't advance on webhook failure
                'success' => false,
            ];
        }
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::WEBHOOK;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::WEBHOOK->value];
    }

    /**
     * Get webhook configuration from step
     *
     * @param Step $step
     * @return array|null
     */
    protected function getWebhookConfiguration(Step $step): ?array
    {
        if (!$step->json) {
            return null;
        }

        $jsonData = json_decode($step->json, true);
        if (!is_array($jsonData) || !isset($jsonData['url'])) {
            return null;
        }

        return $jsonData;
    }

    /**
     * Execute webhook call
     *
     * @param array $webhookConfig
     * @param WhatsAppConversation $conversation
     * @param WhatsAppInteraction $interaction
     * @return array
     * @throws \Exception
     */
    protected function executeWebhookCall(
        array $webhookConfig,
        WhatsAppConversation $conversation,
        WhatsAppInteraction $interaction
    ): array {
        // TODO: Implement actual webhook call logic
        // This would:
        // 1. Prepare the payload with conversation and interaction data
        // 2. Make HTTP request to the configured URL
        // 3. Handle response and extract relevant data
        // 4. Return structured result

        // For now, return a placeholder success response
        return [
            'success' => true,
            'message' => 'Webhook call completed (placeholder implementation)',
            'data' => [
                'url' => $webhookConfig['url'],
                'method' => $webhookConfig['method'] ?? 'POST',
                'status_code' => 200,
                'response' => 'Placeholder response'
            ]
        ];
    }
}
