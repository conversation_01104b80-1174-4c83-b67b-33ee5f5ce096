<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand;
use App\Helpers\DBLog;

/**
 * Processor for COMMAND type steps
 *
 * Handles command steps that execute business logic operations
 * such as updating client data, processing orders, or performing
 * calculations. Integrates with the ExecuteCommand UseCase.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class CommandStepProcessor implements StepProcessorInterface
{
    protected ExecuteCommand $executeCommand;

    public function __construct(ExecuteCommand $executeCommand)
    {
        $this->executeCommand = $executeCommand;
    }

    /**
     * Process a command step
     *
     * Command steps execute business logic and can either succeed
     * or fail. On success, they advance to the next step. On failure,
     * they typically stay on the current step or handle the error.
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        try {
            // Execute command using ExecuteCommand UseCase
            $commandResult = $this->executeCommand->perform($step, $interaction, $conversation);

            return [
                'type' => 'command',
                'step_id' => $step->id,
                'action' => 'command_executed',
                'result' => $commandResult,
                'next_step' => $step->next_step,
                'move_to_next' => true,
                'success' => $commandResult['success'] ?? true,
                'message' => $commandResult['message'] ?? null,
            ];
        } catch (\Exception $e) {
            DBLog::logError(
                'Command step execution failed',
                'CommandStepProcessor',
                $conversation->organization_id ?? null,
                null,
                [
                    'step_id' => $step->id,
                    'step_identifier' => $step->step,
                    'error' => $e->getMessage(),
                    'conversation_id' => $conversation->id
                ]
            );

            return [
                'type' => 'command',
                'step_id' => $step->id,
                'action' => 'command_failed',
                'result' => [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'message' => 'Erro ao executar comando. Tente novamente.'
                ],
                'next_step' => $step->next_step,
                'move_to_next' => false, // Don't move to next step on failure
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::COMMAND;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::COMMAND->value];
    }
}
