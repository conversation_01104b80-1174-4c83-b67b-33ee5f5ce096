<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;

/**
 * Processor for DELAY type steps
 *
 * Handles delay steps that introduce time delays in the conversation
 * flow. This can be used to pace conversations, simulate thinking time,
 * or coordinate with external processes that need time to complete.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class DelayStepProcessor implements StepProcessorInterface
{
    /**
     * Process a delay step
     *
     * Delay steps introduce a time delay before proceeding to the next
     * step. They can optionally send a message to the user during the delay.
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // Get delay configuration from step
        $delayConfig = $this->getDelayConfiguration($step);

        if (!$delayConfig) {
            // No delay configuration, proceed immediately
            return [
                'type' => 'delay',
                'step_id' => $step->id,
                'action' => 'no_delay_config',
                'delay_seconds' => 0,
                'next_step' => $step->next_step,
                'move_to_next' => true,
            ];
        }

        $delaySeconds = $delayConfig['duration_seconds'] ?? 0;
        $delayMessage = $delayConfig['message'] ?? null;

        // TODO: Implement actual delay logic
        // This would typically:
        // 1. Schedule the next step to be processed after the delay
        // 2. Optionally send a message to the user
        // 3. Return appropriate response indicating delay is in progress

        // For now, we'll simulate the delay processing
        return [
            'type' => 'delay',
            'step_id' => $step->id,
            'action' => 'delay_processing',
            'delay_seconds' => $delaySeconds,
            'delay_message' => $delayMessage,
            'next_step' => $step->next_step,
            'move_to_next' => true,
            'delay_scheduled' => true,
        ];
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::DELAY;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::DELAY->value];
    }

    /**
     * Get delay configuration from step
     *
     * @param Step $step
     * @return array|null
     */
    protected function getDelayConfiguration(Step $step): ?array
    {
        if (!$step->json) {
            return null;
        }

        $jsonData = json_decode($step->json, true);
        if (!is_array($jsonData) || !isset($jsonData['duration_seconds'])) {
            return null;
        }

        return $jsonData;
    }
}
