<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;

/**
 * Processor for INPUT type steps
 *
 * Handles input steps that collect user data, validate it,
 * and update domain objects accordingly. Integrates with
 * DynamicInputService for domain object updates.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class InputStepProcessor implements StepProcessorInterface
{
    protected DynamicInputService $dynamicInputService;

    public function __construct(DynamicInputService $dynamicInputService)
    {
        $this->dynamicInputService = $dynamicInputService;
    }

    /**
     * Process an input step
     *
     * Input steps either request input from the user or process
     * the input they provided, validating and storing it.
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        $userInput = $interaction->getTextContent();

        if ($userInput) {
            // Process dynamic input (update domain objects)
            $inputResult = $this->dynamicInputService->processInputStep($step, $interaction, $conversation);

            if ($inputResult['success']) {
                return [
                    'type' => 'input',
                    'step_id' => $step->id,
                    'action' => 'input_processed',
                    'input' => $userInput,
                    'updated_field' => $inputResult['updated_field'],
                    'updated_value' => $inputResult['updated_value'],
                    'message' => "✅ Got it! Your {$inputResult['updated_field']} has been saved.",
                    'next_step' => $step->next_step,
                    'move_to_next' => true,
                ];
            } else {
                // Handle input processing failure
                if ($inputResult['finish_flow'] ?? false) {
                    return [
                        'type' => 'input',
                        'step_id' => $step->id,
                        'action' => 'input_error_finish',
                        'error' => $inputResult['error'],
                        'message' => "❌ {$inputResult['error']}",
                        'move_to_next' => false,
                        'finish_conversation' => true,
                    ];
                } else {
                    return [
                        'type' => 'input',
                        'step_id' => $step->id,
                        'action' => 'input_invalid',
                        'error' => $inputResult['error'],
                        'message' => "❌ {$inputResult['error']} Please try again.",
                        'move_to_next' => false,
                    ];
                }
            }
        } else {
            // Request input
            return [
                'type' => 'input',
                'step_id' => $step->id,
                'action' => 'request_input',
                'message' => $this->getStepMessage($step),
                'move_to_next' => false,
            ];
        }
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::INPUT;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::INPUT->value];
    }

    /**
     * Get message content for step
     *
     * @param Step $step
     * @return string
     */
    protected function getStepMessage(Step $step): string
    {
        // First try to get message from JSON configuration
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData)) {
                // Check for prompt field (specific to input steps)
                if (isset($jsonData['prompt'])) {
                    return $jsonData['prompt'];
                }
                // Check for message field
                if (isset($jsonData['message'])) {
                    return $jsonData['message'];
                }
                // Check for text field
                if (isset($jsonData['text'])) {
                    return $jsonData['text'];
                }
            }
        }

        // Fallback to step name
        return $step->step ?? 'Please provide your input:';
    }
}
