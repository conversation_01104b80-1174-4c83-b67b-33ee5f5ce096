<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Factories;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Models\Conversation as ConversationModel;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Flow;
use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;

class WhatsAppConversationFactory
{
    protected ClientFactory $clientFactory;

    public function __construct(ClientFactory $clientFactory)
    {
        $this->clientFactory = $clientFactory;
    }

    /**
     * Build WhatsAppConversation from Eloquent model
     */
    public function buildFromModel(?ConversationModel $conversation): ?WhatsAppConversation
    {
        if (!$conversation) {
            return null;
        }

        // Parse WhatsApp metadata from JSON
        $metadata = [];
        $whatsappContactName = null;
        $whatsappProfileName = null;
        $whatsappMetadata = null;

        if ($conversation->json) {
            $jsonData = json_decode($conversation->json, true);
            if (is_array($jsonData)) {
                $whatsappContactName = $jsonData['whatsapp_contact_name'] ?? null;
                $whatsappProfileName = $jsonData['whatsapp_profile_name'] ?? null;
                $whatsappMetadata = $jsonData['whatsapp_metadata'] ?? null;
            }
        }

        // Load client if available
        $client = null;
        if ($conversation->client_id && $conversation->client) {
            $client = $this->clientFactory->buildFromModel($conversation->client);
        }

        return new WhatsAppConversation(
            $conversation->id,
            $conversation->organization_id,
            $conversation->user_id,
            $conversation->client_id,
            $conversation->flow_id,
            $conversation->phone_number_id,
            $conversation->current_step_id,
            $conversation->json,
            $conversation->is_finished,
            $conversation->created_at,
            $conversation->updated_at,
            $whatsappContactName,
            $whatsappProfileName,
            $whatsappMetadata,
            $client
        );
    }

    /**
     * Build new WhatsAppConversation for client and phone number
     */
    public function buildForClientAndPhoneNumber(
        Client $client,
        PhoneNumber $phoneNumber,
        Flow $flow,
        array $whatsappData = []
    ): WhatsAppConversation {
        $whatsappContactName = $whatsappData['contact_name'] ?? null;
        $whatsappProfileName = $whatsappData['profile_name'] ?? null;
        $whatsappMetadata = $whatsappData['metadata'] ?? [];

        return new WhatsAppConversation(
            null, // id
            $phoneNumber->organization_id,
            null, // user_id
            $client->id,
            $flow->id,
            $phoneNumber->id,
            null, // current_step_id - will be set when flow starts
            null, // json
            false, // is_finished
            null, // created_at
            null, // updated_at
            $whatsappContactName,
            $whatsappProfileName,
            $whatsappMetadata,
            $client
        );
    }

    /**
     * Build WhatsAppConversation from webhook message data
     */
    public function buildFromWebhookData(
        array $messageData,
        Client $client,
        PhoneNumber $phoneNumber,
        Flow $flow
    ): WhatsAppConversation {
        $whatsappData = [
            'contact_name' => $messageData['contact_name'] ?? null,
            'profile_name' => $messageData['profile_name'] ?? null,
            'metadata' => [
                'from' => $messageData['from'] ?? null,
                'timestamp' => $messageData['timestamp'] ?? null,
                'message_id' => $messageData['message_id'] ?? null,
            ]
        ];

        return $this->buildForClientAndPhoneNumber(
            $client,
            $phoneNumber,
            $flow,
            $whatsappData
        );
    }
}
