<?php

namespace App\Services\Meta\WhatsApp;

use App\Domains\ChatBot\PhoneNumber;
use App\Helpers\DBLog;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;

class WhatsAppService
{
    protected Client $client;
    protected string $apiUrl;
    protected string $phoneNumberId;
    protected string $businessId;
    protected string $token;
    protected string $endpoint;

    public function __construct(?PhoneNumber $phoneNumber = null)
    {
        $this->client = new Client();
        $this->apiUrl = config('whatsapp.base_url');
        $this->phoneNumberId = $phoneNumber?->whatsapp_phone_number_id ?? config('whatsapp.phone_number_id');
        $this->businessId = $phoneNumber?->whatsapp_business_id ?? config('whatsapp.business_id');
        $this->token = $phoneNumber?->whatsapp_access_token ?? config('whatsapp.access_token');
    }

    /**
     * @throws GuzzleException
     */
    protected function post(array $payload, ?string $endpoint = null, bool $isBusiness = false) : ResponseInterface {
        $endpoint = $endpoint ?? $this->endpoint;
        $id = $isBusiness ? $this->businessId : $this->phoneNumberId;

        $url = "{$this->apiUrl}/{$id}/{$endpoint}";

        try{
            return $this->client->post($url, [
                'headers' => [
                    'Authorization' => "Bearer {$this->token}",
                    'Content-Type' => 'application/json',
                ],
                'json' => $payload,
            ]);
        } catch (GuzzleException $e) {
            DBLog::logError(
                $e->getMessage(),
                "WhatsAppService::post", null,null,
                [
                    'response' => $e->getResponse()->getBody()->getContents(),
                    'url' => $url,
                    'trace' => $e->getTraceAsString(),
                    'payload' => $payload,
                ]
            );
            throw $e;
        }
    }

    /**
     * @throws GuzzleException
     */
    protected function get(?string $endpoint = null, bool $isBusiness = false) : ResponseInterface {
        $endpoint = $endpoint ?? $this->endpoint;
        $id = $isBusiness ? $this->businessId : $this->phoneNumberId;

        $url = "{$this->apiUrl}/{$id}/{$endpoint}";

        return $this->client->get($url, [
            'headers' => [
                'Authorization' => "Bearer {$this->token}",
            ],
        ]);
    }

    /**
     * @throws GuzzleException
     */
    protected function delete(?string $endpoint = null, bool $isBusiness = false) : ResponseInterface {
        $endpoint = $endpoint ?? $this->endpoint;
        $id = $isBusiness ? $this->businessId : $this->phoneNumberId;

        $url = "{$this->apiUrl}/{$id}/{$endpoint}";

        return $this->client->delete($url, [
            'headers' => [
                'Authorization' => "Bearer {$this->token}",
            ],
        ]);
    }
}
