<?php

namespace App\Services\Meta\WhatsApp\Repositories;

use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Services\Meta\WhatsApp\Factories\WhatsAppTemplateFactory;
use App\Services\Meta\WhatsApp\Models\WhatsAppTemplate as WhatsAppTemplateModel;

class WhatsAppTemplateRepository
{
    private WhatsAppTemplateFactory $whatsAppTemplateFactory;

    public function __construct(WhatsAppTemplateFactory $whatsAppTemplateFactory)
    {
        $this->whatsAppTemplateFactory = $whatsAppTemplateFactory;
    }

    public function fetchByTemplateId(int $templateId): ?WhatsAppTemplate
    {
        $model = WhatsAppTemplateModel::where('template_id', $templateId)
            ->with('template')
            ->first();

        return $this->whatsAppTemplateFactory->buildFromModel($model);
    }

    public function fetchById(int $id): ?WhatsAppTemplate
    {
        $model = WhatsAppTemplateModel::with('template')->find($id);

        return $this->whatsAppTemplateFactory->buildFromModel($model);
    }

    public function store(WhatsAppTemplate $whatsAppTemplate): WhatsAppTemplate
    {
        $savedWhatsAppTemplate = WhatsAppTemplateModel::create($whatsAppTemplate->toStoreArray());

        $whatsAppTemplate->id = $savedWhatsAppTemplate->id;

        return $whatsAppTemplate;
    }

    public function update(WhatsAppTemplate $whatsAppTemplate): WhatsAppTemplate
    {
        WhatsAppTemplateModel::where('id', $whatsAppTemplate->id)
            ->update($whatsAppTemplate->toUpdateArray());

        return $whatsAppTemplate;
    }

    public function save(WhatsAppTemplate $whatsAppTemplate): WhatsAppTemplate
    {
        if ($whatsAppTemplate->id) {
            return $this->update($whatsAppTemplate);
        }
        return $this->store($whatsAppTemplate);
    }

    public function delete(WhatsAppTemplate $whatsAppTemplate): bool
    {
        return WhatsAppTemplateModel::find($whatsAppTemplate->id)->delete();
    }
}
