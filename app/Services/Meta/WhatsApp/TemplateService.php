<?php

namespace App\Services\Meta\WhatsApp;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Template;
use App\Helpers\DBLog;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppTemplateRepository;
use App\Services\Meta\WhatsApp\Factories\WhatsAppTemplateFactory;
use GuzzleHttp\Exception\GuzzleException;
use Exception;

class TemplateService extends WhatsAppService
{
    protected WhatsAppTemplateRepository $repository;
    protected WhatsAppTemplateFactory $factory;

    public function __construct(
        WhatsAppTemplateRepository $repository,
        WhatsAppTemplateFactory $factory,
        ?PhoneNumber $phoneNumber = null
    ) {
        parent::__construct($phoneNumber);
        $this->endpoint = "message_templates";
        $this->repository = $repository;
        $this->factory = $factory;
    }

    /**
     * Register a new WhatsApp template: API call + save locally.
     *
     * @param Template $template
     * @return WhatsAppTemplate
     * @throws Exception|GuzzleException
     */
    public function register(Template $template) : WhatsAppTemplate {
        // @todo validate template
        /**  if(!$template->validateForWhatsApp()){
            // @todo improve error message
            throw new Exception("Template is not valid for WhatsApp");
        } **/

        $response = $this->post(
            $template->toWhatsAppPayload(),
            null,
            true
        );
        $responseData = json_decode(
            (string) $response->getBody(), true
        );

        DBLog::log(
            "WhatsApp response returned",
            "WhatsApp::TemplateService::register",
            $template->organization_id ?? null,
            $template->user_id ?? null,
            ['template_id' => $template->id, 'template_name' => $template->name, 'whatsapp_response' => $responseData]
        );

        $status = $responseData['status'] ?? null;
        $external_id = $responseData['id'] ?? null;

        $whatsAppTemplate = $this->factory->buildFromTemplate(
            $template,
            $status,
            $external_id,
            json_encode($responseData)
        );

        return $this->repository->save($whatsAppTemplate);
    }
}
