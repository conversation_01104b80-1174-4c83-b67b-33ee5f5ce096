<?php

namespace App\Services\Meta\WhatsApp\Domains;

use App\Domains\ChatBot\Message;
use Carbon\Carbon;

class WhatsAppMessage
{
    public ?int $id;
    public ?int $message_id;
    public ?Message $message;
    public ?string $whatsapp_message_id;
    public ?string $message_status;
    public ?string $wa_id;
    public ?string $input_phone;
    public ?string $messaging_product;
    public ?string $json;
    public ?array $webhook_entries;
    public ?Carbon $last_status_check;
    public ?int $status_check_count;
    public ?Carbon $delivery_confirmed_at;
    public ?bool $needs_status_check;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id,
        ?int $message_id,
        ?Message $message = null,
        ?string $whatsapp_message_id = null,
        ?string $message_status = null,
        ?string $wa_id = null,
        ?string $input_phone = null,
        ?string $messaging_product = 'whatsapp',
        ?string $json = null,
        ?array $webhook_entries = null,
        ?Carbon $last_status_check = null,
        ?int $status_check_count = 0,
        ?Carbon $delivery_confirmed_at = null,
        ?bool $needs_status_check = false,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null
    ) {
        $this->id = $id;
        $this->message_id = $message_id;
        $this->message = $message;
        $this->whatsapp_message_id = $whatsapp_message_id;
        $this->message_status = $message_status;
        $this->wa_id = $wa_id;
        $this->input_phone = $input_phone;
        $this->messaging_product = $messaging_product;
        $this->json = $json;
        $this->webhook_entries = $webhook_entries;
        $this->last_status_check = $last_status_check;
        $this->status_check_count = $status_check_count;
        $this->delivery_confirmed_at = $delivery_confirmed_at;
        $this->needs_status_check = $needs_status_check;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "message_id" => $this->message_id,
            "message" => $this->message?->toArray(),
            "whatsapp_message_id" => $this->whatsapp_message_id,
            "message_status" => $this->message_status,
            "wa_id" => $this->wa_id,
            "input_phone" => $this->input_phone,
            "messaging_product" => $this->messaging_product,
            "json" => $this->json,
            "webhook_entries" => $this->webhook_entries,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "message_id" => $this->message_id,
            "whatsapp_message_id" => $this->whatsapp_message_id,
            "message_status" => $this->message_status,
            "wa_id" => $this->wa_id,
            "input_phone" => $this->input_phone,
            "messaging_product" => $this->messaging_product,
            "json" => $this->json,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "message_id" => $this->message_id,
            "whatsapp_message_id" => $this->whatsapp_message_id,
            "message_status" => $this->message_status,
            "wa_id" => $this->wa_id,
            "input_phone" => $this->input_phone,
            "messaging_product" => $this->messaging_product,
            "json" => $this->json,
        ];
    }

    /**
     * Check if message needs status check
     */
    public function needsStatusCheck(): bool
    {
        return $this->needs_status_check ||
               ($this->last_status_check === null && $this->created_at && $this->created_at < now()->subMinutes(30)) ||
               ($this->last_status_check && $this->last_status_check < now()->subHours(2) && !$this->isDeliveryConfirmed());
    }

    /**
     * Mark message as needing status check
     */
    public function markForStatusCheck(): void
    {
        $this->needs_status_check = true;
    }

    /**
     * Record status check attempt
     */
    public function recordStatusCheck(bool $success = true): void
    {
        $this->last_status_check = now();
        $this->status_check_count = ($this->status_check_count ?? 0) + 1;
        $this->needs_status_check = !$success;
    }

    /**
     * Mark delivery as confirmed
     */
    public function confirmDelivery(): void
    {
        $this->delivery_confirmed_at = now();
        $this->needs_status_check = false;
    }

    /**
     * Check if delivery is confirmed
     */
    public function isDeliveryConfirmed(): bool
    {
        return $this->delivery_confirmed_at !== null;
    }

    /**
     * Get sync status summary
     */
    public function getSyncStatusSummary(): array
    {
        return [
            'needs_status_check' => $this->needsStatusCheck(),
            'is_delivery_confirmed' => $this->isDeliveryConfirmed(),
            'status_check_count' => $this->status_check_count ?? 0,
            'last_status_check' => $this->last_status_check?->toISOString(),
            'delivery_confirmed_at' => $this->delivery_confirmed_at?->toISOString(),
            'minutes_since_last_check' => $this->last_status_check ? now()->diffInMinutes($this->last_status_check) : null,
        ];
    }

    /**
     * Check if this is a recent message (within last hour)
     */
    public function isRecent(): bool
    {
        if (!$this->created_at) {
            return false;
        }
        return $this->created_at->diffInMinutes(now()) <= 60;
    }

    /**
     * Atualiza o status do WhatsAppMessage baseado no status do WhatsApp
     */
    public function updateWhatsAppMessageStatus(?string $whatsAppStatus): void
    {
        $this->message_status = $whatsAppStatus;
    }
}
