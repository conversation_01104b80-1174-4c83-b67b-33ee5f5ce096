<?php

namespace App\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\Organization;
use App\Helpers\DBLog;
use App\UseCases\Organization\FetchOrganizationFromPhoneNumber;
use Throwable;

class ValidateWebhookSignatureWithOrganization
{
    private FetchOrganizationFromPhoneNumber $fetchOrganizationFromPhoneNumber;

    public function __construct(FetchOrganizationFromPhoneNumber $fetchOrganizationFromPhoneNumber)
    {
        $this->fetchOrganizationFromPhoneNumber = $fetchOrganizationFromPhoneNumber;
    }

    /**
     * Validate webhook signature using organization-specific webhook secret
     *
     * @param string $payload Raw request body
     * @param string|null $signature X-Hub-Signature-256 header value
     * @param string $phoneNumberId Phone number ID from webhook payload
     * @return array{success: bool, organization: ?Organization, error?: string}
     */
    public function perform(string $payload, ?string $signature, string $phoneNumberId, Organization $organization): array
    {
        try {
            $webhookSecret = $organization->whatsapp_webhook_secret;

            if (!$webhookSecret) {
                throw new \Exception("Organization {$organization->id} does not have webhook secret configured");
            }

            if (!$signature) {
                throw new \Exception("Missing signature");
            }

            $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $webhookSecret);

            $isValid = hash_equals($expectedSignature, $signature);
            if (!$isValid) {
                throw new \Exception("Invalid signature");
            }

            DBLog::logInfo(
                "WhatsApp webhook: Valid signature for organization {$organization->id}",
                "WhatsApp::ValidateWebhookSignatureWithOrganization",
                null,
                $organization->id,
                [
                    'organization_id' => $organization->id,
                    'phone_number_id' => $phoneNumberId
                ]
            );

            return [
                'success' => true,
                'organization' => $organization
            ];

        } catch (Throwable $e) {
            DBLog::logError(
                "Failed to validate webhook signature with organization: {$e->getMessage()}",
                "WhatsApp::ValidateWebhookSignatureWithOrganization",
                $organization->id ?? null,
                null,
                [
                    'phone_number_id' => $phoneNumberId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );

            return [
                'success' => false,
                'organization' => null,
                'error' => 'Internal error during validation'
            ];
        }
    }


}
