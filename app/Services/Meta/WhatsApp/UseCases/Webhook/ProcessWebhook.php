<?php

namespace App\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\UseCases\WhatsAppWebhookLog\LogWebhookEvent;
use Illuminate\Contracts\Container\BindingResolutionException;

class ProcessWebhook
{

    /**
     * Process webhook data for a specific organization and phone number
     *
     * @param ChangeValue $changeValue
     * @param Organization $organization
     * @param PhoneNumber $phoneNumber
     * @return array|null
     * @throws BindingResolutionException
     */
    public function perform(
        ChangeValue $changeValue,
        Organization $organization,
        PhoneNumber $phoneNumber
    ): ?array {

        /** @var LogWebhookEvent $logWebhookEvent */
        $logWebhookEvent = app()->make(LogWebhookEvent::class);

        $webhookLog = $logWebhookEvent->perform(
            $organization->id,
            $phoneNumber->whatsapp_phone_number_id,
            $changeValue->getEventType(),
            $changeValue->toArray()
        );

        $webhookLogId = $webhookLog->id ?? null;

        if ($changeValue->hasMessages()) {
            /** @var ProcessWebhookMessage $processWebhookMessage */
            $processWebhookMessage = app()->make(ProcessWebhookMessage::class);
            return $processWebhookMessage->perform($changeValue, $organization, $phoneNumber, $webhookLogId);
        } elseif ($changeValue->hasStatuses()) {
            /** @var ProcessWebhookMessageStatusUpdate $processWebhookStatus */
            $processWebhookStatus = app()->make(ProcessWebhookMessageStatusUpdate::class);
            return $processWebhookStatus->perform($changeValue, $organization, $phoneNumber);
        }

        return null;
    }
}
