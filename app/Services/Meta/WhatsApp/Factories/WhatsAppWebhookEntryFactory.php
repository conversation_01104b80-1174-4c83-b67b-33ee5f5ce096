<?php

namespace App\Services\Meta\WhatsApp\Factories;

use App\Services\Meta\WhatsApp\Domains\WhatsAppWebhookEntry;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Models\WhatsAppWebhookEntry as WhatsAppWebhookEntryModel;

class WhatsAppWebhookEntryFactory
{
    private WhatsAppMessageFactory $whatsAppMessageFactory;

    public function __construct(WhatsAppMessageFactory $whatsAppMessageFactory)
    {
        $this->whatsAppMessageFactory = $whatsAppMessageFactory;
    }

    public function buildFromModel($whatsappWebhookEntry): ?WhatsAppWebhookEntry
    {
        if (!$whatsappWebhookEntry) {
            return null;
        }

        $whatsappMessage = null;
        if ($whatsappWebhookEntry->whatsappMessage) {
            $whatsappMessage = $this->whatsAppMessageFactory->buildFromModel($whatsappWebhookEntry->whatsappMessage);
        }

        return new WhatsAppWebhookEntry(
            $whatsappWebhookEntry->id ?? null,
            $whatsappWebhookEntry->whatsapp_message_id ?? null,
            $whatsappMessage,
            $whatsappWebhookEntry->external_wam_id ?? null,
            $whatsappWebhookEntry->status ?? null,
            $whatsappWebhookEntry->timestamp ?? null,
            $whatsappWebhookEntry->recipient_id ?? null,
            $whatsappWebhookEntry->conversation_id ?? null,
            $whatsappWebhookEntry->conversation_origin_type ?? null,
            $whatsappWebhookEntry->json ?? null,
            $whatsappWebhookEntry->created_at ?? null,
            $whatsappWebhookEntry->updated_at ?? null
        );
    }

    /**
     * Build WhatsAppWebhookEntry domain from webhook status data
     *
     * @param int $whatsappMessageId
     * @param array $statusData
     * @param array $fullWebhookEntry
     * @return WhatsAppWebhookEntry
     */
    public function buildFromWebhookStatus(int $whatsappMessageId, array $statusData, array $fullWebhookEntry): WhatsAppWebhookEntry
    {
        // Extract data from webhook status
        $externalWamId = $statusData['id'] ?? null;
        $status = $statusData['status'] ?? null;
        $timestamp = $statusData['timestamp'] ?? null;
        $recipientId = $statusData['recipient_id'] ?? null;
        $conversationId = $statusData['conversation']['id'] ?? null;
        $conversationOriginType = $statusData['conversation']['origin']['type'] ?? null;

        return new WhatsAppWebhookEntry(
            null, // id - will be set after saving
            $whatsappMessageId,
            null, // whatsapp_message - will be loaded if needed
            $externalWamId,
            $status,
            $timestamp,
            $recipientId,
            $conversationId,
            $conversationOriginType,
            json_encode($fullWebhookEntry),
            null, // created_at - will be set by database
            null  // updated_at - will be set by database
        );
    }
}
