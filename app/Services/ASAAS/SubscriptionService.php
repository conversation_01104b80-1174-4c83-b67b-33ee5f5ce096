<?php

namespace App\Services\ASAAS;

use App\Domains\Organization;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Subscription Service
 *
 * Responsible for ASAAS subscription endpoints.
 * Can be instantiated with both Main and Org credentials.
 */
class SubscriptionService extends AsaasService
{
    protected const ENDPOINT_SUBSCRIPTIONS = 'subscriptions';

    public function __construct(?Organization $organization = null)
    {
        parent::__construct($organization); // Main or org credentials based on parameter
        $this->endpoint = self::ENDPOINT_SUBSCRIPTIONS;
    }

    /**
     * Create a subscription
     * @throws GuzzleException
     */
    public function create(array $data): array
    {
        $response = $this->post($data);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get subscriptions list with filters
     * @throws GuzzleException
     */
    public function getAll(array $filters = []): array
    {
        $response = $this->get($filters);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get specific subscription by ID
     * @throws GuzzleException
     */
    public function getById(string $subscriptionId): array
    {
        $response = $this->get([], self::ENDPOINT_SUBSCRIPTIONS . "/{$subscriptionId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Update subscription
     * @throws GuzzleException
     */
    public function update(string $subscriptionId, array $data): array
    {
        $response = $this->put($data, self::ENDPOINT_SUBSCRIPTIONS . "/{$subscriptionId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Delete subscription
     * @throws GuzzleException
     */
    public function deleteSubscription(string $subscriptionId): array
    {
        $response = $this->delete(self::ENDPOINT_SUBSCRIPTIONS . "/{$subscriptionId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get subscription payments
     * @throws GuzzleException
     */
    public function getPayments(string $subscriptionId, array $filters = []): array
    {
        $response = $this->get($filters, self::ENDPOINT_SUBSCRIPTIONS . "/{$subscriptionId}/payments");
        return json_decode($response->getBody()->getContents(), true);
    }
}
