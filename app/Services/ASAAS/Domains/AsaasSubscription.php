<?php

namespace App\Services\ASAAS\Domains;

use App\Domains\Subscription;
use Carbon\Carbon;

class AsaasSubscription
{
    public function __construct(
        public ?int $id,
        public int $subscription_id,
        public string $asaas_customer_id,
        public string $asaas_subscription_id,
        public Carbon $asaas_date_created,
        public ?Carbon $asaas_synced_at,
        public ?array $asaas_sync_errors,
        public string $sync_status,
        public string $billing_type,
        public string $cycle,
        public float $value,
        public ?Carbon $next_due_date,
        public ?Carbon $end_date,
        public ?string $description,
        public string $status,
        public ?int $max_payments,
        public ?string $external_reference,
        public ?string $payment_link,
        public ?string $checkout_session,
        public ?float $discount_value,
        public ?string $discount_type,
        public ?int $discount_due_date_limit_days,
        public ?float $fine_value,
        public ?string $fine_type,
        public ?float $interest_value,
        public ?string $credit_card_number,
        public ?string $credit_card_brand,
        public ?string $credit_card_token,
        public ?array $split_data,
        public bool $deleted,
        public ?Subscription $subscription = null,
        public ?AsaasOrganizationCustomer $asaasCustomer = null,
        public ?Carbon $created_at = null,
        public ?Carbon $updated_at = null,
        public ?Carbon $deleted_at = null,
    ) {}

    public function isActive(): bool
    {
        return $this->status === 'ACTIVE';
    }

    public function isExpired(): bool
    {
        return $this->status === 'EXPIRED';
    }

    public function isCancelled(): bool
    {
        return $this->status === 'CANCELLED';
    }

    public function isInactive(): bool
    {
        return $this->status === 'INACTIVE';
    }

    public function needsSync(): bool
    {
        return $this->asaas_synced_at === null ||
               $this->asaas_synced_at->isBefore(now()->subHour()) ||
               $this->sync_status === 'pending' ||
               $this->sync_status === 'error';
    }

    public function hasErrors(): bool
    {
        return !empty($this->asaas_sync_errors);
    }

    public function isPending(): bool
    {
        return $this->sync_status === 'pending';
    }

    public function isSynced(): bool
    {
        return $this->sync_status === 'synced';
    }

    public function hasError(): bool
    {
        return $this->sync_status === 'error';
    }

    public function hasDiscount(): bool
    {
        return $this->discount_value !== null && $this->discount_value > 0;
    }

    public function hasFine(): bool
    {
        return $this->fine_value !== null && $this->fine_value > 0;
    }

    public function hasInterest(): bool
    {
        return $this->interest_value !== null && $this->interest_value > 0;
    }

    public function hasCreditCard(): bool
    {
        return !empty($this->credit_card_token) || !empty($this->credit_card_number);
    }

    public function hasSplit(): bool
    {
        return !empty($this->split_data);
    }

    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    public function isBoleto(): bool
    {
        return $this->billing_type === 'BOLETO';
    }

    public function isCreditCard(): bool
    {
        return $this->billing_type === 'CREDIT_CARD';
    }

    public function isPix(): bool
    {
        return $this->billing_type === 'PIX';
    }

    public function isMonthly(): bool
    {
        return $this->cycle === 'MONTHLY';
    }

    public function isYearly(): bool
    {
        return $this->cycle === 'YEARLY';
    }

    /**
     * Convert to ASAAS API payload format for creation
     */
    public function toAsaasPayload(): array
    {
        $payload = [
            'customer' => $this->asaas_customer_id,
            'billingType' => $this->billing_type,
            'value' => $this->value,
            'cycle' => $this->cycle,
        ];

        if ($this->next_due_date) {
            $payload['nextDueDate'] = $this->next_due_date->format('Y-m-d');
        }

        if ($this->end_date) {
            $payload['endDate'] = $this->end_date->format('Y-m-d');
        }

        if ($this->description) {
            $payload['description'] = $this->description;
        }

        if ($this->max_payments) {
            $payload['maxPayments'] = $this->max_payments;
        }

        if ($this->external_reference) {
            $payload['externalReference'] = $this->external_reference;
        }

        // Discount
        if ($this->hasDiscount()) {
            $payload['discount'] = [
                'value' => $this->discount_value,
                'type' => $this->discount_type,
                'dueDateLimitDays' => $this->discount_due_date_limit_days ?? 0,
            ];
        }

        // Fine
        if ($this->hasFine()) {
            $payload['fine'] = [
                'value' => $this->fine_value,
                'type' => $this->fine_type ?? 'FIXED',
            ];
        }

        // Interest
        if ($this->hasInterest()) {
            $payload['interest'] = [
                'value' => $this->interest_value,
            ];
        }

        // Split
        if ($this->hasSplit()) {
            $payload['split'] = $this->split_data;
        }

        // Credit Card
        if ($this->hasCreditCard() && $this->credit_card_token) {
            $payload['creditCardToken'] = $this->credit_card_token;
        }

        return $payload;
    }

    /**
     * Convert to ASAAS API payload format for updates
     */
    public function toAsaasUpdatePayload(): array
    {
        $payload = [
            'value' => $this->value,
            'cycle' => $this->cycle,
        ];

        if ($this->next_due_date) {
            $payload['nextDueDate'] = $this->next_due_date->format('Y-m-d');
        }

        if ($this->end_date) {
            $payload['endDate'] = $this->end_date->format('Y-m-d');
        }

        if ($this->description) {
            $payload['description'] = $this->description;
        }

        if ($this->max_payments) {
            $payload['maxPayments'] = $this->max_payments;
        }

        // Discount
        if ($this->hasDiscount()) {
            $payload['discount'] = [
                'value' => $this->discount_value,
                'type' => $this->discount_type,
                'dueDateLimitDays' => $this->discount_due_date_limit_days ?? 0,
            ];
        }

        // Fine
        if ($this->hasFine()) {
            $payload['fine'] = [
                'value' => $this->fine_value,
                'type' => $this->fine_type ?? 'FIXED',
            ];
        }

        // Interest
        if ($this->hasInterest()) {
            $payload['interest'] = [
                'value' => $this->interest_value,
            ];
        }

        return $payload;
    }

    /**
     * Convert to array format
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'subscription_id' => $this->subscription_id,
            'asaas_customer_id' => $this->asaas_customer_id,
            'asaas_subscription_id' => $this->asaas_subscription_id,
            'asaas_date_created' => $this->asaas_date_created->format('Y-m-d'),
            'asaas_synced_at' => $this->asaas_synced_at?->format('Y-m-d H:i:s'),
            'asaas_sync_errors' => $this->asaas_sync_errors,
            'sync_status' => $this->sync_status,
            'billing_type' => $this->billing_type,
            'cycle' => $this->cycle,
            'value' => $this->value,
            'next_due_date' => $this->next_due_date?->format('Y-m-d'),
            'end_date' => $this->end_date?->format('Y-m-d'),
            'description' => $this->description,
            'status' => $this->status,
            'max_payments' => $this->max_payments,
            'external_reference' => $this->external_reference,
            'payment_link' => $this->payment_link,
            'checkout_session' => $this->checkout_session,
            'discount_value' => $this->discount_value,
            'discount_type' => $this->discount_type,
            'discount_due_date_limit_days' => $this->discount_due_date_limit_days,
            'fine_value' => $this->fine_value,
            'fine_type' => $this->fine_type,
            'interest_value' => $this->interest_value,
            'credit_card_number' => $this->credit_card_number,
            'credit_card_brand' => $this->credit_card_brand,
            'credit_card_token' => $this->credit_card_token,
            'split_data' => $this->split_data,
            'deleted' => $this->deleted,
            'subscription' => $this->subscription?->toArray(),
            'asaas_customer' => $this->asaasCustomer?->toArray(),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'deleted_at' => $this->deleted_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Convert to store array format (for database insertion)
     */
    public function toStoreArray(): array
    {
        return [
            'subscription_id' => $this->subscription_id,
            'asaas_customer_id' => $this->asaas_customer_id,
            'asaas_subscription_id' => $this->asaas_subscription_id,
            'asaas_date_created' => $this->asaas_date_created,
            'asaas_synced_at' => $this->asaas_synced_at,
            'asaas_sync_errors' => $this->asaas_sync_errors,
            'sync_status' => $this->sync_status,
            'billing_type' => $this->billing_type,
            'cycle' => $this->cycle,
            'value' => $this->value,
            'next_due_date' => $this->next_due_date,
            'end_date' => $this->end_date,
            'description' => $this->description,
            'status' => $this->status,
            'max_payments' => $this->max_payments,
            'external_reference' => $this->external_reference,
            'payment_link' => $this->payment_link,
            'checkout_session' => $this->checkout_session,
            'discount_value' => $this->discount_value,
            'discount_type' => $this->discount_type,
            'discount_due_date_limit_days' => $this->discount_due_date_limit_days,
            'fine_value' => $this->fine_value,
            'fine_type' => $this->fine_type,
            'interest_value' => $this->interest_value,
            'credit_card_number' => $this->credit_card_number,
            'credit_card_brand' => $this->credit_card_brand,
            'credit_card_token' => $this->credit_card_token,
            'split_data' => $this->split_data,
            'deleted' => $this->deleted,
        ];
    }

    /**
     * Convert to update array format (for database updates)
     */
    public function toUpdateArray(): array
    {
        return [
            'asaas_synced_at' => $this->asaas_synced_at,
            'asaas_sync_errors' => $this->asaas_sync_errors,
            'sync_status' => $this->sync_status,
            'billing_type' => $this->billing_type,
            'cycle' => $this->cycle,
            'value' => $this->value,
            'next_due_date' => $this->next_due_date,
            'end_date' => $this->end_date,
            'description' => $this->description,
            'status' => $this->status,
            'max_payments' => $this->max_payments,
            'external_reference' => $this->external_reference,
            'payment_link' => $this->payment_link,
            'checkout_session' => $this->checkout_session,
            'discount_value' => $this->discount_value,
            'discount_type' => $this->discount_type,
            'discount_due_date_limit_days' => $this->discount_due_date_limit_days,
            'fine_value' => $this->fine_value,
            'fine_type' => $this->fine_type,
            'interest_value' => $this->interest_value,
            'credit_card_number' => $this->credit_card_number,
            'credit_card_brand' => $this->credit_card_brand,
            'credit_card_token' => $this->credit_card_token,
            'split_data' => $this->split_data,
            'deleted' => $this->deleted,
        ];
    }

    /**
     * Get status display name
     */
    public function getStatusDisplayName(): string
    {
        return match ($this->status) {
            'ACTIVE' => 'Ativo',
            'INACTIVE' => 'Inativo',
            'EXPIRED' => 'Expirado',
            'CANCELLED' => 'Cancelado',
            default => 'Desconhecido',
        };
    }

    /**
     * Get sync status display name
     */
    public function getSyncStatusDisplayName(): string
    {
        return match ($this->sync_status) {
            'pending' => 'Pendente',
            'synced' => 'Sincronizado',
            'error' => 'Erro',
            default => 'Desconhecido',
        };
    }

    /**
     * Get billing type display name
     */
    public function getBillingTypeDisplayName(): string
    {
        return match ($this->billing_type) {
            'BOLETO' => 'Boleto',
            'CREDIT_CARD' => 'Cartão de Crédito',
            'PIX' => 'PIX',
            'UNDEFINED' => 'Indefinido',
            default => 'Desconhecido',
        };
    }

    /**
     * Get cycle display name
     */
    public function getCycleDisplayName(): string
    {
        return match ($this->cycle) {
            'MONTHLY' => 'Mensal',
            'QUARTERLY' => 'Trimestral',
            'SEMIANNUAL' => 'Semestral',
            'YEARLY' => 'Anual',
            default => 'Desconhecido',
        };
    }
}
