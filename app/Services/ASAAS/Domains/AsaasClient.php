<?php

namespace App\Services\ASAAS\Domains;

use App\Domains\Inventory\Client;
use App\Helpers\Traits\PeopleTyped;
use Carbon\Carbon;

class AsaasClient
{
    use PeopleTyped;

    public function __construct(
        public ?int $id,
        public int $client_id,
        public int $organization_id,
        public string $asaas_customer_id,
        public ?Carbon $asaas_synced_at,
        public ?array $asaas_sync_errors,
        public string $sync_status,
        public ?string $name,
        public ?string $email,
        public ?string $phone,
        public ?string $mobile_phone,
        public ?string $address,
        public ?string $address_number,
        public ?string $complement,
        public ?string $province,
        public ?string $city_name,
        public ?string $state,
        public ?string $country,
        public ?string $postal_code,
        public ?string $cpf_cnpj,
        public ?string $person_type,
        public ?string $external_reference,
        public bool $notification_disabled,
        public ?string $additional_emails,
        public ?string $observations,
        public bool $foreign_customer,
        public bool $deleted,
        public ?Client $client = null,
        public ?Carbon $created_at = null,
        public ?Carbon $updated_at = null,
        public ?Carbon $deleted_at = null,
    ) {
        $this->document = $this->cpf_cnpj;
        $this->setDocumentOnlyNumbers();
    }

    public function hasAsaasIntegration(): bool
    {
        return !empty($this->asaas_customer_id);
    }

    public function needsSync(): bool
    {
        return $this->asaas_synced_at === null ||
               $this->asaas_synced_at->isBefore(now()->subHour());
    }

    public function hasErrors(): bool
    {
        return !empty($this->asaas_sync_errors);
    }

    public function isPending(): bool
    {
        return $this->sync_status === 'pending';
    }

    public function isSynced(): bool
    {
        return $this->sync_status === 'synced';
    }

    public function hasError(): bool
    {
        return $this->sync_status === 'error';
    }

    public function isPersonTypeFisica(): bool
    {
        return $this->person_type === 'FISICA';
    }

    public function isPersonTypeJuridica(): bool
    {
        return $this->person_type === 'JURIDICA';
    }

    public function getPersonTypeFromDocument(): ?string
    {
        if (empty($this->cpf_cnpj)) {
            return null;
        }

        $cleanDocument = preg_replace('/\D/', '', $this->cpf_cnpj);
        return strlen($cleanDocument) === 11 ? 'FISICA' : 'JURIDICA';
    }

    public function isActive(): bool
    {
        return !$this->deleted;
    }

    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    public function hasValidDocument(): bool
    {
        if (empty($this->cpf_cnpj)) {
            return false;
        }

        $cleanDocument = preg_replace('/\D/', '', $this->cpf_cnpj);

        if (strlen($cleanDocument) === 11) {
            return $this->isValidCpf();
        }
        if (strlen($cleanDocument) === 14) {
            return $this->isValidCnpj();
        }

        return false;
    }

    public function hasValidEmail(): bool
    {
        return !empty($this->email) && filter_var($this->email, FILTER_VALIDATE_EMAIL);
    }

    public function hasValidPhone(): bool
    {
        if (empty($this->phone) && empty($this->mobile_phone)) {
            return false;
        }

        $phone = $this->phone ?? $this->mobile_phone;
        $cleanPhone = preg_replace('/\D/', '', $phone);

        return strlen($cleanPhone) >= 10 && strlen($cleanPhone) <= 11;
    }

    public function toAsaasPayload(): array
    {
        $payload = [
            'name' => $this->name,
            'email' => $this->email,
            'cpfCnpj' => $this->cpf_cnpj,
            'notificationDisabled' => $this->notification_disabled,
            'foreignCustomer' => $this->foreign_customer,
        ];

        if ($this->phone) {
            $payload['phone'] = preg_replace('/\D/', '', $this->phone);
        }

        if ($this->mobile_phone) {
            $payload['mobilePhone'] = preg_replace('/\D/', '', $this->mobile_phone);
        }

        if ($this->address) {
            $payload['address'] = $this->address;
        }

        if ($this->address_number) {
            $payload['addressNumber'] = $this->address_number;
        }

        if ($this->complement) {
            $payload['complement'] = $this->complement;
        }

        if ($this->province) {
            $payload['province'] = $this->province;
        }

        if ($this->postal_code) {
            $payload['postalCode'] = preg_replace('/\D/', '', $this->postal_code);
        }

        if ($this->external_reference) {
            $payload['externalReference'] = $this->external_reference;
        }

        if ($this->additional_emails) {
            $payload['additionalEmails'] = $this->additional_emails;
        }

        if ($this->observations) {
            $payload['observations'] = $this->observations;
        }

        return array_filter($payload, fn($value) => $value !== null && $value !== '');
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'client_id' => $this->client_id,
            'organization_id' => $this->organization_id,
            'asaas_customer_id' => $this->asaas_customer_id,
            'asaas_synced_at' => $this->asaas_synced_at?->format('Y-m-d H:i:s'),
            'asaas_sync_errors' => $this->asaas_sync_errors,
            'sync_status' => $this->sync_status,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'mobile_phone' => $this->mobile_phone,
            'address' => $this->address,
            'address_number' => $this->address_number,
            'complement' => $this->complement,
            'province' => $this->province,
            'city_name' => $this->city_name,
            'state' => $this->state,
            'country' => $this->country,
            'postal_code' => $this->postal_code,
            'cpf_cnpj' => $this->cpf_cnpj,
            'person_type' => $this->person_type,
            'external_reference' => $this->external_reference,
            'notification_disabled' => $this->notification_disabled,
            'additional_emails' => $this->additional_emails,
            'observations' => $this->observations,
            'foreign_customer' => $this->foreign_customer,
            'deleted' => $this->deleted,
            'client' => $this->client?->toArray(),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'deleted_at' => $this->deleted_at?->format('Y-m-d H:i:s'),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            'client_id' => $this->client_id,
            'organization_id' => $this->organization_id,
            'asaas_customer_id' => $this->asaas_customer_id,
            'asaas_synced_at' => $this->asaas_synced_at,
            'asaas_sync_errors' => $this->asaas_sync_errors,
            'sync_status' => $this->sync_status,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'mobile_phone' => $this->mobile_phone,
            'address' => $this->address,
            'address_number' => $this->address_number,
            'complement' => $this->complement,
            'province' => $this->province,
            'city_name' => $this->city_name,
            'state' => $this->state,
            'country' => $this->country,
            'postal_code' => $this->postal_code,
            'cpf_cnpj' => $this->cpf_cnpj,
            'person_type' => $this->person_type,
            'external_reference' => $this->external_reference,
            'notification_disabled' => $this->notification_disabled,
            'additional_emails' => $this->additional_emails,
            'observations' => $this->observations,
            'foreign_customer' => $this->foreign_customer,
            'deleted' => $this->deleted,
        ];
    }

    public function toUpdateArray(): array
    {
        return $this->toStoreArray();
    }
}
