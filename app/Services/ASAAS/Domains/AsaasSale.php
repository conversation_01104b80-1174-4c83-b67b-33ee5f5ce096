<?php

namespace App\Services\ASAAS\Domains;

use App\Domains\Inventory\Sale;
use App\Domains\Inventory\Client;
use Carbon\Carbon;

class AsaasSale
{
    public function __construct(
        public ?int $id,
        public int $sale_id,
        public int $organization_id,
        public int $client_id,
        public string $asaas_payment_id,
        public string $asaas_customer_id,
        public float $value,
        public ?float $net_value,
        public ?float $original_value,
        public ?float $interest_value,
        public ?float $discount_value,
        public ?string $description,
        public string $billing_type,
        public Carbon $due_date,
        public ?Carbon $payment_date,
        public ?Carbon $original_due_date,
        public ?Carbon $client_payment_date,
        public string $status,
        public ?string $invoice_url,
        public ?string $invoice_number,
        public ?string $bank_slip_url,
        public ?string $pix_qr_code_id,
        public ?string $external_reference,
        public ?string $installment_id,
        public ?int $installment_count,
        public ?float $installment_value,
        public ?int $installment_number,
        public ?Carbon $credit_date,
        public ?Carbon $estimated_credit_date,
        public bool $anticipated,
        public bool $anticipable,
        public bool $can_be_paid_after_due_date,
        public bool $deleted,
        public ?string $nosso_numero,
        public ?Carbon $asaas_synced_at,
        public ?array $asaas_sync_errors,
        public string $sync_status,
        public ?array $asaas_webhook_data,
        public ?array $discount_config,
        public ?array $fine_config,
        public ?array $interest_config,
        public ?array $split_config,
        public ?array $credit_card_data,
        public ?array $chargeback_data,
        public ?array $escrow_data,
        public ?array $refunds_data,
        public ?Sale $sale = null,
        public ?Client $client = null,
        public ?Carbon $created_at = null,
        public ?Carbon $updated_at = null,
        public ?Carbon $deleted_at = null,
    ) {}

    public function hasAsaasIntegration(): bool
    {
        return !empty($this->asaas_payment_id);
    }

    public function needsSync(): bool
    {
        return $this->asaas_synced_at === null ||
               $this->asaas_synced_at->isBefore(now()->subHour());
    }

    public function hasErrors(): bool
    {
        return !empty($this->asaas_sync_errors);
    }

    public function isPending(): bool
    {
        return $this->sync_status === 'pending';
    }

    public function isSynced(): bool
    {
        return $this->sync_status === 'synced';
    }

    public function hasError(): bool
    {
        return $this->sync_status === 'error';
    }

    public function isStatusPending(): bool
    {
        return $this->status === 'PENDING';
    }

    public function isStatusConfirmed(): bool
    {
        return $this->status === 'CONFIRMED';
    }

    public function isStatusReceived(): bool
    {
        return $this->status === 'RECEIVED';
    }

    public function isStatusOverdue(): bool
    {
        return $this->status === 'OVERDUE';
    }

    public function isStatusRefunded(): bool
    {
        return $this->status === 'REFUNDED';
    }

    public function isPaid(): bool
    {
        return in_array($this->status, ['RECEIVED', 'RECEIVED_IN_CASH']);
    }

    public function canBeRefunded(): bool
    {
        return $this->isPaid() && !$this->isStatusRefunded();
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['PENDING', 'PENDING_CONFIRMATION']);
    }

    public function isBoleto(): bool
    {
        return $this->billing_type === 'BOLETO';
    }

    public function isCreditCard(): bool
    {
        return $this->billing_type === 'CREDIT_CARD';
    }

    public function isPix(): bool
    {
        return $this->billing_type === 'PIX';
    }

    public function isOverdue(): bool
    {
        return $this->due_date && $this->due_date->isPast() && !$this->isPaid();
    }

    public function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }
        
        return $this->due_date->diffInDays(now());
    }

    public function isActive(): bool
    {
        return !$this->deleted;
    }

    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    public function hasValidValue(): bool
    {
        return $this->value > 0;
    }

    public function hasValidDueDate(): bool
    {
        return $this->due_date && $this->due_date->isFuture();
    }

    public function toAsaasPayload(): array
    {
        $payload = [
            'customer' => $this->asaas_customer_id,
            'billingType' => $this->billing_type,
            'value' => $this->value,
            'dueDate' => $this->due_date->format('Y-m-d'),
        ];

        if ($this->description) {
            $payload['description'] = $this->description;
        }

        if ($this->external_reference) {
            $payload['externalReference'] = $this->external_reference;
        }

        if ($this->installment_count) {
            $payload['installmentCount'] = $this->installment_count;
        }

        if ($this->installment_value) {
            $payload['installmentValue'] = $this->installment_value;
        }

        if ($this->discount_config) {
            $payload['discount'] = $this->discount_config;
        }

        if ($this->fine_config) {
            $payload['fine'] = $this->fine_config;
        }

        if ($this->interest_config) {
            $payload['interest'] = $this->interest_config;
        }

        if ($this->split_config) {
            $payload['split'] = $this->split_config;
        }

        return $payload;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'sale_id' => $this->sale_id,
            'organization_id' => $this->organization_id,
            'client_id' => $this->client_id,
            'asaas_payment_id' => $this->asaas_payment_id,
            'asaas_customer_id' => $this->asaas_customer_id,
            'value' => $this->value,
            'net_value' => $this->net_value,
            'original_value' => $this->original_value,
            'interest_value' => $this->interest_value,
            'discount_value' => $this->discount_value,
            'description' => $this->description,
            'billing_type' => $this->billing_type,
            'due_date' => $this->due_date?->format('Y-m-d'),
            'payment_date' => $this->payment_date?->format('Y-m-d'),
            'original_due_date' => $this->original_due_date?->format('Y-m-d'),
            'client_payment_date' => $this->client_payment_date?->format('Y-m-d'),
            'status' => $this->status,
            'invoice_url' => $this->invoice_url,
            'invoice_number' => $this->invoice_number,
            'bank_slip_url' => $this->bank_slip_url,
            'pix_qr_code_id' => $this->pix_qr_code_id,
            'external_reference' => $this->external_reference,
            'installment_id' => $this->installment_id,
            'installment_count' => $this->installment_count,
            'installment_value' => $this->installment_value,
            'installment_number' => $this->installment_number,
            'credit_date' => $this->credit_date?->format('Y-m-d'),
            'estimated_credit_date' => $this->estimated_credit_date?->format('Y-m-d'),
            'anticipated' => $this->anticipated,
            'anticipable' => $this->anticipable,
            'can_be_paid_after_due_date' => $this->can_be_paid_after_due_date,
            'deleted' => $this->deleted,
            'nosso_numero' => $this->nosso_numero,
            'asaas_synced_at' => $this->asaas_synced_at?->format('Y-m-d H:i:s'),
            'asaas_sync_errors' => $this->asaas_sync_errors,
            'sync_status' => $this->sync_status,
            'asaas_webhook_data' => $this->asaas_webhook_data,
            'discount_config' => $this->discount_config,
            'fine_config' => $this->fine_config,
            'interest_config' => $this->interest_config,
            'split_config' => $this->split_config,
            'credit_card_data' => $this->credit_card_data,
            'chargeback_data' => $this->chargeback_data,
            'escrow_data' => $this->escrow_data,
            'refunds_data' => $this->refunds_data,
            'sale' => $this->sale?->toArray(),
            'client' => $this->client?->toArray(),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'deleted_at' => $this->deleted_at?->format('Y-m-d H:i:s'),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            'sale_id' => $this->sale_id,
            'organization_id' => $this->organization_id,
            'client_id' => $this->client_id,
            'asaas_payment_id' => $this->asaas_payment_id,
            'asaas_customer_id' => $this->asaas_customer_id,
            'value' => $this->value,
            'net_value' => $this->net_value,
            'original_value' => $this->original_value,
            'interest_value' => $this->interest_value,
            'discount_value' => $this->discount_value,
            'description' => $this->description,
            'billing_type' => $this->billing_type,
            'due_date' => $this->due_date,
            'payment_date' => $this->payment_date,
            'original_due_date' => $this->original_due_date,
            'client_payment_date' => $this->client_payment_date,
            'status' => $this->status,
            'invoice_url' => $this->invoice_url,
            'invoice_number' => $this->invoice_number,
            'bank_slip_url' => $this->bank_slip_url,
            'pix_qr_code_id' => $this->pix_qr_code_id,
            'external_reference' => $this->external_reference,
            'installment_id' => $this->installment_id,
            'installment_count' => $this->installment_count,
            'installment_value' => $this->installment_value,
            'installment_number' => $this->installment_number,
            'credit_date' => $this->credit_date,
            'estimated_credit_date' => $this->estimated_credit_date,
            'anticipated' => $this->anticipated,
            'anticipable' => $this->anticipable,
            'can_be_paid_after_due_date' => $this->can_be_paid_after_due_date,
            'deleted' => $this->deleted,
            'nosso_numero' => $this->nosso_numero,
            'asaas_synced_at' => $this->asaas_synced_at,
            'asaas_sync_errors' => $this->asaas_sync_errors,
            'sync_status' => $this->sync_status,
            'asaas_webhook_data' => $this->asaas_webhook_data,
            'discount_config' => $this->discount_config,
            'fine_config' => $this->fine_config,
            'interest_config' => $this->interest_config,
            'split_config' => $this->split_config,
            'credit_card_data' => $this->credit_card_data,
            'chargeback_data' => $this->chargeback_data,
            'escrow_data' => $this->escrow_data,
            'refunds_data' => $this->refunds_data,
        ];
    }

    public function toUpdateArray(): array
    {
        return $this->toStoreArray();
    }
}
