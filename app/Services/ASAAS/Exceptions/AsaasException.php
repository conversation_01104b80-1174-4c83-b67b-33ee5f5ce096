<?php

namespace App\Services\ASAAS\Exceptions;

use Exception;
use Psr\Http\Message\ResponseInterface;

class AsaasException extends Exception
{
    protected ?ResponseInterface $response;
    protected ?array $responseData;
    protected ?string $asaasErrorCode;

    public function __construct(
        string $message = "",
        int $code = 0,
        ?Exception $previous = null,
        ?ResponseInterface $response = null,
        ?array $responseData = null,
        ?string $asaasErrorCode = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->response = $response;
        $this->responseData = $responseData;
        $this->asaasErrorCode = $asaasErrorCode;
    }

    public function getResponse(): ?ResponseInterface
    {
        return $this->response;
    }

    public function getResponseData(): ?array
    {
        return $this->responseData;
    }

    public function getAsaasErrorCode(): ?string
    {
        return $this->asaasErrorCode;
    }

    public function getHttpStatusCode(): ?int
    {
        return $this->response?->getStatusCode();
    }

    public function isClientError(): bool
    {
        $statusCode = $this->getHttpStatusCode();
        return $statusCode >= 400 && $statusCode < 500;
    }

    public function isServerError(): bool
    {
        $statusCode = $this->getHttpStatusCode();
        return $statusCode >= 500;
    }

    public function isAuthenticationError(): bool
    {
        return $this->getHttpStatusCode() === 401;
    }

    public function isRateLimitError(): bool
    {
        return $this->getHttpStatusCode() === 429;
    }

    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'http_status_code' => $this->getHttpStatusCode(),
            'asaas_error_code' => $this->getAsaasErrorCode(),
            'response_data' => $this->getResponseData(),
            'is_client_error' => $this->isClientError(),
            'is_server_error' => $this->isServerError(),
            'is_authentication_error' => $this->isAuthenticationError(),
            'is_rate_limit_error' => $this->isRateLimitError(),
        ];
    }
}
