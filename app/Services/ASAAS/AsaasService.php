<?php

namespace App\Services\ASAAS;

use App\Domains\Organization;
use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\UseCases\AsaasLog\LogAsaasRequest;
use App\Services\ASAAS\UseCases\AsaasLog\LogAsaasError;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;
use App\Helpers\DBLog;

/**
 * Base ASAAS Service Class
 *
 * This is the base service that all ASAAS specialized services extend.
 * Handles credential management and HTTP operations.
 *
 * Main Credentials: Our main account credentials from .env
 * Org Credentials: Organization-specific credentials from AsaasOrganization domain
 * Environment: Set ONLY by .env values
 */
class AsaasService
{
    protected Client $client;
    protected string $baseUrl;
    protected ?string $token;
    protected AsaasEnvironment $environment;
    protected ?Organization $organization;
    protected ?int $organization_id;
    protected string $endpoint;
    protected LogAsaasRequest $logAsaasRequest;
    protected LogAsaasError $logAsaasError;

    private const string ENVIRONMENT_CONFIG_KEY = 'asaas.environment';
    private const string ENVIRONMENT_CONFIG_KEY_DEFAULT = 'sandbox';

    public function __construct(?Organization $organization = null )
    {
        $this->client = new Client();
        $this->organization = $organization;
        $this->organization_id = $organization?->id;
        $this->environment = AsaasEnvironment::from(
            config(self::ENVIRONMENT_CONFIG_KEY, self::ENVIRONMENT_CONFIG_KEY_DEFAULT)
        );
        $this->baseUrl = $this->environment->getBaseUrl();
        $this->token = $this->organization?->asaas_api_key ?? config($this->environment->getTokenConfigKey());
        $this->logAsaasRequest = app(LogAsaasRequest::class);
        $this->logAsaasError = app(LogAsaasError::class);
    }

    /**
     * @throws GuzzleException
     */
    protected function get(array $query = [], ?string $endpoint = null): ResponseInterface
    {
        $endpoint = $endpoint ?? $this->endpoint;
        $url = "{$this->baseUrl}/v3/{$endpoint}";
        $startTime = microtime(true);

        try {
            $response = $this->client->get($url, [
                'headers' => [
                    'access_token' => $this->token,
                    'Content-Type' => 'application/json',
                ],
                'query' => $query,
            ]);

            $executionTime = microtime(true) - $startTime;
            $responseData = json_decode($response->getBody()->getContents(), true);

            $this->logSuccess('GET', "/v3/{$endpoint}", $query, $responseData, $response->getStatusCode(), $executionTime);

            // Reset body stream for consumption by caller
            $response->getBody()->rewind();
            return $response;

        } catch (GuzzleException $e) {
            $executionTime = microtime(true) - $startTime;
            $this->logError($e, 'GET', "/v3/{$endpoint}", $query, $executionTime);

            throw $e;
        }
    }

    /**
     * @throws GuzzleException
     */
    protected function post(array $payload, ?string $endpoint = null): ResponseInterface
    {
        $endpoint = $endpoint ?? $this->endpoint;
        $url = "{$this->baseUrl}/v3/{$endpoint}";
        $startTime = microtime(true);

        try {
            $response = $this->client->post($url, [
                'headers' => [
                    'access_token' => $this->token,
                    'Content-Type' => 'application/json',
                ],
                'json' => $payload,
            ]);

            $executionTime = microtime(true) - $startTime;
            $responseData = json_decode($response->getBody()->getContents(), true);

            $this->logSuccess('POST', "/v3/{$endpoint}", $payload, $responseData, $response->getStatusCode(), $executionTime);

            // Reset body stream for consumption by caller
            $response->getBody()->rewind();
            return $response;

        } catch (GuzzleException $e) {
            $executionTime = microtime(true) - $startTime;
            $this->logError($e, 'POST', "/v3/{$endpoint}", $payload, $executionTime);
            throw $e;
        }
    }

    /**
     * @throws GuzzleException
     */
    protected function put(array $payload, ?string $endpoint = null): ResponseInterface
    {
        $endpoint = $endpoint ?? $this->endpoint;
        $url = "{$this->baseUrl}/v3/{$endpoint}";
        $startTime = microtime(true);

        try {
            $response = $this->client->put($url, [
                'headers' => [
                    'access_token' => $this->token,
                    'Content-Type' => 'application/json',
                ],
                'json' => $payload,
            ]);

            $executionTime = microtime(true) - $startTime;
            $responseData = json_decode($response->getBody()->getContents(), true);

            $this->logSuccess('PUT', "/v3/{$endpoint}", $payload, $responseData, $response->getStatusCode(), $executionTime);

            // Reset body stream for consumption by caller
            $response->getBody()->rewind();
            return $response;

        } catch (GuzzleException $e) {
            $executionTime = microtime(true) - $startTime;
            $this->logError($e, 'PUT', "/v3/{$endpoint}", $payload, $executionTime);
            throw $e;
        }
    }

    /**
     * @throws GuzzleException
     */
    protected function delete(?string $endpoint = null, array $query = []): ResponseInterface
    {
        $endpoint = $endpoint ?? $this->endpoint;
        $url = "{$this->baseUrl}/v3/{$endpoint}";
        $startTime = microtime(true);

        try {
            $response = $this->client->delete($url, [
                'headers' => [
                    'access_token' => $this->token,
                    'Content-Type' => 'application/json',
                ],
                'query' => $query,
            ]);

            $executionTime = microtime(true) - $startTime;
            $responseData = json_decode($response->getBody()->getContents(), true);

            $this->logSuccess('DELETE', "/v3/{$endpoint}", [], $responseData, $response->getStatusCode(), $executionTime);

            // Reset body stream for consumption by caller
            $response->getBody()->rewind();
            return $response;

        } catch (GuzzleException $e) {
            $executionTime = microtime(true) - $startTime;
            $this->logError($e, 'DELETE', "/v3/{$endpoint}", [], $executionTime);
            throw $e;
        }
    }

    /**
     * Get environment
     */
    public function getEnvironment(): AsaasEnvironment
    {
        return $this->environment;
    }

    /**
     * Log successful ASAAS request
     */
    protected function logSuccess(string $method, string $endpoint, array $requestData, array $responseData, int $statusCode, float $executionTime): void
    {
        $this->logAsaasRequest->perform(
            $method,
            $endpoint,
            $requestData,
            $responseData,
            $statusCode,
            $executionTime,
            $this->organization_id,
            $this->environment->value
        );
    }

    /**
     * Log error ASAAS request and general error
     */
    protected function logError(GuzzleException $e, string $method, string $endpoint, array $requestData, float $executionTime): void
    {
        $user_id = auth()?->user()?->id ?? null;

        // Log to AsaasLog table
        $this->logAsaasError->perform(
            $method,
            $endpoint,
            $requestData,
            null,
            $e->getCode(),
            $executionTime,
            $e->getMessage(),
            null,
            $this->organization_id,
            $this->environment->value
        );

        DBLog::logError(
            $e->getMessage(),
            "AsaasService::{$method}",
            $this->organization_id,
            $user_id,
            [
                'response' => $e->getResponse()?->getBody()?->getContents(),
                'endpoint' => $endpoint,
                'trace' => $e->getTraceAsString(),
                'request_data' => $requestData,
            ]
        );
    }
}
