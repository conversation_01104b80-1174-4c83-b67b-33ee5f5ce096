<?php

namespace App\Services\ASAAS;

use App\Domains\Organization;
use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\Exceptions\AsaasException;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Account Service
 *
 * Responsible for ASAAS Account endpoints.
 * Always uses main credentials (our clients/organizations are accounts, they don't have accounts).
 */
class AccountService extends AsaasService
{
    protected const ENDPOINT_MY_ACCOUNT = 'myAccount';
    protected const ENDPOINT_ACCOUNTS = 'accounts';
    protected const ENDPOINT_BALANCE = 'finance/balance';
    protected const ENDPOINT_STATISTICS = 'finance/statistics';

    public function __construct(?Organization $organization = null)
    {
        parent::__construct($organization);
        $this->endpoint = self::ENDPOINT_ACCOUNTS;
    }

    public function getEnvironment(): AsaasEnvironment
    {
        return $this->environment;
    }

    /**
     * Get account information
     * @throws GuzzleException
     */
    public function getMyAccount(): array
    {
        $this->endpoint = self::ENDPOINT_MY_ACCOUNT;
        $response = $this->get();
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Update account information
     * @throws GuzzleException
     */
    public function updateMyAccount(array $data): array
    {
        $response = $this->put($data);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get account balance
     * @throws GuzzleException
     */
    public function getBalance(): array
    {
        $response = $this->get([], self::ENDPOINT_BALANCE);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get account statistics
     * @throws GuzzleException
     */
    public function getStatistics(array $filters = []): array
    {
        $response = $this->get($filters, self::ENDPOINT_STATISTICS);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Create subaccount (organization)
     * @throws GuzzleException
     */
    public function createSubaccount(array $data): array
    {
        $response = $this->post($data, self::ENDPOINT_ACCOUNTS);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get subaccounts list
     * @throws GuzzleException
     */
    public function getSubaccounts(array $filters = []): array
    {
        $response = $this->get($filters, self::ENDPOINT_ACCOUNTS);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Get specific subaccount
     * @throws GuzzleException
     */
    public function getSubaccount(string $accountId): array
    {
        $response = $this->get([], self::ENDPOINT_ACCOUNTS . "/{$accountId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Update subaccount
     * @throws GuzzleException
     */
    public function updateSubaccount(string $accountId, array $data): array
    {
        $response = $this->put($data, self::ENDPOINT_ACCOUNTS . "/{$accountId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Delete subaccount
     * @throws GuzzleException
     */
    public function deleteSubaccount(string $accountId): array
    {
        $response = $this->delete(self::ENDPOINT_ACCOUNTS . "/{$accountId}");
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Delete subaccount
     * @throws GuzzleException
     * @throws AsaasException
     */
    public function deleteMyAccountSubaccount(Organization $organization, string $remove_reason): array
    {
        if ($this->token !== $organization->asaas_api_key || $this->organization->asaas_api_key !== $organization->asaas_api_key) {
            throw new AsaasException('Invalid API key for organization. Only subaccounts allowed here!');
        }
        $response = $this->delete(self::ENDPOINT_MY_ACCOUNT, ['removeReason' => $remove_reason]);
        return json_decode($response->getBody()->getContents(), true);
    }
}
