<?php

namespace App\Services\ASAAS\UseCases\Subscription;

use App\Services\ASAAS\SubscriptionService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Domains\Subscription;
use GuzzleHttp\Exception\GuzzleException;

class CreateSubscription
{
    /**
     * @throws GuzzleException
     * @throws AsaasException
     */
    public function perform(Subscription $subscription): array
    {
        try {
            if ($subscription->hasAsaasIntegration()) {
                throw new AsaasException("Subscription already has an ASAAS integration");
            }

            if (!$subscription->hasAsaasCustomer()) {
                $customerType = $subscription->is_client_subscription ? 'client' : 'organization';
                throw new AsaasException("Subscription {$customerType} does not have ASAAS customer configured");
            }

            $organization = ($subscription->is_client_subscription) ? $subscription->organization : null;

            $subscriptionService = new SubscriptionService($organization);
            return $subscriptionService->create($subscription->toAsaasPayload());
        } catch (\Exception $e) {
            throw new AsaasException("Failed to create subscription: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
