<?php

namespace App\Services\ASAAS\UseCases\Subscription;

use App\Services\ASAAS\SubscriptionService;
use App\Services\ASAAS\Exceptions\AsaasException;
use GuzzleHttp\Exception\GuzzleException;

class GetAllSubscriptions
{
    private SubscriptionService $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * @throws GuzzleException
     * @throws AsaasException
     */
    public function perform(array $filters = []): array
    {
        try {
            return $this->subscriptionService->getAll($filters);
        } catch (\Exception $e) {
            throw new AsaasException("Failed to get subscriptions: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
