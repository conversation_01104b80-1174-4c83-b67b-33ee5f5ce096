<?php

namespace App\Services\ASAAS\UseCases\Subscription;

use App\Services\ASAAS\SubscriptionService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Domains\Subscription;
use GuzzleHttp\Exception\GuzzleException;

class DeleteSubscription
{
    /**
     * @throws GuzzleException
     * @throws AsaasException
     */
    public function perform(Subscription $subscription): array
    {
        try {
            if (!$subscription->hasAsaasIntegration()) {
                throw new AsaasException("Subscription does not have an ASAAS integration");
            }

            $subscriptionService = new SubscriptionService($subscription->organization);
            return $subscriptionService->ßßßdeleteSubscription($subscription->getAsaasSubscriptionId());
        } catch (\Exception $e) {
            throw new AsaasException("Failed to delete subscription: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
