<?php

namespace App\Services\ASAAS\UseCases\Subscription;

use App\Services\ASAAS\SubscriptionService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Domains\Subscription;
use GuzzleHttp\Exception\GuzzleException;

class GetSubscriptionById
{
    /**
     * @throws GuzzleException
     * @throws AsaasException
     */
    public function perform(Subscription $subscription): array
    {
        try {
            if (!$subscription->hasAsaasIntegration()) {
                throw new AsaasException("Subscription does not have an ASAAS integration");
            }
            if (!$subscription->hasAsaasCustomer()) {
                $customerType = $subscription->is_client_subscription ? 'client' : 'organization';
                throw new AsaasException("Subscription {$customerType} does not have ASAAS customer configured");
            }

            $organization = ($subscription->is_client_subscription) ? $subscription->client->organization : null;
            $subscriptionService = new SubscriptionService($organization);
            return $subscriptionService->getById($subscription->getAsaasSubscriptionId());
        } catch (\Exception $e) {
            throw new AsaasException("Failed to get subscription: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
