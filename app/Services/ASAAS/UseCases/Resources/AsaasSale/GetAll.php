<?php

namespace App\Services\ASAAS\UseCases\Resources\AsaasSale;

use App\Services\ASAAS\Repositories\AsaasSaleRepository;

class GetAll
{
    private AsaasSaleRepository $asaasSaleRepository;

    public function __construct(AsaasSaleRepository $asaasSaleRepository)
    {
        $this->asaasSaleRepository = $asaasSaleRepository;
    }

    /**
     * @return array
     */
    public function perform(): array
    {
        $organization_id = request()->user()->organization_id;

        return $this->asaasSaleRepository->fetchFromOrganization($organization_id);
    }
}
