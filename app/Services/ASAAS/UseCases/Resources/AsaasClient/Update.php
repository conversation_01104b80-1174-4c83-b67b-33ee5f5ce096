<?php

namespace App\Services\ASAAS\UseCases\Resources\AsaasClient;

use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use App\Http\Requests\ASAAS\AsaasClient\UpdateAsaasClientRequest;
use App\Services\ASAAS\Repositories\AsaasClientRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private AsaasClientRepository $asaasClientRepository;
    private AsaasClientFactory $asaasClientFactory;

    public function __construct(AsaasClientRepository $asaasClientRepository, AsaasClientFactory $asaasClientFactory)
    {
        $this->asaasClientRepository = $asaasClientRepository;
        $this->asaasClientFactory = $asaasClientFactory;
    }

    /**
     * @param UpdateAsaasClientRequest $request
     * @param int $id
     * @return AsaasClient
     */
    public function perform(UpdateAsaasClientRequest $request, int $id): AsaasClient
    {
        DB::beginTransaction();

        $domain = $this->asaasClientFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $asaasClient = $this->asaasClientRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $asaasClient;
    }
}
