<?php

namespace App\Services\ASAAS\UseCases\Resources\AsaasOrganizationCustomer;

use App\Services\ASAAS\Domains\AsaasOrganizationCustomer;
use App\Services\ASAAS\Factories\AsaasOrganizationCustomerFactory;
use App\Http\Requests\ASAAS\AsaasOrganizationCustomer\UpdateAsaasOrganizationCustomerRequest;
use App\Services\ASAAS\Repositories\AsaasOrganizationCustomerRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository;
    private AsaasOrganizationCustomerFactory $asaasOrganizationCustomerFactory;

    public function __construct(AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository, AsaasOrganizationCustomerFactory $asaasOrganizationCustomerFactory)
    {
        $this->asaasOrganizationCustomerRepository = $asaasOrganizationCustomerRepository;
        $this->asaasOrganizationCustomerFactory = $asaasOrganizationCustomerFactory;
    }

    /**
     * @param UpdateAsaasOrganizationCustomerRequest $request
     * @param int $id
     * @return AsaasOrganizationCustomer
     */
    public function perform(UpdateAsaasOrganizationCustomerRequest $request, int $id): AsaasOrganizationCustomer
    {
        DB::beginTransaction();

        $domain = $this->asaasOrganizationCustomerFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $asaasOrganizationCustomer = $this->asaasOrganizationCustomerRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $asaasOrganizationCustomer;
    }
}
