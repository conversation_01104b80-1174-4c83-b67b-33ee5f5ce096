<?php

namespace App\Services\ASAAS\UseCases\Resources\AsaasOrganizationCustomer;

use App\Services\ASAAS\Repositories\AsaasOrganizationCustomerRepository;

class GetAll
{
    private AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository;

    public function __construct(AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository)
    {
        $this->asaasOrganizationCustomerRepository = $asaasOrganizationCustomerRepository;
    }

    /**
     * @return array
     */
    public function perform(): array
    {
        $organization_id = request()->user()->organization_id;

        return $this->asaasOrganizationCustomerRepository->fetchFromOrganization($organization_id);
    }
}
