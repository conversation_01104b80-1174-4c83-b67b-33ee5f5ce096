<?php

namespace App\Services\ASAAS\UseCases\Resources\AsaasSubscription;

use App\Services\ASAAS\Repositories\AsaasSubscriptionRepository;

class GetAll
{
    private AsaasSubscriptionRepository $asaasSubscriptionRepository;

    public function __construct(AsaasSubscriptionRepository $asaasSubscriptionRepository)
    {
        $this->asaasSubscriptionRepository = $asaasSubscriptionRepository;
    }

    /**
     * @return array
     */
    public function perform(): array
    {
        // Note: AsaasSubscription doesn't have organization_id directly, 
        // but we can filter by subscription->organization_id if needed
        return $this->asaasSubscriptionRepository->fetchAll();
    }
}
