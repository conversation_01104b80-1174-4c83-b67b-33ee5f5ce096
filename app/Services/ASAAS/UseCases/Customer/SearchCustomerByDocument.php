<?php

namespace App\Services\ASAAS\UseCases\Customer;

use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Exceptions\AsaasException;

class SearchCustomerByDocument
{
    private CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    public function perform(string $cpf_cnpj, ?int $user_id = null): array
    {
        try {
            return $this->customerService->searchByDocument($cpf_cnpj);
        } catch (\Exception $e) {
            throw new AsaasException("Failed to search customer by document: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
