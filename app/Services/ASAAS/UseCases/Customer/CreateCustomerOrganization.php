<?php

namespace App\Services\ASAAS\UseCases\Customer;

use App\Domains\Organization;
use App\Helpers\DBLog;
use App\Services\ASAAS\CustomerService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Factories\AsaasOrganizationCustomerFactory;
use App\Services\ASAAS\Repositories\AsaasOrganizationCustomerRepository;
use App\Services\ASAAS\Models\AsaasLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreateCustomerOrganization
{
    private CustomerService $customerService;
    private AsaasOrganizationCustomerFactory $asaasOrganizationCustomerFactory;
    private AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository;


    public function __construct(
        AsaasOrganizationCustomerFactory $asaasOrganizationCustomerFactory,
        AsaasOrganizationCustomerRepository $asaasOrganizationCustomerRepository,
        CustomerService $customerService

    ) {
        $this->asaasOrganizationCustomerFactory = $asaasOrganizationCustomerFactory;
        $this->asaasOrganizationCustomerRepository = $asaasOrganizationCustomerRepository;
        $this->customerService = $customerService;
    }

    /**
     * Create ASAAS customer for organization using default .env credentials
     *
     * @param Organization $organization
     * @return array
     * @throws AsaasException
     */
    public function perform(Organization $organization): array
    {
        if ($organization->hasAsaasIntegration()) {
            throw new AsaasException('Organization already has an ASAAS customer integration');
        }

        DB::beginTransaction();

        try {
            $asaasResponse = $this->customerService->create(
                $organization->toAsaasCustomerPayload()
            );

            $asaasOrganizationCustomer = $this->asaasOrganizationCustomerFactory->buildFromAsaasResponse(
                $asaasResponse,
                $organization->id
            );

            $this->asaasOrganizationCustomerRepository->store($asaasOrganizationCustomer);

            DB::commit();

            DBLog::log('ASAAS customer created successfully for organization', 'CreateCustomerOrganization',
                $organization->id, null, [
                'organization_id' => $organization->id,
                'asaas_customer_id' => $asaasResponse['id'] ?? null
            ]);

            return [
                'status' => 'success',
                'message' => 'Customer created successfully',
                'asaas_customer_id' => $asaasResponse['id'] ?? null,
                'customer_data' => $asaasOrganizationCustomer->toArray()
            ];

        } catch (\Throwable $e) {
            DB::rollBack();

            DBLog::logError('Failed to create ASAAS customer for organization', "CreateCustomerOrganization",
            $organization->id, null, [
                'organization_id' => $organization->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($e instanceof AsaasException) {
                throw $e;
            }

            throw new AsaasException(
                'Failed to create customer: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }
}
