<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Domains\Organization as OrganizationDomain;

class DeleteMyAccountSubaccount
{

    public function perform(OrganizationDomain $organization, string $remove_reason): array
    {
        if (!$organization->hasAsaasAPIIntegration()) {
            return [
                'success' => false,
                'organization' => $organization,
                'error' => 'Organization does not have ASAAS API integration configured',
                'asaas_account_id' => null
            ];
        }
        try {
            $accountService = new AccountService($organization);
            $response = $accountService->deleteMyAccountSubaccount(
                $organization,
                $remove_reason
            );

            return [
                'success' => true,
                'organization' => $organization,
                'asaas_organization' => $organization->asaas,
                'error' => null,
                'asaas_account_id' => $organization->asaas_account_id,
                'response' => $response
            ];

        } catch (\Throwable $e) {
            return [
                'success' => false,
                'organization' => $organization,
                'error' => "Failed to delete subaccount: {$e->getMessage()}",
                'asaas_account_id' => $organization->asaas_account_id,
                'response' => null
            ];
        }
    }
}
