<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Exceptions\AsaasException;
use Throwable;

class GetMyAccount
{
    private AccountService $accountService;

    public function __construct(AccountService $accountService)
    {
        $this->accountService = $accountService;
    }

    public function perform(): array
    {
        try {
            return $this->accountService->getMyAccount();
        } catch (Throwable $e) {
            throw new AsaasException("Failed to get account information: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
