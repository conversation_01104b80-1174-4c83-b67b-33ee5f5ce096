<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Domains\Organization as OrganizationDomain;

class DeleteSubaccount
{
    private AccountService $accountService;

    public function __construct(
        AccountService $accountService
    ) {
        $this->accountService = $accountService;
    }

    public function perform(OrganizationDomain $organization): array
    {
        if (!$organization->hasAsaasIntegration()) {
            return [
                'success' => false,
                'organization' => $organization,
                'error' => 'Organization does not have ASAAS integration configured',
                'asaas_account_id' => null
            ];
        }
        try {
            $response = $this->accountService->deleteSubaccount(
                $organization->asaas_account_id
            );

            return [
                'success' => true,
                'organization' => $organization,
                'asaas_organization' => $organization->asaas,
                'error' => null,
                'asaas_account_id' => $organization->asaas_account_id,
                'response' => $response
            ];

        } catch (\Throwable $e) {
            return [
                'success' => false,
                'organization' => $organization,
                'error' => "Failed to delete subaccount: {$e->getMessage()}",
                'asaas_account_id' => $organization->asaas_account_id,
                'response' => null
            ];
        }
    }
}
