<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Repositories\OrganizationRepository;
use App\Domains\Organization as OrganizationDomain;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\DB;

class UpdateSubaccount
{
    private AccountService $accountService;
    private AsaasOrganizationRepository $asaasOrganizationRepository;
    private AsaasOrganizationFactory $asaasOrganizationFactory;

    public function __construct(
        AccountService $accountService,
        AsaasOrganizationRepository $asaasOrganizationRepository,
        AsaasOrganizationFactory $asaasOrganizationFactory,
    ) {
        $this->accountService = $accountService;
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
        $this->asaasOrganizationFactory = $asaasOrganizationFactory;
    }

    /**
     * @throws AsaasException
     */
    public function perform(OrganizationDomain $organization): OrganizationDomain
    {
        if (!$organization->hasAsaasIntegration()) {
            throw new AsaasException("Organization does not have ASAAS integration configured");
        }
        DB::beginTransaction();

        try {
            $payload = $organization->toAsaasPayload();
            $response = $this->accountService->updateSubaccount($organization->asaas_account_id, $payload);

            $updatedAsaasOrganization = $this->asaasOrganizationFactory->buildFromAsaasResponse(
                $response,
                $organization,
                $this->accountService->getEnvironment()
            );

            $updatedAsaasOrganization->id = $organization->asaas->id;
            $savedAsaasOrganization = $this->asaasOrganizationRepository->update($updatedAsaasOrganization);
            DB::commit();

            $organization->asaas = $savedAsaasOrganization;
            return $organization;

        } catch (\Throwable $e) {
            DB::rollBack();
            throw new AsaasException("Failed to update subaccount: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
