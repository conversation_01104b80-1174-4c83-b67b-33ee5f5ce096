<?php

namespace App\Services\ASAAS\UseCases\Account;

use App\Helpers\DBLog;
use App\Services\ASAAS\AccountService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Repositories\AsaasOrganizationRepository;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Repositories\OrganizationRepository;
use App\Domains\Organization as OrganizationDomain;
use Illuminate\Support\Facades\DB;
use Throwable;

class CreateSubaccount
{
    private AccountService $accountService;
    private AsaasOrganizationRepository $asaasOrganizationRepository;
    private AsaasOrganizationFactory $asaasOrganizationFactory;
    private OrganizationRepository $organizationRepository;

    public function __construct(
        AccountService $accountService,
        AsaasOrganizationRepository $asaasOrganizationRepository,
        AsaasOrganizationFactory $asaasOrganizationFactory,
        OrganizationRepository $organizationRepository
    ) {
        $this->accountService = $accountService;
        $this->asaasOrganizationRepository = $asaasOrganizationRepository;
        $this->asaasOrganizationFactory = $asaasOrganizationFactory;
        $this->organizationRepository = $organizationRepository;
    }

    private function logApiKey(mixed $response, OrganizationDomain $organization) : void {
        $apiKey = $response['apiKey'] ?? false;
        if ($apiKey) {
            DBLog::log('ASAAS subaccount apiKey generated successfully', 'CreateSubaccount::apiKey',
                $organization->id, null, [
                'organization_id' => $organization->id,
                'asaas_account_id' => $response['id'] ?? null,
                'asaas_api_key' => $apiKey
            ]);
        }
    }

    /**
     * @throws AsaasException
     */
    public function perform(OrganizationDomain $organization): OrganizationDomain
    {
        if ($organization->hasAsaasAPIIntegration()) {
            throw new AsaasException("Organization already has an API ASAAS integration");
        }

        DB::beginTransaction();

        try {
            $response = $this->accountService->createSubaccount(
                $organization->toAsaasPayload()
            );

            $this->logApiKey($response, $organization);

            $asaasOrganization = $this->asaasOrganizationFactory->buildFromAsaasResponse(
                $response,
                $organization,
                $this->accountService->getEnvironment()
            );

            $this->asaasOrganizationRepository->store($asaasOrganization);

            $organization->setAsaasIntegration($asaasOrganization);

            $this->organizationRepository->updateAsaasIntegration($organization);

            DB::commit();

            return $organization;

        } catch (Throwable $e) {
            DB::rollBack();
            throw new AsaasException("Failed to create subaccount: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
