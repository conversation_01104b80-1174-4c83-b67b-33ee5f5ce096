<?php

namespace App\Services\ASAAS\UseCases\Payment;

use App\Domains\Organization;
use App\Services\ASAAS\PaymentService;
use App\Services\ASAAS\Exceptions\AsaasException;
use GuzzleHttp\Exception\GuzzleException;

class GetAllPayments
{


    /**
     * TODO: improve this use case
     * @throws GuzzleException
     * @throws AsaasException
     */
    public function perform(Organization $organization, array $filters = []): array
    {
        try {
            $paymentService = new PaymentService($organization);

            return $paymentService->getAll($filters);
        } catch (\Exception $e) {
            throw new AsaasException("Failed to get payments: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
