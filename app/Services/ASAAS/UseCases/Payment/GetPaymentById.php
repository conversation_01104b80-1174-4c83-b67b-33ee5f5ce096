<?php

namespace App\Services\ASAAS\UseCases\Payment;

use App\Services\ASAAS\PaymentService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Domains\Inventory\Sale;
use GuzzleHttp\Exception\GuzzleException;

class GetPaymentById
{
    /**
     * @throws GuzzleException
     * @throws AsaasException
     */
    public function perform(Sale $sale): array
    {
        try {
            if (!$sale->hasAsaasPayment()) {
                throw new AsaasException("Sale does not have an ASAAS payment");
            }

            $paymentService = new PaymentService($sale->organization);
            return $paymentService->getById($sale->getAsaasPaymentId());
        } catch (\Exception $e) {
            throw new AsaasException("Failed to get payment: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
