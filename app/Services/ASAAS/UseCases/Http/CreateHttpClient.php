<?php

namespace App\Services\ASAAS\UseCases\Http;

use App\Enums\AsaasEnvironment;
use App\Services\ASAAS\Domains\AsaasHttpClient;
use App\Services\ASAAS\Exceptions\AsaasException;

/**
 * Creates configured HTTP clients for ASAAS API communication.
 * Handles token resolution, environment configuration, and context setup for API requests.
 */
class CreateHttpClient
{
    /**
     * Create an ASAAS HTTP client with dynamic token and environment
     *
     * @param string|null $token Custom token (if null, uses config)
     * @param AsaasEnvironment|null $environment Custom environment (if null, uses config)
     * @param int|null $organizationId Organization ID for logging
     * @param int|null $userId User ID for logging
     * @return AsaasHttpClient
     * @throws AsaasException
     */
    public function perform(
        ?string $token = null,
        ?AsaasEnvironment $environment = null,
        ?int $organizationId = null,
        ?int $userId = null
    ): AsaasHttpClient {
        // Determine environment
        if ($environment === null) {
            $envString = config('asaas.environment', 'sandbox');
            $environment = AsaasEnvironment::from($envString);
        }

        if ($token === null) {
            $token = config($environment->getTokenConfigKey());

            if (empty($token)) {
                throw new AsaasException(
                    "ASAAS token not configured for environment: {$environment->value}. " .
                    "Please set the appropriate environment variable."
                );
            }
        }

        // Get organization and user from request if not provided
        if ($organizationId === null || $userId === null) {
            $user = request()->user();
            if ($user) {
                $organizationId = $organizationId ?? $user->organization_id;
                $userId = $userId ?? $user->id;
            }
        }

        return new AsaasHttpClient(
            $environment,
            $token,
            $organizationId,
            $userId
        );
    }

    /**
     * Create an ASAAS HTTP client for a specific organization
     *
     * @param int $organizationId
     * @param string|null $token Custom token for this organization
     * @param AsaasEnvironment|null $environment
     * @param int|null $userId
     * @return AsaasHttpClient
     * @throws AsaasException
     */
    public function performForOrganization(
        int $organizationId,
        ?string $token = null,
        ?AsaasEnvironment $environment = null,
        ?int $userId = null
    ): AsaasHttpClient {
        // TODO: In the future, we could fetch organization-specific tokens from database
        // For now, we use the global configuration

        return $this->perform($token, $environment, $organizationId, $userId);
    }

    /**
     * Create an ASAAS HTTP client for sandbox environment
     *
     * @param string|null $token
     * @param int|null $organizationId
     * @param int|null $userId
     * @return AsaasHttpClient
     * @throws AsaasException
     */
    public function performForSandbox(
        ?string $token = null,
        ?int $organizationId = null,
        ?int $userId = null
    ): AsaasHttpClient {
        return $this->perform($token, AsaasEnvironment::SANDBOX, $organizationId, $userId);
    }

    /**
     * Create an ASAAS HTTP client for production environment
     *
     * @param string|null $token
     * @param int|null $organizationId
     * @param int|null $userId
     * @return AsaasHttpClient
     * @throws AsaasException
     */
    public function performForProduction(
        ?string $token = null,
        ?int $organizationId = null,
        ?int $userId = null
    ): AsaasHttpClient {
        return $this->perform($token, AsaasEnvironment::PRODUCTION, $organizationId, $userId);
    }
}
