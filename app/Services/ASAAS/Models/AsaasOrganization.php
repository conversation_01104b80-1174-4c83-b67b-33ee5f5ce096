<?php
namespace App\Services\ASAAS\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Database\Factories\AsaasOrganizationFactory;
use App\Models\Organization;
use App\Enums\AsaasEnvironment;
use Illuminate\Database\Eloquent\SoftDeletes;

class AsaasOrganization extends Model
{
    use HasFactory, SoftDeletes;

    protected static function newFactory()
    {
        return AsaasOrganizationFactory::new();
    }

    protected $table = 'asaas_organizations';

    protected $fillable = [
        'organization_id',
        'asaas_account_id',
        'asaas_api_key',
        'asaas_wallet_id',
        'asaas_environment',
        'is_active',
        'last_sync_at',
        'sync_errors',
        'name',
        'email',
        'login_email',
        'phone',
        'mobile_phone',
        'address',
        'address_number',
        'complement',
        'province',
        'postal_code',
        'cpf_cnpj',
        'birth_date',
        'person_type',
        'company_type',
        'city',
        'state',
        'country',
        'trading_name',
        'income_value',
        'site',
        'account_number',
        'commercial_info_expiration',
    ];

    protected $casts = [
        'asaas_environment' => AsaasEnvironment::class,
        'is_active' => 'boolean',
        'last_sync_at' => 'datetime',
        'sync_errors' => 'array',
        'income_value' => 'float',
        'birth_date' => 'date',
        'city' => 'integer',
        'account_number' => 'array',
        'commercial_info_expiration' => 'array',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function hasAsaasIntegration(): bool
    {
        return !empty($this->asaas_account_id) && !empty($this->asaas_api_key);
    }

    public function isProduction(): bool
    {
        return $this->asaas_environment === AsaasEnvironment::PRODUCTION;
    }

    public function isSandbox(): bool
    {
        return $this->asaas_environment === AsaasEnvironment::SANDBOX;
    }

    public function getAsaasEnvironmentLabelAttribute(): string
    {
        return $this->asaas_environment?->label() ?? 'Não definido';
    }

    /**
     * Check if organization can access ASAAS system
     */
    public function canAccessSystem(): bool
    {
        return $this->is_active &&
               !empty($this->asaas_account_id) &&
               !empty($this->asaas_api_key);
    }
}
