<?php

namespace App\Services\ASAAS\Factories;

use App\Domains\Inventory\Sale;
use App\Services\ASAAS\Domains\AsaasSale;
use App\Services\ASAAS\Models\AsaasSale as AsaasSaleModel;
use App\Factories\Inventory\SaleFactory;
use App\Factories\Inventory\ClientFactory;
use Carbon\Carbon;

class AsaasSaleFactory
{
    public function __construct(
        private SaleFactory $saleFactory,
        private ClientFactory $clientFactory
    ) {}

    /**
     * Build AsaasSale domain from Eloquent model
     */
    public function buildFromModel(?AsaasSaleModel $model, bool $with_sale = true, bool $with_client = true): ?AsaasSale
    {
        if (!$model) {
            return null;
        }

        $sale = ($with_sale) ? $this->saleFactory->buildFromModel($model->sale, false, false, false, false, false, false) : null;
        $client = ($with_client) ? $this->clientFactory->buildFromModel($model->client, false, false) : null;

        return new AsaasSale(
            id: $model->id,
            sale_id: $model->sale_id,
            organization_id: $model->organization_id,
            client_id: $model->client_id,
            asaas_payment_id: $model->asaas_payment_id,
            asaas_customer_id: $model->asaas_customer_id,
            value: $model->value,
            net_value: $model->net_value,
            original_value: $model->original_value,
            interest_value: $model->interest_value,
            discount_value: $model->discount_value,
            description: $model->description,
            billing_type: $model->billing_type,
            due_date: $model->due_date,
            payment_date: $model->payment_date,
            original_due_date: $model->original_due_date,
            client_payment_date: $model->client_payment_date,
            status: $model->status ?? 'PENDING',
            invoice_url: $model->invoice_url,
            invoice_number: $model->invoice_number,
            bank_slip_url: $model->bank_slip_url,
            pix_qr_code_id: $model->pix_qr_code_id,
            external_reference: $model->external_reference,
            installment_id: $model->installment_id,
            installment_count: $model->installment_count,
            installment_value: $model->installment_value,
            installment_number: $model->installment_number,
            credit_date: $model->credit_date,
            estimated_credit_date: $model->estimated_credit_date,
            anticipated: $model->anticipated ?? false,
            anticipable: $model->anticipable ?? false,
            can_be_paid_after_due_date: $model->can_be_paid_after_due_date ?? true,
            deleted: $model->deleted ?? false,
            nosso_numero: $model->nosso_numero,
            asaas_synced_at: $model->asaas_synced_at,
            asaas_sync_errors: $model->asaas_sync_errors,
            sync_status: $model->sync_status ?? 'pending',
            asaas_webhook_data: $model->asaas_webhook_data,
            discount_config: $model->discount_config,
            fine_config: $model->fine_config,
            interest_config: $model->interest_config,
            split_config: $model->split_config,
            credit_card_data: $model->credit_card_data,
            chargeback_data: $model->chargeback_data,
            escrow_data: $model->escrow_data,
            refunds_data: $model->refunds_data,
            sale: $sale,
            client: $client,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
            deleted_at: $model->deleted_at,
        );
    }

    /**
     * Build AsaasSale domain from Asaas API response
     */
    public function buildFromAsaasResponse(array $response, int $saleId, int $organizationId, int $clientId): AsaasSale
    {
        return new AsaasSale(
            id: null,
            sale_id: $saleId,
            organization_id: $organizationId,
            client_id: $clientId,
            asaas_payment_id: $response['id'],
            asaas_customer_id: $response['customer'],
            value: $response['value'],
            net_value: $response['netValue'] ?? null,
            original_value: $response['originalValue'] ?? null,
            interest_value: $response['interestValue'] ?? null,
            discount_value: $response['discount']['value'] ?? null,
            description: $response['description'] ?? null,
            billing_type: $response['billingType'],
            due_date: Carbon::parse($response['dueDate']),
            payment_date: $response['paymentDate'] ? Carbon::parse($response['paymentDate']) : null,
            original_due_date: $response['originalDueDate'] ? Carbon::parse($response['originalDueDate']) : null,
            client_payment_date: $response['clientPaymentDate'] ? Carbon::parse($response['clientPaymentDate']) : null,
            status: $response['status'] ?? 'PENDING',
            invoice_url: $response['invoiceUrl'] ?? null,
            invoice_number: $response['invoiceNumber'] ?? null,
            bank_slip_url: $response['bankSlipUrl'] ?? null,
            pix_qr_code_id: $response['pixQrCodeId'] ?? null,
            external_reference: $response['externalReference'] ?? null,
            installment_id: $response['installment'] ?? null,
            installment_count: $response['installmentCount'] ?? null,
            installment_value: $response['installmentValue'] ?? null,
            installment_number: $response['installmentNumber'] ?? null,
            credit_date: $response['creditDate'] ? Carbon::parse($response['creditDate']) : null,
            estimated_credit_date: $response['estimatedCreditDate'] ? Carbon::parse($response['estimatedCreditDate']) : null,
            anticipated: $response['anticipated'] ?? false,
            anticipable: $response['anticipable'] ?? false,
            can_be_paid_after_due_date: $response['canBePaidAfterDueDate'] ?? true,
            deleted: $response['deleted'] ?? false,
            nosso_numero: $response['nossoNumero'] ?? null,
            asaas_synced_at: now(),
            asaas_sync_errors: null,
            sync_status: 'synced',
            asaas_webhook_data: null,
            discount_config: $response['discount'] ?? null,
            fine_config: $response['fine'] ?? null,
            interest_config: $response['interest'] ?? null,
            split_config: $response['split'] ?? null,
            credit_card_data: $response['creditCard'] ?? null,
            chargeback_data: $response['chargeback'] ?? null,
            escrow_data: $response['escrow'] ?? null,
            refunds_data: $response['refunds'] ?? null,
        );
    }

    /**
     * Build AsaasSale domain from Sale domain
     */
    public function buildFromSale(Sale $sale, string $asaasCustomerId, ?string $asaasPaymentId = null): AsaasSale
    {
        return new AsaasSale(
            id: null,
            sale_id: $sale->id,
            organization_id: $sale->organization_id,
            client_id: $sale->client_id,
            asaas_payment_id: $asaasPaymentId ?? '',
            asaas_customer_id: $asaasCustomerId,
            value: $sale->total_value,
            net_value: null,
            original_value: null,
            interest_value: null,
            discount_value: null,
            description: $sale->description ?? "Venda #{$sale->id}",
            billing_type: 'BOLETO', // Default billing type
            due_date: Carbon::parse($sale->due_date ?? now()->addDays(7)),
            payment_date: null,
            original_due_date: null,
            client_payment_date: null,
            status: 'PENDING',
            invoice_url: null,
            invoice_number: null,
            bank_slip_url: null,
            pix_qr_code_id: null,
            external_reference: (string) $sale->id,
            installment_id: null,
            installment_count: null,
            installment_value: null,
            installment_number: null,
            credit_date: null,
            estimated_credit_date: null,
            anticipated: false,
            anticipable: false,
            can_be_paid_after_due_date: true,
            deleted: false,
            nosso_numero: null,
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            asaas_webhook_data: null,
            discount_config: null,
            fine_config: null,
            interest_config: null,
            split_config: null,
            credit_card_data: null,
            chargeback_data: null,
            escrow_data: null,
            refunds_data: null,
            sale: $sale,
        );
    }

    /**
     * Build collection of AsaasSale domains from models
     */
    public function buildFromModels(array $models): array
    {
        return array_filter(array_map(
            fn($model) => $this->buildFromModel($model),
            $models
        ));
    }

    /**
     * Build AsaasSale for creation with minimal data
     */
    public function buildForCreation(
        int $saleId,
        int $organizationId,
        int $clientId,
        string $asaasCustomerId,
        float $value,
        string $billingType,
        Carbon $dueDate,
        ?string $description = null,
        ?string $externalReference = null
    ): AsaasSale {
        return new AsaasSale(
            id: null,
            sale_id: $saleId,
            organization_id: $organizationId,
            client_id: $clientId,
            asaas_payment_id: '',
            asaas_customer_id: $asaasCustomerId,
            value: $value,
            net_value: null,
            original_value: null,
            interest_value: null,
            discount_value: null,
            description: $description,
            billing_type: $billingType,
            due_date: $dueDate,
            payment_date: null,
            original_due_date: null,
            client_payment_date: null,
            status: 'PENDING',
            invoice_url: null,
            invoice_number: null,
            bank_slip_url: null,
            pix_qr_code_id: null,
            external_reference: $externalReference,
            installment_id: null,
            installment_count: null,
            installment_value: null,
            installment_number: null,
            credit_date: null,
            estimated_credit_date: null,
            anticipated: false,
            anticipable: false,
            can_be_paid_after_due_date: true,
            deleted: false,
            nosso_numero: null,
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            asaas_webhook_data: null,
            discount_config: null,
            fine_config: null,
            interest_config: null,
            split_config: null,
            credit_card_data: null,
            chargeback_data: null,
            escrow_data: null,
            refunds_data: null,
        );
    }

    /**
     * Build AsaasSale with sync error
     */
    public function buildWithSyncError(AsaasSale $asaasSale, array $errorData): AsaasSale
    {
        return new AsaasSale(
            id: $asaasSale->id,
            sale_id: $asaasSale->sale_id,
            organization_id: $asaasSale->organization_id,
            client_id: $asaasSale->client_id,
            asaas_payment_id: $asaasSale->asaas_payment_id,
            asaas_customer_id: $asaasSale->asaas_customer_id,
            value: $asaasSale->value,
            net_value: $asaasSale->net_value,
            original_value: $asaasSale->original_value,
            interest_value: $asaasSale->interest_value,
            discount_value: $asaasSale->discount_value,
            description: $asaasSale->description,
            billing_type: $asaasSale->billing_type,
            due_date: $asaasSale->due_date,
            payment_date: $asaasSale->payment_date,
            original_due_date: $asaasSale->original_due_date,
            client_payment_date: $asaasSale->client_payment_date,
            status: $asaasSale->status,
            invoice_url: $asaasSale->invoice_url,
            invoice_number: $asaasSale->invoice_number,
            bank_slip_url: $asaasSale->bank_slip_url,
            pix_qr_code_id: $asaasSale->pix_qr_code_id,
            external_reference: $asaasSale->external_reference,
            installment_id: $asaasSale->installment_id,
            installment_count: $asaasSale->installment_count,
            installment_value: $asaasSale->installment_value,
            installment_number: $asaasSale->installment_number,
            credit_date: $asaasSale->credit_date,
            estimated_credit_date: $asaasSale->estimated_credit_date,
            anticipated: $asaasSale->anticipated,
            anticipable: $asaasSale->anticipable,
            can_be_paid_after_due_date: $asaasSale->can_be_paid_after_due_date,
            deleted: $asaasSale->deleted,
            nosso_numero: $asaasSale->nosso_numero,
            asaas_synced_at: $asaasSale->asaas_synced_at,
            asaas_sync_errors: $errorData,
            sync_status: 'error',
            asaas_webhook_data: $asaasSale->asaas_webhook_data,
            discount_config: $asaasSale->discount_config,
            fine_config: $asaasSale->fine_config,
            interest_config: $asaasSale->interest_config,
            split_config: $asaasSale->split_config,
            credit_card_data: $asaasSale->credit_card_data,
            chargeback_data: $asaasSale->chargeback_data,
            escrow_data: $asaasSale->escrow_data,
            refunds_data: $asaasSale->refunds_data,
            sale: $asaasSale->sale,
            client: $asaasSale->client,
            created_at: $asaasSale->created_at,
            updated_at: $asaasSale->updated_at,
            deleted_at: $asaasSale->deleted_at,
        );
    }

    /**
     * Build AsaasSale marked as synced
     */
    public function buildAsSynced(AsaasSale $asaasSale, string $asaasPaymentId): AsaasSale
    {
        return new AsaasSale(
            id: $asaasSale->id,
            sale_id: $asaasSale->sale_id,
            organization_id: $asaasSale->organization_id,
            client_id: $asaasSale->client_id,
            asaas_payment_id: $asaasPaymentId,
            asaas_customer_id: $asaasSale->asaas_customer_id,
            value: $asaasSale->value,
            net_value: $asaasSale->net_value,
            original_value: $asaasSale->original_value,
            interest_value: $asaasSale->interest_value,
            discount_value: $asaasSale->discount_value,
            description: $asaasSale->description,
            billing_type: $asaasSale->billing_type,
            due_date: $asaasSale->due_date,
            payment_date: $asaasSale->payment_date,
            original_due_date: $asaasSale->original_due_date,
            client_payment_date: $asaasSale->client_payment_date,
            status: $asaasSale->status,
            invoice_url: $asaasSale->invoice_url,
            invoice_number: $asaasSale->invoice_number,
            bank_slip_url: $asaasSale->bank_slip_url,
            pix_qr_code_id: $asaasSale->pix_qr_code_id,
            external_reference: $asaasSale->external_reference,
            installment_id: $asaasSale->installment_id,
            installment_count: $asaasSale->installment_count,
            installment_value: $asaasSale->installment_value,
            installment_number: $asaasSale->installment_number,
            credit_date: $asaasSale->credit_date,
            estimated_credit_date: $asaasSale->estimated_credit_date,
            anticipated: $asaasSale->anticipated,
            anticipable: $asaasSale->anticipable,
            can_be_paid_after_due_date: $asaasSale->can_be_paid_after_due_date,
            deleted: $asaasSale->deleted,
            nosso_numero: $asaasSale->nosso_numero,
            asaas_synced_at: now(),
            asaas_sync_errors: null,
            sync_status: 'synced',
            asaas_webhook_data: $asaasSale->asaas_webhook_data,
            discount_config: $asaasSale->discount_config,
            fine_config: $asaasSale->fine_config,
            interest_config: $asaasSale->interest_config,
            split_config: $asaasSale->split_config,
            credit_card_data: $asaasSale->credit_card_data,
            chargeback_data: $asaasSale->chargeback_data,
            escrow_data: $asaasSale->escrow_data,
            refunds_data: $asaasSale->refunds_data,
            sale: $asaasSale->sale,
            client: $asaasSale->client,
            created_at: $asaasSale->created_at,
            updated_at: $asaasSale->updated_at,
            deleted_at: $asaasSale->deleted_at,
        );
    }
}
