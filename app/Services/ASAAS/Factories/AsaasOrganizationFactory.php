<?php

namespace App\Services\ASAAS\Factories;

use App\Domains\Organization;
use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Services\ASAAS\Models\AsaasOrganization as AsaasOrganizationModel;
use App\Factories\OrganizationFactory;
use App\Enums\AsaasEnvironment;
use Illuminate\Support\Collection;

class AsaasOrganizationFactory
{
    private OrganizationFactory $organizationFactory;

    public function __construct(OrganizationFactory $organizationFactory)
    {
        $this->organizationFactory = $organizationFactory;
    }

    public function buildFromModelOrganization(?AsaasOrganizationModel $model): ?AsaasOrganization
    {
        if (!$model) {  return null; }

        return new AsaasOrganization(
            id: $model->id,
            organization_id: $model->organization_id,
            organization: null,
            asaas_account_id: $model->asaas_account_id,
            asaas_api_key: $model->asaas_api_key,
            asaas_wallet_id: $model->asaas_wallet_id,
            asaas_environment: is_string($model->asaas_environment) ? AsaasEnvironment::from($model->asaas_environment) : $model->asaas_environment,
            is_active: $model->is_active,
            last_sync_at: $model->last_sync_at,
            sync_errors: $model->sync_errors,
            income_value: $model->income_value,
            site: $model->site,
            name: $model->name,
            email: $model->email,
            login_email: $model->login_email,
            phone: $model->phone,
            mobile_phone: $model->mobile_phone,
            address: $model->address,
            address_number: $model->address_number,
            complement: $model->complement,
            province: $model->province,
            postal_code: $model->postal_code,
            cpf_cnpj: $model->cpf_cnpj,
            birth_date: $model->birth_date?->format('Y-m-d'),
            person_type: $model->person_type,
            company_type: $model->company_type,
            city: $model->city,
            state: $model->state,
            country: $model->country,
            trading_name: $model->trading_name,
            account_number: $model->account_number,
            commercial_info_expiration: $model->commercial_info_expiration,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
        );
    }

    public function buildFromModel(?AsaasOrganizationModel $model): ?AsaasOrganization
    {
        if (!$model) {  return null; }

        $organization = $this->organizationFactory->buildFromModel($model->organization);

        return new AsaasOrganization(
            id: $model->id,
            organization_id: $model->organization_id,
            organization: $organization,
            asaas_account_id: $model->asaas_account_id,
            asaas_api_key: $model->asaas_api_key,
            asaas_wallet_id: $model->asaas_wallet_id,
            asaas_environment: is_string($model->asaas_environment) ? AsaasEnvironment::from($model->asaas_environment) : $model->asaas_environment,
            is_active: $model->is_active,
            last_sync_at: $model->last_sync_at,
            sync_errors: $model->sync_errors,
            income_value: $model->income_value,
            site: $model->site,
            name: $model->name,
            email: $model->email,
            login_email: $model->login_email,
            phone: $model->phone,
            mobile_phone: $model->mobile_phone,
            address: $model->address,
            address_number: $model->address_number,
            complement: $model->complement,
            province: $model->province,
            postal_code: $model->postal_code,
            cpf_cnpj: $model->cpf_cnpj,
            birth_date: $model->birth_date?->format('Y-m-d'),
            person_type: $model->person_type,
            company_type: $model->company_type,
            city: $model->city,
            state: $model->state,
            country: $model->country,
            trading_name: $model->trading_name,
            account_number: $model->account_number,
            commercial_info_expiration: $model->commercial_info_expiration,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
        );
    }

    public function buildFromStoreArray(array $data, Organization $organization): AsaasOrganization
    {
        return new AsaasOrganization(
            id: null,
            organization_id: $data['organization_id'],
            organization: $organization,
            asaas_account_id: $data['asaas_account_id'] ?? null,
            asaas_api_key: $data['asaas_api_key'] ?? null,
            asaas_wallet_id: $data['asaas_wallet_id'] ?? null,
            asaas_environment: isset($data['asaas_environment']) ?
                (is_string($data['asaas_environment']) ? AsaasEnvironment::from($data['asaas_environment']) : $data['asaas_environment']) :
                AsaasEnvironment::SANDBOX,
            is_active: $data['is_active'] ?? true,
            last_sync_at: $data['last_sync_at'] ?? null,
            sync_errors: $data['sync_errors'] ?? null,
            income_value: $data['income_value'] ?? null,
            site: $data['site'] ?? null,
            name: $data['name'] ?? null,
            email: $data['email'] ?? null,
            login_email: $data['login_email'] ?? null,
            phone: $data['phone'] ?? null,
            mobile_phone: $data['mobile_phone'] ?? null,
            address: $data['address'] ?? null,
            address_number: $data['address_number'] ?? null,
            complement: $data['complement'] ?? null,
            province: $data['province'] ?? null,
            postal_code: $data['postal_code'] ?? null,
            cpf_cnpj: $data['cpf_cnpj'] ?? null,
            birth_date: $data['birth_date'] ?? null,
            person_type: $data['person_type'] ?? null,
            company_type: $data['company_type'] ?? null,
            city: $data['city'] ?? null,
            state: $data['state'] ?? null,
            country: $data['country'] ?? null,
            trading_name: $data['trading_name'] ?? null,
            account_number: $data['account_number'] ?? null,
            commercial_info_expiration: $data['commercial_info_expiration'] ?? null,
            created_at: null,
            updated_at: null,
        );
    }

    public function buildFromModels(?Collection $models): array
    {
        return $models->map(fn($model) => $this->buildFromModel($model))
                     ->filter()
                     ->values()
                     ->toArray();
    }

    public function buildCollection(array $models): array
    {
        return array_map(fn($model) => $this->buildFromModel($model), $models);
    }

    public function buildFromAsaasResponse(array $response, Organization $organization, ?AsaasEnvironment $environment = null): ?AsaasOrganization
    {
        return new AsaasOrganization(
            id: null,
            organization_id: $organization->id,
            organization: $organization,
            asaas_account_id: $response['id'] ?? null,
            asaas_api_key: $response['apiKey'] ?? "apiKey__NOT_PROVIDED",
            asaas_wallet_id: $response['walletId'] ?? null,
            asaas_environment: $environment ?? AsaasEnvironment::SANDBOX,
            is_active: true,
            last_sync_at: now(),
            sync_errors: null,
            income_value: null,
            site: $response['site'] ?? null,
            name: $response['name'] ?? null,
            email: $response['email'] ?? null,
            login_email: $response['loginEmail'] ?? null,
            phone: $response['phone'] ?? null,
            mobile_phone: $response['mobilePhone'] ?? null,
            address: $response['address'] ?? null,
            address_number: $response['addressNumber'] ?? null,
            complement: $response['complement'] ?? null,
            province: $response['province'] ?? null,
            postal_code: $response['postalCode'] ?? null,
            cpf_cnpj: $response['cpfCnpj'] ?? null,
            birth_date: $response['birthDate'] ?? null,
            person_type: $response['personType'] ?? null,
            company_type: $response['companyType'] ?? null,
            city: $response['city'] ?? null,
            state: $response['state'] ?? null,
            country: $response['country'] ?? null,
            trading_name: $response['tradingName'] ?? null,
            account_number: $response['accountNumber'] ?? null,
            commercial_info_expiration: $response['commercialInfoExpiration'] ?? null,
            created_at: null,
            updated_at: null,
        );
    }

    public function buildFromAsaasListResponse(
        array $response,
        ?Organization $organization,
        ?AsaasEnvironment $environment = AsaasEnvironment::SANDBOX
    ): ?AsaasOrganization
    {
        return new AsaasOrganization(
            id: null,
            organization_id: $organization->id ?? null,
            organization: null,
            asaas_account_id: $response['id'] ?? null,
            asaas_api_key: $response['apiKey'] ?? null,
            asaas_wallet_id: $response['walletId'] ?? null,
            asaas_environment: null,
            is_active: true,
            last_sync_at: null,
            sync_errors: null,
            income_value: null,
            site: $response['site'] ?? null,
            name: $response['name'] ?? null,
            email: $response['email'] ?? null,
            login_email: $response['loginEmail'] ?? null,
            phone: $response['phone'] ?? null,
            mobile_phone: $response['mobilePhone'] ?? null,
            address: $response['address'] ?? null,
            address_number: $response['addressNumber'] ?? null,
            complement: $response['complement'] ?? null,
            province: $response['province'] ?? null,
            postal_code: $response['postalCode'] ?? null,
            cpf_cnpj: $response['cpfCnpj'] ?? null,
            birth_date: $response['birthDate'] ?? null,
            person_type: $response['personType'] ?? null,
            company_type: $response['companyType'] ?? null,
            city: $response['city'] ?? null,
            state: $response['state'] ?? null,
            country: $response['country'] ?? null,
            trading_name: $response['tradingName'] ?? null,
            account_number: $response['accountNumber'] ?? null,
            commercial_info_expiration: $response['commercialInfoExpiration'] ?? null,
            created_at: null,
            updated_at: null,
        );
    }
}
