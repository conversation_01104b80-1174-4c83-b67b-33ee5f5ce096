<?php

namespace App\Services\ASAAS\Factories;

use App\Services\ASAAS\Domains\AsaasClient;
use App\Services\ASAAS\Models\AsaasClient as AsaasClientModel;
use App\Factories\Inventory\ClientFactory;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class AsaasClientFactory
{
    public function __construct(
        private ClientFactory $clientFactory
    ) {}

    /**
     * Build AsaasClient domain from Eloquent model
     */
    public function buildFromModel(?AsaasClientModel $model, bool $with_client = true): ?AsaasClient
    {
        if (!$model) {
            return null;
        }
        $client = ($with_client) ? $this->clientFactory->buildFromModel($model->client) : null;
        return new AsaasClient(
            id: $model->id,
            client_id: $model->client_id,
            organization_id: $model->organization_id,
            asaas_customer_id: $model->asaas_customer_id,
            asaas_synced_at: $model->asaas_synced_at,
            asaas_sync_errors: $model->asaas_sync_errors,
            sync_status: $model->sync_status ?? 'pending',
            name: $model->name,
            email: $model->email,
            phone: $model->phone,
            mobile_phone: $model->mobile_phone,
            address: $model->address,
            address_number: $model->address_number,
            complement: $model->complement,
            province: $model->province,
            city_name: $model->city_name,
            state: $model->state,
            country: $model->country,
            postal_code: $model->postal_code,
            cpf_cnpj: $model->cpf_cnpj,
            person_type: $model->person_type,
            external_reference: $model->external_reference,
            notification_disabled: $model->notification_disabled ?? false,
            additional_emails: $model->additional_emails,
            observations: $model->observations,
            foreign_customer: $model->foreign_customer ?? false,
            deleted: $model->deleted ?? false,
            client: $client,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
            deleted_at: $model->deleted_at,
        );
    }

    /**
     * Build AsaasClient domain from Asaas API response
     */
    public function buildFromAsaasResponse(array $response, int $clientId, int $organizationId): AsaasClient
    {
        return new AsaasClient(
            id: null,
            client_id: $clientId,
            organization_id: $organizationId,
            asaas_customer_id: $response['id'],
            asaas_synced_at: now(),
            asaas_sync_errors: null,
            sync_status: 'synced',
            name: $response['name'] ?? null,
            email: $response['email'] ?? null,
            phone: $response['phone'] ?? null,
            mobile_phone: $response['mobilePhone'] ?? null,
            address: $response['address'] ?? null,
            address_number: $response['addressNumber'] ?? null,
            complement: $response['complement'] ?? null,
            province: $response['province'] ?? null,
            city_name: $response['cityName'] ?? null,
            state: $response['state'] ?? null,
            country: $response['country'] ?? null,
            postal_code: $response['postalCode'] ?? null,
            cpf_cnpj: $response['cpfCnpj'] ?? null,
            person_type: $response['personType'] ?? null,
            external_reference: $response['externalReference'] ?? null,
            notification_disabled: $response['notificationDisabled'] ?? false,
            additional_emails: $response['additionalEmails'] ?? null,
            observations: $response['observations'] ?? null,
            foreign_customer: $response['foreignCustomer'] ?? false,
            deleted: $response['deleted'] ?? false,
        );
    }

    /**
     * Build AsaasClient domain from Client domain
     */
    public function buildFromClient(\App\Domains\Inventory\Client $client, ?string $asaasCustomerId = null): AsaasClient
    {
        return new AsaasClient(
            id: null,
            client_id: $client->id,
            organization_id: $client->organization_id,
            asaas_customer_id: $asaasCustomerId ?? '',
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            name: $client->name,
            email: $client->email,
            phone: $client->phone,
            mobile_phone: null,
            address: $client->address,
            address_number: $client->number,
            complement: $client->complement,
            province: $client->neighborhood,
            city_name: null,
            state: null,
            country: 'Brasil',
            postal_code: $client->cep,
            cpf_cnpj: $client->cpf ?? $client->cnpj,
            person_type: $client->cpf ? 'FISICA' : ($client->cnpj ? 'JURIDICA' : null),
            external_reference: (string) $client->id,
            notification_disabled: false,
            additional_emails: null,
            observations: $client->description,
            foreign_customer: false,
            deleted: false,
            client: $client,
        );
    }

    /**
     * Build collection of AsaasClient domains from models
     */
    public function buildFromModels(Collection $models): array
    {
        $domains = [];
        foreach ($models as $model) {
            $domains[] = $this->buildFromModel($model);
        }
        return $domains;
    }

    /**
     * Build AsaasClient for creation with minimal data
     */
    public function buildForCreation(
        int $clientId,
        int $organizationId,
        string $name,
        string $email,
        string $cpfCnpj,
        ?string $phone = null,
        ?string $externalReference = null
    ): AsaasClient {
        return new AsaasClient(
            id: null,
            client_id: $clientId,
            organization_id: $organizationId,
            asaas_customer_id: '',
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            name: $name,
            email: $email,
            phone: $phone,
            mobile_phone: null,
            address: null,
            address_number: null,
            complement: null,
            province: null,
            city_name: null,
            state: null,
            country: 'Brasil',
            postal_code: null,
            cpf_cnpj: $cpfCnpj,
            person_type: strlen(preg_replace('/\D/', '', $cpfCnpj)) === 11 ? 'FISICA' : 'JURIDICA',
            external_reference: $externalReference,
            notification_disabled: false,
            additional_emails: null,
            observations: null,
            foreign_customer: false,
            deleted: false,
        );
    }

    /**
     * Build AsaasClient with sync error
     */
    public function buildWithSyncError(AsaasClient $asaasClient, array $errorData): AsaasClient
    {
        return new AsaasClient(
            id: $asaasClient->id,
            client_id: $asaasClient->client_id,
            organization_id: $asaasClient->organization_id,
            asaas_customer_id: $asaasClient->asaas_customer_id,
            asaas_synced_at: $asaasClient->asaas_synced_at,
            asaas_sync_errors: $errorData,
            sync_status: 'error',
            name: $asaasClient->name,
            email: $asaasClient->email,
            phone: $asaasClient->phone,
            mobile_phone: $asaasClient->mobile_phone,
            address: $asaasClient->address,
            address_number: $asaasClient->address_number,
            complement: $asaasClient->complement,
            province: $asaasClient->province,
            city_name: $asaasClient->city_name,
            state: $asaasClient->state,
            country: $asaasClient->country,
            postal_code: $asaasClient->postal_code,
            cpf_cnpj: $asaasClient->cpf_cnpj,
            person_type: $asaasClient->person_type,
            external_reference: $asaasClient->external_reference,
            notification_disabled: $asaasClient->notification_disabled,
            additional_emails: $asaasClient->additional_emails,
            observations: $asaasClient->observations,
            foreign_customer: $asaasClient->foreign_customer,
            deleted: $asaasClient->deleted,
            client: $asaasClient->client,
            created_at: $asaasClient->created_at,
            updated_at: $asaasClient->updated_at,
            deleted_at: $asaasClient->deleted_at,
        );
    }

    /**
     * Build AsaasClient marked as synced
     */
    public function buildAsSynced(AsaasClient $asaasClient, string $asaasCustomerId): AsaasClient
    {
        return new AsaasClient(
            id: $asaasClient->id,
            client_id: $asaasClient->client_id,
            organization_id: $asaasClient->organization_id,
            asaas_customer_id: $asaasCustomerId,
            asaas_synced_at: now(),
            asaas_sync_errors: null,
            sync_status: 'synced',
            name: $asaasClient->name,
            email: $asaasClient->email,
            phone: $asaasClient->phone,
            mobile_phone: $asaasClient->mobile_phone,
            address: $asaasClient->address,
            address_number: $asaasClient->address_number,
            complement: $asaasClient->complement,
            province: $asaasClient->province,
            city_name: $asaasClient->city_name,
            state: $asaasClient->state,
            country: $asaasClient->country,
            postal_code: $asaasClient->postal_code,
            cpf_cnpj: $asaasClient->cpf_cnpj,
            person_type: $asaasClient->person_type,
            external_reference: $asaasClient->external_reference,
            notification_disabled: $asaasClient->notification_disabled,
            additional_emails: $asaasClient->additional_emails,
            observations: $asaasClient->observations,
            foreign_customer: $asaasClient->foreign_customer,
            deleted: $asaasClient->deleted,
            client: $asaasClient->client,
            created_at: $asaasClient->created_at,
            updated_at: $asaasClient->updated_at,
            deleted_at: $asaasClient->deleted_at,
        );
    }
}
