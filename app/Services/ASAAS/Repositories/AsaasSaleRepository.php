<?php

namespace App\Services\ASAAS\Repositories;

use App\Services\ASAAS\Domains\AsaasSale;
use App\Services\ASAAS\Models\AsaasSale as AsaasSaleModel;
use App\Services\ASAAS\Factories\AsaasSaleFactory;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class AsaasSaleRepository
{
    public function __construct(
        private AsaasSaleFactory $asaasSaleFactory
    ) {}

    /**
     * Find AsaasSale by ID
     */
    public function findById(int $id): ?AsaasSale
    {
        $model = AsaasSaleModel::with(['sale', 'client'])->find($id);
        return $this->asaasSaleFactory->buildFromModel($model);
    }

    /**
     * Find AsaasSale by sale ID
     */
    public function findBySaleId(int $saleId): ?AsaasSale
    {
        $model = AsaasSaleModel::with(['sale', 'client'])
            ->where('sale_id', $saleId)
            ->first();
        return $this->asaasSaleFactory->buildFromModel($model);
    }

    /**
     * Find AsaasSale by ASAAS payment ID
     */
    public function findByAsaasPaymentId(string $asaasPaymentId): ?AsaasSale
    {
        $model = AsaasSaleModel::with(['sale', 'client'])
            ->where('asaas_payment_id', $asaasPaymentId)
            ->first();
        return $this->asaasSaleFactory->buildFromModel($model);
    }

    /**
     * Find AsaasSale by external reference
     */
    public function findByExternalReference(string $externalReference): ?AsaasSale
    {
        $model = AsaasSaleModel::with(['sale', 'client'])
            ->where('external_reference', $externalReference)
            ->first();
        return $this->asaasSaleFactory->buildFromModel($model);
    }

    /**
     * Get AsaasSales by organization ID
     */
    public function getByOrganizationId(int $organizationId): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->where('organization_id', $organizationId)
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get AsaasSales by client ID
     */
    public function getByClientId(int $clientId): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->where('client_id', $clientId)
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get AsaasSales by status
     */
    public function getByStatus(string $status): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->where('status', $status)
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get paid AsaasSales
     */
    public function getPaid(): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->whereIn('status', ['RECEIVED', 'RECEIVED_IN_CASH'])
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get pending AsaasSales
     */
    public function getPending(): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->where('status', 'PENDING')
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get overdue AsaasSales
     */
    public function getOverdue(): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->where('status', 'OVERDUE')
            ->orWhere(function ($query) {
                $query->where('status', 'PENDING')
                      ->where('due_date', '<', now());
            })
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get AsaasSales due today
     */
    public function getDueToday(): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->whereDate('due_date', today())
            ->whereIn('status', ['PENDING', 'CONFIRMED'])
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get AsaasSales due in date range
     */
    public function getDueInRange(Carbon $startDate, Carbon $endDate): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->whereBetween('due_date', [$startDate, $endDate])
            ->whereIn('status', ['PENDING', 'CONFIRMED'])
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get AsaasSales that need sync
     */
    public function getNeedingSync(): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->where(function ($query) {
                $query->whereNull('asaas_synced_at')
                      ->orWhere('asaas_synced_at', '<', now()->subHour());
            })
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get AsaasSales with sync errors
     */
    public function getWithSyncErrors(): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->whereNotNull('asaas_sync_errors')
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Get installment AsaasSales
     */
    public function getInstallments(): Collection
    {
        $models = AsaasSaleModel::with(['sale', 'client'])
            ->whereNotNull('installment_id')
            ->orWhere('installment_count', '>', 1)
            ->get();

        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }

    /**
     * Create new AsaasSale
     */
    public function store(AsaasSale $asaasSale): AsaasSale
    {
        $model = AsaasSaleModel::create($asaasSale->toStoreArray());
        $model->load(['sale', 'client']);
        return $this->asaasSaleFactory->buildFromModel($model);
    }

    public function create(AsaasSale $asaasSale): AsaasSale
    {
        return $this->store($asaasSale);
    }

    /**
     * Update AsaasSale
     */
    public function update(AsaasSale $asaasSale): AsaasSale
    {
        $model = AsaasSaleModel::findOrFail($asaasSale->id);
        $model->update($asaasSale->toUpdateArray());
        $model->load(['sale', 'client']);
        return $this->asaasSaleFactory->buildFromModel($model);
    }

    /**
     * Delete AsaasSale
     */
    public function delete(int $id): bool
    {
        return AsaasSaleModel::destroy($id) > 0;
    }

    /**
     * Mark as synced
     */
    public function markAsSynced(int $id, ?array $response = null): bool
    {
        return AsaasSaleModel::where('id', $id)->update([
            'asaas_synced_at' => now(),
            'sync_status' => 'synced',
            'asaas_sync_errors' => null,
            'asaas_webhook_data' => $response,
        ]) > 0;
    }

    /**
     * Mark sync error
     */
    public function markSyncError(int $id, array $errorData): bool
    {
        return AsaasSaleModel::where('id', $id)->update([
            'sync_status' => 'error',
            'asaas_sync_errors' => $errorData,
        ]) > 0;
    }

    /**
     * Update payment status
     */
    public function updateStatus(int $id, string $status, ?Carbon $paymentDate = null): bool
    {
        $updateData = ['status' => $status];

        if ($paymentDate) {
            $updateData['payment_date'] = $paymentDate;
        }

        return AsaasSaleModel::where('id', $id)->update($updateData) > 0;
    }

    /**
     * Update payment data from webhook
     */
    public function updateFromWebhook(int $id, array $webhookData): bool
    {
        return AsaasSaleModel::where('id', $id)->update([
            'asaas_webhook_data' => $webhookData,
            'asaas_synced_at' => now(),
            'sync_status' => 'synced',
        ]) > 0;
    }

    /**
     * Check if sale exists by sale_id
     */
    public function existsBySaleId(int $saleId): bool
    {
        return AsaasSaleModel::where('sale_id', $saleId)->exists();
    }

    /**
     * Check if ASAAS payment ID is already in use
     */
    public function existsByAsaasPaymentId(string $asaasPaymentId): bool
    {
        return AsaasSaleModel::where('asaas_payment_id', $asaasPaymentId)->exists();
    }

    /**
     * Get total value by status
     */
    public function getTotalValueByStatus(string $status): float
    {
        return AsaasSaleModel::where('status', $status)
            ->sum('net_value') ?? 0.0;
    }

    /**
     * Get count by status
     */
    public function countByStatus(string $status): int
    {
        return AsaasSaleModel::where('status', $status)->count();
    }

    /**
     * Get revenue for period
     */
    public function getRevenueForPeriod(Carbon $startDate, Carbon $endDate): float
    {
        return AsaasSaleModel::whereIn('status', ['RECEIVED', 'RECEIVED_IN_CASH'])
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->sum('net_value') ?? 0.0;
    }

    /**
     * Search AsaasSales
     */
    public function search(array $filters = [], int $limit = 50): Collection
    {
        $query = AsaasSaleModel::with(['sale', 'client']);

        if (isset($filters['organization_id'])) {
            $query->where('organization_id', $filters['organization_id']);
        }

        if (isset($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['billing_type'])) {
            $query->where('billing_type', $filters['billing_type']);
        }

        if (isset($filters['sync_status'])) {
            $query->where('sync_status', $filters['sync_status']);
        }

        if (isset($filters['due_date_from'])) {
            $query->where('due_date', '>=', $filters['due_date_from']);
        }

        if (isset($filters['due_date_to'])) {
            $query->where('due_date', '<=', $filters['due_date_to']);
        }

        if (isset($filters['payment_date_from'])) {
            $query->where('payment_date', '>=', $filters['payment_date_from']);
        }

        if (isset($filters['payment_date_to'])) {
            $query->where('payment_date', '<=', $filters['payment_date_to']);
        }

        if (isset($filters['has_asaas_payment_id'])) {
            if ($filters['has_asaas_payment_id']) {
                $query->whereNotNull('asaas_payment_id');
            } else {
                $query->whereNull('asaas_payment_id');
            }
        }

        if (isset($filters['has_sync_errors'])) {
            if ($filters['has_sync_errors']) {
                $query->whereNotNull('asaas_sync_errors');
            } else {
                $query->whereNull('asaas_sync_errors');
            }
        }

        if (isset($filters['is_installment'])) {
            if ($filters['is_installment']) {
                $query->where('installment_count', '>', 1);
            } else {
                $query->where(function ($q) {
                    $q->whereNull('installment_count')
                      ->orWhere('installment_count', '<=', 1);
                });
            }
        }

        if (isset($filters['value_min'])) {
            $query->where('value', '>=', $filters['value_min']);
        }

        if (isset($filters['value_max'])) {
            $query->where('value', '<=', $filters['value_max']);
        }

        $models = $query->limit($limit)->get();
        return collect($this->asaasSaleFactory->buildFromModels($models->toArray()));
    }
}
