<?php

namespace App\UseCases\Import;

use App\Domains\Imports\Import;
use App\Repositories\ImportRepository;

class GetAll
{
    private ImportRepository $importRepository;

    public function __construct(ImportRepository $importRepository) {
        $this->importRepository = $importRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->importRepository->fetchFromOrganization(
            request()->user()->organization_id
        );
    }
}
