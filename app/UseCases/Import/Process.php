<?php

namespace App\UseCases\Import;

use App\Domains\Imports\Import;
use App\Factories\ImportFactory;
use App\Repositories\ImportRepository;
use Exception;
use Illuminate\Support\Facades\DB;

class Process
{
    private const FILE_STRING = "file";

    private ImportRepository $importRepository;
    private ImportFactory $importFactory;

    public function __construct(ImportRepository $importRepository, ImportFactory $importFactory) {
        $this->importRepository = $importRepository;
        $this->importFactory = $importFactory;
    }

    /**
     * @param Import $import
     * @return Import
     * @throws Exception
     */
    public function perform(Import $import) : Import {
        DB::beginTransaction();

        $organization_id = request()->user()->organization_id;
        $this->validate($import, $organization_id);

        $import->process();

        $import->is_processed = true;

        $this->importRepository->update($import, $organization_id);

        DB::commit();

        return $import;
    }

    /**
     * @throws Exception
     */
    private function validate(Import $import, int $organization_id) : void {
        if($import->organization_id !== $organization_id){
            throw new Exception(
                "This import don't belong to this organization." ,
                403
            );
        }
        if($import->is_processed && !$import->reprocess){
            throw new Exception(
                "This import is already processed." ,
                403
            );
        }
    }
}
