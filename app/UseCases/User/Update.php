<?php

namespace App\UseCases\User;

use App\Domains\User;
use App\Factories\UserFactory;
use App\Http\Requests\User\UpdateRequest;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private UserRepository $userRepository;
    private UserFactory $userFactory;

    public function __construct(UserRepository $userRepository, UserFactory $userFactory) {
        $this->userRepository = $userRepository;
        $this->userFactory = $userFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return User
     */
    public function perform(UpdateRequest $request, int $id) : User {
        DB::beginTransaction();
        $domain = $this->userFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $user = $this->userRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $user;
    }
}
