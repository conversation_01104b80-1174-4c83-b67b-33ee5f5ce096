<?php

namespace App\UseCases\User;

use App\Repositories\UserRepository;
use Exception;

class Delete
{
    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository) {
        $this->userRepository = $userRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $user = $this->userRepository->fetchById($id);

        if($user->organization_id !== $organization_id){
            throw new Exception(
                "This user don't belong to this organization." ,
                403
            );
        }

        return $this->userRepository->delete($user);
    }
}
