<?php

namespace App\UseCases\Subscription;

use App\Domains\Subscription;
use App\Http\Requests\Subscription\GrantCourtesyRequest;
use App\Repositories\SubscriptionRepository;
use App\Repositories\OrganizationRepository;
use App\Factories\SubscriptionFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GrantCourtesy
{
    private SubscriptionRepository $subscriptionRepository;
    private OrganizationRepository $organizationRepository;
    private SubscriptionFactory $subscriptionFactory;

    public function __construct(
        SubscriptionRepository $subscriptionRepository,
        OrganizationRepository $organizationRepository,
        SubscriptionFactory $subscriptionFactory
    ) {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->organizationRepository = $organizationRepository;
        $this->subscriptionFactory = $subscriptionFactory;
    }

    public function perform(
        GrantCourtesyRequest $request
    ): Subscription {
        DB::beginTransaction();

        try {
            $organizationId = $request->input('organization_id');
            $expiresAt = $request->input('expires_at');
            $reason = $request->input('reason');

            $organization = $this->organizationRepository->fetchById($organizationId);
            if (!$organization) {
                throw new \InvalidArgumentException('Organization not found');
            }

            $existingSubscription = $this->subscriptionRepository->findByOrganizationId($organizationId);

            $subscription = $this->subscriptionFactory->buildFromStoreArray(
                request()->all(), $organization
            );

            if ($existingSubscription && $existingSubscription->isActive()) {
                $subscription->id = $existingSubscription->id;
                $subscription->value = $existingSubscription->value;
                $subscription = $this->subscriptionRepository->update($subscription);
            } else {
                $subscription = $this->subscriptionRepository->store($subscription);
            }

            DB::commit();

            Log::info('Courtesy granted successfully', [
                'organization_id' => $organizationId,
                'subscription_id' => $subscription->id,
                'expires_at' => $expiresAt,
                'reason' => $reason,
            ]);

            return $subscription;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to grant courtesy', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
