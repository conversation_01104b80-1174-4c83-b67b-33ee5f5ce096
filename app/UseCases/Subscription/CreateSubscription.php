<?php

namespace App\UseCases\Subscription;

use App\Domains\Subscription;
use App\Repositories\SubscriptionRepository;
use App\Factories\SubscriptionFactory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreateSubscription
{
    private SubscriptionRepository $subscriptionRepository;
    private SubscriptionFactory $subscriptionFactory;

    public function __construct(
        SubscriptionRepository $subscriptionRepository,
        SubscriptionFactory $subscriptionFactory
    ) {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->subscriptionFactory = $subscriptionFactory;
    }

    public function perform(int $organizationId, array $data): Subscription
    {
        DB::beginTransaction();

        try {
            $subscription = $this->subscriptionFactory->buildFromStoreArray([
                'organization_id' => $organizationId,
                'type' => $data['type'] ?? 'trial',
                'status' => $data['status'] ?? 'active',
                'value' => $data['value'] ?? null,
                'started_at' => $data['started_at'] ?? now(),
                'expires_at' => $data['expires_at'] ?? null,
                'is_courtesy' => $data['is_courtesy'] ?? false,
                'courtesy_expires_at' => $data['courtesy_expires_at'] ?? null,
                'courtesy_reason' => $data['courtesy_reason'] ?? null,
                'allowed_modules' => $data['allowed_modules'] ?? null,
            ]);

            $subscription = $this->subscriptionRepository->store($subscription);

            DB::commit();

            Log::info('Subscription created successfully', [
                'subscription_id' => $subscription->id,
                'organization_id' => $organizationId,
                'type' => $subscription->type,
            ]);

            return $subscription;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to create subscription', [
                'organization_id' => $organizationId,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }
}
