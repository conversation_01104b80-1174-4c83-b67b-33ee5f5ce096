<?php

namespace App\UseCases\ChatBot\Button;

use App\Domains\ChatBot\Button;
use App\Factories\ChatBot\ButtonFactory;
use App\Http\Requests\Button\StoreRequest;
use App\Repositories\ButtonRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ButtonRepository $buttonRepository;
    private ButtonFactory $buttonFactory;

    public function __construct(ButtonRepository $buttonRepository, ButtonFactory $buttonFactory) {
        $this->buttonRepository = $buttonRepository;
        $this->buttonFactory = $buttonFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Button
     */
    public function perform(StoreRequest $request) : Button {
        DB::beginTransaction();

        $domain = $this->buttonFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $button = $this->buttonRepository->store($domain);

        DB::commit();

        return $button;
    }
}
