<?php

namespace App\UseCases\ChatBot\Button;

use App\Domains\ChatBot\Button;
use App\Factories\ChatBot\ButtonFactory;
use App\Http\Requests\Button\UpdateRequest;
use App\Repositories\ButtonRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ButtonRepository $buttonRepository;
    private ButtonFactory $buttonFactory;

    public function __construct(ButtonRepository $buttonRepository, ButtonFactory $buttonFactory) {
        $this->buttonRepository = $buttonRepository;
        $this->buttonFactory = $buttonFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Button
     */
    public function perform(UpdateRequest $request, int $id) : Button {
        DB::beginTransaction();

        $domain = $this->buttonFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $button = $this->buttonRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $button;
    }
}
