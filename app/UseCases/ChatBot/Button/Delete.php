<?php

namespace App\UseCases\ChatBot\Button;

use App\Repositories\ButtonRepository;
use Exception;

class Delete
{
    private ButtonRepository $buttonRepository;

    public function __construct(ButtonRepository $buttonRepository) {
        $this->buttonRepository = $buttonRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $button = $this->buttonRepository->fetchById($id);

        if($button->organization_id !== $organization_id){
            throw new Exception(
                "This button don't belong to this organization." ,
                403
            );
        }

        return $this->buttonRepository->delete($button);
    }
}
