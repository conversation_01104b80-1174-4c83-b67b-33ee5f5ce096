<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Domains\ChatBot\Campaign;
use App\Repositories\CampaignRepository;
use App\UseCases\ChatBot\Campaign\GenerateMessages;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class LaunchCampaign
{
    private CampaignRepository $campaignRepository;
    private GenerateMessages $generateMessages;

    public function __construct(
        CampaignRepository $campaignRepository,
        GenerateMessages $generateMessages
    ) {
        $this->campaignRepository = $campaignRepository;
        $this->generateMessages = $generateMessages;
    }

    /**
     * Launch a campaign - generate messages and mark as sending
     *
     * @param int $campaign_id
     * @param bool $resend
     * @return Campaign
     * @throws Exception
     */
    public function perform(int $campaign_id, bool $resend = false): Campaign
    {
        DB::beginTransaction();

        try {
            $campaign = $this->campaignRepository->fetchFullById($campaign_id);
            $organization_id = request()->user()->organization_id;

            if ($organization_id !== $campaign->organization_id) {
                throw new Exception("This campaign doesn't belong to this organization.", 403);
            }
            if(count($campaign->clients) === 0){
                throw new Exception("Campaign can't be launched because it has no client.", 400);
            }

            if ($campaign->is_sent && !$resend) {
                throw new Exception("Campaign has already been sent.", 400);
            }

            if ($campaign->is_sending && !$resend) {
                throw new Exception("Campaign is already being sent.", 400);
            }

            $this->generateMessages->perform($campaign_id);

            $campaign->is_sending = true;
            $campaign->sent_at = Carbon::now();

            $this->campaignRepository->update($campaign, $organization_id);

            DB::commit();

            return $campaign;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
