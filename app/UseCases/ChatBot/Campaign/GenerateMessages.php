<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Enums\MessageStatus;
use App\Factories\ChatBot\MessageFactory;
use App\Repositories\CampaignRepository;
use App\Repositories\MessageRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class GenerateMessages
{
    private CampaignRepository $campaignRepository;
    private MessageRepository $messageRepository;
    private MessageFactory $messageFactory;

    public function __construct(
        CampaignRepository $campaignRepository,
        MessageRepository $messageRepository,
        MessageFactory $messageFactory
    ) {
        $this->campaignRepository = $campaignRepository;
        $this->messageRepository = $messageRepository;
        $this->messageFactory = $messageFactory;
    }

    /**
     * Generate messages for a campaign
     *
     * @param int $campaign_id
     * @param bool $is_draft
     * @param bool $is_direct_message
     * @return array
     * @throws \Exception
     */
    public function perform(
        int $campaign_id,
        bool $is_draft = false,
        bool $is_direct_message = false
    ): array {
        DB::beginTransaction();

        $campaign = $this->campaignRepository->fetchFullById($campaign_id);
        $organization_id = request()->user()->organization_id;
        if ($organization_id !== $campaign->organization_id) {
            throw new \Exception("This campaign don't belong to this organization.", 403);
        }

        $messages = [];

        $should_be_direct = $campaign->is_direct_message || $is_direct_message;

        foreach ($campaign->clients ?? [] as $client) {
            $has_message_already = $this->messageRepository->fetchByCampaignAndClient($campaign_id, $client->id);
            if ($has_message_already) {
                $has_message_already->launch($should_be_direct);
                $messages[] = $this->messageRepository->update($has_message_already, $organization_id);
                continue;
            }
            $messageDomain = $this->messageFactory->buildFromGeneration(
                $campaign,
                $client,
                $organization_id,
                $is_draft,
                $should_be_direct
            );
            $messages[] = $this->messageRepository->store($messageDomain);
        }

        $campaign->message_count = count($messages);

        $campaign->is_sending = !($is_draft) && ((!($campaign->is_scheduled) || Carbon::now()->gte($campaign->scheduled_at)));

        $this->campaignRepository->update($campaign, $organization_id);

        DB::commit();

        return $messages;
    }
}
