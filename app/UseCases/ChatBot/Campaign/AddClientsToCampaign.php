<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Domains\ChatBot\Campaign;
use App\Repositories\CampaignRepository;
use App\Models\Campaign as CampaignModel;
use Illuminate\Support\Facades\DB;
use Exception;

class AddClientsToCampaign
{
    private CampaignRepository $campaignRepository;

    public function __construct(CampaignRepository $campaignRepository)
    {
        $this->campaignRepository = $campaignRepository;
    }

    /**
     * Add clients to a campaign
     *
     * @param int $campaign_id
     * @param array $client_ids
     * @return Campaign
     * @throws Exception
     */
    public function perform(int $campaign_id, array $client_ids): Campaign
    {
        DB::beginTransaction();

        try {
            $campaign = $this->campaignRepository->fetchById($campaign_id);
            $organization_id = request()->user()->organization_id;

            if ($organization_id !== $campaign->organization_id) {
                throw new Exception("This campaign doesn't belong to this organization.", 403);
            }

            if ($campaign->is_sent) {
                throw new Exception("Cannot modify campaign that has already been sent.", 400);
            }

            if ($campaign->is_sending) {
                throw new Exception("Cannot modify campaign that is currently being sent.", 400);
            }

            $this->campaignRepository->addClients($campaign_id, $client_ids);

            DB::commit();

            return $this->campaignRepository->fetchFullById($campaign_id);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Add single client to campaign
     *
     * @param int $campaign_id
     * @param int $client_id
     * @return Campaign
     * @throws Exception
     */
    public function addSingleClient(int $campaign_id, int $client_id): Campaign
    {
        return $this->perform($campaign_id, [$client_id]);
    }
}
