<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Repositories\CampaignRepository;
use App\Repositories\CampaignStatusHistoryRepository;
use App\Helpers\DBLog;

class GetStatusHistory
{
    private CampaignRepository $campaignRepository;
    private CampaignStatusHistoryRepository $statusHistoryRepository;

    public function __construct(
        CampaignRepository $campaignRepository,
        CampaignStatusHistoryRepository $statusHistoryRepository
    ) {
        $this->campaignRepository = $campaignRepository;
        $this->statusHistoryRepository = $statusHistoryRepository;
    }

    public function execute(int $campaign_id, int $organization_id, int $limit = 50): array
    {
        try {
            // Validate campaign exists and belongs to organization
            $campaign = $this->campaignRepository->fetchById($campaign_id);
            
            if ($campaign->organization_id !== $organization_id) {
                throw new \InvalidArgumentException("Campaign not found or access denied");
            }

            // Fetch status history
            $history = $this->statusHistoryRepository->fetchByCampaign($campaign_id, $limit);

            return [
                'campaign_id' => $campaign_id,
                'campaign_name' => $campaign->name,
                'current_status' => $campaign->getCurrentStatus()->value,
                'current_status_label' => $campaign->getCurrentStatus()->label(),
                'history' => array_map(fn($item) => $item->toArray(), $history),
                'total_changes' => $this->statusHistoryRepository->getStatusChangesCount($campaign_id)
            ];

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "Campaign::GetStatusHistory",
                $organization_id,
                null,
                ['campaign_id' => $campaign_id]
            );
            throw $e;
        }
    }

    public function getTimeline(int $campaign_id, int $organization_id): array
    {
        try {
            // Validate campaign exists and belongs to organization
            $campaign = $this->campaignRepository->fetchById($campaign_id);
            
            if ($campaign->organization_id !== $organization_id) {
                throw new \InvalidArgumentException("Campaign not found or access denied");
            }

            // Fetch complete timeline
            $timeline = $this->statusHistoryRepository->getStatusTimeline($campaign_id);

            return [
                'campaign_id' => $campaign_id,
                'campaign_name' => $campaign->name,
                'current_status' => $campaign->getCurrentStatus()->value,
                'current_status_label' => $campaign->getCurrentStatus()->label(),
                'timeline' => array_map(function($item) {
                    $data = $item->toArray();
                    $data['change_direction'] = $item->getChangeDirection();
                    return $data;
                }, $timeline)
            ];

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "Campaign::GetStatusHistory::Timeline",
                $organization_id,
                null,
                ['campaign_id' => $campaign_id]
            );
            throw $e;
        }
    }
}
