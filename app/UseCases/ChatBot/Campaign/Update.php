<?php

namespace App\UseCases\ChatBot\Campaign;

use App\Domains\ChatBot\Campaign;
use App\Factories\ChatBot\CampaignFactory;
use App\Http\Requests\Campaign\UpdateRequest;
use App\Repositories\CampaignRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private CampaignRepository $campaignRepository;
    private CampaignFactory $campaignFactory;

    public function __construct(CampaignRepository $campaignRepository, CampaignFactory $campaignFactory) {
        $this->campaignRepository = $campaignRepository;
        $this->campaignFactory = $campaignFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Campaign
     */
    public function perform(UpdateRequest $request, int $id) : Campaign {
        DB::beginTransaction();

        $domain = $this->campaignFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $campaign = $this->campaignRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $campaign;
    }
}
