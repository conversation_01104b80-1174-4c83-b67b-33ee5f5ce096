<?php

namespace App\UseCases\ChatBot\Lead;

use App\Domains\ChatBot\Lead;
use App\Factories\ChatBot\LeadFactory;
use App\Repositories\ClientRepository;
use App\Repositories\LeadRepository;

class CreateLead
{
    protected LeadRepository $leadRepository;
    protected ClientRepository $clientRepository;
    protected LeadFactory $leadFactory;

    public function __construct(
        LeadRepository $leadRepository,
        ClientRepository $clientRepository,
        LeadFactory $leadFactory
    ) {
        $this->leadRepository = $leadRepository;
        $this->clientRepository = $clientRepository;
        $this->leadFactory = $leadFactory;
    }

    /**
     * Create a new lead
     */
    public function perform(array $leadData): Lead
    {
        // Validate required fields
        $this->validateLeadData($leadData);

        // Verify client exists
        $client = $this->clientRepository->findById($leadData['client_id']);
        if (!$client) {
            throw new \Exception('Client not found');
        }

        // Create lead domain
        $lead = $this->leadFactory->buildFromArray($leadData);

        // Save lead
        return $this->leadRepository->save($lead);
    }

    /**
     * Create lead from ChatBot interaction
     */
    public function performFromChatBot(
        int $organizationId,
        int $clientId,
        array $conversationData = [],
        string $source = 'chatbot_whatsapp'
    ): Lead {
        // Verify client exists
        $client = $this->clientRepository->findById($clientId);
        if (!$client) {
            throw new \Exception('Client not found');
        }

        // Create lead for chatbot
        $lead = $this->leadFactory->buildForChatBot(
            $organizationId,
            $clientId,
            $conversationData,
            $source
        );

        // Save lead
        return $this->leadRepository->save($lead);
    }

    /**
     * Validate lead data
     */
    protected function validateLeadData(array $leadData): void
    {
        $required = ['organization_id', 'client_id'];

        foreach ($required as $field) {
            if (!isset($leadData[$field]) || empty($leadData[$field])) {
                throw new \Exception("Required field missing: {$field}");
            }
        }

        // Validate status if provided
        if (isset($leadData['status'])) {
            $validStatuses = ['new', 'contacted', 'qualified', 'proposal', 'won', 'lost'];
            if (!in_array($leadData['status'], $validStatuses)) {
                throw new \Exception("Invalid status: {$leadData['status']}");
            }
        }

        // Validate priority if provided
        if (isset($leadData['priority'])) {
            $validPriorities = ['low', 'medium', 'high', 'urgent'];
            if (!in_array($leadData['priority'], $validPriorities)) {
                throw new \Exception("Invalid priority: {$leadData['priority']}");
            }
        }

        // Validate estimated_value if provided
        if (isset($leadData['estimated_value']) && !is_numeric($leadData['estimated_value'])) {
            throw new \Exception("Estimated value must be numeric");
        }
    }
}
