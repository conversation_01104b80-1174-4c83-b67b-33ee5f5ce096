<?php

namespace App\UseCases\ChatBot\Template;

use App\Http\Requests\Template\UpdateRequest;
use App\Domains\ChatBot\Template;
use App\Factories\ChatBot\TemplateFactory;
use App\Repositories\TemplateRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private TemplateRepository $templateRepository;
    private TemplateFactory $templateFactory;

    public function __construct(TemplateRepository $templateRepository, TemplateFactory $templateFactory) {
        $this->templateRepository = $templateRepository;
        $this->templateFactory = $templateFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Template
     */
    public function perform(UpdateRequest $request, int $id) : Template {
        DB::beginTransaction();

        $domain = $this->templateFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $template = $this->templateRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $template;
    }
}
