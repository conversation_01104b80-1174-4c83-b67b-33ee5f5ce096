<?php

namespace App\UseCases\ChatBot\Client;

use App\Domains\ChatBot\Message;
use App\Domains\Organization;
use App\Repositories\ClientRepository;

class UpdateWhatsAppFromStatusUpdate
{
    private ClientRepository $clientRepository;

    public function __construct(ClientRepository $clientRepository)
    {
        $this->clientRepository = $clientRepository;
    }

    public function perform(
        array $statusData,
        Organization $organization,
        Message $message
    ): array {
        try {
            // 1. Verificar se a message tem um client
            if (!$message->client_id) {
                return [
                    'success' => false,
                    'reason' => 'Message has no client_id',
                    'processed' => 0
                ];
            }

            // 2. Buscar o client
            try {
                $client = $message->client ?? $this->clientRepository->fetchById($message->client_id);

                // Verificar se o client pertence à organização
                if ($client->organization_id !== $organization->id) {
                    return [
                        'success' => false,
                        'reason' => 'Client does not belong to organization',
                        'processed' => 0
                    ];
                }
            } catch (\Exception $e) {
                return [
                    'success' => false,
                    'reason' => 'Client not found',
                    'processed' => 0
                ];
            }

            // 3. Verificar se o client já tem whatsapp_from preenchido
            if ($client->whatsapp_from) {
                return [
                    'success' => true,
                    'reason' => 'Client already has whatsapp_from',
                    'processed' => 0,
                    'whatsapp_from' => $client->whatsapp_from
                ];
            }

            // 4. Extrair recipient_id do status data
            $recipientId = $statusData['recipient_id'] ?? null;
            if (!$recipientId) {
                return [
                    'success' => false,
                    'reason' => 'No recipient_id found in status data',
                    'processed' => 0
                ];
            }

            // 5. Atualizar o client com o whatsapp_from
            $client->updateWhatsAppFrom($recipientId);

            // 6. Salvar no banco
            $this->clientRepository->update($client, $organization->id);

            return [
                'success' => true,
                'reason' => 'Client whatsapp_from updated successfully',
                'processed' => 1,
                'client_id' => $client->id,
                'whatsapp_from' => $recipientId,
                'previous_whatsapp_from' => null
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'reason' => 'Error updating client whatsapp_from: ' . $e->getMessage(),
                'processed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
