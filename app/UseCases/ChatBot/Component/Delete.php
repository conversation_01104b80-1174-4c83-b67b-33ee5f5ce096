<?php

namespace App\UseCases\ChatBot\Component;

use App\Repositories\ComponentRepository;
use Exception;

class Delete
{
    private ComponentRepository $componentRepository;

    public function __construct(ComponentRepository $componentRepository) {
        $this->componentRepository = $componentRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $component = $this->componentRepository->fetchById($id);

        if($component->organization_id !== $organization_id){
            throw new Exception(
                "This component doesn't belong to this organization." ,
                403
            );
        }

        return $this->componentRepository->delete($component);
    }
}
