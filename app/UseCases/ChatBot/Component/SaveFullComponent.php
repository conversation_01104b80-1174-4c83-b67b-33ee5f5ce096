<?php

namespace App\UseCases\ChatBot\Component;

use App\Domains\ChatBot\Step;
use App\Factories\ChatBot\ComponentFactory;
use App\Helpers\DBLog;
use App\Repositories\ComponentRepository;
use App\UseCases\ChatBot\Button\SaveFullButton;

class SaveFullComponent
{
    private ComponentRepository $componentRepository;
    private ComponentFactory $componentFactory;

    public function __construct(ComponentRepository $componentRepository, ComponentFactory $componentFactory) {
        $this->componentRepository = $componentRepository;
        $this->componentFactory = $componentFactory;
    }

    /**
     * @param array $component
     * @param Step $step
     * @return void
     */
    public function perform(array $component, Step $step) : void {
        try {
            $organization_id = request()->user()->organization_id ?? null;
            $user_id = request()->user()->id ?? null;
            $json = json_encode($component);
            $id = $component['id'] ?? null;

            $domain = $this->componentFactory->buildFromSaveFullComponent(
                $component,
                $step,
                $json,
                $id
            );
            $componentDomain = $this->componentRepository->save($domain, $organization_id);

            /** @var SaveFullButton $useCase */
            $useCase = app()->make(SaveFullButton::class);
            $buttons = $component['buttons'] ?? (($component['action'] ?? false) ? ($component['action']['buttons'] ?? []) : []);
            foreach ($buttons as $button) {
                $useCase->perform($button, $componentDomain);
            }

        } catch (\Throwable $exception) {
            DBLog::logError(
                $exception->getMessage() ?? null,
                "ChatBot::SaveFullComponent",
                $organization_id ?? null,
                $user_id ?? null,
                $component
            );
            throw $exception;
        }
    }
}
