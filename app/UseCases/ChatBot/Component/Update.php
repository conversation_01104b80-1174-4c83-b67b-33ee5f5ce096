<?php

namespace App\UseCases\ChatBot\Component;

use App\Domains\ChatBot\Component;
use App\Factories\ChatBot\ComponentFactory;
use App\Http\Requests\Component\UpdateRequest;
use App\Repositories\ComponentRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ComponentRepository $componentRepository;
    private ComponentFactory $componentFactory;

    public function __construct(ComponentRepository $componentRepository, ComponentFactory $componentFactory) {
        $this->componentRepository = $componentRepository;
        $this->componentFactory = $componentFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Component
     */
    public function perform(UpdateRequest $request, int $id) : Component {
        DB::beginTransaction();

        $domain = $this->componentFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $component = $this->componentRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $component;
    }
}
