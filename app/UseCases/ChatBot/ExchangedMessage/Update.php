<?php

namespace App\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\ExchangedMessage;
use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Http\Requests\ExchangedMessage\UpdateRequest;
use App\Repositories\ExchangedMessageRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ExchangedMessageRepository $exchangedMessageRepository;
    private ExchangedMessageFactory $exchangedMessageFactory;

    public function __construct(
        ExchangedMessageRepository $exchangedMessageRepository,
        ExchangedMessageFactory $exchangedMessageFactory
    ) {
        $this->exchangedMessageRepository = $exchangedMessageRepository;
        $this->exchangedMessageFactory = $exchangedMessageFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return ExchangedMessage
     */
    public function perform(UpdateRequest $request, int $id): ExchangedMessage
    {
        DB::beginTransaction();

        $domain = $this->exchangedMessageFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $exchangedMessage = $this->exchangedMessageRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $exchangedMessage;
    }
}
