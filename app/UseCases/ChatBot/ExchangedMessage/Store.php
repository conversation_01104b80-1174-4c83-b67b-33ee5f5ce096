<?php

namespace App\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\ExchangedMessage;
use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Http\Requests\ExchangedMessage\StoreRequest;
use App\Repositories\ExchangedMessageRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ExchangedMessageRepository $exchangedMessageRepository;
    private ExchangedMessageFactory $exchangedMessageFactory;

    public function __construct(
        ExchangedMessageRepository $exchangedMessageRepository,
        ExchangedMessageFactory $exchangedMessageFactory
    ) {
        $this->exchangedMessageRepository = $exchangedMessageRepository;
        $this->exchangedMessageFactory = $exchangedMessageFactory;
    }

    /**
     * @param StoreRequest $request
     * @return ExchangedMessage
     */
    public function perform(StoreRequest $request): ExchangedMessage
    {
        DB::beginTransaction();

        $domain = $this->exchangedMessageFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $exchangedMessage = $this->exchangedMessageRepository->store($domain);

        DB::commit();

        return $exchangedMessage;
    }
}
