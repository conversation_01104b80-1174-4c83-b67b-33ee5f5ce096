<?php

namespace App\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\ExchangedMessage;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use App\Domains\Organization;
use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Repositories\ClientRepository;
use App\Repositories\ExchangedMessageRepository;
use App\Helpers\DBLog;
use Carbon\Carbon;

class SaveFromWebhook
{
    private ExchangedMessageFactory $exchangedMessageFactory;
    private ExchangedMessageRepository $exchangedMessageRepository;
    private ClientRepository $clientRepository;

    public function __construct(
        ExchangedMessageFactory $exchangedMessageFactory,
        ExchangedMessageRepository $exchangedMessageRepository,
        ClientRepository $clientRepository
    ) {
        $this->exchangedMessageFactory = $exchangedMessageFactory;
        $this->exchangedMessageRepository = $exchangedMessageRepository;
        $this->clientRepository = $clientRepository;
    }

    /**
     * Process webhook data and save as ExchangedMessage if it's a valid inbound message
     *
     * @param array $webhookData
     * @param Organization $organization
     * @param PhoneNumber $phoneNumber
     * @param int|null $webhookLogId
     * @return array
     */
    public function perform(
        array $webhookData,
        Organization $organization,
        PhoneNumber $phoneNumber,
        ?int $webhookLogId = null
    ): array {
        try {
            // 1. Verificar se devemos processar este webhook
            if (!$this->shouldProcessWebhook($webhookData)) {
                return [
                    'success' => false,
                    'reason' => 'Webhook does not contain processable message data',
                    'processed' => 0
                ];
            }

            $messageData = $webhookData['message'] ?? [];
            $fromNumber = $messageData['from'] ?? null;

            if (!$fromNumber) {
                return [
                    'success' => false,
                    'reason' => 'No sender phone number found in webhook',
                    'processed' => 0
                ];
            }

            // 2. Buscar ou criar cliente baseado no número de telefone
            $client = $this->findOrCreateClient($fromNumber, $organization->id, $messageData);

            if (!$client) {
                return [
                    'success' => false,
                    'reason' => 'Could not find or create client',
                    'processed' => 0
                ];
            }

            // 3. Criar ExchangedMessage usando a factory
            $exchangedMessage = $this->exchangedMessageFactory->buildFromWebhookInbound(
                $webhookData,
                $phoneNumber,
                $client,
                null, // conversation - pode ser implementado depois
                $webhookLogId
            );

            // 4. Salvar no repository
            $savedMessage = $this->exchangedMessageRepository->store($exchangedMessage);

            DBLog::log(
                "ExchangedMessage created from webhook",
                "SaveFromWebhook::perform",
                $organization->id,
                null,
                [
                    'exchanged_message_id' => $savedMessage->id,
                    'client_id' => $client->id,
                    'phone_number_id' => $phoneNumber->id,
                    'message_type' => $messageData['type'] ?? 'unknown',
                    'webhook_log_id' => $webhookLogId
                ]
            );

            return [
                'success' => true,
                'reason' => 'Message processed and saved successfully',
                'processed' => 1,
                'exchanged_message_id' => $savedMessage->id,
                'client_id' => $client->id
            ];

        } catch (\Exception $e) {
            DBLog::logError(
                "Error processing webhook for ExchangedMessage: " . $e->getMessage(),
                "SaveFromWebhook::perform",
                $organization->id,
                null,
                [
                    'webhook_data' => $webhookData,
                    'phone_number_id' => $phoneNumber->id,
                    'webhook_log_id' => $webhookLogId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            );

            return [
                'success' => false,
                'reason' => 'Error processing webhook: ' . $e->getMessage(),
                'processed' => 0
            ];
        }
    }

    /**
     * Verificar se o webhook deve ser processado
     */
    private function shouldProcessWebhook(array $webhookData): bool
    {
        // Verificar se tem dados de mensagem
        $messageData = $webhookData['message'] ?? [];

        if (empty($messageData)) {
            return false;
        }

        // Verificar se tem os campos obrigatórios
        $requiredFields = ['id', 'from', 'timestamp', 'type'];
        foreach ($requiredFields as $field) {
            if (empty($messageData[$field])) {
                return false;
            }
        }

        // Verificar se é uma mensagem de entrada (não de status)
        $type = $messageData['type'] ?? '';
        $validTypes = ['text', 'button', 'interactive', 'image', 'video', 'audio', 'document', 'location', 'contacts'];

        return in_array($type, $validTypes);
    }

    /**
     * Buscar cliente por número de telefone com variações
     */
    private function findOrCreateClient(string $phoneNumber, int $organizationId, array $messageData): ?Client
    {
        // Tentar buscar cliente existente
        $client = $this->findClientByPhone($phoneNumber, $organizationId);

        if ($client) {
            return $client;
        }

        // Se não encontrou, criar novo cliente
        return $this->createNewClient($phoneNumber, $organizationId, $messageData);
    }

    /**
     * Buscar cliente por telefone com variações
     */
    private function findClientByPhone(string $phoneNumber, int $organizationId): ?Client
    {
        // 1. Tentar busca por whatsapp_from primeiro (mais eficiente e preciso)
        $client = $this->clientRepository->fetchByWhatsAppFrom($phoneNumber, $organizationId);

        if ($client) {
            return $client;
        }

        // 2. Tentar busca exata por phone
        $client = $this->clientRepository->findByPhoneAndOrganization($phoneNumber, $organizationId);

        if ($client) {
            // Atualizar o whatsapp_from para futuras buscas mais eficientes
            $client->whatsapp_from = $phoneNumber;
            $this->clientRepository->save($client);
            return $client;
        }

        // 3. Tentar com variações do número
        $phoneVariations = $this->generatePhoneVariations($phoneNumber);

        foreach ($phoneVariations as $variation) {
            $client = $this->clientRepository->findByPhoneAndOrganization($variation, $organizationId);
            if ($client) {
                // Atualizar o whatsapp_from para futuras buscas mais eficientes
                $client->whatsapp_from = $phoneNumber;
                $this->clientRepository->save($client);
                return $client;
            }
        }

        return null;
    }

    /**
     * Gerar variações do número de telefone para busca
     */
    private function generatePhoneVariations(string $phoneNumber): array
    {
        $variations = [];

        // Remove todos os caracteres não numéricos
        $digitsOnly = preg_replace('/[^0-9]/', '', $phoneNumber);
        $variations[] = $digitsOnly;

        // Se tem 13 dígitos (55 + DDD + número), criar variações
        if (strlen($digitsOnly) === 13) {
            // Sem código do país: DDD + número
            $variations[] = substr($digitsOnly, 2);

            // Só o número: sem DDD
            $variations[] = substr($digitsOnly, 4);
        }

        // Se tem 12 dígitos (55 + DDD + número sem 9), criar variações
        if (strlen($digitsOnly) === 12) {
            // Sem código do país: DDD + número
            $variations[] = substr($digitsOnly, 2);

            // Só o número: sem DDD
            $variations[] = substr($digitsOnly, 4);
        }

        // Se tem 11 dígitos (DDD + número), criar variações
        if (strlen($digitsOnly) === 11) {
            // Com código do país
            $variations[] = '55' . $digitsOnly;

            // Só o número: sem DDD
            $variations[] = substr($digitsOnly, 2);
        }

        // Se tem 10 dígitos (DDD + número sem 9), criar variações
        if (strlen($digitsOnly) === 10) {
            // Com código do país
            $variations[] = '55' . $digitsOnly;

            // Só o número: sem DDD
            $variations[] = substr($digitsOnly, 2);
        }

        // Se tem 9 dígitos (só o número), criar variações
        if (strlen($digitsOnly) === 9) {
            // Com DDD comum (assumir alguns DDDs comuns)
            $commonDDDs = ['11', '21', '31', '41', '51', '61', '71', '81', '85'];
            foreach ($commonDDDs as $ddd) {
                $variations[] = $ddd . $digitsOnly;
                $variations[] = '55' . $ddd . $digitsOnly;
            }
        }

        // Adicionar formatação com parênteses e traço
        foreach ($variations as $variation) {
            if (strlen($variation) === 11) {
                $formatted = '(' . substr($variation, 0, 2) . ') ' .
                           substr($variation, 2, 5) . '-' .
                           substr($variation, 7);
                $variations[] = $formatted;
            }
        }

        return array_unique($variations);
    }

    /**
     * Criar novo cliente a partir dos dados do webhook
     */
    private function createNewClient(string $phoneNumber, int $organizationId, array $messageData): ?Client
    {
        try {
            // Extrair nome do contato se disponível
            $contactName = $this->extractContactName($messageData);

            $client = new Client(
                null, // id
                $organizationId,
                $contactName,
                $phoneNumber,
                $phoneNumber, // whatsapp_from
                null, // email
                null, // profession
                null, // birthdate
                null, // cpf
                null, // cnpj
                null, // service
                null, // address
                null, // number
                null, // neighborhood
                null, // cep
                null, // complement
                null, // civil_state
                'Created from WhatsApp webhook', // description
                Carbon::now(),
                Carbon::now()
            );

            return $this->clientRepository->store($client);

        } catch (\Exception $e) {
            DBLog::logError(
                "Error creating client from webhook: " . $e->getMessage(),
                "SaveFromWebhook::createNewClient",
                $organizationId,
                null,
                [
                    'phone_number' => $phoneNumber,
                    'message_data' => $messageData,
                    'error' => $e->getMessage()
                ]
            );

            return null;
        }
    }

    /**
     * Extrair nome do contato dos dados do webhook
     */
    private function extractContactName(array $messageData): string
    {
        // Tentar extrair nome de diferentes campos possíveis
        $possibleNames = [
            $messageData['contact_name'] ?? null,
            $messageData['profile_name'] ?? null,
            $messageData['name'] ?? null
        ];

        foreach ($possibleNames as $name) {
            if (!empty($name) && is_string($name)) {
                return $name;
            }
        }

        // Se não encontrou nome, usar o número como fallback
        $phoneNumber = $messageData['from'] ?? 'Unknown';
        return "WhatsApp User {$phoneNumber}";
    }
}
