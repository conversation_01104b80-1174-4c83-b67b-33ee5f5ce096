<?php

namespace App\UseCases\ChatBot\ExchangedMessage;

use App\Domains\Organization;
use App\Repositories\ExchangedMessageRepository;
use App\Repositories\ClientRepository;

class GetChatByClient
{
    private ExchangedMessageRepository $exchangedMessageRepository;
    private ClientRepository $clientRepository;

    public function __construct(
        ExchangedMessageRepository $exchangedMessageRepository,
        ClientRepository $clientRepository
    ) {
        $this->exchangedMessageRepository = $exchangedMessageRepository;
        $this->clientRepository = $clientRepository;
    }

    public function perform(
        int $client_id,
        Organization $organization,
        int $limit = 50
    ): array {
        try {
            // 1. Verificar se o cliente existe e pertence à organização
            try {
                $client = $this->clientRepository->fetchById($client_id);
                
                if ($client->organization_id !== $organization->id) {
                    return [
                        'success' => false,
                        'reason' => 'Client does not belong to organization',
                        'data' => [],
                        'count' => 0
                    ];
                }
            } catch (\Exception $e) {
                return [
                    'success' => false,
                    'reason' => 'Client not found',
                    'data' => [],
                    'count' => 0
                ];
            }

            // 2. Buscar o histórico de conversa
            $exchangedMessages = $this->exchangedMessageRepository->fetchChatByClient(
                $client_id,
                $organization->id,
                $limit
            );

            // 3. Converter para array
            $messagesArray = [];
            foreach ($exchangedMessages as $exchangedMessage) {
                $messagesArray[] = $exchangedMessage->toArray();
            }

            return [
                'success' => true,
                'reason' => 'Chat history retrieved successfully',
                'data' => $messagesArray,
                'count' => count($messagesArray),
                'client_id' => $client_id,
                'organization_id' => $organization->id,
                'limit' => $limit
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'reason' => 'Error retrieving chat history: ' . $e->getMessage(),
                'data' => [],
                'count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
