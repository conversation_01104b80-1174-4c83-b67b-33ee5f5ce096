<?php

namespace App\UseCases\ChatBot\ExchangedMessage;

use App\Repositories\ExchangedMessageRepository;

class Delete
{
    private ExchangedMessageRepository $exchangedMessageRepository;

    public function __construct(ExchangedMessageRepository $exchangedMessageRepository)
    {
        $this->exchangedMessageRepository = $exchangedMessageRepository;
    }

    /**
     * @param int $id
     * @return bool
     */
    public function perform(int $id): bool
    {
        $exchangedMessage = $this->exchangedMessageRepository->fetchById(
            $id,
            request()->user()->organization_id
        );

        return $this->exchangedMessageRepository->delete($exchangedMessage);
    }
}
