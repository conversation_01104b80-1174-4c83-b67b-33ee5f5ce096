<?php

namespace App\UseCases\ChatBot\Parameter;

use App\Repositories\ParameterRepository;
use Exception;

class Delete
{
    private ParameterRepository $parameterRepository;

    public function __construct(ParameterRepository $parameterRepository) {
        $this->parameterRepository = $parameterRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $parameter = $this->parameterRepository->fetchById($id);

        if($parameter->organization_id !== $organization_id){
            throw new Exception(
                "This parameter doesn't belong to this organization." ,
                403
            );
        }

        return $this->parameterRepository->delete($parameter);
    }
}
