<?php

namespace App\UseCases\ChatBot\WhatsApp;

use App\Repositories\WhatsAppSyncLogRepository;
use App\Enums\SyncType;
use App\Enums\SyncStatus;
use App\Helpers\DBLog;

class GetSyncLogs
{
    private WhatsAppSyncLogRepository $syncLogRepository;

    public function __construct(WhatsAppSyncLogRepository $syncLogRepository)
    {
        $this->syncLogRepository = $syncLogRepository;
    }

    public function execute(
        int $organization_id,
        ?SyncType $sync_type = null,
        ?SyncStatus $status = null,
        int $hours = 24,
        int $limit = 100
    ): array {
        try {
            // Get logs based on filters
            if ($status === SyncStatus::FAILED) {
                $logs = $this->syncLogRepository->getFailedLogs($hours, $limit);
            } elseif ($status === SyncStatus::SUCCESS) {
                $logs = $this->syncLogRepository->getSuccessfulLogs($hours, $limit);
            } else {
                $logs = $this->syncLogRepository->getRecentLogs($hours, $limit);
            }

            // Filter by sync_type if specified
            if ($sync_type) {
                $logs = array_filter($logs, fn($log) => $log->sync_type === $sync_type);
            }

            // Get statistics
            $statistics = $this->syncLogRepository->getSyncStatistics($hours);

            return [
                'logs' => array_map(fn($log) => $log->toArray(), $logs),
                'statistics' => $statistics,
                'filters' => [
                    'sync_type' => $sync_type?->value,
                    'status' => $status?->value,
                    'hours' => $hours,
                    'limit' => $limit
                ],
                'total_logs' => count($logs)
            ];

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "WhatsApp::GetSyncLogs",
                $organization_id,
                null,
                [
                    'sync_type' => $sync_type?->value,
                    'status' => $status?->value,
                    'hours' => $hours
                ]
            );
            throw $e;
        }
    }

    public function getEntityLogs(
        SyncType $sync_type,
        int $entity_id,
        int $organization_id,
        int $limit = 20
    ): array {
        try {
            $logs = $this->syncLogRepository->fetchByEntity($sync_type, $entity_id, $limit);
            $lastSync = $this->syncLogRepository->getLastSyncForEntity($sync_type, $entity_id);
            $lastSuccessfulSync = $this->syncLogRepository->getLastSuccessfulSyncForEntity($sync_type, $entity_id);

            return [
                'entity_type' => $sync_type->value,
                'entity_id' => $entity_id,
                'logs' => array_map(fn($log) => $log->toArray(), $logs),
                'last_sync' => $lastSync?->toArray(),
                'last_successful_sync' => $lastSuccessfulSync?->toArray(),
                'total_logs' => count($logs),
                'sync_summary' => [
                    'has_recent_sync' => $lastSync && $lastSync->isRecent(),
                    'last_sync_successful' => $lastSync && $lastSync->wasSuccessful(),
                    'minutes_since_last_sync' => $lastSync ? $lastSync->getDurationSinceSync() : null,
                    'minutes_since_last_successful_sync' => $lastSuccessfulSync ? $lastSuccessfulSync->getDurationSinceSync() : null,
                ]
            ];

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "WhatsApp::GetSyncLogs::EntityLogs",
                $organization_id,
                null,
                [
                    'sync_type' => $sync_type->value,
                    'entity_id' => $entity_id
                ]
            );
            throw $e;
        }
    }

    public function getSyncTrends(int $organization_id, int $days = 7): array
    {
        try {
            $trends = $this->syncLogRepository->getSyncTrends($days);
            $statistics = $this->syncLogRepository->getSyncStatistics($days * 24);

            return [
                'trends' => $trends,
                'period_statistics' => $statistics,
                'period_days' => $days,
                'summary' => [
                    'total_days_with_syncs' => count($trends),
                    'avg_syncs_per_day' => count($trends) > 0 ? round($statistics['total_syncs'] / count($trends), 2) : 0,
                    'avg_success_rate' => count($trends) > 0 ? round(array_sum(array_column($trends, 'success_rate')) / count($trends), 2) : 0,
                ]
            ];

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "WhatsApp::GetSyncLogs::Trends",
                $organization_id,
                null,
                ['days' => $days]
            );
            throw $e;
        }
    }
}
