<?php

namespace App\UseCases\ChatBot\Step;

use App\Domains\ChatBot\Flow;
use App\Factories\ChatBot\StepFactory;
use App\Helpers\DBLog;
use App\Repositories\StepRepository;
use App\UseCases\ChatBot\Component\SaveFullComponent;

class SaveFullStep
{
    private StepRepository $stepRepository;
    private StepFactory $stepFactory;

    public function __construct(StepRepository $stepRepository, StepFactory $stepFactory) {
        $this->stepRepository = $stepRepository;
        $this->stepFactory = $stepFactory;
    }

    /**
     * @param array $step
     * @param int $index
     * @param Flow $flow
     * @return void
     */
    public function perform(array $step, int $index, Flow $flow) : void {
        try {
            $organization_id = request()->user()->organization_id ?? null;
            $user_id = request()->user()->id ?? null;
            $json = json_encode($step);
            $id = $step['id'] ?? null;

            $domain = $this->stepFactory->buildFromSaveFullStep(
                $step,
                $flow,
                $json,
                $index,
                $id
            );

            $stepDomain = $this->stepRepository->save($domain, $organization_id);

            /** @var SaveFullComponent $useCase */
            $useCase = app()->make(SaveFullComponent::class);
            $useCase->perform($step['component'] ?? null, $stepDomain);
        } catch (\Throwable $exception) {
            DBLog::logError(
                $exception->getMessage() ?? null,
                "ChatBot::SaveFullStep",
                $organization_id ?? null,
                $user_id ?? null,
                $step
            );
            throw $exception;
        }
    }
}
