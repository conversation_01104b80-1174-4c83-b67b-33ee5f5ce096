<?php

namespace App\UseCases\ChatBot\PhoneNumber;

use App\Domains\ChatBot\PhoneNumber;
use App\Factories\ChatBot\PhoneNumberFactory;
use App\Http\Requests\PhoneNumber\StoreRequest;
use App\Repositories\PhoneNumberRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private PhoneNumberRepository $phoneNumberRepository;
    private PhoneNumberFactory $phoneNumberFactory;

    public function __construct(PhoneNumberRepository $phoneNumberRepository, PhoneNumberFactory $phoneNumberFactory) {
        $this->phoneNumberRepository = $phoneNumberRepository;
        $this->phoneNumberFactory = $phoneNumberFactory;
    }

    /**
     * @param StoreRequest $request
     * @return PhoneNumber
     */
    public function perform(StoreRequest $request) : PhoneNumber {
        DB::beginTransaction();

        $domain = $this->phoneNumberFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $phoneNumber = $this->phoneNumberRepository->store($domain);

        DB::commit();

        return $phoneNumber;
    }
}