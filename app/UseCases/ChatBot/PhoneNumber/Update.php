<?php

namespace App\UseCases\ChatBot\PhoneNumber;

use App\Domains\ChatBot\PhoneNumber;
use App\Factories\ChatBot\PhoneNumberFactory;
use App\Http\Requests\PhoneNumber\UpdateRequest;
use App\Repositories\PhoneNumberRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private PhoneNumberRepository $phoneNumberRepository;
    private PhoneNumberFactory $phoneNumberFactory;

    public function __construct(PhoneNumberRepository $phoneNumberRepository, PhoneNumberFactory $phoneNumberFactory) {
        $this->phoneNumberRepository = $phoneNumberRepository;
        $this->phoneNumberFactory = $phoneNumberFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return PhoneNumber
     */
    public function perform(UpdateRequest $request, int $id) : PhoneNumber {
        DB::beginTransaction();

        $organization_id = request()->user()->organization_id ?? null;

        $domain = $this->phoneNumberFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $phoneNumber = $this->phoneNumberRepository->update($domain, $organization_id);

        DB::commit();

        return $phoneNumber;
    }
}
