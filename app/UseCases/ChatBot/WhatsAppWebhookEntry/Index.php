<?php

namespace App\UseCases\ChatBot\WhatsAppWebhookEntry;

use App\Services\Meta\WhatsApp\Repositories\WhatsAppWebhookEntryRepository;
use App\Services\Meta\WhatsApp\Models\WhatsAppWebhookEntry as WhatsAppWebhookEntryModel;
use App\Services\Meta\WhatsApp\Factories\WhatsAppWebhookEntryFactory;

class Index
{
    private WhatsAppWebhookEntryRepository $whatsAppWebhookEntryRepository;
    private WhatsAppWebhookEntryFactory $whatsAppWebhookEntryFactory;

    public function __construct(
        WhatsAppWebhookEntryRepository $whatsAppWebhookEntryRepository,
        WhatsAppWebhookEntryFactory $whatsAppWebhookEntryFactory
    ) {
        $this->whatsAppWebhookEntryRepository = $whatsAppWebhookEntryRepository;
        $this->whatsAppWebhookEntryFactory = $whatsAppWebhookEntryFactory;
    }

    /**
     * Get all WhatsApp webhook entries with pagination
     *
     * @param array $filters
     * @return array
     */
    public function perform(array $filters = []): array
    {
        $query = WhatsAppWebhookEntryModel::with('whatsappMessage.message');

        // Apply filters
        if (isset($filters['whatsapp_message_id'])) {
            $query->where('whatsapp_message_id', $filters['whatsapp_message_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['external_wam_id'])) {
            $query->where('external_wam_id', $filters['external_wam_id']);
        }

        if (isset($filters['recipient_id'])) {
            $query->where('recipient_id', $filters['recipient_id']);
        }

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        $page = $filters['page'] ?? 1;

        $paginatedResults = $query->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        // Convert models to domains
        $webhookEntries = $paginatedResults->getCollection()->map(function ($model) {
            return $this->whatsAppWebhookEntryFactory->buildFromModel($model);
        });

        return [
            'data' => $webhookEntries->map(fn($entry) => $entry->toArray())->toArray(),
            'current_page' => $paginatedResults->currentPage(),
            'last_page' => $paginatedResults->lastPage(),
            'per_page' => $paginatedResults->perPage(),
            'total' => $paginatedResults->total(),
        ];
    }
}
