<?php

namespace App\UseCases\ChatBot\Conversation;

use App\Domains\ChatBot\Conversation;
use App\Factories\ChatBot\ConversationFactory;
use App\Http\Requests\Conversation\UpdateRequest;
use App\Repositories\ConversationRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ConversationRepository $conversationRepository;
    private ConversationFactory $conversationFactory;

    public function __construct(ConversationRepository $conversationRepository, ConversationFactory $conversationFactory) {
        $this->conversationRepository = $conversationRepository;
        $this->conversationFactory = $conversationFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Conversation
     */
    public function perform(UpdateRequest $request, int $id) : Conversation {
        DB::beginTransaction();

        $domain = $this->conversationFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $conversation = $this->conversationRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $conversation;
    }
}
