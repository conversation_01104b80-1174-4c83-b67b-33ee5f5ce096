<?php

namespace App\UseCases\ChatBot\Message;

use App\Repositories\MessageRepository;
use Exception;

class Delete
{
    private MessageRepository $messageRepository;

    public function __construct(MessageRepository $messageRepository) {
        $this->messageRepository = $messageRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $message = $this->messageRepository->fetchById($id);

        if($message->organization_id !== $organization_id){
            throw new Exception(
                "This message don't belong to this organization." ,
                403
            );
        }

        return $this->messageRepository->delete($message);
    }
}
