<?php

namespace App\UseCases\ChatBot\Message;

use App\Repositories\MessageRepository;
use App\Repositories\MessageDeliveryAttemptRepository;
use App\Enums\MessageStatus;
use App\Helpers\DBLog;

class Resend
{
    private MessageRepository $messageRepository;
    private MessageDeliveryAttemptRepository $deliveryAttemptRepository;

    public function __construct(
        MessageRepository $messageRepository,
        MessageDeliveryAttemptRepository $deliveryAttemptRepository
    ) {
        $this->messageRepository = $messageRepository;
        $this->deliveryAttemptRepository = $deliveryAttemptRepository;
    }

    public function execute(
        int $message_id, 
        int $organization_id, 
        int $user_id,
        bool $reset_retry_count = false,
        bool $force_resend = false
    ): array {
        try {
            // Fetch message
            $message = $this->messageRepository->fetchById($message_id);
            
            // Validate organization access
            if ($message->organization_id !== $organization_id) {
                throw new \InvalidArgumentException("Message not found or access denied");
            }

            // Check if message can be resent
            if (!$force_resend && !$this->canResendMessage($message, $reset_retry_count)) {
                throw new \InvalidArgumentException($this->getResendErrorMessage($message));
            }

            // Get delivery history before resend
            $deliveryHistory = $this->deliveryAttemptRepository->fetchByMessage($message_id);

            if ($reset_retry_count) {
                // Reset retry state completely
                $this->messageRepository->resetMessageForRetry($message_id);
                $action = 'reset_and_queued';
            } else {
                // Queue for immediate retry
                $this->queueMessageForImmediateRetry($message_id);
                $action = 'queued_for_retry';
            }

            // Refresh message data
            $updatedMessage = $this->messageRepository->fetchById($message_id);

            DBLog::logInfo(
                "Message resend initiated",
                "Message::Resend",
                $organization_id,
                $user_id,
                [
                    'message_id' => $message_id,
                    'campaign_id' => $message->campaign_id,
                    'action' => $action,
                    'reset_retry_count' => $reset_retry_count,
                    'force_resend' => $force_resend,
                    'previous_attempts' => count($deliveryHistory)
                ]
            );

            return [
                'message_id' => $message_id,
                'campaign_id' => $message->campaign_id,
                'action' => $action,
                'message' => $this->getSuccessMessage($action),
                'retry_info' => $updatedMessage->getRetryStatusSummary(),
                'delivery_history' => array_map(fn($attempt) => $attempt->toArray(), $deliveryHistory)
            ];

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "Message::Resend",
                $organization_id,
                $user_id,
                [
                    'message_id' => $message_id, 
                    'reset_retry_count' => $reset_retry_count,
                    'force_resend' => $force_resend
                ]
            );
            throw $e;
        }
    }

    private function canResendMessage($message, bool $reset_retry_count): bool
    {
        // If resetting retry count, always allow
        if ($reset_retry_count) {
            return true;
        }

        // Check if message is in a resendable state
        if ($message->status !== MessageStatus::is_failed) {
            return false;
        }

        // Check if message can still be retried
        return $message->canRetry();
    }

    private function getResendErrorMessage($message): string
    {
        if ($message->status === MessageStatus::is_sent) {
            return "Message has already been sent successfully";
        }

        if ($message->status === MessageStatus::is_sending) {
            return "Message is currently being sent";
        }

        if ($message->hasFailedPermanently()) {
            return "Message has reached maximum retry attempts. Use reset_retry_count=true to reset and try again.";
        }

        if ($message->status !== MessageStatus::is_failed) {
            return "Message is not in a failed state and cannot be resent";
        }

        return "Message cannot be resent at this time";
    }

    private function queueMessageForImmediateRetry(int $message_id): void
    {
        \App\Models\Message::where('id', $message_id)
                          ->update([
                              'next_retry_at' => now(),
                              'status' => MessageStatus::is_draft->value
                          ]);
    }

    private function getSuccessMessage(string $action): string
    {
        return match($action) {
            'reset_and_queued' => 'Message retry count has been reset and queued for sending',
            'queued_for_retry' => 'Message has been queued for immediate retry',
            default => 'Message has been processed for resend'
        };
    }

    public function getDeliveryStatus(int $message_id, int $organization_id): array
    {
        try {
            // Fetch message
            $message = $this->messageRepository->fetchById($message_id);
            
            // Validate organization access
            if ($message->organization_id !== $organization_id) {
                throw new \InvalidArgumentException("Message not found or access denied");
            }

            // Get delivery attempts
            $deliveryAttempts = $this->deliveryAttemptRepository->fetchByMessage($message_id);
            $statistics = $this->deliveryAttemptRepository->getAttemptsStatistics($message_id);

            return [
                'message_id' => $message_id,
                'campaign_id' => $message->campaign_id,
                'current_status' => $message->status->name,
                'retry_info' => $message->getRetryStatusSummary(),
                'delivery_attempts' => array_map(fn($attempt) => $attempt->toArray(), $deliveryAttempts),
                'statistics' => $statistics,
                'client_info' => $message->client ? [
                    'id' => $message->client->id,
                    'name' => $message->client->name,
                    'phone' => $message->client->phone,
                ] : null
            ];

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "Message::GetDeliveryStatus",
                $organization_id,
                null,
                ['message_id' => $message_id]
            );
            throw $e;
        }
    }
}
