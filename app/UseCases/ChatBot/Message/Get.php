<?php

namespace App\UseCases\ChatBot\Message;

use App\Domains\ChatBot\Message;
use App\Repositories\MessageRepository;
use Exception;

class Get
{
    private MessageRepository $messageRepository;

    public function __construct(MessageRepository $messageRepository) {
        $this->messageRepository = $messageRepository;
    }

    /**
     * @param int $id
     * @return Message
     * @throws Exception
     */
    public function perform(int $id) : Message {
        $organization_id = request()->user()->organization_id;

        $message = $this->messageRepository->fetchById($id);

        if($message->organization_id !== $organization_id){
            throw new Exception(
                "This message don't belong to this organization." ,
                403
            );
        }
        return $message;
    }
}
