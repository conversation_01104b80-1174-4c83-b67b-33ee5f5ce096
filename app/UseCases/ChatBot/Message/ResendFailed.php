<?php

namespace App\UseCases\ChatBot\Message;

use App\Repositories\CampaignRepository;
use App\Repositories\MessageRepository;
use App\Repositories\MessageDeliveryAttemptRepository;
use App\Enums\MessageStatus;
use App\Helpers\DBLog;

class ResendFailed
{
    private CampaignRepository $campaignRepository;
    private MessageRepository $messageRepository;
    private MessageDeliveryAttemptRepository $deliveryAttemptRepository;

    public function __construct(
        CampaignRepository $campaignRepository,
        MessageRepository $messageRepository,
        MessageDeliveryAttemptRepository $deliveryAttemptRepository
    ) {
        $this->campaignRepository = $campaignRepository;
        $this->messageRepository = $messageRepository;
        $this->deliveryAttemptRepository = $deliveryAttemptRepository;
    }

    public function execute(
        int $campaign_id, 
        int $organization_id, 
        int $user_id,
        bool $reset_retry_count = false,
        ?array $message_ids = null
    ): array {
        try {
            // Validate campaign exists and belongs to organization
            $campaign = $this->campaignRepository->fetchById($campaign_id);
            
            if ($campaign->organization_id !== $organization_id) {
                throw new \InvalidArgumentException("Campaign not found or access denied");
            }

            // Get failed messages
            $failedMessages = $this->getFailedMessagesToResend($campaign_id, $message_ids);

            if (empty($failedMessages)) {
                return [
                    'campaign_id' => $campaign_id,
                    'messages_processed' => 0,
                    'messages_queued_for_retry' => 0,
                    'messages_reset' => 0,
                    'message' => 'No failed messages found to resend'
                ];
            }

            $processedCount = 0;
            $queuedCount = 0;
            $resetCount = 0;

            foreach ($failedMessages as $message) {
                $processedCount++;

                if ($reset_retry_count) {
                    // Reset retry state completely
                    $this->messageRepository->resetMessageForRetry($message->id);
                    $resetCount++;
                } else {
                    // Only queue if message can still be retried
                    if ($message->canRetry()) {
                        // Update next_retry_at to now so it gets picked up immediately
                        $this->queueMessageForImmediateRetry($message->id);
                        $queuedCount++;
                    }
                }
            }

            DBLog::logInfo(
                "Failed messages resend initiated",
                "Message::ResendFailed",
                $organization_id,
                $user_id,
                [
                    'campaign_id' => $campaign_id,
                    'processed_count' => $processedCount,
                    'queued_count' => $queuedCount,
                    'reset_count' => $resetCount,
                    'reset_retry_count' => $reset_retry_count
                ]
            );

            return [
                'campaign_id' => $campaign_id,
                'messages_processed' => $processedCount,
                'messages_queued_for_retry' => $queuedCount,
                'messages_reset' => $resetCount,
                'message' => $this->getSuccessMessage($processedCount, $queuedCount, $resetCount, $reset_retry_count)
            ];

        } catch (\Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "Message::ResendFailed",
                $organization_id,
                $user_id,
                ['campaign_id' => $campaign_id, 'reset_retry_count' => $reset_retry_count]
            );
            throw $e;
        }
    }

    private function getFailedMessagesToResend(int $campaign_id, ?array $message_ids): array
    {
        if ($message_ids) {
            // Get specific messages
            $messages = [];
            foreach ($message_ids as $id) {
                try {
                    $message = $this->messageRepository->fetchById($id);
                    if ($message->campaign_id === $campaign_id && $message->status === MessageStatus::is_failed) {
                        $messages[] = $message;
                    }
                } catch (\Exception $e) {
                    // Skip invalid message IDs
                    continue;
                }
            }
            return $messages;
        }

        // Get all failed messages for campaign
        return $this->messageRepository->getFailedMessagesByCampaign($campaign_id);
    }

    private function queueMessageForImmediateRetry(int $message_id): void
    {
        // Update the message to be retried immediately
        \App\Models\Message::where('id', $message_id)
                          ->update([
                              'next_retry_at' => now(),
                              'status' => MessageStatus::is_draft->value
                          ]);
    }

    private function getSuccessMessage(int $processed, int $queued, int $reset, bool $resetRetryCount): string
    {
        if ($resetRetryCount) {
            return "Reset {$reset} messages for retry. They will be processed in the next sending cycle.";
        }

        if ($queued > 0) {
            return "Queued {$queued} messages for immediate retry out of {$processed} processed.";
        }

        return "Processed {$processed} messages, but none were eligible for retry (max retries reached).";
    }
}
