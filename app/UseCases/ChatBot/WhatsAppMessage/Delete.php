<?php

namespace App\UseCases\ChatBot\WhatsAppMessage;

use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use Exception;
use Illuminate\Support\Facades\DB;

class Delete
{
    private WhatsAppMessageRepository $whatsAppMessageRepository;

    public function __construct(WhatsAppMessageRepository $whatsAppMessageRepository)
    {
        $this->whatsAppMessageRepository = $whatsAppMessageRepository;
    }

    /**
     * Delete a WhatsApp message
     *
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id): bool
    {
        DB::beginTransaction();

        try {
            $whatsAppMessage = $this->whatsAppMessageRepository->fetchById($id);

            if (!$whatsAppMessage) {
                throw new Exception("WhatsApp message not found", 404);
            }

            // Check organization access if message has organization_id
            if ($whatsAppMessage->message && $whatsAppMessage->message->organization_id) {
                $organizationId = request()->user()->organization_id;
                if ($whatsAppMessage->message->organization_id !== $organizationId) {
                    throw new Exception(
                        "This WhatsApp message doesn't belong to this organization.",
                        403
                    );
                }
            }

            $result = $this->whatsAppMessageRepository->delete($whatsAppMessage);

            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
