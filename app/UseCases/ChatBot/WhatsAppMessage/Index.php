<?php

namespace App\UseCases\ChatBot\WhatsAppMessage;

use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Services\Meta\WhatsApp\Models\WhatsAppMessage as WhatsAppMessageModel;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;

class Index
{
    private WhatsAppMessageRepository $whatsAppMessageRepository;
    private WhatsAppMessageFactory $whatsAppMessageFactory;

    public function __construct(
        WhatsAppMessageRepository $whatsAppMessageRepository,
        WhatsAppMessageFactory $whatsAppMessageFactory
    ) {
        $this->whatsAppMessageRepository = $whatsAppMessageRepository;
        $this->whatsAppMessageFactory = $whatsAppMessageFactory;
    }

    /**
     * Get all WhatsApp messages with pagination
     *
     * @param array $filters
     * @return array
     */
    public function perform(array $filters = []): array
    {
        $query = WhatsAppMessageModel::with('message');

        // Apply filters
        if (isset($filters['message_id'])) {
            $query->where('message_id', $filters['message_id']);
        }

        if (isset($filters['message_status'])) {
            $query->where('message_status', $filters['message_status']);
        }

        if (isset($filters['wa_id'])) {
            $query->where('wa_id', $filters['wa_id']);
        }

        if (isset($filters['whatsapp_message_id'])) {
            $query->where('whatsapp_message_id', $filters['whatsapp_message_id']);
        }

        $query->orderBy('id', 'desc');

        // Pagination
        $perPage = $filters['per_page'] ?? 50;
        $page = $filters['page'] ?? 1;

        $paginatedResults = $query->paginate($perPage, ['*'], 'page', $page);

        // Convert models to domains
        $whatsAppMessages = $paginatedResults->getCollection()->map(function ($model) {
            return $this->whatsAppMessageFactory->buildFromModel($model);
        });

        return [
            'data' => $whatsAppMessages->map(fn($message) => $message->toArray())->toArray(),
            'current_page' => $paginatedResults->currentPage(),
            'last_page' => $paginatedResults->lastPage(),
            'per_page' => $paginatedResults->perPage(),
            'total' => $paginatedResults->total(),
        ];
    }
}
