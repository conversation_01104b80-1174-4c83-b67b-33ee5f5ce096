<?php

namespace App\UseCases\ChatBot\Flow;

use App\Domains\ChatBot\Flow;
use App\Factories\ChatBot\FlowFactory;
use App\Http\Requests\Flow\UpdateRequest;
use App\Repositories\FlowRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private FlowRepository $flowRepository;
    private FlowFactory $flowFactory;

    public function __construct(FlowRepository $flowRepository, FlowFactory $flowFactory) {
        $this->flowRepository = $flowRepository;
        $this->flowFactory = $flowFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Flow
     */
    public function perform(UpdateRequest $request, int $id) : Flow {
        DB::beginTransaction();

        $domain = $this->flowFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $flow = $this->flowRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $flow;
    }
}
