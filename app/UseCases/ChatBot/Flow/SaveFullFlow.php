<?php

namespace App\UseCases\ChatBot\Flow;

use App\Domains\ChatBot\Flow;
use App\Factories\ChatBot\FlowFactory;
use App\Helpers\DBLog;
use App\Http\Requests\Flow\SaveFullFlowRequest;
use App\Repositories\FlowRepository;
use App\Services\ChatBot\FlowValidationService;
use App\UseCases\ChatBot\Step\SaveFullStep;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class SaveFullFlow
{
    private FlowRepository $flowRepository;
    private FlowFactory $flowFactory;
    private FlowValidationService $validationService;
    private SaveFullStep $saveFullStepUseCase;

    public function __construct(
        FlowRepository $flowRepository,
        FlowFactory $flowFactory,
        FlowValidationService $validationService,
        SaveFullStep $saveFullStepUseCase
    ) {
        $this->flowRepository = $flowRepository;
        $this->flowFactory = $flowFactory;
        $this->validationService = $validationService;
        $this->saveFullStepUseCase = $saveFullStepUseCase;
    }

    /**
     * Perform SaveFullFlow operation with optimized architecture
     *
     * @param SaveFullFlowRequest $request
     * @return Flow
     * @throws ValidationException
     * @throws \Throwable
     */
    public function perform(SaveFullFlowRequest $request): Flow
    {
        $startTime = microtime(true);
        $organizationId = auth()->user()->organization_id;
        $userId = auth()->user()->id;

        // Extract validated data
        $flowData = $request->validated();
        $flowArray = $flowData['flow'];
        $stepsArray = $flowData['steps'];
        $stepsCount = count($stepsArray);
        $flowId = $flowArray['id'] ?? null;

        // Log operation start
        Log::info('SaveFullFlow operation started', [
            'user_id' => $userId,
            'organization_id' => $organizationId,
            'flow_id' => $flowId,
            'flow_name' => $flowArray['name'] ?? 'Unknown',
            'steps_count' => $stepsCount,
            'operation_type' => $flowId ? 'update' : 'create'
        ]);

        return DB::transaction(function () use (
            $request, $flowArray, $stepsArray, $stepsCount, $flowId,
            $organizationId, $userId, $startTime
        ) {
            try {
                // Validate flow integrity before processing
                $this->validateFlowIntegrity($flowArray, $stepsArray);

                // Build flow domain
                $flowDomain = $this->buildFlowDomain($flowArray, $stepsArray, $stepsCount, $organizationId, $flowId);

                // Save flow
                $flow = $this->flowRepository->save($flowDomain, $organizationId);

                // Process steps with optimized batch processing
                $this->processSteps($stepsArray, $flow);

                // Log successful operation
                $executionTime = round((microtime(true) - $startTime) * 1000, 2);
                Log::info('SaveFullFlow operation completed successfully', [
                    'user_id' => $userId,
                    'organization_id' => $organizationId,
                    'flow_id' => $flow->id,
                    'flow_name' => $flow->name,
                    'steps_count' => $flow->steps_count,
                    'execution_time_ms' => $executionTime,
                    'operation_type' => $flowId ? 'update' : 'create'
                ]);

                return $flow;

            } catch (\Throwable $exception) {
                // Log error with context
                $executionTime = round((microtime(true) - $startTime) * 1000, 2);
                Log::error('SaveFullFlow operation failed', [
                    'user_id' => $userId,
                    'organization_id' => $organizationId,
                    'flow_id' => $flowId,
                    'error' => $exception->getMessage(),
                    'execution_time_ms' => $executionTime,
                    'exception_class' => get_class($exception)
                ]);

                // Legacy DBLog for compatibility
                DBLog::logError(
                    $exception->getMessage(),
                    "ChatBot::SaveFullFlow",
                    $organizationId,
                    $userId,
                    $request->validated()
                );

                throw $exception;
            }
        });
    }

    /**
     * Validate flow integrity using FlowValidationService
     */
    private function validateFlowIntegrity(array $flowArray, array $stepsArray): void
    {
        $flowData = [
            'flow' => $flowArray,
            'steps' => $stepsArray
        ];

        $validation = $this->validationService->validateFlowStructure($flowData);

        if (!$validation['valid']) {
            $errorMessage = 'Flow integrity validation failed: ' . implode('; ', $validation['errors']);
            throw new ValidationException(validator([], []), ['flow_structure' => $validation['errors']]);
        }

        // Log warnings if any
        if (!empty($validation['warnings'])) {
            Log::warning('SaveFullFlow validation warnings', [
                'warnings' => $validation['warnings'],
                'flow_name' => $flowArray['name'] ?? 'Unknown'
            ]);
        }
    }

    /**
     * Build flow domain object with validated data
     */
    private function buildFlowDomain(
        array $flowArray,
        array $stepsArray,
        int $stepsCount,
        int $organizationId,
        ?int $flowId
    ): Flow {
        $json = json_encode([
            'flow' => $flowArray,
            'steps' => $stepsArray
        ]);

        return $this->flowFactory->buildFromSaveFullFlow(
            $flowArray,
            $json,
            $stepsCount,
            $organizationId,
            $flowId
        );
    }

    /**
     * Process steps with optimized approach
     */
    private function processSteps(array $stepsArray, Flow $flow): void
    {
        foreach ($stepsArray as $index => $stepData) {
            try {
                $this->saveFullStepUseCase->perform($stepData, $index, $flow);
            } catch (\Throwable $exception) {
                Log::error('SaveFullFlow step processing failed', [
                    'flow_id' => $flow->id,
                    'step_index' => $index,
                    'step_data' => $stepData,
                    'error' => $exception->getMessage()
                ]);
                throw $exception;
            }
        }
    }
}
