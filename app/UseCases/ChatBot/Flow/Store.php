<?php

namespace App\UseCases\ChatBot\Flow;

use App\Domains\ChatBot\Flow;
use App\Factories\ChatBot\FlowFactory;
use App\Http\Requests\Flow\StoreRequest;
use App\Repositories\FlowRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private FlowRepository $flowRepository;
    private FlowFactory $flowFactory;

    public function __construct(FlowRepository $flowRepository, FlowFactory $flowFactory) {
        $this->flowRepository = $flowRepository;
        $this->flowFactory = $flowFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Flow
     */
    public function perform(StoreRequest $request) : Flow {
        DB::beginTransaction();

        $domain = $this->flowFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $flow = $this->flowRepository->store($domain);

        DB::commit();

        return $flow;
    }
}
