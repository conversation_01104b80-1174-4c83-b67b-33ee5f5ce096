<?php

namespace App\UseCases\Inventory\Client;

use App\Domains\Filters\ClientFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\ClientRepository;

class GetAll
{
    private ClientRepository $clientRepository;

    public function __construct(ClientRepository $clientRepository) {
        $this->clientRepository = $clientRepository;
    }

    /**
     * @param ClientFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function perform(ClientFilters $filters, OrderBy $orderBy) : array {
        return $this->clientRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
