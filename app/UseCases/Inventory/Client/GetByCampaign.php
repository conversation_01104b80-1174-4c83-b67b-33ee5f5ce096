<?php

namespace App\UseCases\Inventory\Client;

use App\Domains\Filters\ClientFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\ClientRepository;
use Exception;

class GetByCampaign
{
    private ClientRepository $clientRepository;

    public function __construct(ClientRepository $clientRepository) {
        $this->clientRepository = $clientRepository;
    }

    /**
     * @param ClientFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function perform(int $campaign_id, ClientFilters $filters, OrderBy $orderBy) : array {
        return $this->clientRepository->fetchByCampaignId(
            request()->user()->organization_id,
            $campaign_id,
            $filters,
            $orderBy
        );
    }
}
