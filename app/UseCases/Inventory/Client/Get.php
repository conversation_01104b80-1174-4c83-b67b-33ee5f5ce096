<?php

namespace App\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Repositories\ClientRepository;
use Exception;

class Get
{
    private ClientRepository $clientRepository;

    public function __construct(ClientRepository $clientRepository) {
        $this->clientRepository = $clientRepository;
    }

    /**
     * @param int $id
     * @return Client
     * @throws Exception
     */
    public function perform(int $id) : Client {
        $organization_id = request()->user()->organization_id;

        $client = $this->clientRepository->fetchById($id);

        if($client->organization_id !== $organization_id){
            throw new Exception(
                "This client don't belong to this organization." ,
                403
            );
        }
        return $client;
    }
}
