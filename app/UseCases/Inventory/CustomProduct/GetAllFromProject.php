<?php

namespace App\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Repositories\CustomProductRepository;

class GetAllFromProject
{
    private CustomProductRepository $projectProductRepository;

    public function __construct(CustomProductRepository $projectProductRepository) {
        $this->projectProductRepository = $projectProductRepository;
    }

    /**
     * @return CustomProduct[]
     */
    public function perform(int $project_id) : array {
        return $this->projectProductRepository->fetchFromCustom(
            $project_id
        );
    }
}
