<?php

namespace App\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Factories\Inventory\CustomProductFactory;
use App\Http\Requests\CustomProduct\StoreRequest;
use App\Repositories\CustomProductRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private CustomProductRepository $projectProductRepository;
    private CustomProductFactory $projectProductFactory;

    public function __construct(CustomProductRepository $projectProductRepository, CustomProductFactory $projectProductFactory) {
        $this->projectProductRepository = $projectProductRepository;
        $this->projectProductFactory = $projectProductFactory;
    }

    /**
     * @param StoreRequest $request
     * @return CustomProduct
     */
    public function perform(StoreRequest $request) : CustomProduct {
        DB::beginTransaction();

        $domain = $this->projectProductFactory->buildFromStoreRequest($request);

        $projectProduct = $this->projectProductRepository->store($domain);

        DB::commit();

        return $projectProduct;
    }
}
