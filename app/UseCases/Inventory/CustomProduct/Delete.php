<?php

namespace App\UseCases\Inventory\CustomProduct;

use App\Repositories\CustomProductRepository;
use Exception;

class Delete
{
    private CustomProductRepository $projectProductRepository;

    public function __construct(CustomProductRepository $projectProductRepository) {
        $this->projectProductRepository = $projectProductRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        return $this->projectProductRepository->delete(
            $this->projectProductRepository->fetchById($id)
        );
    }
}
