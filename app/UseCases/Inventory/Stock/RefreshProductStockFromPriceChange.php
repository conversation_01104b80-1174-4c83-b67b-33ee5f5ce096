<?php

namespace App\UseCases\Inventory\Stock;

use App\Domains\Inventory\Product;
use App\Domains\Inventory\Stock;
use App\Factories\Inventory\StockFactory;
use App\Helpers\DBLog;
use App\Repositories\StockRepository;
use Throwable;

class RefreshProductStockFromPriceChange
{
    private StockRepository $stockRepository;
    private StockFactory $stockFactory;

    public function __construct(StockRepository $stockRepository, StockFactory $stockFactory) {
        $this->stockRepository = $stockRepository;
        $this->stockFactory = $stockFactory;
    }

    /**
     * @param Product $product
     * @return null|Stock
     */
    public function perform(Product $product) : ?Stock {
        try{
            $domain = $this->stockRepository->fetchByProductId($product->id);
            $domain->refreshFromPrice($product->price);

            return $this->stockRepository->update(
                $domain,
                request()->user()->organization_id
            );
        } catch(Throwable $e) {
            DBLog::logError(
                $e->getMessage(),
                "RefreshProductStockFromPriceChange::No Stock Found",
                request()->user()->organization_id,
                request()->user()->id,
                ['product_id' => $product->id, 'domain' => $domain ?? null]
            );
            return null;
        }
    }
}
