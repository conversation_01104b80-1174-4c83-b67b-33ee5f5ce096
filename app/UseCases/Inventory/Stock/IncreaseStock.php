<?php

namespace App\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockFactory;
use App\Repositories\StockRepository;

class IncreaseStock
{
    private StockRepository $stockRepository;

    public function __construct(
        StockRepository $stockRepository
    ) {
        $this->stockRepository = $stockRepository;
    }

    /**
     * @param StockEntry $stockEntry
     * @return Stock
     */
    public function perform(StockEntry $stockEntry) : Stock {

        $stock = $this->stockRepository->fetchByProductId(
            $stockEntry->product_id
        );

        if(!$stock){
            /** @var CreateStockFromEntry $createStockFromEntryUseCase */
            $createStockFromEntryUseCase = app()->make(CreateStockFromEntry::class);
            return $createStockFromEntryUseCase->perform($stockEntry);
        }

        $stock->increaseStock($stockEntry->quantity);

        return $this->stockRepository->update($stock, $stock->organization_id);
    }
}
