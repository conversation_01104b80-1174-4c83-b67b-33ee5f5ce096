<?php

namespace App\UseCases\Inventory\Stock;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockFilters;
use App\Repositories\StockRepository;

class GetAll
{
    private StockRepository $stockRepository;

    public function __construct(StockRepository $stockRepository) {
        $this->stockRepository = $stockRepository;
    }

    /**
     * @return array
     */
    public function perform(StockFilters $filters, OrderBy $orderBy) : array {
        return $this->stockRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
