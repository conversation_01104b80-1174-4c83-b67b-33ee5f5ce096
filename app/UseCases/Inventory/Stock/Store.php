<?php

namespace App\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Factories\Inventory\StockFactory;
use App\Http\Requests\Stock\StoreRequest;
use App\Repositories\StockRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private StockRepository $stockRepository;
    private StockFactory $stockFactory;

    public function __construct(StockRepository $stockRepository, StockFactory $stockFactory) {
        $this->stockRepository = $stockRepository;
        $this->stockFactory = $stockFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Stock
     */
    public function perform(StoreRequest $request) : Stock {
        DB::beginTransaction();

        $domain = $this->stockFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $stock = $this->stockRepository->store($domain);

        DB::commit();

        return $stock;
    }
}
