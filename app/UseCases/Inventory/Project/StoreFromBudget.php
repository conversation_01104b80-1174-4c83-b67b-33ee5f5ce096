<?php

namespace App\UseCases\Inventory\Project;

use App\Domains\Inventory\Budget;
use App\Domains\Inventory\ProductsAttachs\AttachProductsDomain;
use App\Domains\Inventory\Project;
use App\Domains\Inventory\Shop;
use App\Factories\Inventory\ProjectFactory;
use App\Repositories\ProjectRepository;
use App\UseCases\Inventory\ProjectProduct\GetAllFromProject;
use App\UseCases\Inventory\StockExit\StoreFromProjectProduct;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\UseCases\Inventory\Shop\Get as GetShop;

class StoreFromBudget
{
    private ProjectRepository $projectRepository;
    private ProjectFactory $projectFactory;

    public function __construct(ProjectRepository $projectRepository, ProjectFactory $projectFactory) {
        $this->projectRepository = $projectRepository;
        $this->projectFactory = $projectFactory;
    }

    /**
     * @param Request $request
     * @param Budget $budget
     * @return Project
     * @throws BindingResolutionException
     */
    public function perform(Request $request, Budget $budget) : Project {
        DB::beginTransaction();

        $domain = $this->projectFactory->buildFromBudget($budget, $request);
        $domain->organization_id = request()->user()->organization_id;

        $project = $this->projectRepository->store($domain);

        $this->projectRepository->attachProducts(
            $project,
            new AttachProductsDomain(
                $budget->exportProducts()
            )
        );

        if(!empty($request->remove_from_stock) && $request->remove_from_stock == 1) {
            /** @var GetAllFromProject $useCaseGetAllFromProject */
            $useCaseGetAllFromProject = app()->make(GetAllFromProject::class);
            $projectProducts = $useCaseGetAllFromProject->perform($project->id);

            /** @var StoreFromProjectProduct $useCaseStoreFromProjectProduct */
            $useCaseStoreFromProjectProduct = app()->make(StoreFromProjectProduct::class);
            foreach ($projectProducts as $projectProduct){
                $useCaseStoreFromProjectProduct->perform($projectProduct, $this->checkForShop($request));
            }
        }

        DB::commit();

        return $project;
    }

    private function checkForShop(Request $request) : ?Shop {
        try{
            if(!empty($request->shop_id) && is_numeric($request->shop_id)) {
                /** @var GetShop $getShop */
                $getShop = app()->make(GetShop::class);
                return $getShop->perform($request->shop_id);
            }
            return null;
        }catch (\Throwable $e){
            logger()->error($e->getMessage(), $e->getTrace());
            return null;
        }
    }
}
