<?php

namespace App\UseCases\Inventory\Project;

use App\Domains\Inventory\Project;
use App\Repositories\ProjectRepository;
use Exception;

class Get
{
    private ProjectRepository $projectRepository;

    public function __construct(ProjectRepository $projectRepository) {
        $this->projectRepository = $projectRepository;
    }

    /**
     * @param int $id
     * @return Project
     * @throws Exception
     */
    public function perform(int $id) : Project {
        $organization_id = request()->user()->organization_id;

        $project = $this->projectRepository->fetchById($id);

        if($project->organization_id !== $organization_id){
            throw new Exception(
                "This project don't belong to this organization." ,
                403
            );
        }
        return $project;
    }
}
