<?php

namespace App\UseCases\Inventory\Project;

use App\Domains\Inventory\Project;
use App\Factories\Inventory\ProjectFactory;
use App\Http\Requests\Project\UpdateRequest;
use App\Repositories\ProjectRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ProjectRepository $projectRepository;
    private ProjectFactory $projectFactory;

    public function __construct(ProjectRepository $projectRepository, ProjectFactory $projectFactory) {
        $this->projectRepository = $projectRepository;
        $this->projectFactory = $projectFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Project
     */
    public function perform(UpdateRequest $request, int $id) : Project {
        DB::beginTransaction();
        $domain = $this->projectFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $project = $this->projectRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $project;
    }
}
