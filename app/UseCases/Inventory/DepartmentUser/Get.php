<?php

namespace App\UseCases\Inventory\DepartmentUser;

use App\Domains\Inventory\DepartmentUser;
use App\Repositories\DepartmentUserRepository;
use Exception;

class Get
{
    private DepartmentUserRepository $departmentUserRepository;

    public function __construct(DepartmentUserRepository $departmentUserRepository) {
        $this->departmentUserRepository = $departmentUserRepository;
    }

    /**
     * @param int $id
     * @return DepartmentUser
     * @throws Exception
     */
    public function perform(int $id) : DepartmentUser {
        return $this->departmentUserRepository->fetchById($id);
    }
}
