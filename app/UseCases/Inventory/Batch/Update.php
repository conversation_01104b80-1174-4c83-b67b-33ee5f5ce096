<?php

namespace App\UseCases\Inventory\Batch;

use App\Domains\Inventory\Batch;
use App\Factories\Inventory\BatchFactory;
use App\Http\Requests\Batch\UpdateRequest;
use App\Repositories\BatchRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private BatchRepository $batchRepository;
    private BatchFactory $batchFactory;

    public function __construct(BatchRepository $batchRepository, BatchFactory $batchFactory) {
        $this->batchRepository = $batchRepository;
        $this->batchFactory = $batchFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Batch
     */
    public function perform(UpdateRequest $request, int $id) : Batch {
        DB::beginTransaction();

        $domain = $this->batchFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $batch = $this->batchRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $batch;
    }
}
