<?php

namespace App\UseCases\Inventory\Batch;

use App\Domains\Filters\BatchFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\BatchRepository;

class GetAll
{
    private BatchRepository $batchRepository;

    public function __construct(BatchRepository $batchRepository) {
        $this->batchRepository = $batchRepository;
    }

    /**
     * @return array
     */
    public function perform(BatchFilters $filters, OrderBy $orderBy) : array {
        return $this->batchRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
