<?php

namespace App\UseCases\Inventory\Batch;

use App\Domains\Inventory\Batch;
use App\Repositories\BatchRepository;
use App\UseCases\Inventory\StockEntry\CreateFromBatch;
use Exception;
use Illuminate\Support\Facades\DB;

class AddBatchToStock
{
    private BatchRepository $batchRepository;

    public function __construct(
        BatchRepository $batchRepository
    ) {
        $this->batchRepository = $batchRepository;
    }

    /**
     * @param int $id
     * @return Batch
     * @throws Exception
     *
     * @todo build trait to validate organization belonging
     */
    public function perform(int $id) : Batch {
        $organization_id = request()->user()->organization_id;

        $batch = $this->batchRepository->fetchById($id);

        if($batch->is_processed_at_stock){
            throw new Exception(
                "This batch is already processed." ,
                403
            );
        }

        if($batch->organization_id !== $organization_id){
            throw new Exception(
                "This batch don't belong to this organization." ,
                403
            );
        }

        /** @var CreateFromBatch $createFromBatchUseCase */
        $createFromBatchUseCase = app()->make(CreateFromBatch::class);
        $createFromBatchUseCase->perform($batch);

        DB::beginTransaction();
        $batch->processAtStock();
        $this->batchRepository->update($batch, $organization_id);
        DB::commit();

        return $batch;
    }


}
