<?php

namespace App\UseCases\Inventory\Batch;

use App\Domains\Inventory\Batch;
use App\Factories\Inventory\BatchFactory;
use App\Http\Requests\Batch\StoreRequest;
use App\Repositories\BatchRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private BatchRepository $batchRepository;
    private BatchFactory $batchFactory;

    public function __construct(BatchRepository $batchRepository, BatchFactory $batchFactory) {
        $this->batchRepository = $batchRepository;
        $this->batchFactory = $batchFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Batch
     */
    public function perform(StoreRequest $request) : Batch {
        DB::beginTransaction();

        $domain = $this->batchFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $batch = $this->batchRepository->store($domain);

        DB::commit();

        return $batch;
    }
}
