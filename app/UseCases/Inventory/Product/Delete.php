<?php

namespace App\UseCases\Inventory\Product;

use App\Repositories\ProductRepository;
use Exception;

class Delete
{
    private ProductRepository $productRepository;

    public function __construct(ProductRepository $productRepository) {
        $this->productRepository = $productRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $product = $this->productRepository->fetchById($id);

        if($product->organization_id !== $organization_id){
            throw new Exception(
                "This product don't belong to this organization." ,
                403
            );
        }

        return $this->productRepository->delete($product);
    }
}
