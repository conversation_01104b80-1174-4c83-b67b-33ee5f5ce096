<?php

namespace App\UseCases\Inventory\Shop;

use App\Domains\Inventory\Shop;
use App\Factories\Inventory\ShopFactory;
use App\Http\Requests\Shop\StoreRequest;
use App\Repositories\ShopRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ShopRepository $shopRepository;
    private ShopFactory $shopFactory;

    public function __construct(ShopRepository $shopRepository, ShopFactory $shopFactory) {
        $this->shopRepository = $shopRepository;
        $this->shopFactory = $shopFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Shop
     */
    public function perform(StoreRequest $request) : Shop {
        DB::beginTransaction();

        $domain = $this->shopFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $shop = $this->shopRepository->store($domain);

        DB::commit();

        return $shop;
    }
}
