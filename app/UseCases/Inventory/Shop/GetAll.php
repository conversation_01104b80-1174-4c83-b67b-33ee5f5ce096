<?php

namespace App\UseCases\Inventory\Shop;

use App\Domains\Filters\ShopFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\ShopRepository;

class GetAll
{
    private ShopRepository $shopRepository;

    public function __construct(ShopRepository $shopRepository) {
        $this->shopRepository = $shopRepository;
    }

    /**
     * @return array
     */
    public function perform(ShopFilters $filters, OrderBy $orderBy) : array {
        return $this->shopRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
