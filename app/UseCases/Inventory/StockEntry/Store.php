<?php

namespace App\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockEntryFactory;
use App\Http\Requests\StockEntry\StoreRequest;
use App\Repositories\StockEntryRepository;
use App\UseCases\Inventory\Stock\IncreaseStock;
use Illuminate\Support\Facades\DB;

class Store
{
    private StockEntryRepository $stockEntryRepository;
    private StockEntryFactory $stockEntryFactory;

    public function __construct(StockEntryRepository $stockEntryRepository, StockEntryFactory $stockEntryFactory) {
        $this->stockEntryRepository = $stockEntryRepository;
        $this->stockEntryFactory = $stockEntryFactory;
    }

    /**
     * @param StoreRequest $request
     * @return StockEntry
     */
    public function perform(StoreRequest $request) : StockEntry {
        DB::beginTransaction();

        $domain = $this->stockEntryFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;
        $domain->calculateValue();

        $stockEntry = $this->stockEntryRepository->store($domain);

        /** @var IncreaseStock $increaseStockUseCase */
        $increaseStockUseCase = app()->make(IncreaseStock::class);
        $increaseStockUseCase->perform($stockEntry);

        DB::commit();

        return $stockEntry;
    }
}
