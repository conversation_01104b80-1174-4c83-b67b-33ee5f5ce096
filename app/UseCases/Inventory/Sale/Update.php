<?php

namespace App\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Factories\Inventory\SaleFactory;
use App\Http\Requests\Sale\UpdateRequest;
use App\Repositories\SaleRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private SaleRepository $saleRepository;
    private SaleFactory $saleFactory;

    public function __construct(SaleRepository $saleRepository, SaleFactory $saleFactory) {
        $this->saleRepository = $saleRepository;
        $this->saleFactory = $saleFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Sale
     */
    public function perform(UpdateRequest $request, int $id) : Sale {
        DB::beginTransaction();
        $domain = $this->saleFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $sale = $this->saleRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $sale;
    }
}
