<?php

namespace App\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Repositories\SaleRepository;
use Exception;

class Get
{
    private SaleRepository $saleRepository;

    public function __construct(SaleRepository $saleRepository) {
        $this->saleRepository = $saleRepository;
    }

    /**
     * @param int $id
     * @return Sale
     * @throws Exception
     */
    public function perform(int $id) : Sale {
        $organization_id = request()->user()->organization_id;

        $sale = $this->saleRepository->fetchById($id);

        if($sale->organization_id !== $organization_id){
            throw new Exception(
                "This sale don't belong to this organization." ,
                403
            );
        }
        return $sale;
    }
}
