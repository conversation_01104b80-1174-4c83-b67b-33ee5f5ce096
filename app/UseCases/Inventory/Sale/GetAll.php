<?php

namespace App\UseCases\Inventory\Sale;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\SaleFilters;
use App\Domains\Inventory\Sale;
use App\Repositories\SaleRepository;

class GetAll
{
    private SaleRepository $saleRepository;

    public function __construct(SaleRepository $saleRepository) {
        $this->saleRepository = $saleRepository;
    }

    /**
     * @return array
     */
    public function perform(SaleFilters $filters, OrderBy $orderBy) : array {
        return $this->saleRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
