<?php

namespace App\UseCases\Inventory\StockExit;

use App\Domains\Inventory\StockExit;
use App\Repositories\StockExitRepository;
use Exception;

class Get
{
    private StockExitRepository $stockExitRepository;

    public function __construct(StockExitRepository $stockExitRepository) {
        $this->stockExitRepository = $stockExitRepository;
    }

    /**
     * @param int $id
     * @return StockExit
     * @throws Exception
     */
    public function perform(int $id) : StockExit {
        $organization_id = request()->user()->organization_id;

        $stockExit = $this->stockExitRepository->fetchById($id);

        if($stockExit->organization_id !== $organization_id){
            throw new Exception(
                "This stockExit don't belong to this organization." ,
                403
            );
        }
        return $stockExit;
    }
}
