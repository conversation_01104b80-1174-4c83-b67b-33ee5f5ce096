<?php

namespace App\UseCases\Inventory\StockExit;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockExitFilters;
use App\Domains\Inventory\StockExit;
use App\Repositories\StockExitRepository;

class GetAll
{
    private StockExitRepository $stockExitRepository;

    public function __construct(StockExitRepository $stockExitRepository) {
        $this->stockExitRepository = $stockExitRepository;
    }

    /**
     * @return array
     */
    public function perform(StockExitFilters $filters, OrderBy $orderBy) : array {
        return $this->stockExitRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
