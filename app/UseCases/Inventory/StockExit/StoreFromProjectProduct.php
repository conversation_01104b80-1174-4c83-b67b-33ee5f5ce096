<?php

namespace App\UseCases\Inventory\StockExit;

use App\Domains\Inventory\ProjectProduct;
use App\Domains\Inventory\Shop;
use App\Domains\Inventory\StockExit;
use App\Factories\Inventory\StockExitFactory;
use App\Repositories\StockExitRepository;
use App\UseCases\Inventory\Stock\DecreaseStock;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;

class StoreFromProjectProduct
{
    private StockExitRepository $stockExitRepository;
    private StockExitFactory $stockExitFactory;

    public function __construct(StockExitRepository $stockExitRepository, StockExitFactory $stockExitFactory) {
        $this->stockExitRepository = $stockExitRepository;
        $this->stockExitFactory = $stockExitFactory;
    }

    /**
     * @param ProjectProduct $projectProduct
     * @param Shop|null $shop
     * @return StockExit
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function perform(ProjectProduct $projectProduct, ?Shop $shop = null) : StockExit {
        $domain = $this->stockExitFactory->buildFromProjectProduct($projectProduct, $shop);
        $domain->organization_id = request()->user()->organization_id;

        $domain->calculateValue();

        $stockExit = $this->stockExitRepository->store($domain);

        /** @var DecreaseStock $decreaseStockUseCase */
        $decreaseStockUseCase = app()->make(DecreaseStock::class);
        $decreaseStockUseCase->perform($stockExit);

        return $stockExit;
    }
}
