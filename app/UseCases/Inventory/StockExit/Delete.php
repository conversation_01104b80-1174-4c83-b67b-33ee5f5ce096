<?php

namespace App\UseCases\Inventory\StockExit;

use App\Repositories\StockExitRepository;
use Exception;

class Delete
{
    private StockExitRepository $stockExitRepository;

    public function __construct(StockExitRepository $stockExitRepository) {
        $this->stockExitRepository = $stockExitRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $stockExit = $this->stockExitRepository->fetchById($id);

        if($stockExit->organization_id !== $organization_id){
            throw new Exception(
                "This stockExit don't belong to this organization." ,
                403
            );
        }

        return $this->stockExitRepository->delete($stockExit);
    }
}
