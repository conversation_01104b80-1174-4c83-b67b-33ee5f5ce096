<?php

namespace App\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Factories\Inventory\ItemFactory;
use App\Http\Requests\Item\UpdateRequest;
use App\Repositories\ItemRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private ItemRepository $itemRepository;
    private ItemFactory $itemFactory;

    public function __construct(ItemRepository $itemRepository, ItemFactory $itemFactory) {
        $this->itemRepository = $itemRepository;
        $this->itemFactory = $itemFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Item
     */
    public function perform(UpdateRequest $request, int $id) : Item {
        DB::beginTransaction();
        $domain = $this->itemFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $item = $this->itemRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $item;
    }
}
