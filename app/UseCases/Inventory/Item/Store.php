<?php

namespace App\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Factories\Inventory\ItemFactory;
use App\Http\Requests\Item\StoreRequest;
use App\Repositories\ItemRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ItemRepository $itemRepository;
    private ItemFactory $itemFactory;

    public function __construct(ItemRepository $itemRepository, ItemFactory $itemFactory) {
        $this->itemRepository = $itemRepository;
        $this->itemFactory = $itemFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Item
     */
    public function perform(StoreRequest $request) : Item {
        DB::beginTransaction();

        $domain = $this->itemFactory->buildFromStoreRequest($request);

        $item = $this->itemRepository->store($domain);

        DB::commit();

        return $item;
    }
}
