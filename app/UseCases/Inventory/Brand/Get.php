<?php

namespace App\UseCases\Inventory\Brand;

use App\Domains\Inventory\Brand;
use App\Repositories\BrandRepository;
use Exception;

class Get
{
    private BrandRepository $brandRepository;

    public function __construct(BrandRepository $brandRepository) {
        $this->brandRepository = $brandRepository;
    }

    /**
     * @param int $id
     * @return Brand
     * @throws Exception
     */
    public function perform(int $id) : Brand {
        $organization_id = request()->user()->organization_id;

        $brand = $this->brandRepository->fetchById($id);

        if($brand->organization_id !== $organization_id){
            throw new Exception(
                "This brand don't belong to this organization." ,
                403
            );
        }
        return $brand;
    }
}
