<?php

namespace App\UseCases\Inventory\Brand;

use App\Domains\Inventory\Brand;
use App\Factories\Inventory\BrandFactory;
use App\Http\Requests\Brand\UpdateRequest;
use App\Repositories\BrandRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private BrandRepository $brandRepository;
    private BrandFactory $brandFactory;

    public function __construct(BrandRepository $brandRepository, BrandFactory $brandFactory) {
        $this->brandRepository = $brandRepository;
        $this->brandFactory = $brandFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Brand
     */
    public function perform(UpdateRequest $request, int $id) : Brand {
        DB::beginTransaction();

        $domain = $this->brandFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $brand = $this->brandRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $brand;
    }
}
