<?php

namespace App\UseCases\Inventory\Brand;

use App\Repositories\BrandRepository;
use Exception;

class Delete
{
    private BrandRepository $brandRepository;

    public function __construct(BrandRepository $brandRepository) {
        $this->brandRepository = $brandRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $brand = $this->brandRepository->fetchById($id);

        if($brand->organization_id !== $organization_id){
            throw new Exception(
                "This brand don't belong to this organization." ,
                403
            );
        }

        return $this->brandRepository->delete($brand);
    }
}
