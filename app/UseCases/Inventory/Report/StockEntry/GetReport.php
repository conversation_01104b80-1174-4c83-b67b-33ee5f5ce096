<?php

namespace App\UseCases\Inventory\Report\StockEntry;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockEntryReportFilters;
use App\Domains\Inventory\Report\StockEntryReport;
use App\Repositories\StockEntryRepository;

class GetReport
{
    private StockEntryRepository $stockEntryRepository;

    public function __construct(StockEntryRepository $stockEntryRepository) {
        $this->stockEntryRepository = $stockEntryRepository;
    }

    /**
     * @return StockEntryReport
     */
    public function perform(
        StockEntryReportFilters $filters, OrderBy $orderBy, ?string $grouped_by
    ) : StockEntryReport {
        return $this->stockEntryRepository->fetchReport(
            request()->user()->organization_id,
            $filters,
            $orderBy,
            $grouped_by
        );
    }
}
