<?php

namespace App\UseCases\Inventory\Report\Sum;

use App\Domains\Inventory\Report\CountSumReport;

class GetReport
{
    /**
     * @return CountSumReport
     */
    public function perform(string $model, string $column) : CountSumReport {
        $countReport = new CountSumReport($model, $column);
        $countReport->getSum(
            request()->user()->organization_id,
        );

        return $countReport;
    }
}
