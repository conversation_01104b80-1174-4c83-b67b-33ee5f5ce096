<?php

namespace App\UseCases\Inventory\Report\StockExit;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockExitReportFilters;
use App\Domains\Inventory\Report\StockExitReport;
use App\Repositories\StockExitRepository;

class GetReport
{
    private StockExitRepository $stockExitRepository;

    public function __construct(StockExitRepository $stockExitRepository) {
        $this->stockExitRepository = $stockExitRepository;
    }

    /**
     * @return StockExitReport
     */
    public function perform(
        StockExitReportFilters $filters, OrderBy $orderBy, ?string $grouped_by
    ) : StockExitReport {
        return $this->stockExitRepository->fetchReport(
            request()->user()->organization_id,
            $filters,
            $orderBy,
            $grouped_by
        );
    }
}
