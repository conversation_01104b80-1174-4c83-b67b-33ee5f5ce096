<?php

namespace App\UseCases\Inventory\ProductHistory;

use App\Repositories\ProductHistoryRepository;
use Exception;

class Delete
{
    private ProductHistoryRepository $productHistoryRepository;

    public function __construct(ProductHistoryRepository $productHistoryRepository) {
        $this->productHistoryRepository = $productHistoryRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        return $this->productHistoryRepository->delete(
            $this->productHistoryRepository->fetchById($id)
        );
    }
}
