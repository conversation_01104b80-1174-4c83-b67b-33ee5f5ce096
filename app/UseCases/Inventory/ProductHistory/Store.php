<?php

namespace App\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\ProductHistory;
use App\Factories\Inventory\ProductHistoryFactory;
use App\Http\Requests\ProductHistory\StoreRequest;
use App\Repositories\ProductHistoryRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ProductHistoryRepository $productHistoryRepository;
    private ProductHistoryFactory $productHistoryFactory;

    public function __construct(ProductHistoryRepository $productHistoryRepository, ProductHistoryFactory $productHistoryFactory) {
        $this->productHistoryRepository = $productHistoryRepository;
        $this->productHistoryFactory = $productHistoryFactory;
    }

    /**
     * @param StoreRequest $request
     * @return ProductHistory
     */
    public function perform(StoreRequest $request) : ProductHistory {
        DB::beginTransaction();

        $domain = $this->productHistoryFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $productHistory = $this->productHistoryRepository->store($domain);

        DB::commit();

        return $productHistory;
    }
}
