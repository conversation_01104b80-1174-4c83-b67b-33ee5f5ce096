<?php

namespace App\UseCases\Inventory\Department;

use App\Domains\Inventory\Department;
use App\Factories\Inventory\DepartmentFactory;
use App\Http\Requests\Department\UpdateRequest;
use App\Repositories\DepartmentRepository;
use Illuminate\Support\Facades\DB;

class Update
{
    private DepartmentRepository $departmentRepository;
    private DepartmentFactory $departmentFactory;

    public function __construct(DepartmentRepository $departmentRepository, DepartmentFactory $departmentFactory) {
        $this->departmentRepository = $departmentRepository;
        $this->departmentFactory = $departmentFactory;
    }

    /**
     * @param UpdateRequest $request
     * @param int $id
     * @return Department
     */
    public function perform(UpdateRequest $request, int $id) : Department {
        DB::beginTransaction();
        $domain = $this->departmentFactory->buildFromUpdateRequest($request);
        $domain->id = $id;
        $department = $this->departmentRepository->update(
            $domain,
            request()->user()->organization_id
        );

        DB::commit();

        return $department;
    }
}
