<?php

namespace App\UseCases\Inventory\Department;

use App\Domains\Inventory\Department;
use App\Factories\Inventory\DepartmentFactory;
use App\Http\Requests\Department\StoreRequest;
use App\Repositories\DepartmentRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private DepartmentRepository $departmentRepository;
    private DepartmentFactory $departmentFactory;

    public function __construct(DepartmentRepository $departmentRepository, DepartmentFactory $departmentFactory) {
        $this->departmentRepository = $departmentRepository;
        $this->departmentFactory = $departmentFactory;
    }

    /**
     * @param StoreRequest $request
     * @return Department
     */
    public function perform(StoreRequest $request) : Department {
        DB::beginTransaction();

        $domain = $this->departmentFactory->buildFromStoreRequest($request);
        $domain->organization_id = request()->user()->organization_id;

        $department = $this->departmentRepository->store($domain);

        DB::commit();

        return $department;
    }
}
