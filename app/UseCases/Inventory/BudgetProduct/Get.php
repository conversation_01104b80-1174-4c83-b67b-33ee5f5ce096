<?php

namespace App\UseCases\Inventory\BudgetProduct;

use App\Domains\Inventory\BudgetProduct;
use App\Repositories\BudgetProductRepository;
use Exception;

class Get
{
    private BudgetProductRepository $budgetProductRepository;

    public function __construct(BudgetProductRepository $budgetProductRepository) {
        $this->budgetProductRepository = $budgetProductRepository;
    }

    /**
     * @param int $id
     * @return BudgetProduct
     * @throws Exception
     */
    public function perform(int $id) : BudgetProduct {
        return $this->budgetProductRepository->fetchById($id);
    }
}
