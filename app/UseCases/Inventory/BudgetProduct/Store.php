<?php

namespace App\UseCases\Inventory\BudgetProduct;

use App\Domains\Inventory\BudgetProduct;
use App\Factories\Inventory\BudgetProductFactory;
use App\Http\Requests\BudgetProduct\StoreRequest;
use App\Repositories\BudgetProductRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private BudgetProductRepository $budgetProductRepository;
    private BudgetProductFactory $budgetProductFactory;

    public function __construct(BudgetProductRepository $budgetProductRepository, BudgetProductFactory $budgetProductFactory) {
        $this->budgetProductRepository = $budgetProductRepository;
        $this->budgetProductFactory = $budgetProductFactory;
    }

    /**
     * @param StoreRequest $request
     * @return BudgetProduct
     */
    public function perform(StoreRequest $request) : BudgetProduct {
        DB::beginTransaction();

        $budgetProduct = $this->budgetProductRepository->store(
            $this->budgetProductFactory->buildFromStoreRequest($request)
        );

        DB::commit();

        return $budgetProduct;
    }
}
