<?php

namespace App\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Factories\Inventory\ProjectProductFactory;
use App\Http\Requests\ProjectProduct\StoreRequest;
use App\Repositories\ProjectProductRepository;
use Illuminate\Support\Facades\DB;

class Store
{
    private ProjectProductRepository $projectProductRepository;
    private ProjectProductFactory $projectProductFactory;

    public function __construct(ProjectProductRepository $projectProductRepository, ProjectProductFactory $projectProductFactory) {
        $this->projectProductRepository = $projectProductRepository;
        $this->projectProductFactory = $projectProductFactory;
    }

    /**
     * @param StoreRequest $request
     * @return ProjectProduct
     */
    public function perform(StoreRequest $request) : ProjectProduct {
        DB::beginTransaction();

        $domain = $this->projectProductFactory->buildFromStoreRequest($request);

        $projectProduct = $this->projectProductRepository->store($domain);

        DB::commit();

        return $projectProduct;
    }
}
