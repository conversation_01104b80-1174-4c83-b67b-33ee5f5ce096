<?php

namespace App\UseCases\Auth;

use App\Factories\UserFactory;
use App\Http\Requests\Auth\DeleteRequest;
use App\Repositories\UserRepository;

class Delete
{
    public UserRepository $userRepository;

    public function __construct(
        UserRepository $userRepository
    ){
        $this->userRepository = $userRepository;
    }

    public function perform(DeleteRequest $request) : bool {
        $this->userRepository->delete(
            $this->userRepository->fetchById(
                $request->user()->id
            )
        );

        return $request->user()->currentAccessToken()->delete();
    }
}
