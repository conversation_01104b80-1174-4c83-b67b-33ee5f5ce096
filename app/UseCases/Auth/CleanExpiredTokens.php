<?php

namespace App\UseCases\Auth;

use App\Helpers\DBLog;
use App\Repositories\PasswordResetTokenRepository;

class CleanExpiredTokens
{
    private PasswordResetTokenRepository $tokenRepository;

    public function __construct(PasswordResetTokenRepository $tokenRepository)
    {
        $this->tokenRepository = $tokenRepository;
    }

    /**
     * Clean expired and used password reset tokens
     */
    public function perform(): array
    {
        DBLog::log(
            "Starting cleanup of expired password reset tokens",
            "Auth\\CleanExpiredTokens",
            null,
            null,
            []
        );

        try {
            // Delete expired tokens
            $expiredCount = $this->tokenRepository->deleteExpiredTokens();

            // Delete used tokens older than 7 days
            $usedCount = $this->tokenRepository->deleteUsedTokens(7);

            $totalCleaned = $expiredCount + $usedCount;

            DBLog::log(
                "Password reset tokens cleanup completed",
                "Auth\\CleanExpiredTokens",
                null,
                null,
                [
                    'expired_tokens_deleted' => $expiredCount,
                    'used_tokens_deleted' => $usedCount,
                    'total_cleaned' => $totalCleaned
                ]
            );

            return [
                'expired_tokens_deleted' => $expiredCount,
                'used_tokens_deleted' => $usedCount,
                'total_cleaned' => $totalCleaned
            ];

        } catch (\Exception $e) {
            DBLog::logError(
                "Password reset tokens cleanup failed: " . $e->getMessage(),
                "Auth\\CleanExpiredTokens",
                null,
                null,
                [
                    'error' => $e->getMessage()
                ]
            );

            throw $e;
        }
    }
}
