<?php

namespace App\UseCases\Auth;

use App\Factories\Auth\PasswordResetTokenFactory;
use App\Helpers\DBLog;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Repositories\PasswordResetTokenRepository;
use App\Repositories\UserRepository;
use App\Services\Resend\Domains\PasswordResetEmail;
use App\Services\Resend\UseCases\Send as SendEmail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class RequestPasswordReset
{
    private UserRepository $userRepository;
    private PasswordResetTokenRepository $tokenRepository;
    private PasswordResetTokenFactory $tokenFactory;

    public function __construct(
        UserRepository $userRepository,
        PasswordResetTokenRepository $tokenRepository,
        PasswordResetTokenFactory $tokenFactory
    ) {
        $this->userRepository = $userRepository;
        $this->tokenRepository = $tokenRepository;
        $this->tokenFactory = $tokenFactory;
    }

    /**
     * Process password reset request
     */
    public function perform(ForgotPasswordRequest $request): bool
    {
        $email = $request->email;
        $ipAddress = $request->ip();
        $userAgent = $request->userAgent();

        // Check rate limiting
        $this->checkRateLimit($email, $ipAddress);

        // Log the attempt
        DBLog::log(
            "Password reset requested for email: {$email}",
            "Auth\\RequestPasswordReset",
            null,
            null,
            [
                'email' => $email,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent
            ]
        );

        // Check if user exists
        $user = null;
        try {
            $user = $this->userRepository->fetchByEmail($email);
        } catch (\Exception $e) {
            // User not found, but we don't reveal this for security
        }

        if (!$user) {
            // For security, we don't reveal if the email exists or not
            // But we still log the attempt
            DBLog::log(
                "Password reset requested for non-existent email: {$email}",
                "Auth\\RequestPasswordReset",
                null,
                null,
                [
                    'email' => $email,
                    'ip_address' => $ipAddress,
                    'result' => 'email_not_found'
                ]
            );
            
            // Return true to not reveal if email exists
            return true;
        }

        // Delete any existing tokens for this email
        $this->tokenRepository->deleteTokensByEmail($email);

        // Create new token
        $token = $this->tokenFactory->createForPasswordReset(
            $email,
            $user->organization_id,
            $ipAddress,
            $userAgent
        );

        // Store the token
        $this->tokenRepository->store($token);

        // Send email
        try {
            $passwordResetEmail = new PasswordResetEmail($user, $token->token);
            $sendEmailUseCase = app()->make(SendEmail::class);
            $sendEmailUseCase->perform($passwordResetEmail);

            DBLog::log(
                "Password reset email sent successfully to: {$email}",
                "Auth\\RequestPasswordReset",
                $user->organization_id,
                $user->id,
                [
                    'email' => $email,
                    'token_id' => $token->id
                ]
            );
        } catch (\Exception $e) {
            DBLog::logError(
                "Failed to send password reset email: " . $e->getMessage(),
                "Auth\\RequestPasswordReset",
                $user->organization_id ?? null,
                $user->id ?? null,
                [
                    'email' => $email,
                    'error' => $e->getMessage()
                ]
            );
            
            throw $e;
        }

        return true;
    }

    /**
     * Check rate limiting for password reset requests
     */
    private function checkRateLimit(string $email, string $ipAddress): void
    {
        // Check IP-based rate limiting
        $ipKey = 'password-reset-ip:' . $ipAddress;
        if (RateLimiter::tooManyAttempts($ipKey, 3)) {
            $seconds = RateLimiter::availableIn($ipKey);
            throw new \Exception("Muitas tentativas de recuperação de senha. Tente novamente em {$seconds} segundos.");
        }

        // Check email-based rate limiting
        $emailKey = 'password-reset-email:' . $email;
        if (RateLimiter::tooManyAttempts($emailKey, 2)) {
            $seconds = RateLimiter::availableIn($emailKey);
            throw new \Exception("Muitas tentativas de recuperação para este email. Tente novamente em {$seconds} segundos.");
        }

        // Hit the rate limiters
        RateLimiter::hit($ipKey, 300); // 5 minutes
        RateLimiter::hit($emailKey, 600); // 10 minutes
    }
}
