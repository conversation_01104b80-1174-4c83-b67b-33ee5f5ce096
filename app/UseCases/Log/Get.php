<?php

namespace App\UseCases\Log;

use App\Domains\Log;
use App\Repositories\LogRepository;
use Exception;

class Get
{
    private LogRepository $logRepository;

    public function __construct(LogRepository $logRepository) {
        $this->logRepository = $logRepository;
    }

    /**
     * @param int $id
     * @return Log
     * @throws Exception
     */
    public function perform(int $id) : Log {
       return $this->logRepository->fetchById($id);
    }
}
