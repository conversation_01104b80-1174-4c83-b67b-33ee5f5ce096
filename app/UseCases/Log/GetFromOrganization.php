<?php

namespace App\UseCases\Log;

use App\Domains\Filters\LogFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\LogRepository;

class GetFromOrganization
{
    private LogRepository $logRepository;

    public function __construct(LogRepository $logRepository) {
        $this->logRepository = $logRepository;
    }

    /**
     * @param int $organization_id
     * @param LogFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function perform(int $organization_id, LogFilters $filters, OrderBy $orderBy) : array {
        return $this->logRepository->fetchFromOrganization(
            $organization_id,
            $filters,
            $orderBy
        );
    }
}
