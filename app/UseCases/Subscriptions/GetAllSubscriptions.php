<?php

namespace App\UseCases\Subscriptions;

use App\Repositories\SubscriptionRepository;
use Illuminate\Support\Collection;

class GetAllSubscriptions
{
    public function __construct(
        private SubscriptionRepository $repository
    ) {}

    /**
     * Get all subscriptions with pagination
     */
    public function perform(array $filters = [], int $limit = 100, int $offset = 0): array
    {
        $subscriptions = empty($filters) 
            ? $this->repository->getAll($limit, $offset)
            : $this->repository->findWithFilters($filters, $limit);

        return [
            'data' => $subscriptions->map(fn($subscription) => $subscription->toArray())->toArray(),
            'meta' => [
                'total' => $this->repository->count(),
                'limit' => $limit,
                'offset' => $offset,
                'count' => $subscriptions->count(),
            ],
        ];
    }

    /**
     * Get active subscriptions
     */
    public function getActive(int $limit = 100): Collection
    {
        return $this->repository->findActive($limit);
    }

    /**
     * Get expired subscriptions
     */
    public function getExpired(int $limit = 100): Collection
    {
        return $this->repository->findExpired($limit);
    }

    /**
     * Get subscriptions expiring soon
     */
    public function getExpiring(int $days = 7, int $limit = 100): Collection
    {
        return $this->repository->findExpiring($days, $limit);
    }

    /**
     * Get courtesy subscriptions
     */
    public function getCourtesies(int $limit = 100): Collection
    {
        return $this->repository->findCourtesies($limit);
    }

    /**
     * Get trial subscriptions
     */
    public function getTrials(int $limit = 100): Collection
    {
        return $this->repository->findTrials($limit);
    }

    /**
     * Get paid subscriptions
     */
    public function getPaid(int $limit = 100): Collection
    {
        return $this->repository->findPaid($limit);
    }

    /**
     * Get subscriptions by status
     */
    public function getByStatus(string $status, int $limit = 100): Collection
    {
        return $this->repository->findByStatus($status, $limit);
    }

    /**
     * Get subscriptions by billing type
     */
    public function getByBillingType(string $billingType, int $limit = 100): Collection
    {
        return $this->repository->findByBillingType($billingType, $limit);
    }

    /**
     * Get subscriptions by cycle
     */
    public function getByCycle(string $cycle, int $limit = 100): Collection
    {
        return $this->repository->findByCycle($cycle, $limit);
    }

    /**
     * Get subscription statistics
     */
    public function getStatistics(): array
    {
        $stats = $this->repository->getStatistics();
        
        return [
            'totals' => $stats,
            'percentages' => [
                'active_percentage' => $stats['total'] > 0 ? round(($stats['active'] / $stats['total']) * 100, 2) : 0,
                'expired_percentage' => $stats['total'] > 0 ? round(($stats['expired'] / $stats['total']) * 100, 2) : 0,
                'courtesy_percentage' => $stats['total'] > 0 ? round(($stats['courtesy'] / $stats['total']) * 100, 2) : 0,
                'trial_percentage' => $stats['total'] > 0 ? round(($stats['trial'] / $stats['total']) * 100, 2) : 0,
                'paid_percentage' => $stats['total'] > 0 ? round(($stats['paid'] / $stats['total']) * 100, 2) : 0,
            ],
            'alerts' => [
                'expiring_soon' => $stats['expiring_soon'],
                'expired_count' => $stats['expired'],
                'needs_attention' => $stats['expiring_soon'] + $stats['expired'],
            ],
        ];
    }

    /**
     * Get dashboard data
     */
    public function getDashboardData(): array
    {
        $statistics = $this->getStatistics();
        $expiringSoon = $this->getExpiring(7, 10);
        $recentlyExpired = $this->getExpired(10);

        return [
            'statistics' => $statistics,
            'alerts' => [
                'expiring_soon' => $expiringSoon->map(fn($sub) => [
                    'id' => $sub->id,
                    'organization_id' => $sub->organization_id,
                    'organization_name' => $sub->organization?->name,
                    'days_until_expiration' => $sub->getDaysUntilExpiration(),
                    'access_type' => $sub->getAccessType(),
                    'value' => $sub->value,
                ])->toArray(),
                'recently_expired' => $recentlyExpired->map(fn($sub) => [
                    'id' => $sub->id,
                    'organization_id' => $sub->organization_id,
                    'organization_name' => $sub->organization?->name,
                    'expired_at' => $sub->expires_at?->format('Y-m-d'),
                    'access_type' => $sub->getAccessType(),
                    'value' => $sub->value,
                ])->toArray(),
            ],
            'quick_stats' => [
                'total_revenue' => $this->calculateTotalRevenue(),
                'monthly_revenue' => $this->calculateMonthlyRevenue(),
                'average_subscription_value' => $this->calculateAverageSubscriptionValue(),
            ],
        ];
    }

    /**
     * Search subscriptions
     */
    public function search(string $query, int $limit = 50): Collection
    {
        $filters = [];
        
        // Try to search by external reference
        if (strlen($query) >= 3) {
            $filters['external_reference'] = $query;
        }

        return $this->repository->findWithFilters($filters, $limit);
    }

    /**
     * Get subscriptions requiring action
     */
    public function getRequiringAction(): array
    {
        return [
            'expiring_soon' => $this->getExpiring(7, 50),
            'expired' => $this->getExpired(50),
            'suspended' => $this->getByStatus('SUSPENDED', 50),
            'cancelled' => $this->getByStatus('CANCELLED', 50),
        ];
    }

    /**
     * Calculate total revenue from active paid subscriptions
     */
    private function calculateTotalRevenue(): float
    {
        $paidSubscriptions = $this->repository->findPaid(1000); // Get more for calculation
        
        return $paidSubscriptions
            ->filter(fn($sub) => $sub->isActive())
            ->sum(fn($sub) => $sub->getEffectiveValue());
    }

    /**
     * Calculate monthly revenue
     */
    private function calculateMonthlyRevenue(): float
    {
        $paidSubscriptions = $this->repository->findPaid(1000);
        
        return $paidSubscriptions
            ->filter(fn($sub) => $sub->isActive())
            ->sum(function($sub) {
                $value = $sub->getEffectiveValue();
                
                // Convert to monthly value based on cycle
                return match($sub->cycle) {
                    'MONTHLY' => $value,
                    'QUARTERLY' => $value / 3,
                    'SEMIANNUAL' => $value / 6,
                    'YEARLY' => $value / 12,
                    default => $value,
                };
            });
    }

    /**
     * Calculate average subscription value
     */
    private function calculateAverageSubscriptionValue(): float
    {
        $paidSubscriptions = $this->repository->findPaid(1000);
        $activeSubscriptions = $paidSubscriptions->filter(fn($sub) => $sub->isActive());
        
        if ($activeSubscriptions->isEmpty()) {
            return 0.0;
        }

        $totalValue = $activeSubscriptions->sum(fn($sub) => $sub->getEffectiveValue());
        
        return $totalValue / $activeSubscriptions->count();
    }

    /**
     * Export subscriptions data
     */
    public function export(array $filters = []): array
    {
        $subscriptions = $this->repository->findWithFilters($filters, 10000); // Large limit for export
        
        return $subscriptions->map(function($subscription) {
            return [
                'ID' => $subscription->id,
                'Organização ID' => $subscription->organization_id,
                'Organização' => $subscription->organization?->name,
                'Status' => $subscription->getStatusDisplayName(),
                'Tipo de Cobrança' => $subscription->getBillingTypeDisplayName(),
                'Ciclo' => $subscription->getCycleDisplayName(),
                'Valor' => $subscription->value,
                'Valor Efetivo' => $subscription->getEffectiveValue(),
                'Tipo de Acesso' => $subscription->getAccessType(),
                'Iniciado em' => $subscription->started_at->format('d/m/Y'),
                'Expira em' => $subscription->expires_at?->format('d/m/Y'),
                'Próximo Vencimento' => $subscription->next_due_date?->format('d/m/Y'),
                'É Cortesia' => $subscription->is_courtesy ? 'Sim' : 'Não',
                'É Teste' => $subscription->is_trial ? 'Sim' : 'Não',
                'Pode Acessar' => $subscription->canAccessSystem() ? 'Sim' : 'Não',
                'Dias até Expiração' => $subscription->getDaysUntilExpiration(),
                'Referência Externa' => $subscription->external_reference,
                'Descrição' => $subscription->description,
                'Criado em' => $subscription->created_at?->format('d/m/Y H:i:s'),
                'Atualizado em' => $subscription->updated_at?->format('d/m/Y H:i:s'),
            ];
        })->toArray();
    }
}
