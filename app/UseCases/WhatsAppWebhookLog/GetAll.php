<?php

namespace App\UseCases\WhatsAppWebhookLog;

use App\Domains\Filters\WhatsAppWebhookLogFilters;
use App\Domains\Filters\OrderBy;
use App\Repositories\WhatsAppWebhookLogRepository;

class GetAll
{
    // TODO: separate all those use cases for fuck sake
    private WhatsAppWebhookLogRepository $repository;

    public function __construct(WhatsAppWebhookLogRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get all WhatsApp webhook logs with filters and pagination
     *
     * @param WhatsAppWebhookLogFilters $filters
     * @param OrderBy $orderBy
     * @return array
     */
    public function perform(WhatsAppWebhookLogFilters $filters, OrderBy $orderBy): array
    {
        $organization_id = request()->user()->organization_id;

        // TODO: when profile is ready, check if user is super admin
        $isSuperAdmin = true;
        if (!$isSuperAdmin) {
            return $this->repository->fetchFromOrganization($organization_id, $filters, $orderBy);
        }

        return $this->repository->fetchAll($filters, $orderBy);
    }

    /**
     * Get recent webhook logs
     *
     * @param int $hours
     * @param int $limit
     * @return array
     */
    public function getRecent(int $hours = 24, int $limit = 100): array
    {
        $filters = [];

        // Add organization filter if user is authenticated
        if (auth()->check()) {
            $user = auth()->user();
            if ($user->organization_id ?? false) {
                $filters['organization_id'] = $user->organization_id ?? false;
            }
        }

        $filters['hours'] = $hours;

        return $this->repository->fetchWithFilters($filters, 1, $limit)['data'];
    }

    /**
     * Get webhook logs by event type
     *
     * @param string $eventType
     * @param int $limit
     * @return array
     */
    public function getByEventType(string $eventType, int $limit = 50): array
    {
        $filters = ['event_type' => $eventType];

        // Add organization filter if user is authenticated
        if (auth()->check()) {
            $user = auth()->user();
            if ($user->organization_id ?? false) {
                $filters['organization_id'] = $user->organization_id ?? false;
            }
        }

        return $this->repository->fetchWithFilters($filters, 1, $limit)['data'];
    }

    /**
     * Get webhook logs by processing status
     *
     * @param string $status
     * @param int $limit
     * @return array
     */
    public function getByProcessingStatus(string $status, int $limit = 50): array
    {
        $filters = ['processing_status' => $status];

        // Add organization filter if user is authenticated
        if (auth()->check()) {
            $user = auth()->user();
            if ($user->organization_id ?? false) {
                $filters['organization_id'] = $user->organization_id ?? false;
            }
        }

        return $this->repository->fetchWithFilters($filters, 1, $limit)['data'];
    }

    /**
     * Get webhook logs statistics
     *
     * @param int $hours
     * @return array
     */
    public function getStatistics(int $hours = 24): array
    {
        // For now, return global statistics
        // TODO: Filter by organization if needed
        return $this->repository->getStatistics($hours);
    }
}
