<?php

namespace App\UseCases\WhatsAppWebhookLog;

use App\Domains\WhatsAppWebhookLog;
use App\Repositories\WhatsAppWebhookLogRepository;
use Exception;

class Get
{
    private WhatsAppWebhookLogRepository $repository;

    public function __construct(WhatsAppWebhookLogRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Get a specific WhatsApp webhook log by ID
     *
     * @param int $id
     * @return WhatsAppWebhookLog
     * @throws Exception
     */
    public function perform(int $id): WhatsAppWebhookLog
    {
        $organization_id = request()->user()->organization_id;

        $log = $this->repository->fetchById($id);

        if($log->organization_id !== $organization_id){
            throw new Exception(
                "This log don't belong to this organization." ,
                403
            );
        }

        return $log;
    }
}
