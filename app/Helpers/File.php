<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;

class File {

    private UploadedFile $file;
    private string $originalName;
    private string $originalExtension;
    private string $mimeType;
    private int $size;

    private string $disk;

    public function __construct(
        UploadedFile $file,
        string $disk = "public"
    ){
        $this->file = $file;
        $this->originalName = $file->getClientOriginalName();
        $this->originalExtension = $file->getClientOriginalExtension();
        $this->mimeType = $file->getClientMimeType();
        $this->size = $file->getSize();
        $this->disk = $disk;
    }

    public function upload(string $filepath) : bool|string {
        return $this->file->store($filepath, $this->disk);
    }
    public function uploadAs(string $filepath, string $filename) : bool|string {
        return $this->file->storeAs($filepath, $filename, $this->disk);
    }
    public function uploadAsOriginalName(string $filepath) : bool|string {
        return $this->file->storeAs($filepath, $this->originalName, $this->disk);
    }

    public function getMeta() : array {
        return [
            'originalName' => $this->originalName,
            'originalExtension' => $this->originalExtension,
            'mimeType' => $this->mimeType,
            'size' => $this->size
        ];
    }

    public function getRealPath() : string {
        return $this->file->getRealPath();
    }

}
