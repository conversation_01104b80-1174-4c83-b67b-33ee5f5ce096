<?php

namespace App\Helpers\Traits;

trait PeopleTyped
{
    public ?string $document;

    private const JURIDIC_PEOPLE_TYPES = [
        'MEI',
        'EIRELI',
        'LTDA',
        'S.A.',
        'S/C',
        'S/C LTDA',
        'S/C MEI',
        'S/C EIRELI',
        'S/C LTDA MEI',
        'S/C LTDA EIRELI',
    ];

    private function setDocumentOnlyNumbers(): void
    {
        $this->document = preg_replace('/\D/', '', $this->document);
    }

    private const JURIDIC_PEOPLE_TYPE = "CNPJ";
    private const PERSONAL_PEOPLE_TYPE = "CPF";
    private const INVALID_PEOPLE_TYPE = "INVALID";

    public function peopleType(): string
    {
        if ($this->isValidCpf()) {
            return self::PERSONAL_PEOPLE_TYPE;
        } elseif ($this->isValidCnpj()) {
            return self::JURIDIC_PEOPLE_TYPE;
        }
        return self::INVALID_PEOPLE_TYPE;
    }

    public function isValidCpf(): bool
    {
        if (strlen($this->document) !== 11 || preg_match('/(\d)\1{10}/', $this->document)) {
            return false;
        }

        for ($t = 9; $t < 11; $t++) {
            for ($d = 0, $c = 0; $c < $t; $c++) {
                $d += $this->document[$c] * (($t + 1) - $c);
            }
            $d = ((10 * $d) % 11) % 10;
            if ($this->document[$c] != $d) {
                return false;
            }
        }

        return true;
    }

    public function isValidCnpj(): bool
    {
        if (strlen($this->document) !== 14 || preg_match('/(\d)\1{13}/', $this->document)) {
            return false;
        }

        $weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
        $weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum += $this->document[$i] * $weights1[$i];
        }
        $remainder = $sum % 11;
        $digit1 = $remainder < 2 ? 0 : 11 - $remainder;

        if ($this->document[12] != $digit1) {
            return false;
        }

        $sum = 0;
        for ($i = 0; $i < 13; $i++) {
            $sum += $this->document[$i] * $weights2[$i];
        }
        $remainder = $sum % 11;
        $digit2 = $remainder < 2 ? 0 : 11 - $remainder;

        return $this->document[13] == $digit2;
    }

    public function isValidDocument(): bool
    {
        $peopleType = $this->peopleType();
        if ($peopleType === self::PERSONAL_PEOPLE_TYPE) {
            return $this->isValidCpf();
        } elseif ($peopleType === self::JURIDIC_PEOPLE_TYPE) {
            return $this->isValidCnpj();
        }
        return false;
    }
}
