<?php

namespace App\Helpers\Traits;

use Illuminate\Http\JsonResponse;

trait Response
{
    public function response(
        string $message,
        string $status = "success",
        int $status_code = 200,
        ?array $data = [],
        ?array $errors = null,
        ?array $pagination = null
    ) : JsonResponse {
        return response()->json([
            'status' => $status,
            'message' => $message,
            'data' => $data,
            'errors' => $errors,
            'pagination' => $pagination
        ], $status_code);
    }


    public function errorResponse(
        string $message,
        int|string $status_code = 500,
        ?array $data = [],
        ?array $errors = null
    ) : JsonResponse {
        return response()->json([
            'status' => "error",
            'message' => $message,
            'data' => $data,
            'errors' => $errors
        ], $status_code);
    }
}
