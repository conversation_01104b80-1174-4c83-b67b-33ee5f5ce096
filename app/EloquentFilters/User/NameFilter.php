<?php

namespace App\EloquentFilters\User;

use Fouladgar\EloquentBuilder\Support\Foundation\Contracts\Filter;
use Illuminate\Database\Eloquent\Builder;

class NameFilter extends Filter
{
    /**
     * Apply the condition to the query.
     */
    public function apply(Builder $builder, mixed $value): Builder
    {
        return $builder->where(function ($query) use ($value) {
            $query->where('first_name', 'LIKE', '%'.$value.'%')
                  ->orWhere('last_name', 'LIKE', '%'.$value.'%');
        });
    }
}
