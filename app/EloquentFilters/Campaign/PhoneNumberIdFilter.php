<?php

namespace App\EloquentFilters\Campaign;

use Fouladgar\EloquentBuilder\Support\Foundation\Contracts\Filter;
use Illuminate\Database\Eloquent\Builder;

class PhoneNumberIdFilter extends Filter
{
    /**
     * Apply the filter to the given query.
     *
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function apply(Builder $builder, mixed $value): Builder
    {
        return $builder->where('phone_number_id', $value);
    }
}
