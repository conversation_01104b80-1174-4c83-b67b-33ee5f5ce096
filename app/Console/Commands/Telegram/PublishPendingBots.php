<?php

namespace App\Console\Commands\Telegram;

use Illuminate\Console\Command;
use App\Services\Telegram\UseCases\TelegramBot\PublishPendingBots as PublishPendingBotsUseCase;

class PublishPendingBots extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:publish-pending-bots';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publishes all Telegram bots with pending_publication status';

    /**
     * The PublishPendingBots use case.
     *
     * @var PublishPendingBotsUseCase
     */
    protected $publishPendingBotsUseCase;

    /**
     * Create a new command instance.
     *
     * @param PublishPendingBotsUseCase $publishPendingBotsUseCase
     * @return void
     */
    public function __construct(PublishPendingBotsUseCase $publishPendingBotsUseCase)
    {
        parent::__construct();
        $this->publishPendingBotsUseCase = $publishPendingBotsUseCase;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Publishing pending Telegram bots...');

        try {
            $results = $this->publishPendingBotsUseCase->perform();

            if (empty($results['published']) && empty($results['errors'])) {
                $this->info('No pending bots found for publishing.');
                return Command::SUCCESS;
            }

            foreach ($results['published'] as $bot) {
                $this->info("Published bot: {$bot['name']} (ID: {$bot['id']}) with webhook URL: {$bot['webhook_url']}");
            }

            foreach ($results['errors'] as $bot) {
                $this->error("Failed to publish bot: {$bot['name']} (ID: {$bot['id']}). Error: {$bot['error']}");
            }

            $this->info('Finished publishing pending bots.');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('An error occurred while publishing pending bots: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
