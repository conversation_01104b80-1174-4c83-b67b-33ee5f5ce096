<?php

namespace App\Console\Commands\ChatBot;

use Illuminate\Console\Command;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ValidateFlow;
use App\Repositories\FlowRepository;

class ValidateFlows extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chatbot:validate-flows
                            {--flow-id= : Validate specific flow by ID}
                            {--organization-id= : Validate flows for specific organization}
                            {--no-cache : Skip cache and force fresh validation}
                            {--fix : Attempt to fix common issues automatically}
                            {--export= : Export results to file (json|csv)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate ChatBot flow integrity and structure';

    protected ValidateFlow $validateFlow;
    protected FlowRepository $flowRepository;

    public function __construct(
        ValidateFlow $validateFlow,
        FlowRepository $flowRepository
    ) {
        parent::__construct();
        $this->validateFlow = $validateFlow;
        $this->flowRepository = $flowRepository;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $flowId = $this->option('flow-id');
        $organizationId = $this->option('organization-id');
        $useCache = !$this->option('no-cache');
        $fix = $this->option('fix');
        $export = $this->option('export');

        $this->info('ChatBot Flow Validation');
        $this->info('========================');

        try {
            if ($flowId) {
                return $this->validateSingleFlow($flowId, $useCache, $fix, $export);
            } else {
                return $this->validateMultipleFlows($organizationId, $useCache, $fix, $export);
            }
        } catch (\Exception $e) {
            $this->error('Validation failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Validate a single flow
     *
     * @param int $flowId
     * @param bool $useCache
     * @param bool $fix
     * @param string|null $export
     * @return int
     */
    protected function validateSingleFlow(int $flowId, bool $useCache, bool $fix, ?string $export): int
    {
        $this->info("Validating flow ID: {$flowId}");

        $flow = $this->flowRepository->findById($flowId);
        if (!$flow) {
            $this->error("Flow with ID {$flowId} not found");
            return Command::FAILURE;
        }

        $result = $this->validateFlow->perform($flow, $useCache);

        $this->displaySingleFlowResult($flow, $result);

        if ($fix && !$result['valid']) {
            $this->attemptFixes($flow, $result);
        }

        if ($export) {
            $this->exportResults([$flowId => $result], $export);
        }

        return $result['valid'] ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * Validate multiple flows
     *
     * @param int|null $organizationId
     * @param bool $useCache
     * @param bool $fix
     * @param string|null $export
     * @return int
     */
    protected function validateMultipleFlows(?int $organizationId, bool $useCache, bool $fix, ?string $export): int
    {
        $this->info('Loading flows...');

        $flows = $organizationId
            ? $this->flowRepository->findByOrganizationId($organizationId)
            : $this->flowRepository->findAll();

        if (empty($flows)) {
            $this->warn('No flows found to validate');
            return Command::SUCCESS;
        }

        $this->info("Found " . count($flows) . " flows to validate");

        $results = $this->validateFlow->validateMultiple($flows, $useCache);

        $this->displayMultipleFlowResults($results);

        if ($fix) {
            $this->attemptMultipleFixes($flows, $results['results']);
        }

        if ($export) {
            $this->exportResults($results, $export);
        }

        return $results['summary']['invalid_flows'] === 0 ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * Display results for a single flow
     *
     * @param mixed $flow
     * @param array $result
     */
    protected function displaySingleFlowResult($flow, array $result): void
    {
        $this->line('');
        $this->info("Flow: {$flow->name} (ID: {$flow->id})");
        $this->line("Steps: {$result['total_steps']}");

        if ($result['valid']) {
            $this->info('✓ Flow is valid');
        } else {
            $this->error('✗ Flow has validation errors');
        }

        if (!empty($result['errors'])) {
            $this->line('');
            $this->error('Errors:');
            foreach ($result['errors'] as $error) {
                $this->line("  • {$error}");
            }
        }

        if (!empty($result['warnings'])) {
            $this->line('');
            $this->warn('Warnings:');
            foreach ($result['warnings'] as $warning) {
                $this->line("  • {$warning}");
            }
        }

        if ($result['from_cache'] ?? false) {
            $this->line('');
            $this->comment('(Results from cache)');
        }
    }

    /**
     * Display results for multiple flows
     *
     * @param array $results
     */
    protected function displayMultipleFlowResults(array $results): void
    {
        $summary = $results['summary'];

        $this->line('');
        $this->info('Validation Summary:');
        $this->line("Total flows: {$summary['total_flows']}");
        $this->line("Valid flows: {$summary['valid_flows']}");

        if ($summary['invalid_flows'] > 0) {
            $this->error("Invalid flows: {$summary['invalid_flows']}");
        }

        if ($summary['flows_with_warnings'] > 0) {
            $this->warn("Flows with warnings: {$summary['flows_with_warnings']}");
        }

        $this->line("Total errors: {$summary['total_errors']}");
        $this->line("Total warnings: {$summary['total_warnings']}");

        // Show details for invalid flows
        if ($summary['invalid_flows'] > 0) {
            $this->line('');
            $this->error('Invalid Flows Details:');

            foreach ($results['results'] as $flowId => $result) {
                if (!$result['valid']) {
                    $this->line('');
                    $this->line("Flow ID {$flowId}: {$result['flow_name']}");
                    foreach ($result['errors'] as $error) {
                        $this->line("  • {$error}");
                    }
                }
            }
        }
    }

    /**
     * Attempt to fix common issues
     *
     * @param mixed $flow
     * @param array $result
     */
    protected function attemptFixes($flow, array $result): void
    {
        $this->line('');
        $this->info('Attempting to fix issues...');

        // This is a placeholder for automatic fixes
        // In a real implementation, you would analyze the errors and attempt fixes

        $this->warn('Automatic fixes not yet implemented');
    }

    /**
     * Attempt to fix issues for multiple flows
     *
     * @param array $flows
     * @param array $results
     */
    protected function attemptMultipleFixes(array $flows, array $results): void
    {
        $invalidFlows = array_filter($results, fn($result) => !$result['valid']);

        if (empty($invalidFlows)) {
            return;
        }

        $this->line('');
        $this->info('Attempting to fix issues for ' . count($invalidFlows) . ' flows...');

        foreach ($invalidFlows as $flowId => $result) {
            $flow = array_values(array_filter($flows, fn($f) => $f->id === $flowId))[0] ?? null;
            if ($flow) {
                $this->attemptFixes($flow, $result);
            }
        }
    }

    /**
     * Export results to file
     *
     * @param array $results
     * @param string $format
     */
    protected function exportResults(array $results, string $format): void
    {
        $filename = 'flow_validation_' . date('Y-m-d_H-i-s') . '.' . $format;
        $path = storage_path('app/' . $filename);

        try {
            if ($format === 'json') {
                file_put_contents($path, json_encode($results, JSON_PRETTY_PRINT));
            } elseif ($format === 'csv') {
                $this->exportToCsv($results, $path);
            } else {
                $this->error("Unsupported export format: {$format}");
                return;
            }

            $this->info("Results exported to: {$path}");
        } catch (\Exception $e) {
            $this->error("Failed to export results: {$e->getMessage()}");
        }
    }

    /**
     * Export results to CSV format
     *
     * @param array $results
     * @param string $path
     */
    protected function exportToCsv(array $results, string $path): void
    {
        $handle = fopen($path, 'w');

        // Write header
        fputcsv($handle, ['Flow ID', 'Flow Name', 'Valid', 'Errors', 'Warnings', 'Total Steps']);

        // Write data
        $flowResults = $results['results'] ?? $results;
        foreach ($flowResults as $flowId => $result) {
            fputcsv($handle, [
                $flowId,
                $result['flow_name'] ?? '',
                $result['valid'] ? 'Yes' : 'No',
                implode('; ', $result['errors'] ?? []),
                implode('; ', $result['warnings'] ?? []),
                $result['total_steps'] ?? 0
            ]);
        }

        fclose($handle);
    }
}
