<?php

namespace App\Console\Commands\WhatsApp;

use Illuminate\Console\Command;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Domains\ChatBot\Step;
use App\Domains\Inventory\Client;

class TestDynamicInput extends Command
{
    protected $signature = 'whatsapp:test-input';
    protected $description = 'Test the dynamic input processing service';

    public function handle()
    {
        $this->info("🧪 Testing Dynamic Input Processing Service");
        $this->info("==========================================");

        $dynamicInputService = app()->make(DynamicInputService::class);

        // Create sample objects
        $client = $this->createSampleClient();
        $conversation = $this->createSampleConversation($client);

        // Test cases
        $testCases = [
            [
                'name' => 'Valid Name Input',
                'step_config' => [
                    'input_field' => 'client.name',
                    'required' => true,
                    'min_length' => 2,
                    'max_length' => 100
                ],
                'input_value' => 'John Doe',
                'expected_success' => true
            ],
            [
                'name' => 'Valid Email Input',
                'step_config' => [
                    'input_field' => 'client.email',
                    'required' => true
                ],
                'input_value' => '<EMAIL>',
                'expected_success' => true
            ],
            [
                'name' => 'Invalid Email Input',
                'step_config' => [
                    'input_field' => 'client.email',
                    'required' => true
                ],
                'input_value' => 'invalid-email',
                'expected_success' => false
            ],
            [
                'name' => 'Valid Phone Input',
                'step_config' => [
                    'input_field' => 'client.phone',
                    'required' => true,
                    'min_length' => 10,
                    'max_length' => 15
                ],
                'input_value' => '11999887766',
                'expected_success' => true
            ],
            [
                'name' => 'Too Short Name',
                'step_config' => [
                    'input_field' => 'client.name',
                    'required' => true,
                    'min_length' => 5
                ],
                'input_value' => 'Jo',
                'expected_success' => false
            ],
            [
                'name' => 'Empty Required Field',
                'step_config' => [
                    'input_field' => 'client.name',
                    'required' => true
                ],
                'input_value' => '',
                'expected_success' => false
            ],
            [
                'name' => 'Valid Address Input',
                'step_config' => [
                    'input_field' => 'client.address',
                    'required' => true,
                    'min_length' => 10
                ],
                'input_value' => 'Rua das Flores, 123, Centro',
                'expected_success' => true
            ]
        ];

        foreach ($testCases as $index => $testCase) {
            $this->info("\n" . ($index + 1) . ". Testing: " . $testCase['name']);
            $this->info("Input: '" . $testCase['input_value'] . "'");
            $this->info("Field: " . $testCase['step_config']['input_field']);
            
            // Create step with test configuration
            $step = $this->createTestStep($testCase['step_config']);
            
            // Create interaction with test input
            $interaction = $this->createTestInteraction($testCase['input_value']);
            
            try {
                // Process the input
                $result = $dynamicInputService->processInputStep($step, $interaction, $conversation);
                
                $this->info("Success: " . ($result['success'] ? 'true' : 'false'));
                
                if ($result['success']) {
                    $this->info("Updated: " . $result['updated_field'] . " = '" . $result['updated_value'] . "'");
                } else {
                    $this->info("Error: " . $result['error']);
                }
                
                // Validate result
                if ($result['success'] === $testCase['expected_success']) {
                    $this->info("✅ Test passed!");
                } else {
                    $this->error("❌ Test failed! Expected success: " . ($testCase['expected_success'] ? 'true' : 'false'));
                }
                
            } catch (\Exception $e) {
                $this->error("❌ Exception: " . $e->getMessage());
                if ($testCase['expected_success']) {
                    $this->error("❌ Test failed! Expected success but got exception");
                } else {
                    $this->info("✅ Test passed! Expected failure and got exception");
                }
            }
        }

        // Test input configuration parsing
        $this->info("\n🔍 Testing Input Configuration Parsing");
        $this->info("=====================================");
        
        $configTests = [
            [
                'name' => 'Valid client.name configuration',
                'json' => json_encode([
                    'input_field' => 'client.name',
                    'required' => true,
                    'min_length' => 2
                ]),
                'expected_model' => 'client',
                'expected_field' => 'name'
            ],
            [
                'name' => 'Valid client.email configuration',
                'json' => json_encode([
                    'input_field' => 'client.email',
                    'required' => true
                ]),
                'expected_model' => 'client',
                'expected_field' => 'email'
            ]
        ];

        foreach ($configTests as $configTest) {
            $this->info("\n• " . $configTest['name']);
            
            $step = new Step(
                1, 1, 1, 'test_step', 'test', 1, null, null,
                false, false, false, false, false, true,
                $configTest['json']
            );
            
            $reflection = new \ReflectionClass($dynamicInputService);
            $method = $reflection->getMethod('getInputConfiguration');
            $method->setAccessible(true);
            
            $config = $method->invoke($dynamicInputService, $step);
            
            if ($config && $config['model'] === $configTest['expected_model'] && $config['field'] === $configTest['expected_field']) {
                $this->info("  ✅ Configuration parsed correctly");
                $this->info("  Model: " . $config['model'] . ", Field: " . $config['field']);
            } else {
                $this->error("  ❌ Configuration parsing failed");
                $this->error("  Expected: " . $configTest['expected_model'] . "." . $configTest['expected_field']);
                $this->error("  Got: " . ($config ? $config['model'] . "." . $config['field'] : 'null'));
            }
        }

        $this->info("\n🎉 Dynamic Input Testing Complete!");
        
        return 0;
    }

    private function createSampleClient(): Client
    {
        return new Client(
            1, // id
            1, // organization_id
            'Test User', // name
            '11999887766', // phone
            '<EMAIL>', // email
            'Developer', // profession
            '1990-01-01', // birthdate
            '12345678901', // cpf
            null, // cnpj
            'Test Service', // service
            'Test Address', // address
            '123', // number
            'Test Neighborhood', // neighborhood
            '12345-678', // cep
            'Apt 1', // complement
            'Single', // civil_state
            'Test client for dynamic input' // description
        );
    }

    private function createSampleConversation(Client $client): WhatsAppConversation
    {
        return new WhatsAppConversation(
            1, // id
            1, // organization_id
            null, // user_id
            $client->id, // client_id
            1, // flow_id
            1, // phone_number_id
            1, // current_step_id
            null, // json
            false, // is_finished
            now(), // created_at
            now(), // updated_at
            'Test Contact', // whatsapp_contact_name
            'Test Profile', // whatsapp_profile_name
            [], // whatsapp_metadata
            $client // client
        );
    }

    private function createTestStep(array $config): Step
    {
        return new Step(
            1, // id
            1, // organization_id
            1, // flow_id
            'test_step', // step
            'test', // type
            1, // position
            null, // next_step
            null, // earlier_step
            false, // is_initial_step
            false, // is_ending_step
            false, // is_message
            false, // is_interactive
            false, // is_command
            true, // is_input
            json_encode($config) // json
        );
    }

    private function createTestInteraction(string $inputValue): WhatsAppInteraction
    {
        return new WhatsAppInteraction(
            1, // id
            1, // organization_id
            null, // user_id
            1, // client_id
            1, // flow_id
            1, // step_id
            1, // conversation_id
            $inputValue, // message
            null, // answer
            null, // result
            null, // json
            now(), // created_at
            now(), // updated_at
            'test_msg_123', // whatsapp_message_id
            'text', // whatsapp_message_type
            ['text' => ['body' => $inputValue]] // whatsapp_raw_data
        );
    }
}
