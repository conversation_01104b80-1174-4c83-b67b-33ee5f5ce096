<?php

namespace App\Console\Commands\Auth;

use App\UseCases\Auth\CleanExpiredTokens as CleanExpiredTokensUseCase;
use Illuminate\Console\Command;

class CleanExpiredTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:clean-expired-tokens';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean expired and used password reset tokens';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting cleanup of expired password reset tokens...');

        try {
            /** @var CleanExpiredTokensUseCase $useCase */
            $useCase = app()->make(CleanExpiredTokensUseCase::class);
            $result = $useCase->perform();

            $this->info("Cleanup completed successfully:");
            $this->line("- Expired tokens deleted: {$result['expired_tokens_deleted']}");
            $this->line("- Used tokens deleted: {$result['used_tokens_deleted']}");
            $this->line("- Total tokens cleaned: {$result['total_cleaned']}");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Cleanup failed: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
