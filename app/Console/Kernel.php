<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('telegram:publish-pending-bots')->everyFiveMinutes();

        $schedule->command('whatsapp:send-messages')->everyMinute()->withoutOverlapping();

        $schedule->command('whatsapp:publish-templates')->everyMinute()->withoutOverlapping();

        $schedule->command('auth:clean-expired-tokens')->daily();

        $schedule->command('asaas:process-logs')->daily();

        // Campaign status synchronization job
        // $schedule->job(new \App\Jobs\SyncCampaignStatusWithBooleans)->everyFiveMinutes();
        // Message retry processing job
        // $schedule->job(new \App\Jobs\ProcessMessageRetries)->everyMinute();
        // Proactive WhatsApp sync job
        // $schedule->job(new \App\Jobs\ProactiveWhatsAppSync)->everyFiveMinutes();
        // Campaign analytics calculation job
        // $schedule->job(new \App\Jobs\CalculateCampaignAnalytics)->hourly();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
