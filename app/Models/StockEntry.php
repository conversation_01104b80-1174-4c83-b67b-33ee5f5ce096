<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockEntry extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'stock_entries';

    protected $fillable = [
        'organization_id',
        'shop_id',
        'user_id',
        'brand_id',
        'product_id',
        'batch_id',
        'client_id',
        'project_id',
        'quantity',
        'value',
        'description',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function user(){
        return $this->belongsTo(User::class);
    }
    public function brand(){
        return $this->belongsTo(Brand::class);
    }
    public function product(){
        return $this->belongsTo(Product::class);
    }
    public function batch(){
        return $this->belongsTo(Batch::class);
    }
    public function client(){
        return $this->belongsTo(Client::class);
    }
    public function project(){
        return $this->belongsTo(Project::class);
    }
    public function shop(){
        return $this->belongsTo(Shop::class);
    }
}
