<?php

namespace App\Models;

use App\Enums\ChatBot\ComponentFormat;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Component extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'components';

    protected $fillable = [
        'organization_id',
        'step_id',
        'template_id',
        'name',
        'type',
        'sub_type',
        'index',
        'format',
        'text',
        'json',
    ];

    protected $casts = [
        'format' => ComponentFormat::class,
    ];

    public function step(){
        return $this->belongsTo(Step::class);
    }

    public function organization(){
        return $this->belongsTo(Organization::class);
    }

    public function buttons() {
        return $this->belongsToMany(Button::class, 'buttons_components', 'component_id', 'button_id');
    }

    public function template() {
        return $this->belongsTo(Template::class);
    }

    public function parameters() {
        return $this->hasMany(Parameter::class);
    }
}
