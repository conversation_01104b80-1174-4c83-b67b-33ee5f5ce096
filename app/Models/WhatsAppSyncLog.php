<?php

namespace App\Models;

use App\Enums\SyncType;
use App\Enums\SyncStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WhatsAppSyncLog extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_sync_logs';

    protected $fillable = [
        'sync_type',
        'entity_id',
        'status',
        'response_data_json',
        'error_message',
        'messages_synced',
        'messages_updated',
        'synced_at',
    ];

    protected $casts = [
        'sync_type' => SyncType::class,
        'status' => SyncStatus::class,
        'response_data_json' => 'array',
        'messages_synced' => 'integer',
        'messages_updated' => 'integer',
        'synced_at' => 'datetime',
    ];

    /**
     * Get the entity being synced (Message or Campaign)
     */
    public function entity()
    {
        return match($this->sync_type) {
            SyncType::MESSAGE => $this->belongsTo(Message::class, 'entity_id'),
            SyncType::CAMPAIGN => $this->belongsTo(Campaign::class, 'entity_id'),
        };
    }

    /**
     * Check if sync was successful
     */
    public function wasSuccessful(): bool
    {
        return $this->status === SyncStatus::SUCCESS;
    }

    /**
     * Check if sync failed
     */
    public function failed(): bool
    {
        return $this->status === SyncStatus::FAILED;
    }

    /**
     * Check if sync was partial
     */
    public function wasPartial(): bool
    {
        return $this->status === SyncStatus::PARTIAL;
    }

    /**
     * Get sync duration in minutes
     */
    public function getDurationSinceSync(): int
    {
        return now()->diffInMinutes($this->synced_at);
    }

    /**
     * Scope for recent sync logs
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('synced_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for failed syncs
     */
    public function scopeFailed($query)
    {
        return $query->where('status', SyncStatus::FAILED);
    }

    /**
     * Scope for successful syncs
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', SyncStatus::SUCCESS);
    }
}
