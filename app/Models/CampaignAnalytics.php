<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CampaignAnalytics extends Model
{
    use HasFactory;

    protected $table = 'campaign_analytics';

    protected $fillable = [
        'campaign_id',
        'total_messages',
        'sent_count',
        'delivered_count',
        'failed_count',
        'read_count',
        'response_count',
        'avg_delivery_time',
        'delivery_rate',
        'read_rate',
        'response_rate',
        'failure_rate',
        'calculated_at',
    ];

    protected $casts = [
        'total_messages' => 'integer',
        'sent_count' => 'integer',
        'delivered_count' => 'integer',
        'failed_count' => 'integer',
        'read_count' => 'integer',
        'response_count' => 'integer',
        'avg_delivery_time' => 'decimal:2',
        'delivery_rate' => 'decimal:2',
        'read_rate' => 'decimal:2',
        'response_rate' => 'decimal:2',
        'failure_rate' => 'decimal:2',
        'calculated_at' => 'datetime',
    ];

    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Calculate all rates based on counts
     */
    public function calculateRates(): void
    {
        if ($this->total_messages > 0) {
            $this->delivery_rate = round(($this->delivered_count / $this->total_messages) * 100, 2);
            $this->failure_rate = round(($this->failed_count / $this->total_messages) * 100, 2);
        }

        if ($this->delivered_count > 0) {
            $this->read_rate = round(($this->read_count / $this->delivered_count) * 100, 2);
            $this->response_rate = round(($this->response_count / $this->delivered_count) * 100, 2);
        }
    }

    /**
     * Get performance grade based on metrics
     */
    public function getPerformanceGrade(): string
    {
        $score = 0;
        
        // Delivery rate weight: 40%
        if ($this->delivery_rate >= 95) $score += 40;
        elseif ($this->delivery_rate >= 90) $score += 35;
        elseif ($this->delivery_rate >= 80) $score += 30;
        elseif ($this->delivery_rate >= 70) $score += 20;
        else $score += 10;

        // Read rate weight: 30%
        if ($this->read_rate >= 80) $score += 30;
        elseif ($this->read_rate >= 70) $score += 25;
        elseif ($this->read_rate >= 60) $score += 20;
        elseif ($this->read_rate >= 50) $score += 15;
        else $score += 10;

        // Response rate weight: 30%
        if ($this->response_rate >= 20) $score += 30;
        elseif ($this->response_rate >= 15) $score += 25;
        elseif ($this->response_rate >= 10) $score += 20;
        elseif ($this->response_rate >= 5) $score += 15;
        else $score += 10;

        return match(true) {
            $score >= 90 => 'A',
            $score >= 80 => 'B',
            $score >= 70 => 'C',
            $score >= 60 => 'D',
            default => 'F'
        };
    }

    /**
     * Check if metrics are recent (calculated within last hour)
     */
    public function isRecent(): bool
    {
        return $this->calculated_at && $this->calculated_at->diffInHours(now()) <= 1;
    }
}
