<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ListRow extends Model
{
    use HasFactory;

    protected $fillable = [
        'list_section_id',
        'row_id',
        'title',
        'description',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the section that owns this row
     */
    public function listSection(): BelongsTo
    {
        return $this->belongsTo(ListSection::class);
    }
}
