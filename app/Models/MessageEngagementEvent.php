<?php

namespace App\Models;

use App\Enums\EngagementEventType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageEngagementEvent extends Model
{
    use HasFactory;

    protected $table = 'message_engagement_events';

    protected $fillable = [
        'message_id',
        'campaign_id',
        'event_type',
        'metadata_json',
        'user_phone',
        'event_timestamp',
    ];

    protected $casts = [
        'event_type' => EngagementEventType::class,
        'metadata_json' => 'array',
        'event_timestamp' => 'datetime',
    ];

    public function message()
    {
        return $this->belongsTo(Message::class);
    }

    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Check if this is a positive engagement event
     */
    public function isPositiveEngagement(): bool
    {
        return $this->event_type->isPositiveEngagement();
    }

    /**
     * Get engagement weight for scoring
     */
    public function getEngagementWeight(): int
    {
        return $this->event_type->getEngagementWeight();
    }

    /**
     * Scope for positive engagement events
     */
    public function scopePositiveEngagement($query)
    {
        return $query->whereIn('event_type', [
            EngagementEventType::DELIVERED,
            EngagementEventType::READ,
            EngagementEventType::REPLIED,
            EngagementEventType::CLICKED_BUTTON,
            EngagementEventType::CLICKED_URL,
            EngagementEventType::FORWARDED,
        ]);
    }

    /**
     * Scope for specific event type
     */
    public function scopeEventType($query, EngagementEventType $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope for recent events
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('event_timestamp', '>=', now()->subHours($hours));
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('event_timestamp', [$startDate, $endDate]);
    }

    /**
     * Get time since event in minutes
     */
    public function getTimeSinceEvent(): int
    {
        return now()->diffInMinutes($this->event_timestamp);
    }
}
