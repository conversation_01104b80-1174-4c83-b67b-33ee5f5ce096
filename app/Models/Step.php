<?php

namespace App\Models;

use App\Enums\StepType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Step extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'steps';

    protected $fillable = [
        'organization_id',
        'flow_id',
        'step',
        'type', // Legacy field
        'step_type', // New enum field
        'position',
        'next_step',
        'earlier_step',
        'is_initial_step',
        'is_ending_step',
        'configuration',
        'navigation_rules',
        'timeout_seconds',
        'json',
        'input',
    ];

    protected $casts = [
        'step_type' => StepType::class,
        'configuration' => 'array',
        'navigation_rules' => 'array',
        'is_initial_step' => 'boolean',
        'is_ending_step' => 'boolean',
        'timeout_seconds' => 'integer',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function flow(){
        return $this->belongsTo(Flow::class);
    }

    public function component(){
        return $this->hasOne(Component::class);
    }

    public function stepNavigations(){
        return $this->hasMany(StepNavigation::class);
    }
}
