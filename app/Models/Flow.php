<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Flow extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'flows';

    protected $fillable = [
        'organization_id',
        'name',
        'description',
        'steps_count',
        'json',
        'is_default_flow',
        'inactivity_minutes',
        'ending_conversation_message',
        'version',
        'status',
        'variables',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }

    public function steps(){
        return $this->hasMany(Step::class);
    }
}
