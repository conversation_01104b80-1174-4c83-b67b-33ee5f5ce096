<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lead extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'organization_id',
        'client_id',
        'source',
        'status',
        'priority',
        'title',
        'description',
        'notes',
        'estimated_value',
        'service_type',
        'budget_range',
        'timeline',
        'company',
        'custom_fields',
        'created_via',
        'contacted_at',
        'qualified_at',
        'closed_at',
    ];

    protected $casts = [
        'custom_fields' => 'array',
        'estimated_value' => 'decimal:2',
        'contacted_at' => 'datetime',
        'qualified_at' => 'datetime',
        'closed_at' => 'datetime',
    ];

    /**
     * Get the organization that owns the lead.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the client that owns the lead.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Scope a query to only include leads for a specific organization.
     */
    public function scopeForOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope a query to only include leads with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include leads with a specific priority.
     */
    public function scopeWithPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to only include leads from a specific source.
     */
    public function scopeFromSource($query, $source)
    {
        return $query->where('source', $source);
    }
}
