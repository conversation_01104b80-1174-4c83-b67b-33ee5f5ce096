<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Budget extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'budgets';

    protected $fillable = [
        'organization_id',
        'client_id',
        'value',
        'cost',
        'name',
        'description',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function client(){
        return $this->belongsTo(Client::class);
    }
    public function custom_products(){
        return $this->hasMany(CustomProduct::class);
    }
    public function projects(){
        return $this->hasMany(Project::class);
    }
    public function products(){
        return $this->belongsToMany(Product::class, 'budgets_products')->withPivot([
            'id',
            'quantity',
            'value',
            'description',
        ]);
    }
}
