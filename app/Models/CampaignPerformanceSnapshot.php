<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CampaignPerformanceSnapshot extends Model
{
    use HasFactory;

    protected $table = 'campaign_performance_snapshots';

    protected $fillable = [
        'campaign_id',
        'snapshot_date',
        'metrics_json',
        'total_messages_at_date',
        'sent_count_at_date',
        'delivered_count_at_date',
        'failed_count_at_date',
        'read_count_at_date',
        'response_count_at_date',
        'delivery_rate_at_date',
        'read_rate_at_date',
        'response_rate_at_date',
    ];

    protected $casts = [
        'snapshot_date' => 'date',
        'metrics_json' => 'array',
        'total_messages_at_date' => 'integer',
        'sent_count_at_date' => 'integer',
        'delivered_count_at_date' => 'integer',
        'failed_count_at_date' => 'integer',
        'read_count_at_date' => 'integer',
        'response_count_at_date' => 'integer',
        'delivery_rate_at_date' => 'decimal:2',
        'read_rate_at_date' => 'decimal:2',
        'response_rate_at_date' => 'decimal:2',
    ];

    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Get growth compared to previous snapshot
     */
    public function getGrowthMetrics(): array
    {
        $previousSnapshot = static::where('campaign_id', $this->campaign_id)
                                  ->where('snapshot_date', '<', $this->snapshot_date)
                                  ->orderBy('snapshot_date', 'desc')
                                  ->first();

        if (!$previousSnapshot) {
            return [
                'has_previous' => false,
                'growth_data' => null
            ];
        }

        return [
            'has_previous' => true,
            'previous_date' => $previousSnapshot->snapshot_date,
            'growth_data' => [
                'total_messages_growth' => $this->total_messages_at_date - $previousSnapshot->total_messages_at_date,
                'delivered_growth' => $this->delivered_count_at_date - $previousSnapshot->delivered_count_at_date,
                'read_growth' => $this->read_count_at_date - $previousSnapshot->read_count_at_date,
                'response_growth' => $this->response_count_at_date - $previousSnapshot->response_count_at_date,
                'delivery_rate_change' => round($this->delivery_rate_at_date - $previousSnapshot->delivery_rate_at_date, 2),
                'read_rate_change' => round($this->read_rate_at_date - $previousSnapshot->read_rate_at_date, 2),
                'response_rate_change' => round($this->response_rate_at_date - $previousSnapshot->response_rate_at_date, 2),
            ]
        ];
    }

    /**
     * Scope for recent snapshots
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('snapshot_date', '>=', now()->subDays($days));
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('snapshot_date', [$startDate, $endDate]);
    }
}
