<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Group extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'groups';

    protected $fillable = [
        'organization_id',
        'name',
        'description',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function products(){
        return $this->belongsToMany(Product::class, 'groups_products');
    }
}
