<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Brand extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'brands';

    protected $fillable = [
        'organization_id',
        'name',
        'description',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }
    public function products(){
        return $this->hasMany(Product::class);
    }
    public function stock_entries(){
        return $this->hasMany(StockEntry::class);
    }
    public function stock_exits(){
        return $this->hasMany(StockExit::class);
    }
    public function stocks(){
        return $this->hasMany(Stock::class);
    }
}
