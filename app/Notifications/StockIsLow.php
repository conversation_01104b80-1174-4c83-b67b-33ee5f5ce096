<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Mail\SendMailStudents as Mailable;
use App\Models\Admin\SpecificTerm;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class StockIsLow extends Notification implements ShouldQueue
{
    use Queueable;

    protected $product;
    protected $quantity;
    protected $user;
    protected $data;
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($product, $quantity, $user)
    {
        $this->product = $product;
        $this->quantity = $quantity;
        $this->user = $user;
        $this->data['title'] = 'Estoque de produto baixo.';
        $this->data['line'] = 'ATENÇÃO: O produto  '.$this->product->name.', possui apenas ' . $this->quantity . ' unidades em estoque. Talvez seja hora de repor mais unidades.';
        $this->data['action'] = 'Visualizar estoque';
        $this->data['path'] = env('APP_URL').'/stocks/'.$this->product->id;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // $name = $this->student->name;
        // $message = new HtmlString($this->data['line']);
        // $type = 'Bonsae - '.$this->data['title'];
        // $url = $this->data['path'];
	
        // try {
        //     //return Mail::to($this->student->email)->send(new SendMailStudents( $name, $message ,$type, $url ));
        //     if($this->student->receive_emails){
        //         return (new Mailable($name, $message ,$type, $url))->to($this->student->email);
        //     }
        // } catch(\Throwable $e){
        //     Log::info($e);
        //     return $e;
        // }
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'text' => $this->data['line'],
            'path' => $this->data['path'],
        ];
    }
}
