<?php

namespace App\Http\Requests\BudgetProduct;

use App\Helpers\Traits\Response;
use App\Repositories\BudgetRepository;
use App\Repositories\ProductRepository;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize() : bool
    {
        if(!empty($this->product_id) && !empty($this->budget_id)){
            /** @var BudgetRepository $budgetRepository */
            $budgetRepository = app()->make(BudgetRepository::class);
            $budget = $budgetRepository->fetchById($this->budget_id);
            if($this->user()->organization_id !== $budget->organization_id){
                return false;
            }

            /** @var ProductRepository $productRepository */
            $productRepository = app()->make(ProductRepository::class);
            $product = $productRepository->fetchById($this->product_id);
            if($this->user()->organization_id !== $product->organization_id){
                return false;
            }

        }
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'budget_id' => 'required',
            'product_id' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'budget_id.required' => __('The budget id field is required.'),
            'product_id.required' => __('The product id id field is required.'),
        ];
    }
}
