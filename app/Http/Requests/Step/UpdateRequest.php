<?php

namespace App\Http\Requests\Step;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'step' => 'required',
            'is_input' => 'nullable|boolean',
            'input' => 'nullable|string'
        ];
    }

    public function messages()
    {
        return [
            'step.required' => __('The step field is required.'),
            'is_input.boolean' => __('The is_input field must be a boolean.'),
            'input.string' => __('The input field must be a string.'),
        ];
    }
}
