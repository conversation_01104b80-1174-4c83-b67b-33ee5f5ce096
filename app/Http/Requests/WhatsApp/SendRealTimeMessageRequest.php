<?php

namespace App\Http\Requests\WhatsApp;

use Illuminate\Foundation\Http\FormRequest;

class SendRealTimeMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Validação de organização será feita no use case
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'text' => 'required|string|max:4096',
            'client_id' => 'required|integer|exists:clients,id',
            'phone_number_id' => 'required|integer|exists:phone_numbers,id',
            'template_id' => 'nullable|integer|exists:templates,id',
            'is_direct_message' => 'boolean'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'text.required' => 'Message text is required',
            'text.max' => 'Message text cannot exceed 4096 characters',
            'client_id.required' => 'Client ID is required',
            'client_id.exists' => 'Client not found',
            'phone_number_id.required' => 'Phone number ID is required',
            'phone_number_id.exists' => 'Phone number not found',
            'template_id.exists' => 'Template not found',
            'is_direct_message.boolean' => 'is_direct_message must be true or false'
        ];
    }
}
