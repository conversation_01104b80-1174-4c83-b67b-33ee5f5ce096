<?php

namespace App\Http\Requests\DepartmentUser;

use App\Helpers\Traits\Response;
use App\Repositories\UserRepository;
use App\Repositories\DepartmentRepository;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class StoreRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        if(!empty($this->department_id) && !empty($this->user_id)){
            /** @var UserRepository $userRepository */
            $userRepository = app()->make(UserRepository::class);
            $group = $userRepository->fetchById($this->user_id);
            if($this->user()->organization_id !== $group->organization_id){
                return false;
            }

            /** @var DepartmentRepository $departmentRepository */
            $departmentRepository = app()->make(DepartmentRepository::class);
            $product = $departmentRepository->fetchById($this->department_id);
            if($this->user()->organization_id !== $product->organization_id){
                return false;
            }

        }
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'user_id' => 'required',
            'department_id' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'user_id.required' => __('The user id id field is required.'),
            'department_id.required' => __('The department id id field is required.'),
        ];
    }
}
