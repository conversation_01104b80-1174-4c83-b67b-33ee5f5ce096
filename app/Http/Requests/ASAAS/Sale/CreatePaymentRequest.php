<?php

namespace App\Http\Requests\ASAAS\Sale;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CreatePaymentRequest extends FormRequest
{
    use Response;

    private const ERROR_STATUS = "error";
    private const ERROR_CODE = 422;
    private const ERROR_MESSAGE = "Validation Failed";

    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                self::ERROR_MESSAGE,
                self::ERROR_STATUS,
                self::ERROR_CODE,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules()
    {
        return [
            'sale_id' => 'required|integer|exists:sales,id',
            'environment' => 'sometimes|in:sandbox,production',
            'billing_type' => 'sometimes|in:BOLETO,CREDIT_CARD,PIX',
            'due_date' => 'sometimes|date|after_or_equal:today'
        ];
    }

    public function messages()
    {
        return [
            'sale_id.required' => __('The sale ID field is required.'),
            'sale_id.integer' => __('The sale ID must be an integer.'),
            'sale_id.exists' => __('The selected sale does not exist.'),
            'environment.in' => __('The environment must be either sandbox or production.'),
            'billing_type.in' => __('The billing type must be BOLETO, CREDIT_CARD, or PIX.'),
            'due_date.date' => __('The due date field must be a valid date.'),
            'due_date.after_or_equal' => __('The due date field must be today or a future date.')
        ];
    }
}
