<?php

namespace App\Http\Requests\ASAAS\AsaasOrganization;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAsaasOrganizationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'organization_id' => 'sometimes|integer|exists:organizations,id',
            'asaas_account_id' => 'sometimes|nullable|string|max:255',
            'asaas_api_key' => 'sometimes|nullable|string|max:255',
            'asaas_wallet_id' => 'sometimes|nullable|string|max:255',
            'asaas_environment' => 'sometimes|nullable|in:sandbox,production',
            'is_active' => 'sometimes|nullable|boolean',
            'last_sync_at' => 'sometimes|nullable|date',
            'sync_errors' => 'sometimes|nullable|array',
            'name' => 'sometimes|nullable|string|max:255',
            'email' => 'sometimes|nullable|email|max:255',
            'login_email' => 'sometimes|nullable|email|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'mobile_phone' => 'sometimes|nullable|string|max:20',
            'address' => 'sometimes|nullable|string|max:255',
            'address_number' => 'sometimes|nullable|string|max:20',
            'complement' => 'sometimes|nullable|string|max:255',
            'province' => 'sometimes|nullable|string|max:255',
            'postal_code' => 'sometimes|nullable|string|max:20',
            'cpf_cnpj' => 'sometimes|nullable|string|max:20',
            'birth_date' => 'sometimes|nullable|date',
            'person_type' => 'sometimes|nullable|in:FISICA,JURIDICA',
            'company_type' => 'sometimes|nullable|string|max:255',
            'city' => 'sometimes|nullable|integer',
            'state' => 'sometimes|nullable|string|max:255',
            'country' => 'sometimes|nullable|string|max:255',
            'trading_name' => 'sometimes|nullable|string|max:255',
            'income_value' => 'sometimes|nullable|numeric|min:0',
            'site' => 'sometimes|nullable|url|max:255',
            'account_number' => 'sometimes|nullable|array',
            'commercial_info_expiration' => 'sometimes|nullable|array',
        ];
    }

    public function messages(): array
    {
        return [
            'organization_id.exists' => 'Organization not found',
            'asaas_environment.in' => 'Environment must be sandbox or production',
            'person_type.in' => 'Person type must be FISICA or JURIDICA',
            'email.email' => 'Email must be a valid email address',
            'login_email.email' => 'Login email must be a valid email address',
            'site.url' => 'Site must be a valid URL',
            'income_value.numeric' => 'Income value must be a number',
            'income_value.min' => 'Income value must be greater than or equal to 0',
        ];
    }
}
