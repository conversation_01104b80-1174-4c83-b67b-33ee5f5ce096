<?php

namespace App\Http\Requests\ASAAS\AsaasOrganization;

use Illuminate\Foundation\Http\FormRequest;

class CreateAsaasOrganizationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'organization_id' => 'required|integer|exists:organizations,id',
            'asaas_account_id' => 'nullable|string|max:255',
            'asaas_api_key' => 'nullable|string|max:255',
            'asaas_wallet_id' => 'nullable|string|max:255',
            'asaas_environment' => 'nullable|in:sandbox,production',
            'is_active' => 'nullable|boolean',
            'last_sync_at' => 'nullable|date',
            'sync_errors' => 'nullable|array',
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'login_email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'address_number' => 'nullable|string|max:20',
            'complement' => 'nullable|string|max:255',
            'province' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'cpf_cnpj' => 'nullable|string|max:20',
            'birth_date' => 'nullable|date',
            'person_type' => 'nullable|in:FISICA,JURIDICA',
            'company_type' => 'nullable|string|max:255',
            'city' => 'nullable|integer',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'trading_name' => 'nullable|string|max:255',
            'income_value' => 'nullable|numeric|min:0',
            'site' => 'nullable|url|max:255',
            'account_number' => 'nullable|array',
            'commercial_info_expiration' => 'nullable|array',
        ];
    }

    public function messages(): array
    {
        return [
            'organization_id.required' => 'Organization ID is required',
            'organization_id.exists' => 'Organization not found',
            'asaas_environment.in' => 'Environment must be sandbox or production',
            'person_type.in' => 'Person type must be FISICA or JURIDICA',
            'email.email' => 'Email must be a valid email address',
            'login_email.email' => 'Login email must be a valid email address',
            'site.url' => 'Site must be a valid URL',
            'income_value.numeric' => 'Income value must be a number',
            'income_value.min' => 'Income value must be greater than or equal to 0',
        ];
    }
}
