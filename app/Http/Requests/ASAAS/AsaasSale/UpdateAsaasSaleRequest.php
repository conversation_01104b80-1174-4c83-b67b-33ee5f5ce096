<?php

namespace App\Http\Requests\ASAAS\AsaasSale;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAsaasSaleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'sale_id' => 'sometimes|integer|exists:sales,id',
            'organization_id' => 'sometimes|integer|exists:organizations,id',
            'client_id' => 'sometimes|nullable|integer|exists:clients,id',
            'asaas_payment_id' => 'sometimes|string|max:255',
            'asaas_customer_id' => 'sometimes|nullable|string|max:255',
            'value' => 'sometimes|numeric|min:0',
            'net_value' => 'sometimes|nullable|numeric|min:0',
            'original_value' => 'sometimes|nullable|numeric|min:0',
            'interest_value' => 'sometimes|nullable|numeric|min:0',
            'discount_value' => 'sometimes|nullable|numeric|min:0',
            'description' => 'sometimes|nullable|string|max:500',
            'billing_type' => 'sometimes|nullable|in:BOLETO,CREDIT_CARD,PIX,UNDEFINED',
            'due_date' => 'sometimes|nullable|date',
            'payment_date' => 'sometimes|nullable|date',
            'original_due_date' => 'sometimes|nullable|date',
            'client_payment_date' => 'sometimes|nullable|date',
            'status' => 'sometimes|nullable|string|max:50',
            'invoice_url' => 'sometimes|nullable|url|max:500',
            'invoice_number' => 'sometimes|nullable|string|max:255',
            'bank_slip_url' => 'sometimes|nullable|url|max:500',
            'pix_qr_code_id' => 'sometimes|nullable|string|max:255',
            'external_reference' => 'sometimes|nullable|string|max:255',
            'installment_id' => 'sometimes|nullable|string|max:255',
            'installment_count' => 'sometimes|nullable|integer|min:1',
            'installment_value' => 'sometimes|nullable|numeric|min:0',
            'installment_number' => 'sometimes|nullable|integer|min:1',
            'credit_date' => 'sometimes|nullable|date',
            'estimated_credit_date' => 'sometimes|nullable|date',
            'anticipated' => 'sometimes|nullable|boolean',
            'anticipable' => 'sometimes|nullable|boolean',
            'can_be_paid_after_due_date' => 'sometimes|nullable|boolean',
            'deleted' => 'sometimes|nullable|boolean',
            'nosso_numero' => 'sometimes|nullable|string|max:255',
            'asaas_synced_at' => 'sometimes|nullable|date',
            'asaas_sync_errors' => 'sometimes|nullable|array',
            'sync_status' => 'sometimes|nullable|in:pending,synced,error',
            'asaas_webhook_data' => 'sometimes|nullable|array',
            'discount_config' => 'sometimes|nullable|array',
            'fine_config' => 'sometimes|nullable|array',
            'interest_config' => 'sometimes|nullable|array',
            'split_config' => 'sometimes|nullable|array',
            'credit_card_data' => 'sometimes|nullable|array',
            'chargeback_data' => 'sometimes|nullable|array',
            'escrow_data' => 'sometimes|nullable|array',
            'refunds_data' => 'sometimes|nullable|array',
        ];
    }

    public function messages(): array
    {
        return [
            'sale_id.exists' => 'Sale not found',
            'organization_id.exists' => 'Organization not found',
            'client_id.exists' => 'Client not found',
            'value.numeric' => 'Value must be a number',
            'value.min' => 'Value must be greater than or equal to 0',
            'billing_type.in' => 'Billing type must be BOLETO, CREDIT_CARD, PIX, or UNDEFINED',
            'sync_status.in' => 'Sync status must be pending, synced, or error',
            'invoice_url.url' => 'Invoice URL must be a valid URL',
            'bank_slip_url.url' => 'Bank slip URL must be a valid URL',
        ];
    }
}
