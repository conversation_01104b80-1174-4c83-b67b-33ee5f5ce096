<?php

namespace App\Http\Requests\ASAAS\AsaasSale;

use Illuminate\Foundation\Http\FormRequest;

class CreateAsaasSaleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'sale_id' => 'required|integer|exists:sales,id',
            'organization_id' => 'required|integer|exists:organizations,id',
            'client_id' => 'nullable|integer|exists:clients,id',
            'asaas_payment_id' => 'required|string|max:255',
            'asaas_customer_id' => 'nullable|string|max:255',
            'value' => 'required|numeric|min:0',
            'net_value' => 'nullable|numeric|min:0',
            'original_value' => 'nullable|numeric|min:0',
            'interest_value' => 'nullable|numeric|min:0',
            'discount_value' => 'nullable|numeric|min:0',
            'description' => 'nullable|string|max:500',
            'billing_type' => 'nullable|in:BOLETO,CREDIT_CARD,PIX,UNDEFINED',
            'due_date' => 'nullable|date',
            'payment_date' => 'nullable|date',
            'original_due_date' => 'nullable|date',
            'client_payment_date' => 'nullable|date',
            'status' => 'nullable|string|max:50',
            'invoice_url' => 'nullable|url|max:500',
            'invoice_number' => 'nullable|string|max:255',
            'bank_slip_url' => 'nullable|url|max:500',
            'pix_qr_code_id' => 'nullable|string|max:255',
            'external_reference' => 'nullable|string|max:255',
            'installment_id' => 'nullable|string|max:255',
            'installment_count' => 'nullable|integer|min:1',
            'installment_value' => 'nullable|numeric|min:0',
            'installment_number' => 'nullable|integer|min:1',
            'credit_date' => 'nullable|date',
            'estimated_credit_date' => 'nullable|date',
            'anticipated' => 'nullable|boolean',
            'anticipable' => 'nullable|boolean',
            'can_be_paid_after_due_date' => 'nullable|boolean',
            'deleted' => 'nullable|boolean',
            'nosso_numero' => 'nullable|string|max:255',
            'asaas_synced_at' => 'nullable|date',
            'asaas_sync_errors' => 'nullable|array',
            'sync_status' => 'nullable|in:pending,synced,error',
            'asaas_webhook_data' => 'nullable|array',
            'discount_config' => 'nullable|array',
            'fine_config' => 'nullable|array',
            'interest_config' => 'nullable|array',
            'split_config' => 'nullable|array',
            'credit_card_data' => 'nullable|array',
            'chargeback_data' => 'nullable|array',
            'escrow_data' => 'nullable|array',
            'refunds_data' => 'nullable|array',
        ];
    }

    public function messages(): array
    {
        return [
            'sale_id.required' => 'Sale ID is required',
            'sale_id.exists' => 'Sale not found',
            'organization_id.required' => 'Organization ID is required',
            'organization_id.exists' => 'Organization not found',
            'client_id.exists' => 'Client not found',
            'asaas_payment_id.required' => 'ASAAS payment ID is required',
            'value.required' => 'Value is required',
            'value.numeric' => 'Value must be a number',
            'value.min' => 'Value must be greater than or equal to 0',
            'billing_type.in' => 'Billing type must be BOLETO, CREDIT_CARD, PIX, or UNDEFINED',
            'sync_status.in' => 'Sync status must be pending, synced, or error',
            'invoice_url.url' => 'Invoice URL must be a valid URL',
            'bank_slip_url.url' => 'Bank slip URL must be a valid URL',
        ];
    }
}
