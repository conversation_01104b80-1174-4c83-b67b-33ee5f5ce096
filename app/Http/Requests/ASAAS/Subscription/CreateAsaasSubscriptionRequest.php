<?php

namespace App\Http\Requests\ASAAS\Subscription;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CreateAsaasSubscriptionRequest extends FormRequest
{
    use Response;

    public function authorize(): bool
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                'Validation Failed',
                'error',
                422,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules(): array
    {
        return [
            'subscription_id' => 'required|integer|exists:subscriptions,id',
            'environment' => 'sometimes|in:sandbox,production'
        ];
    }

    public function messages(): array
    {
        return [
            'subscription_id.required' => 'The subscription ID field is required.',
            'subscription_id.integer' => 'The subscription ID must be an integer.',
            'subscription_id.exists' => 'The selected subscription does not exist.',
            'environment.in' => 'The environment must be either sandbox or production.'
        ];
    }
}
