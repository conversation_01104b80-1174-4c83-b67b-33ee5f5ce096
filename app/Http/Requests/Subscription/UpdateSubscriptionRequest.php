<?php

namespace App\Http\Requests\Subscription;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateSubscriptionRequest extends FormRequest
{
    use Response;

    public function authorize(): bool
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                'Validation Failed',
                'error',
                422,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules(): array
    {
        return [
            'type' => 'sometimes|in:trial,paid,courtesy',
            'status' => 'sometimes|in:active,inactive,expired,cancelled',
            'value' => 'nullable|numeric|min:0',
            'started_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:started_at',
            'is_courtesy' => 'sometimes|boolean',
            'courtesy_expires_at' => 'nullable|date|after:today',
            'courtesy_reason' => 'nullable|string|max:500',
            'allowed_modules' => 'nullable|array',
            'allowed_modules.*' => 'string|max:100',
        ];
    }

    public function messages(): array
    {
        return [
            'type.in' => 'Subscription type must be trial, paid, or courtesy',
            'status.in' => 'Status must be active, inactive, expired, or cancelled',
            'value.numeric' => 'Value must be a number',
            'value.min' => 'Value must be greater than or equal to 0',
            'expires_at.after' => 'Expiration date must be after start date',
            'courtesy_expires_at.after' => 'Courtesy expiration must be after today',
            'courtesy_reason.max' => 'Reason cannot exceed 500 characters',
        ];
    }
}
