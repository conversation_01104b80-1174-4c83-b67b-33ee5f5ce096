<?php

namespace App\Http\Requests\WhatsAppWebhookLog;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'nullable|integer|exists:organizations,id',
            'phone_number_id' => 'required|string|max:255',
            'event_type' => 'required|string|in:message,status,other',
            'webhook_payload' => 'required|array',
            'processing_status' => 'nullable|string|in:pending,success,failed',
            'error_message' => 'nullable|string|max:65535',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'organization_id.exists' => 'The selected organization does not exist.',
            'phone_number_id.required' => 'The phone number ID is required.',
            'phone_number_id.string' => 'The phone number ID must be a string.',
            'phone_number_id.max' => 'The phone number ID may not be greater than 255 characters.',
            'event_type.required' => 'The event type is required.',
            'event_type.in' => 'The event type must be one of: message, status, other.',
            'webhook_payload.required' => 'The webhook payload is required.',
            'webhook_payload.array' => 'The webhook payload must be an array.',
            'processing_status.in' => 'The processing status must be one of: pending, success, failed.',
            'error_message.string' => 'The error message must be a string.',
            'error_message.max' => 'The error message may not be greater than 65535 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'organization_id' => 'organization ID',
            'phone_number_id' => 'phone number ID',
            'event_type' => 'event type',
            'webhook_payload' => 'webhook payload',
            'processing_status' => 'processing status',
            'error_message' => 'error message',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default processing status if not provided
        if (!$this->has('processing_status')) {
            $this->merge([
                'processing_status' => 'pending'
            ]);
        }

        // Ensure organization_id is null if not provided or empty
        if ($this->has('organization_id') && empty($this->organization_id)) {
            $this->merge([
                'organization_id' => null
            ]);
        }
    }
}
