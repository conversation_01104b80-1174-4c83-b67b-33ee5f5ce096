<?php

namespace App\Http\Requests\Campaign;

use Illuminate\Foundation\Http\FormRequest;

class AssignTagsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'tags' => [
                'required',
                'array',
                'min:1'
            ],
            'tags.*' => [
                'required',
                'string',
                'max:50',
                'min:2'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'tags.required' => 'At least one tag must be provided',
            'tags.array' => 'Tags must be provided as an array',
            'tags.min' => 'At least one tag must be provided',
            'tags.*.required' => 'Tag name is required',
            'tags.*.string' => 'Tag name must be a string',
            'tags.*.max' => 'Tag name cannot exceed 50 characters',
            'tags.*.min' => 'Tag name must be at least 2 characters'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and normalize tag names
        if ($this->has('tags') && is_array($this->tags)) {
            $tags = array_unique(array_filter(array_map(function($tag) {
                return is_string($tag) ? trim(strtolower($tag)) : null;
            }, $this->tags), function($tag) {
                return !empty($tag) && strlen($tag) >= 2;
            }));
            
            $this->merge([
                'tags' => array_values($tags)
            ]);
        }
    }
}
