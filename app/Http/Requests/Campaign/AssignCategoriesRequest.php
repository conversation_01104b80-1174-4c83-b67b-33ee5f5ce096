<?php

namespace App\Http\Requests\Campaign;

use Illuminate\Foundation\Http\FormRequest;

class AssignCategoriesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'category_ids' => [
                'required',
                'array',
                'min:1'
            ],
            'category_ids.*' => [
                'required',
                'integer',
                'min:1'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'category_ids.required' => 'At least one category must be selected',
            'category_ids.array' => 'Categories must be provided as an array',
            'category_ids.min' => 'At least one category must be selected',
            'category_ids.*.required' => 'Category ID is required',
            'category_ids.*.integer' => 'Category ID must be a valid integer',
            'category_ids.*.min' => 'Category ID must be a positive integer'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Remove duplicates and filter out invalid values
        if ($this->has('category_ids') && is_array($this->category_ids)) {
            $categoryIds = array_unique(array_filter($this->category_ids, function($id) {
                return is_numeric($id) && $id > 0;
            }));
            
            $this->merge([
                'category_ids' => array_values($categoryIds)
            ]);
        }
    }
}
