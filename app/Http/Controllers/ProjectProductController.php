<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\Response;
use App\Http\Requests\ProjectProduct\StoreRequest;
use App\Http\Requests\ProjectProduct\UpdateRequest;
use App\UseCases\Inventory\ProjectProduct\Delete;
use App\UseCases\Inventory\ProjectProduct\Get;
use App\UseCases\Inventory\ProjectProduct\GetAll;
use App\UseCases\Inventory\ProjectProduct\Store;
use App\UseCases\ProjectProduct\Update;
use Illuminate\Http\JsonResponse;

class ProjectProductController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform();

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $projectProduct = $useCase->perform($request);

            return $this->response(
                "ProjectProduct created successfully",
                "success",
                200 ,
                $projectProduct->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    //Não existe o UseCase Update em ProjectProduct
    // public function update(UpdateRequest $request, int $id) : JsonResponse {
    //     try{/
    //         /** @var Update $useCase */
    //         $useCase = app()->make(Update::class);
    //         $update = $useCase->perform($request, $id);

    //         return $this->response(
    //             "ProjectProduct updated successfully",
    //             "success",
    //             200 ,
    //             $update->toArray()
    //         );
    //     } catch (\Throwable $e){
    //         return $this->errorResponse($e->getMessage());
    //     }
    // }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "ProjectProduct deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
