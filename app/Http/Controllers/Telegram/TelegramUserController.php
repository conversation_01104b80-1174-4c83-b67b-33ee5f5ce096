<?php

namespace App\Http\Controllers\Telegram;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TelegramUserFilters;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\TelegramUser\StoreRequest;
use App\Http\Requests\TelegramUser\UpdateRequest;
use App\Services\Telegram\UseCases\TelegramUser\Delete;
use App\Services\Telegram\UseCases\TelegramUser\Get;
use App\Services\Telegram\UseCases\TelegramUser\GetAll;
use App\Services\Telegram\UseCases\TelegramUser\Store;
use App\Services\Telegram\UseCases\TelegramUser\Update;
use Illuminate\Http\JsonResponse;

class TelegramUserController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new TelegramUserFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $telegramUser = $useCase->perform($request);

            return $this->response(
                "TelegramUser created successfully",
                "success",
                200 ,
                $telegramUser->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "TelegramUser updated successfully",
                "success",
                200 ,
                $update->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "TelegramUser deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
