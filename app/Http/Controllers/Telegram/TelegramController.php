<?php

namespace App\Http\Controllers\Telegram;


use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Services\Telegram\Telegram;
use App\Services\Telegram\UseCases\TelegramBot\GetFromBotID;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;

class TelegramController extends Controller
{
    use Response;

    public function receiveMessage() : JsonResponse|RedirectResponse {
        try{
            Log::info("[TelegramController::receiveMessage]", ["telegram endpoint receiveMessage was hit!"]);

            $service = new Telegram();

            $message = "No message to receive!";
            if($service->receiveMessage()) {
                $message = "Telegram message received successfully";
            }

            return $this->response($message);
        } catch (\Throwable $e){
            Log::error($e->getMessage() , $e->getTrace());

            return $this->errorResponse(
                $e->getMessage(),
                500,
                $e->getTrace(),
            );
        }
    }

    public function receiveCustomMessage(string $bot) : JsonResponse|RedirectResponse {
        try{
            Log::info("[TelegramController::receiveCustomMessage]", ["telegram endpoint receiveMessage was hit!"]);

            /** @var GetFromBotID $useCase */
            $useCase = app()->make(GetFromBotID::class);
            $bot = $useCase->perform($bot);

            $service = new Telegram($bot);

            $message = "No message to receive!";
            if($service->receiveMessage()) {
                $message = "Telegram message received successfully";
            }

            return $this->response($message);
        } catch (\Throwable $e){
            Log::error($e->getMessage() , $e->getTrace());

            return $this->errorResponse(
                $e->getMessage(),
                500,
                $e->getTrace(),
            );
        }
    }
}
