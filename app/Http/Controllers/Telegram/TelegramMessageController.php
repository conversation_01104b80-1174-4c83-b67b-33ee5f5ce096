<?php

namespace App\Http\Controllers\Telegram;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\TelegramMessageFilters;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Services\Telegram\UseCases\TelegramMessage\Delete;
use App\Services\Telegram\UseCases\TelegramMessage\Get;
use App\Services\Telegram\UseCases\TelegramMessage\GetAll;
use Illuminate\Http\JsonResponse;

class TelegramMessageController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new TelegramMessageFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store() : JsonResponse {
        return $this->response(
            "TelegramMessage store can't be triggered",
            "unauthorized",
            404 ,
        );
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update() : JsonResponse {
        return $this->response(
            "TelegramMessage store can't be triggered",
            "unauthorized",
            404 ,
        );
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "TelegramMessage deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
