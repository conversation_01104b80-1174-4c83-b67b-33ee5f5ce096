<?php

namespace App\Http\Controllers;

use App\Factories\UserFactory;
use App\Helpers\Traits\Response;
use App\Http\Requests\Auth\DeleteRequest;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Auth\ValidateTokenRequest;
use App\UseCases\Auth\Delete;
use App\UseCases\Auth\Login;
use App\UseCases\Auth\Logout;
use App\UseCases\Auth\LogoutAllSessions;
use App\UseCases\Auth\Register;
use App\UseCases\Auth\RequestPasswordReset;
use App\UseCases\Auth\ResetPassword;
use App\UseCases\Auth\ValidateResetToken;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AuthController extends Controller
{
    use Response;

    private UserFactory $userFactory;

    public function __construct(UserFactory $userFactory){
        $this->userFactory = $userFactory;
    }

    public function get(Request $request){
        return $this->response(
            "Authorized",
            "success",
            200 ,
            $this->userFactory->buildFromModel(
                $request->user()
            )->toArray()
        );
    }

    public function login(LoginRequest $request) : JsonResponse {

        try{
            /** @var Login $loginUseCase */
            $loginUseCase = app()->make(Login::class);
            $user = $loginUseCase->perform($request);

            if ($user) {
                return $this->response(
                    "Authorized",
                    "success",
                    200 ,
                    $user->toArrayWithToken()
                );
            }

            return $this->errorResponse("Unauthorized", 403);
        }catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function register(RegisterRequest $request) : JsonResponse {
        try{
            /** @var Register $registerUseCase */
            $registerUseCase = app()->make(Register::class);
            $user = $registerUseCase->perform($request);

            return $this->response(
                "User created successfully",
                "success",
                200 ,
                $user->toArrayWithToken()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function delete(DeleteRequest $request) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $useCase->perform($request);

            return $this->response("User deleted successfully");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function logout(Request $request) : JsonResponse {
        try{
            /** @var Logout $useCase */
            $useCase = app()->make(Logout::class);
            $useCase->perform($request);

            return $this->response("User logged out successfully");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function logoutAllSessions(Request $request) : JsonResponse {
        try{
            /** @var LogoutAllSessions $useCase */
            $useCase = app()->make(LogoutAllSessions::class);
            $useCase->perform($request);

            return $this->response("User logged out of all successfully");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Request password reset
     */
    public function forgotPassword(ForgotPasswordRequest $request): JsonResponse
    {
        try {
            /** @var RequestPasswordReset $useCase */
            $useCase = app()->make(RequestPasswordReset::class);
            $useCase->perform($request);

            return $this->response(
                "Se o email estiver cadastrado, você receberá instruções para redefinir sua senha.",
                "success",
                200
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Validate password reset token
     */
    public function validateToken(ValidateTokenRequest $request): JsonResponse
    {
        try {
            /** @var ValidateResetToken $useCase */
            $useCase = app()->make(ValidateResetToken::class);
            $useCase->perform($request);

            return $this->response(
                "Token válido",
                "success",
                200
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage(), 400);
        }
    }

    /**
     * Reset password using token
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        try {
            /** @var ResetPassword $useCase */
            $useCase = app()->make(ResetPassword::class);
            $user = $useCase->perform($request);

            return $this->response(
                "Senha redefinida com sucesso",
                "success",
                200,
                $user->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage(), 400);
        }
    }
}
