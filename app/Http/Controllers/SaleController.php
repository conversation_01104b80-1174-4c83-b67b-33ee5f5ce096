<?php

namespace App\Http\Controllers;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\SaleFilters;
use App\Helpers\Traits\Response;
use App\Http\Requests\Sale\StoreRequest;
use App\Http\Requests\Sale\UpdateRequest;
//use App\UseCases\Inventory\Budget\Get as GetBudget;
//use App\UseCases\Inventory\Sale\AttachProducts;
use App\UseCases\Inventory\Sale\Delete;
use App\UseCases\Inventory\Sale\Get;
use App\UseCases\Inventory\Sale\GetAll;
use App\UseCases\Inventory\Sale\Store;
//use App\UseCases\Inventory\Sale\StoreFromBudget;
use App\UseCases\Inventory\Sale\Update;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SaleController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new SaleFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $sale = $useCase->perform($request);

            return $this->response(
                "Sale created successfully",
                "success",
                200 ,
                $sale->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "Sale updated successfully",
                "success",
                200 ,
                $update->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "Sale deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
