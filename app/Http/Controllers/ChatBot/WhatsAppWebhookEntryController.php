<?php

namespace App\Http\Controllers\ChatBot;

use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\UseCases\ChatBot\WhatsAppWebhookEntry\Index;
use App\UseCases\ChatBot\WhatsAppWebhookEntry\Show;
use Illuminate\Http\JsonResponse;

class WhatsAppWebhookEntryController extends Controller
{
    use Response;

    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        try {
            /** @var Index $useCase */
            $useCase = app()->make(Index::class);
            $result = $useCase->perform(request()->all());

            $data = $result['data'] ?? [];
            unset($result['data']);

            return $this->response(
                "WhatsApp webhook entries retrieved successfully",
                "success",
                200,
                $data,
                null,
                $result
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        try {
            /** @var Show $useCase */
            $useCase = app()->make(Show::class);
            $webhookEntry = $useCase->perform($id);

            return $this->response(
                "WhatsApp webhook entry retrieved successfully",
                "success",
                200,
                $webhookEntry->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
