<?php

namespace App\Http\Controllers\ChatBot;

use App\Domains\Filters\MessageFilters;
use App\Domains\Filters\OrderBy;
use App\Enums\MessageStatus;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\Message\StoreRequest;
use App\Http\Requests\Message\UpdateRequest;
use App\UseCases\ChatBot\Campaign\GenerateMessages;
use App\UseCases\ChatBot\Message\Delete;
use App\UseCases\ChatBot\Message\Get;
use App\UseCases\ChatBot\Message\GetAll;
use App\UseCases\ChatBot\Message\GetFailedByCampaign;
use App\UseCases\ChatBot\Message\GetMessagesAvailableToSent;
use App\UseCases\ChatBot\Message\GetStatisticsByCampaign;
use App\UseCases\ChatBot\Message\Store;
use App\UseCases\ChatBot\Message\Update;
use App\UseCases\ChatBot\Message\GetByCampaign;
use App\UseCases\ChatBot\Message\Resend;
use App\UseCases\ChatBot\Message\ResendFailed;
use App\UseCases\ChatBot\Campaign\Get as GetCampaign;
use App\Repositories\MessageRepository;
use App\Repositories\MessageDeliveryAttemptRepository;
use App\Http\Requests\WhatsApp\SendRealTimeMessageRequest;
use App\UseCases\WhatsApp\SendRealTimeMessage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new MessageFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $flow = $useCase->perform($request);

            return $this->response(
                "Message created successfully",
                "success",
                200 ,
                $flow->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "Message updated successfully",
                "success",
                200 ,
                $update->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "Message deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getToWhatsAppPayload(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $message = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $message->toWhatsAppPayload()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function generateMessages(int $campaign_id) : JsonResponse {
        try{
            $is_sending = request()->input('is_sending') ?? false;
            $is_direct_message = request()->input('is_direct_message') ?? false;

            /** @var GenerateMessages $useCase */
            $useCase = app()->make(GenerateMessages::class);
            $messages = $useCase->perform($campaign_id, !$is_sending, $is_direct_message);

            return $this->response(
                "Messages generated successfully",
                "success",
                200 ,
                $messages
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getMessagesAvailableToSent() : JsonResponse
    {
        try {
            /** @var GetMessagesAvailableToSent $useCase */
            $useCase = app()->make(GetMessagesAvailableToSent::class);
            $messages = $useCase->perform(5);

            return $this->response(
                "Messages available to sent",
                "success",
                200 ,
                $messages
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get messages by campaign with filters
     */
    public function getByCampaign(int $campaign_id, Request $request): JsonResponse
    {
        try {
            /** @var GetByCampaign $useCase */
            $useCase = app()->make(GetByCampaign::class);
            $result = $useCase->perform(
                $campaign_id,
                $request->user()->organization_id,
            );

            return $this->response(
                "Messages retrieved successfully",
                "success",
                200,
                $result
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get failed messages by campaign
     */
    public function getFailedByCampaign(int $campaign_id, Request $request): JsonResponse
    {
        try {
            /** @var GetFailedByCampaign $useCase */
            $useCase = app()->make(GetFailedByCampaign::class);

            $result = $useCase->perform(
                $campaign_id,
                $request->user()->organization_id
            );

            return $this->response(
                "Failed messages retrieved successfully",
                "success",
                200,
                $result
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Resend a specific message
     */
    public function resend(int $id, Request $request): JsonResponse
    {
        try {
            /** @var Resend $useCase */
            $useCase = app()->make(Resend::class);

            $reset_retry_count = $request->boolean('reset_retry_count', false);
            $force_resend = $request->boolean('force_resend', false);

            $result = $useCase->execute(
                $id,
                $request->user()->organization_id,
                $request->user()->id,
                $reset_retry_count,
                $force_resend
            );

            return $this->response(
                $result['message'],
                "success",
                200,
                $result
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Resend all failed messages in a campaign
     */
    public function resendFailedByCampaign(int $campaign_id, Request $request): JsonResponse
    {
        try {
            /** @var ResendFailed $useCase */
            $useCase = app()->make(ResendFailed::class);

            $reset_retry_count = $request->boolean('reset_retry_count', false);
            $message_ids = $request->input('message_ids');

            $result = $useCase->execute(
                $campaign_id,
                $request->user()->organization_id,
                $request->user()->id,
                $reset_retry_count,
                $message_ids
            );

            return $this->response(
                $result['message'],
                "success",
                200,
                $result
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get delivery status and history for a message
     */
    public function getDeliveryStatus(int $id, Request $request): JsonResponse
    {
        try {
            /** @var Resend $useCase */
            $useCase = app()->make(Resend::class);

            $result = $useCase->getDeliveryStatus(
                $id,
                $request->user()->organization_id
            );

            return $this->response(
                "Delivery status retrieved successfully",
                "success",
                200,
                $result
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get campaign message statistics
     */
    public function getCampaignStatistics(int $campaign_id, Request $request): JsonResponse
    {
        try {
            /** @var GetCampaign $getCampaignUseCase */
            $getCampaignUseCase = app()->make(GetCampaign::class);
            $campaign = $getCampaignUseCase->perform($campaign_id);

            /** @var GetStatisticsByCampaign $getStatisticsUseCase */
            $getStatisticsUseCase = app()->make(GetStatisticsByCampaign::class);
            $statistics = $getStatisticsUseCase->perform($campaign);

            return $this->response(
                "Campaign statistics retrieved successfully",
                "success",
                200,
                [
                    'id' => $campaign_id,
                    'statistics' => $statistics,
                    'campaign' => $campaign->toArray()
                ]
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Send real-time WhatsApp message
     */
    public function sendRealTimeMessage(SendRealTimeMessageRequest $request): JsonResponse
    {
        try {
            /** @var SendRealTimeMessage $useCase */
            $useCase = app()->make(SendRealTimeMessage::class);
            $result = $useCase->perform($request);

            return $this->response(
                "Message sent successfully",
                "success",
                200,
                $result
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
