<?php

namespace App\Http\Controllers\ChatBot;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\PhoneNumberFilters;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\PhoneNumber\StoreRequest;
use App\Http\Requests\PhoneNumber\UpdateRequest;
use App\UseCases\ChatBot\PhoneNumber\Delete;
use App\UseCases\ChatBot\PhoneNumber\Get;
use App\UseCases\ChatBot\PhoneNumber\GetAll;
use App\UseCases\ChatBot\PhoneNumber\Store;
use App\UseCases\ChatBot\PhoneNumber\Update;
use Illuminate\Http\JsonResponse;

class PhoneNumberController extends Controller
{
    use Response;

    private const SUCCESS_STATUS = "success";
    private const SUCCESS_CODE = 200;
    private const SUCCESS_MESSAGE = "Operation completed successfully";

    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new PhoneNumberFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request): JsonResponse
    {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $phoneNumber = $useCase->perform($request);

            return $this->response(
                "PhoneNumber created successfully",
                "success",
                200 ,
                $phoneNumber->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(int $id, UpdateRequest $request): JsonResponse
    {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "PhoneNumber updated successfully",
                "success",
                200 ,
                $update->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "PhoneNumber deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
