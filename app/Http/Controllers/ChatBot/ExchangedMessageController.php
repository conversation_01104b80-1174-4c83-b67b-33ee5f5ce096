<?php

namespace App\Http\Controllers\ChatBot;

use App\Domains\Filters\ExchangedMessageFilters;
use App\Domains\Filters\OrderBy;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Http\Requests\ExchangedMessage\StoreRequest;
use App\Http\Requests\ExchangedMessage\UpdateRequest;
use App\UseCases\ChatBot\ExchangedMessage\Delete;
use App\UseCases\ChatBot\ExchangedMessage\Get;
use App\UseCases\ChatBot\ExchangedMessage\GetAll;
use App\UseCases\ChatBot\ExchangedMessage\GetChatByClient;
use App\UseCases\ChatBot\ExchangedMessage\Store;
use App\UseCases\ChatBot\ExchangedMessage\Update;
use App\Factories\OrganizationFactory;
use Illuminate\Http\JsonResponse;

class ExchangedMessageController extends Controller
{
    use Response;

    public function index(): JsonResponse
    {
        try {
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new ExchangedMessageFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request): JsonResponse
    {
        try {
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $exchangedMessage = $useCase->perform($request);

            return $this->response(
                "ExchangedMessage created successfully",
                "success",
                200,
                $exchangedMessage->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200,
                $data->toArray()
            );
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse("ExchangedMessage not found", 404);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id): JsonResponse
    {
        try {
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "ExchangedMessage updated successfully",
                "success",
                200,
                $update->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "ExchangedMessage deleted successfully",
                "success",
                200,
                [$delete]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function chatByClient(int $client_id): JsonResponse
    {
        try {
            /** @var GetChatByClient $useCase */
            $useCase = app()->make(GetChatByClient::class);

            $limit = (int) request()->get('limit', 50);
            $limit = min($limit, 100); // Máximo de 100 mensagens

            /** @var OrganizationFactory $organizationFactory */
            $organizationFactory = app()->make(OrganizationFactory::class);
            $organization = $organizationFactory->buildFromModel(auth()->user()->organization);

            $result = $useCase->perform(
                $client_id,
                $organization,
                $limit
            );

            if (!$result['success']) {
                return $this->errorResponse($result['reason'], 400);
            }

            return $this->response(
                $result['reason'],
                "success",
                200,
                $result['data'],
                null,
                [
                    'count' => $result['count'],
                    'client_id' => $result['client_id'],
                    'organization_id' => $result['organization_id'],
                    'limit' => $result['limit']
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
