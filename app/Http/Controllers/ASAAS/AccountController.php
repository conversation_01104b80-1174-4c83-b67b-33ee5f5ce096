<?php

namespace App\Http\Controllers\ASAAS;

use App\Http\Controllers\Controller;
use App\Helpers\Traits\Response;
use App\Services\ASAAS\Domains\Filters\SubaccountFilters;
use App\Services\ASAAS\UseCases\Account\DeleteMyAccountSubaccount;
use App\Services\ASAAS\UseCases\Account\GetMyAccount;
use App\Services\ASAAS\UseCases\Account\SearchSubaccount;
use App\Services\ASAAS\UseCases\Account\SyncSubaccount;
use App\Services\ASAAS\UseCases\Account\UpdateMyAccount;
use App\Services\ASAAS\UseCases\Account\GetBalance;
use App\Services\ASAAS\UseCases\Account\GetStatistics;
use App\Services\ASAAS\UseCases\Account\CreateSubaccount;
use App\Services\ASAAS\UseCases\Account\GetSubaccounts;
use App\Services\ASAAS\UseCases\Account\GetSubaccount;
use App\Services\ASAAS\UseCases\Account\UpdateSubaccount;
use App\Services\ASAAS\UseCases\Account\DeleteSubaccount;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\UseCases\Organization\Get;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AccountController extends Controller
{
    use Response;

    /**
     * Get main account information
     */
    public function getMyAccount(): JsonResponse
    {
        try {
            /** @var GetMyAccount $useCase */
            $useCase = app()->make(GetMyAccount::class);
            $account = $useCase->perform();

            return $this->response(
                "Account information retrieved successfully",
                "success",
                200,
                $account
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update main account information
     */
    public function updateMyAccount(Request $request): JsonResponse
    {
        try {
            /** @var UpdateMyAccount $useCase */
            $useCase = app()->make(UpdateMyAccount::class);
            $account = $useCase->perform($request->all());

            return $this->response(
                "Account updated successfully",
                "success",
                200,
                $account
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get account balance
     */
    public function getBalance(): JsonResponse
    {
        try {
            /** @var GetBalance $useCase */
            $useCase = app()->make(GetBalance::class);
            $balance = $useCase->perform();

            return $this->response(
                "Balance retrieved successfully",
                "success",
                200,
                $balance
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get account statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            /** @var GetStatistics $useCase */
            $useCase = app()->make(GetStatistics::class);
            $statistics = $useCase->perform($request->all());

            return $this->response(
                "Statistics retrieved successfully",
                "success",
                200,
                $statistics
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Create subaccount for organization
     */
    public function createSubaccount(Request $request): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($request->organization_id, true);

            /** @var CreateSubaccount $useCase */
            $useCase = app()->make(CreateSubaccount::class);
            $result = $useCase->perform($organization);

            return $this->response(
                "Subaccount created successfully",
                "success",
                201,
                $result->toArray()
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get all subaccounts
     */
    public function getSubaccounts(Request $request): JsonResponse
    {
        try {
            /** @var GetSubaccounts $useCase */
            $useCase = app()->make(GetSubaccounts::class);
            $result = $useCase->perform($request->all());

            return $this->response(
                "Subaccounts retrieved successfully",
                "success",
                200,
                $result['data'],
                null,
                [
                    'total_count' => $result['total_count'],
                    'has_more' => $result['has_more'],
                    'limit' => $result['limit'],
                    'offset' => $result['offset']
                ]
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get specific subaccount for organization
     */
    public function getSubaccount(Request $request): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($request->organization_id, true);

            /** @var GetSubaccount $useCase */
            $useCase = app()->make(GetSubaccount::class);
            $result = $useCase->perform($organization);

            return $this->response(
                $result['found'] ? "Subaccount found" : "Subaccount not found",
                $result['found'] ? "success" : "error",
                $result['found'] ? 200 : 404,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function searchSubaccount(Request $request): JsonResponse
    {
        try {
            $syncIfFound = $request->query('sync_if_found', false);

            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($request->organization_id, true);

            /** @var SearchSubaccount $useCase */
            $useCase = app()->make(SearchSubaccount::class);
            $result = $useCase->perform(new SubaccountFilters([]), $organization);

            if ($syncIfFound && $result['found']) {
                /** @var SyncSubaccount $useCaseSync */
                $useCaseSync = app()->make(SyncSubaccount::class);
                $useCaseSync->perform($organization, $result);
            }

            return $this->response(
                "Subaccount search completed",
                "success",
                200,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function syncOrganization(Request $request): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($request->organization_id, true);

            /** @var SearchSubaccount $useCaseSearch */
            $useCaseSearch = app()->make(SearchSubaccount::class);
            $searchResult = $useCaseSearch->perform(new SubaccountFilters([]), $organization);

            if ($searchResult['found']) {
                /** @var SyncSubaccount $useCaseSync */
                $useCaseSync = app()->make(SyncSubaccount::class);
                $useCaseSync->perform($organization, $searchResult);
            } else {
                /** @var  CreateSubaccount $useCaseCreate */
                $useCaseCreate = app()->make(CreateSubaccount::class);
                $useCaseCreate->perform($organization);
            }

            return $this->response(
                "Organization synced successfully",
                "success",
                200,
                $organization->toArray()
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update subaccount for organization
     */
    public function updateSubaccount(Request $request): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($request->organization_id, true);

            /** @var UpdateSubaccount $useCase */
            $useCase = app()->make(UpdateSubaccount::class);
            $result = $useCase->perform($organization);

            return $this->response(
                "Subaccount updated successfully",
                "success",
                200,
                $result->toArray()
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete subaccount for organization
     */
    public function deleteSubaccount(Request $request): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($request->organization_id, true);

            /** @var DeleteSubaccount $useCase */
            $useCase = app()->make(DeleteSubaccount::class);
            $result = $useCase->perform($organization);

            return $this->response(
                $result['success'] ? "Subaccount deleted successfully" : "Failed to delete subaccount",
                $result['success'] ? "success" : "error",
                $result['success'] ? 200 : 400,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function deleteMyAccountSubaccount(Request $request): JsonResponse
    {
        try {
            $remove_reason = $request->input('remove_reason', 'Desativado pelo sistema');

            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($request->organization_id, true);

            /** @var DeleteMyAccountSubaccount $useCase */
            $useCase = app()->make(DeleteMyAccountSubaccount::class);
            $result = $useCase->perform($organization, $remove_reason);

            return $this->response(
                $result['success'] ? "Subaccount deleted successfully" : "Failed to delete subaccount",
                $result['success'] ? "success" : "error",
                $result['success'] ? 200 : 400,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
