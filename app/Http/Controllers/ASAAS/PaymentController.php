<?php

namespace App\Http\Controllers\ASAAS;

use App\Http\Controllers\Controller;
use App\Helpers\Traits\Response;
use App\Services\ASAAS\UseCases\Payment\CreatePayment;
use App\Services\ASAAS\UseCases\Payment\GetAllPayments;
use App\Services\ASAAS\UseCases\Payment\GetPaymentById;
use App\Services\ASAAS\UseCases\Payment\UpdatePayment;
use App\Services\ASAAS\UseCases\Payment\DeletePayment;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\UseCases\Inventory\Sale\Get as GetSale;
use App\UseCases\Organization\Get as GetOrganization;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    // TODO: refund use case
    use Response;

    /**
     * Create a payment
     */
    public function create(Request $request): JsonResponse
    {
        try {
            $sale_id = $request->input('sale_id');

            /** @var GetSale $getSaleUseCase */
            $getSaleUseCase = app()->make(GetSale::class);
            $sale = $getSaleUseCase->perform($sale_id);

            /** @var CreatePayment $useCase */
            $useCase = app()->make(CreatePayment::class);
            $payment = $useCase->perform($sale);

            return $this->response(
                "Payment created successfully",
                "success",
                201,
                $payment->toArray()
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get all payments
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $organization_id = $request->input('organization_id') ?? request()->user()->organization_id;
            if (!$organization_id) {
                throw new \Exception('Organization ID is required');
            }
            /** @var GetOrganization $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(GetOrganization::class);
            $organization = $getOrganizationUseCase->perform($organization_id);

            /** @var GetAllPayments $useCase */
            $useCase = app()->make(GetAllPayments::class);
            $payments = $useCase->perform($organization, $request->all());
            return $this->response(
                "Payments retrieved successfully",
                "success",
                200,
                $payments
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get payment by ID
     */
    public function show(int $payment_id): JsonResponse
    {
        try {
            /** @var GetSale $getSaleUseCase */
            $getSaleUseCase = app()->make(GetSale::class);
            $sale = $getSaleUseCase->perform($payment_id);

            /** @var GetPaymentById $useCase */
            $useCase = app()->make(GetPaymentById::class);
            $payment = $useCase->perform($sale);

            return $this->response(
                "Payment retrieved successfully",
                "success",
                200,
                $payment
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update payment
     */
    public function update(int $payment_id): JsonResponse
    {
        try {

            /** @var GetSale $getSaleUseCase */
            $getSaleUseCase = app()->make(GetSale::class);
            $sale = $getSaleUseCase->perform($payment_id);

            /** @var UpdatePayment $useCase */
            $useCase = app()->make(UpdatePayment::class);
            $payment = $useCase->perform($sale);

            return $this->response(
                "Payment updated successfully",
                "success",
                200,
                $payment
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete payment
     */
    public function destroy(int $payment_id): JsonResponse
    {
        try {

            /** @var GetSale $getSaleUseCase */
            $getSaleUseCase = app()->make(GetSale::class);
            $sale = $getSaleUseCase->perform($payment_id);

            /** @var DeletePayment $useCase */
            $useCase = app()->make(DeletePayment::class);
            $result = $useCase->perform($sale);

            return $this->response(
                "Payment deleted successfully",
                "success",
                200,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
