<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WhatsAppMessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'message_id' => $this->message_id,
            'message' => $this->whenLoaded('message', function () {
                return [
                    'id' => $this->message->id,
                    'organization_id' => $this->message->organization_id,
                    'campaign_id' => $this->message->campaign_id,
                    'template_id' => $this->message->template_id,
                    'client_id' => $this->message->client_id,
                    'message' => $this->message->message,
                    'status' => $this->message->status,
                    'is_sent' => $this->message->is_sent,
                    'is_fail' => $this->message->is_fail,
                    'is_read' => $this->message->is_read,
                    'is_direct_message' => $this->message->is_direct_message,
                    'sent_at' => $this->message->sent_at,
                    'scheduled_at' => $this->message->scheduled_at,
                    'created_at' => $this->message->created_at,
                    'updated_at' => $this->message->updated_at,
                ];
            }),
            'whatsapp_message_id' => $this->whatsapp_message_id,
            'message_status' => $this->message_status,
            'wa_id' => $this->wa_id,
            'input_phone' => $this->input_phone,
            'messaging_product' => $this->messaging_product,
            'json' => $this->json,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
