<?php

namespace App\Factories;

use App\Services\ASAAS\Factories\AsaasOrganizationCustomerFactory;
use App\Services\ASAAS\Factories\AsaasOrganizationFactory;
use App\Domains\Organization;
use App\Http\Requests\Organization\UpdateRequest;
use App\Models\Organization as OrganizationModel;
use App\Enums\CompanyType;
use Carbon\Carbon;

class OrganizationFactory
{
    private ?AsaasOrganizationFactory $asaasOrganizationFactory = null;
    private ?AsaasOrganizationCustomerFactory $asaasOrganizationCustomerFactory = null;

    public function buildFromStoreArray(?array $request) : ?Organization {
        if($request === null){
            return null;
        }

        return new Organization(
            id: null,
            name: $request['name'] ?? "",
            description: $request['description'] ?? "",
            is_active: true,
            is_suspended: false,
            default_flow_id: $request['default_flow_id'] ?? null,
            whatsapp_webhook_verify_token: $request['whatsapp_webhook_verify_token'] ?? null,
            whatsapp_webhook_secret: $request['whatsapp_webhook_secret'] ?? null,
            email: $request['email'] ?? null,
            cpf_cnpj: $request['cpfCnpj'] ?? null,
            company_type: isset($request['companyType']) ? CompanyType::from($request['companyType']) : null,
            phone: $request['phone'] ?? null,
            mobile_phone: $request['mobilePhone'] ?? null,
            address: $request['address'] ?? null,
            address_number: $request['addressNumber'] ?? null,
            complement: $request['complement'] ?? null,
            province: $request['province'] ?? null,
            city: $request['city'] ?? null,
            state: $request['state'] ?? null,
            postal_code: $request['postalCode'] ?? null,
            birth_date: isset($request['birth_date']) ? Carbon::parse($request['birth_date']) : null,
            asaas_account_id: $request['asaas_account_id'] ?? null,
            asaas_api_key: $request['asaas_api_key'] ?? null,
            asaas_wallet_id: $request['asaas_wallet_id'] ?? null,
            asaas_environment: $request['asaas_environment'] ?? 'sandbox',
            asaas_subscription_id: $request['asaas_subscription_id'] ?? null,
            subscription_status: $request['subscription_status'] ?? null,
            subscription_value: $request['subscription_value'] ?? null,
            subscription_due_date: isset($request['subscription_due_date']) ? Carbon::parse($request['subscription_due_date']) : null,
            subscription_started_at: isset($request['subscription_started_at']) ? Carbon::parse($request['subscription_started_at']) : null,
            subscription_expires_at: isset($request['subscription_expires_at']) ? Carbon::parse($request['subscription_expires_at']) : null,
            is_courtesy: $request['is_courtesy'] ?? false,
            courtesy_expires_at: isset($request['courtesy_expires_at']) ? Carbon::parse($request['courtesy_expires_at']) : null,
            courtesy_reason: $request['courtesy_reason'] ?? null,
            monthly_revenue: $request['monthly_revenue'] ?? null,
            income_value: $request['income_value'] ?? null,
            created_at: null,
            updated_at: null,
            asaas: null,
            asaasCustomer: null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Organization {
        return new Organization(
            id: null, // id will be set in use case
            name: $request->name ?? "",
            description: $request->description ?? "",
            is_active: $request->is_active ?? null, // is_active - not updated via this endpoint
            is_suspended: $request->is_suspended ?? null, // is_suspended - not updated via this endpoint
            default_flow_id: $request->default_flow_id ?? null,
            whatsapp_webhook_verify_token: $request->whatsapp_webhook_verify_token ?? null,
            whatsapp_webhook_secret: $request->whatsapp_webhook_secret ?? null,
            email: $request->email ?? null,
            cpf_cnpj: $request->cpf_cnpj ?? null,
            company_type: isset($request->company_type) ? CompanyType::from($request->company_type) : null,
            phone: $request->phone ?? null,
            mobile_phone: $request->mobile_phone ?? null,
            address: $request->address ?? null,
            address_number: $request->address_number ?? null,
            complement: $request->complement ?? null,
            province: $request->province ?? null,
            city: $request->city ?? null,
            state: $request->state ?? null,
            postal_code: $request->postal_code ?? null,
            birth_date: isset($request->birth_date) ? Carbon::parse($request->birth_date) : null,
            asaas_account_id: $request->asaas_account_id ?? null,
            asaas_api_key: $request->asaas_api_key ?? null,
            asaas_wallet_id: $request->asaas_wallet_id ?? null,
            asaas_environment: $request->asaas_environment ?? null,
            asaas_subscription_id: $request->asaas_subscription_id ?? null,
            subscription_status: $request->subscription_status ?? null,
            subscription_value: $request->subscription_value ?? null,
            subscription_due_date: isset($request->subscription_due_date) ? Carbon::parse($request->subscription_due_date) : null,
            subscription_started_at: isset($request->subscription_started_at) ? Carbon::parse($request->subscription_started_at) : null,
            subscription_expires_at: isset($request->subscription_expires_at) ? Carbon::parse($request->subscription_expires_at) : null,
            is_courtesy: $request->is_courtesy ?? null, // Allow courtesy updates if needed
            courtesy_expires_at: isset($request->courtesy_expires_at) ? Carbon::parse($request->courtesy_expires_at) : null,
            courtesy_reason: $request->courtesy_reason ?? null,
            monthly_revenue: $request->monthly_revenue ?? null,
            income_value: $request->income_value ?? null,
            created_at: null,
            updated_at: null,
            asaas: null,
            asaasCustomer: null
        );
    }

    public function buildFromModel(?OrganizationModel $organization, bool $with_asaas = false) : ?Organization {
        if(!$organization){
            return null;
        }
        if ($with_asaas) {
            if (!$this->asaasOrganizationFactory) {
                $this->asaasOrganizationFactory = new AsaasOrganizationFactory($this);
            }
            if (!$this->asaasOrganizationCustomerFactory) {
                $this->asaasOrganizationCustomerFactory = new AsaasOrganizationCustomerFactory();
            }
            $asaas = $this->asaasOrganizationFactory->buildFromModelOrganization($organization->asaas);
            $asaasCustomer = $this->asaasOrganizationCustomerFactory->buildFromModel($organization->asaasCustomer);
        }
        return new Organization(
            id: $organization->id ?? null,
            name: $organization->name ?? "",
            description: $organization->description ?? "",
            is_active: $organization->is_active ?? null,
            is_suspended: $organization->is_suspended ?? false,
            default_flow_id: $organization->default_flow_id ?? null,
            whatsapp_webhook_verify_token: $organization->whatsapp_webhook_verify_token ?? null,
            whatsapp_webhook_secret: $organization->whatsapp_webhook_secret ?? null,
            email: $organization->email ?? null,
            cpf_cnpj: $organization->cpf_cnpj ?? null,
            company_type: $organization->company_type ?? null,
            phone: $organization->phone ?? null,
            mobile_phone: $organization->mobile_phone ?? null,
            address: $organization->address ?? null,
            address_number: $organization->address_number ?? null,
            complement: $organization->complement ?? null,
            province: $organization->province ?? null,
            city: $organization->city ?? null,
            state: $organization->state ?? null,
            postal_code: $organization->postal_code ?? null,
            birth_date: $organization->birth_date ?? null,
            asaas_account_id: $organization->asaas_account_id ?? null,
            asaas_api_key: $organization->asaas_api_key ?? null,
            asaas_wallet_id: $organization->asaas_wallet_id ?? null,
            asaas_environment: $organization->asaas_environment ?? null,
            asaas_subscription_id: $organization->asaas_subscription_id ?? null,
            subscription_status: $organization->subscription_status ?? null,
            subscription_value: $organization->subscription_value ?? null,
            subscription_due_date: $organization->subscription_due_date ?? null,
            subscription_started_at: $organization->subscription_started_at ?? null,
            subscription_expires_at: $organization->subscription_expires_at ?? null,
            is_courtesy: $organization->is_courtesy ?? false,
            courtesy_expires_at: $organization->courtesy_expires_at ?? null,
            courtesy_reason: $organization->courtesy_reason ?? null,
            monthly_revenue: $organization->monthly_revenue ?? null,
            income_value: $organization->income_value ?? null,
            created_at: $organization->created_at ?? null,
            updated_at: $organization->updated_at ?? null,
            asaas: $asaas ?? null,
            asaasCustomer: $asaasCustomer ?? null
        );
    }
}
