<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\CampaignCategoryAssignment as CampaignCategoryAssignmentDomain;
use App\Models\CampaignCategoryAssignment as CampaignCategoryAssignmentModel;

class CampaignCategoryAssignmentFactory
{
    public function buildFromModel(
        ?CampaignCategoryAssignmentModel $model,
        bool $loadCampaign = false,
        bool $loadCategory = false
    ): ?CampaignCategoryAssignmentDomain {
        if (!$model) {
            return null;
        }
        $campaign = null;
        $category = null;

        if ($loadCampaign && $model->relationLoaded('campaign')) {
            /** @var CampaignFactory $campaignFactory */
            $campaignFactory = app()->make(CampaignFactory::class);
            $campaign = $campaignFactory->buildFromModel($model->campaign, false, false, false, false, false, false);
        }

        if ($loadCategory && $model->relationLoaded('category')) {
            /** @var CategoryFactory $categoryFactory */
            $categoryFactory = app()->make(CategoryFactory::class);
            $category = $categoryFactory->buildFromModel($model->category, false);
        }

        return new CampaignCategoryAssignmentDomain(
            id: $model->id,
            campaign_id: $model->campaign_id,
            category_id: $model->category_id,
            assigned_at: $model->assigned_at,
            campaign: $campaign,
            category: $category
        );
    }

    public function buildFromArray(array $data): CampaignCategoryAssignmentDomain
    {
        return new CampaignCategoryAssignmentDomain(
            id: $data['id'] ?? null,
            campaign_id: $data['campaign_id'] ?? null,
            category_id: $data['category_id'] ?? null,
            assigned_at: isset($data['assigned_at']) ? \Carbon\Carbon::parse($data['assigned_at']) : null
        );
    }

    public function buildCollection(
        iterable $models,
        bool $loadCampaign = false,
        bool $loadCategory = false
    ): array {
        $assignments = [];
        foreach ($models as $model) {
            $assignments[] = $this->buildFromModel($model, $loadCampaign, $loadCategory);
        }
        return $assignments;
    }
}
