<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\CampaignTagAssignment as CampaignTagAssignmentDomain;
use App\Models\CampaignTagAssignment as CampaignTagAssignmentModel;

class CampaignTagAssignmentFactory
{
    public function buildFromModel(
        ?CampaignTagAssignmentModel $model,
        bool $loadCampaign = false,
        bool $loadTag = false
    ): ?CampaignTagAssignmentDomain {
        if (!$model) {
            return null;
        }
        $campaign = null;
        $tag = null;

        if ($loadCampaign && $model->relationLoaded('campaign')) {
            /** @var CampaignFactory $campaignFactory */
            $campaignFactory = app()->make(CampaignFactory::class);
            $campaign = $campaignFactory->buildFromModel($model->campaign, false, false, false, false, false, false);
        }

        if ($loadTag && $model->relationLoaded('tag')) {
            /** @var TagFactory $tagFactory */
            $tagFactory = app()->make(TagFactory::class);
            $tag = $tagFactory->buildFromModel($model->tag, false);
        }

        return new CampaignTagAssignmentDomain(
            id: $model->id,
            campaign_id: $model->campaign_id,
            tag_id: $model->tag_id,
            assigned_at: $model->assigned_at,
            campaign: $campaign,
            tag: $tag
        );
    }

    public function buildFromArray(array $data): CampaignTagAssignmentDomain
    {
        return new CampaignTagAssignmentDomain(
            id: $data['id'] ?? null,
            campaign_id: $data['campaign_id'] ?? null,
            tag_id: $data['tag_id'] ?? null,
            assigned_at: isset($data['assigned_at']) ? \Carbon\Carbon::parse($data['assigned_at']) : null
        );
    }

    public function buildCollection(
        iterable $models,
        bool $loadCampaign = false,
        bool $loadTag = false
    ): array {
        $assignments = [];
        foreach ($models as $model) {
            $assignments[] = $this->buildFromModel($model, $loadCampaign, $loadTag);
        }
        return $assignments;
    }
}
