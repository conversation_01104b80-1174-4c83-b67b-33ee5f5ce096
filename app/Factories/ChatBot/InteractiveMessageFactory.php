<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\InteractiveMessage;
use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\ListRow;
use App\Domains\ChatBot\Button;
use App\Enums\ChatBot\InteractiveType;
use App\Models\InteractiveMessage as InteractiveMessageModel;
use Illuminate\Support\Collection;

class InteractiveMessageFactory
{
    private ButtonFactory $buttonFactory;
    private ListSectionFactory $listSectionFactory;

    public function __construct()
    {
        $this->buttonFactory = app()->make(ButtonFactory::class);
        $this->listSectionFactory = app()->make(ListSectionFactory::class);
    }

    /**
     * Build InteractiveMessage from Eloquent model
     */
    public function buildFromModel(?InteractiveMessageModel $model, bool $with_relationships = false): ?InteractiveMessage
    {
        if (!$model) {
            return null;
        }

        $buttons = null;
        $sections = null;

        if ($with_relationships) {
            // Load buttons for BUTTON type
            if ($model->type === InteractiveType::BUTTON->value && $model->buttons) {
                $buttons = $this->buttonFactory->buildFromModels($model->buttons);
            }

            // Load sections for LIST type
            if ($model->type === InteractiveType::LIST->value && $model->sections) {
                $sections = $this->listSectionFactory->buildFromModels($model->sections);
            }
        }

        return new InteractiveMessage(
            id: $model->id,
            organization_id: $model->organization_id,
            header: $model->header,
            body: $model->body,
            footer: $model->footer,
            type: $model->type ? InteractiveType::from($model->type) : null,
            button_text: $model->button_text,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
            buttons: $buttons,
            sections: $sections
        );
    }

    /**
     * Build multiple InteractiveMessages from collection
     */
    public function buildFromModels(?Collection $models, bool $with_relationships = false): ?array
    {
        if (!$models) {
            return null;
        }

        $domains = [];

        foreach ($models as $model) {
            $domain = $this->buildFromModel($model, $with_relationships);
            if ($domain) {
                $domains[] = $domain;
            }
        }

        return $domains;
    }

    /**
     * Build InteractiveMessage from array data (for API requests)
     */
    public function buildFromArray(array $data, ?int $organization_id = null): InteractiveMessage
    {
        $type = isset($data['type']) ? InteractiveType::from($data['type']) : null;
        
        $buttons = null;
        $sections = null;

        // Build buttons for BUTTON type
        if ($type === InteractiveType::BUTTON && isset($data['buttons']) && is_array($data['buttons'])) {
            $buttons = [];
            foreach ($data['buttons'] as $buttonData) {
                $button = $this->buttonFactory->buildFromSaveFullButton(
                    $buttonData,
                    $buttonData['json'] ?? null,
                    $organization_id,
                    $buttonData['id'] ?? null
                );
                if ($button) {
                    $buttons[] = $button;
                }
            }
        }

        // Build sections for LIST type
        if ($type === InteractiveType::LIST && isset($data['sections']) && is_array($data['sections'])) {
            $sections = [];
            foreach ($data['sections'] as $sectionData) {
                $section = $this->listSectionFactory->buildFromArray($sectionData);
                if ($section) {
                    $sections[] = $section;
                }
            }
        }

        return new InteractiveMessage(
            id: $data['id'] ?? null,
            organization_id: $organization_id ?? $data['organization_id'] ?? null,
            header: $data['header'] ?? null,
            body: $data['body'] ?? null,
            footer: $data['footer'] ?? null,
            type: $type,
            button_text: $data['button_text'] ?? null,
            created_at: isset($data['created_at']) ? \Carbon\Carbon::parse($data['created_at']) : null,
            updated_at: isset($data['updated_at']) ? \Carbon\Carbon::parse($data['updated_at']) : null,
            buttons: $buttons,
            sections: $sections
        );
    }

    /**
     * Build InteractiveMessage for button type with validation
     */
    public function buildButtonMessage(
        string $body,
        array $buttons,
        ?int $organization_id = null,
        ?string $header = null,
        ?string $footer = null
    ): InteractiveMessage {
        if (count($buttons) > 3) {
            throw new \InvalidArgumentException('Button messages support maximum 3 buttons');
        }

        $buttonDomains = [];
        foreach ($buttons as $buttonData) {
            $button = $this->buttonFactory->buildFromSaveFullButton(
                $buttonData,
                $buttonData['json'] ?? null,
                $organization_id,
                $buttonData['id'] ?? null
            );
            if ($button) {
                $buttonDomains[] = $button;
            }
        }

        return new InteractiveMessage(
            organization_id: $organization_id,
            header: $header,
            body: $body,
            footer: $footer,
            type: InteractiveType::BUTTON,
            buttons: $buttonDomains
        );
    }

    /**
     * Build InteractiveMessage for list type with validation
     */
    public function buildListMessage(
        string $body,
        array $sections,
        ?int $organization_id = null,
        ?string $header = null,
        ?string $footer = null,
        ?string $button_text = null
    ): InteractiveMessage {
        $sectionDomains = [];
        $totalRows = 0;

        foreach ($sections as $sectionData) {
            $section = $this->listSectionFactory->buildFromArray($sectionData);
            if ($section) {
                $sectionDomains[] = $section;
                $totalRows += $section->getRowCount();
            }
        }

        if ($totalRows > 10) {
            throw new \InvalidArgumentException('List messages support maximum 10 options total');
        }

        return new InteractiveMessage(
            organization_id: $organization_id,
            header: $header,
            body: $body,
            footer: $footer,
            type: InteractiveType::LIST,
            button_text: $button_text ?? 'Ver opções',
            sections: $sectionDomains
        );
    }

    /**
     * Build simple button message with text buttons
     */
    public function buildSimpleButtonMessage(
        string $body,
        array $buttonTexts,
        ?int $organization_id = null,
        ?string $header = null,
        ?string $footer = null
    ): InteractiveMessage {
        if (count($buttonTexts) > 3) {
            throw new \InvalidArgumentException('Maximum 3 buttons allowed');
        }

        $buttons = [];
        foreach ($buttonTexts as $index => $text) {
            $buttons[] = new Button(
                id: null,
                organization_id: $organization_id,
                text: $text,
                type: 'reply',
                internal_type: null,
                internal_data: null,
                callback_data: json_encode(['id' => 'btn_' . ($index + 1), 'text' => $text]),
                json: null
            );
        }

        return new InteractiveMessage(
            organization_id: $organization_id,
            header: $header,
            body: $body,
            footer: $footer,
            type: InteractiveType::BUTTON,
            buttons: $buttons
        );
    }

    /**
     * Build simple list message with basic options
     */
    public function buildSimpleListMessage(
        string $body,
        array $options,
        ?int $organization_id = null,
        ?string $header = null,
        ?string $footer = null,
        ?string $button_text = null,
        ?string $section_title = null
    ): InteractiveMessage {
        if (count($options) > 10) {
            throw new \InvalidArgumentException('Maximum 10 options allowed');
        }

        $rows = [];
        foreach ($options as $index => $option) {
            $title = is_array($option) ? $option['title'] : $option;
            $description = is_array($option) ? ($option['description'] ?? null) : null;
            
            $rows[] = new ListRow(
                id: null,
                list_section_id: null,
                row_id: 'option_' . ($index + 1),
                title: $title,
                description: $description
            );
        }

        $section = new ListSection(
            id: null,
            interactive_message_id: null,
            title: $section_title,
            rows: $rows
        );

        return new InteractiveMessage(
            organization_id: $organization_id,
            header: $header,
            body: $body,
            footer: $footer,
            type: InteractiveType::LIST,
            button_text: $button_text ?? 'Ver opções',
            sections: [$section]
        );
    }
}
