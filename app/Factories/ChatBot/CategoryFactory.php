<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Category as CategoryDomain;
use App\Models\Category as CategoryModel;

class CategoryFactory
{
    public function buildFromModel(?CategoryModel $model, bool $loadCampaigns = false): ?CategoryDomain
    {
        if (!$model) {
            return null;
        }

        $campaigns = null;
        if ($loadCampaigns && $model->relationLoaded('campaigns')) {
            /** @var CampaignFactory $campaignFactory */
            $campaignFactory = app()->make(CampaignFactory::class);
            $campaigns = [];
            foreach ($model->campaigns as $campaign) {
                $campaigns[] = $campaignFactory->buildFromModel($campaign, false, false, false, false, false, false);
            }
        }

        return new CategoryDomain(
            id: $model->id,
            organization_id: $model->organization_id,
            name: $model->name,
            description: $model->description,
            color: $model->color,
            type: $model->type,
            usage_count: $model->campaigns_count ?? 0,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
            campaigns: $campaigns
        );
    }

    public function buildFromArray(array $data): CategoryDomain
    {
        return new CategoryDomain(
            id: $data['id'] ?? null,
            organization_id: $data['organization_id'] ?? null,
            name: $data['name'] ?? null,
            description: $data['description'] ?? null,
            color: $data['color'] ?? '#007bff',
            type: $data['type'] ?? 'campaign',
            usage_count: $data['usage_count'] ?? 0,
            created_at: isset($data['created_at']) ? \Carbon\Carbon::parse($data['created_at']) : null,
            updated_at: isset($data['updated_at']) ? \Carbon\Carbon::parse($data['updated_at']) : null
        );
    }

    public function buildCollection(iterable $models, bool $loadCampaigns = false): array
    {
        $categories = [];
        foreach ($models as $model) {
            $categories[] = $this->buildFromModel($model, $loadCampaigns);
        }
        return $categories;
    }
}
