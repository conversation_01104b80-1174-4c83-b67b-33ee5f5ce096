<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\ListRow;
use App\Models\ListSection as ListSectionModel;
use Illuminate\Support\Collection;

class ListSectionFactory
{
    private ListRowFactory $listRowFactory;

    public function __construct()
    {
        $this->listRowFactory = app()->make(ListRowFactory::class);
    }

    /**
     * Build ListSection from Eloquent model
     */
    public function buildFromModel(?ListSectionModel $model, bool $with_relationships = false): ?ListSection
    {
        if (!$model) {
            return null;
        }

        $rows = null;

        if ($with_relationships && $model->rows) {
            $rows = $this->listRowFactory->buildFromModels($model->rows);
        }

        return new ListSection(
            id: $model->id,
            interactive_message_id: $model->interactive_message_id,
            title: $model->title,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
            rows: $rows
        );
    }

    /**
     * Build multiple ListSections from collection
     */
    public function buildFromModels(?Collection $models, bool $with_relationships = false): ?array
    {
        if (!$models) {
            return null;
        }

        $domains = [];

        foreach ($models as $model) {
            $domain = $this->buildFromModel($model, $with_relationships);
            if ($domain) {
                $domains[] = $domain;
            }
        }

        return $domains;
    }

    /**
     * Build ListSection from array data
     */
    public function buildFromArray(array $data): ListSection
    {
        $rows = null;

        if (isset($data['rows']) && is_array($data['rows'])) {
            $rows = [];
            foreach ($data['rows'] as $rowData) {
                $row = $this->listRowFactory->buildFromArray($rowData);
                if ($row) {
                    $rows[] = $row;
                }
            }
        }

        return new ListSection(
            id: $data['id'] ?? null,
            interactive_message_id: $data['interactive_message_id'] ?? null,
            title: $data['title'] ?? null,
            created_at: isset($data['created_at']) ? \Carbon\Carbon::parse($data['created_at']) : null,
            updated_at: isset($data['updated_at']) ? \Carbon\Carbon::parse($data['updated_at']) : null,
            rows: $rows
        );
    }

    /**
     * Build ListSection with simple row data
     */
    public function buildWithSimpleRows(
        ?string $title,
        array $rowsData,
        ?int $interactive_message_id = null
    ): ListSection {
        $rows = [];

        foreach ($rowsData as $index => $rowData) {
            if (is_string($rowData)) {
                // Simple string title
                $rows[] = new ListRow(
                    id: null,
                    list_section_id: null,
                    row_id: 'row_' . ($index + 1),
                    title: $rowData,
                    description: null
                );
            } elseif (is_array($rowData)) {
                // Array with title and description
                $rows[] = new ListRow(
                    id: null,
                    list_section_id: null,
                    row_id: $rowData['row_id'] ?? 'row_' . ($index + 1),
                    title: $rowData['title'] ?? null,
                    description: $rowData['description'] ?? null
                );
            }
        }

        return new ListSection(
            id: null,
            interactive_message_id: $interactive_message_id,
            title: $title,
            rows: $rows
        );
    }
}
