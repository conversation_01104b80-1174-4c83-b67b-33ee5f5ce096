<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Button;
use App\Enums\ChatBot\WhatsAppButtonType;
use App\Http\Requests\Button\StoreRequest;
use App\Http\Requests\Button\UpdateRequest;
use App\Models\Button as ButtonModel;
use Illuminate\Support\Collection;

class ButtonFactory
{

    public function buildFromStoreRequest(StoreRequest $request) : Button {
        return new Button(
            null,
            $request->organization_id ?? null,
            $request->text ?? null,
            $request->type ?? null,
            $request->internal_type ?? null,
            $request->internal_data ?? null,
            $request->callback_data ?? null,
            $request->json ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Button {
        return new Button(
            null,
            $request->organization_id ?? null,
            $request->text ?? null,
            $request->type ?? null,
            $request->internal_type ?? null,
            $request->internal_data ?? null,
            $request->callback_data ?? null,
            $request->json ?? null,
        );
    }

    public function buildFromModel(?ButtonModel $button) : ?Button {
        if (!$button){ return null; }

        return new Button(
            $button->id ?? null,
            $button->organization_id ?? null,
            $button->text ?? null,
            $button->type ?? null,
            $button->internal_type ?? null,
            $button->internal_data ?? null,
            $button->callback_data ?? null,
            $button->json ?? null,
            $button->created_at ?? null,
            $button->updated_at ?? null,
        );
    }

    /**
     * @param Collection|null $buttons
     * @return Button[]|null
     */
    public function buildFromModels(?Collection $buttons) : ?array {
        $domains = [];

        /** @var ButtonModel $button */
        foreach ($buttons as $button){
            $domains[] = $this->buildFromModel($button);
        }

        return $domains;
    }

    public function buildFromSaveFullButton(array $button, ?string $json, ?int $organization_id, ?int $id) : ?Button {
        /** @var array $callback_reply */
        $callback_reply = $button['callback_data'] ?? $button['reply'] ?? [];

        // Extract internal data based on button type
        $internal_data = Button::extractInternalDataFromButtonArray($button);

        // Normalize button type to enum-compatible format
        $buttonType = $this->normalizeButtonType($button['type'] ?? 'reply');

        return new Button(
            $id,
            $organization_id ?? null,
            $button['text'] ?? "unnamed button",
            $buttonType,
            $button['internal_type'] ?? null,
            $internal_data,
            json_encode($callback_reply) ?? null,
            $json ?? null,
        );
    }

    /**
     * Normalize button type to be compatible with WhatsAppButtonType enum
     * Maintains backward compatibility with existing data
     */
    private function normalizeButtonType(string $type): string
    {
        $normalizedType = strtolower(trim($type));

        return match ($normalizedType) {
            'quick_reply', 'reply' => 'reply',
            'url' => 'url',
            'phone_number' => 'phone_number',
            'copy_code' => 'copy_code',
            'flow' => 'flow',
            default => $normalizedType, // Keep original for backward compatibility
        };
    }

    /**
     * Create a button with WhatsApp validation
     */
    public function buildValidatedWhatsAppButton(
        ?int $id,
        ?int $organization_id,
        string $text,
        WhatsAppButtonType $buttonType,
        ?string $internal_type = null,
        ?string $internal_data = null,
        ?string $callback_data = null,
        ?string $json = null
    ): Button {
        $button = new Button(
            $id,
            $organization_id,
            $text,
            $buttonType->value,
            $internal_type,
            $internal_data,
            $callback_data,
            $json
        );

        // Validate the button for WhatsApp compliance
        if (!$button->validateForWhatsApp()) {
            throw new \InvalidArgumentException('Button does not meet WhatsApp compliance requirements');
        }

        return $button;
    }

    /**
     * Build multiple buttons from array with validation
     *
     * @param array $buttonsData
     * @param int|null $organization_id
     * @return Button[]
     * @throws \Exception
     */
    public function buildValidatedButtonCollection(array $buttonsData, ?int $organization_id = null): array
    {
        $buttons = [];

        foreach ($buttonsData as $buttonData) {
            $button = $this->buildFromSaveFullButton(
                $buttonData,
                $buttonData['json'] ?? null,
                $organization_id,
                $buttonData['id'] ?? null
            );

            if ($button) {
                $buttons[] = $button;
            }
        }

        // Validate the entire collection
        Button::validateButtonCollection($buttons);

        return $buttons;
    }
}
