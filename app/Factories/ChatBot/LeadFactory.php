<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Lead;
use App\Models\Lead as LeadModel;

class LeadFactory
{
    /**
     * Build Lead domain from Model
     */
    public function buildFromModel(?LeadModel $leadModel): ?Lead
    {
        if (!$leadModel) {
            return null;
        }
        return new Lead(
            id: $leadModel->id,
            organization_id: $leadModel->organization_id,
            client_id: $leadModel->client_id,
            source: $leadModel->source,
            status: $leadModel->status,
            priority: $leadModel->priority,
            title: $leadModel->title,
            description: $leadModel->description,
            notes: $leadModel->notes,
            estimated_value: $leadModel->estimated_value ? (float) $leadModel->estimated_value : null,
            service_type: $leadModel->service_type,
            budget_range: $leadModel->budget_range,
            timeline: $leadModel->timeline,
            company: $leadModel->company,
            custom_fields: $leadModel->custom_fields,
            created_via: $leadModel->created_via,
            contacted_at: $leadModel->contacted_at,
            qualified_at: $leadModel->qualified_at,
            closed_at: $leadModel->closed_at,
            created_at: $leadModel->created_at,
            updated_at: $leadModel->updated_at,
            deleted_at: $leadModel->deleted_at
        );
    }

    /**
     * Build Lead domain from array
     */
    public function buildFromArray(array $data): Lead
    {
        return new Lead(
            id: $data['id'] ?? null,
            organization_id: $data['organization_id'],
            client_id: $data['client_id'],
            source: $data['source'] ?? 'manual',
            status: $data['status'] ?? 'new',
            priority: $data['priority'] ?? 'medium',
            title: $data['title'] ?? null,
            description: $data['description'] ?? null,
            notes: $data['notes'] ?? null,
            estimated_value: isset($data['estimated_value']) ? (float) $data['estimated_value'] : null,
            service_type: $data['service_type'] ?? null,
            budget_range: $data['budget_range'] ?? null,
            timeline: $data['timeline'] ?? null,
            company: $data['company'] ?? null,
            custom_fields: $data['custom_fields'] ?? null,
            created_via: $data['created_via'] ?? 'manual',
            contacted_at: isset($data['contacted_at']) ? new \DateTime($data['contacted_at']) : null,
            qualified_at: isset($data['qualified_at']) ? new \DateTime($data['qualified_at']) : null,
            closed_at: isset($data['closed_at']) ? new \DateTime($data['closed_at']) : null,
            created_at: isset($data['created_at']) ? new \DateTime($data['created_at']) : null,
            updated_at: isset($data['updated_at']) ? new \DateTime($data['updated_at']) : null,
            deleted_at: isset($data['deleted_at']) ? new \DateTime($data['deleted_at']) : null
        );
    }

    /**
     * Build Lead domain for ChatBot creation
     */
    public function buildForChatBot(
        int $organizationId,
        int $clientId,
        array $conversationData = [],
        string $source = 'chatbot_whatsapp'
    ): Lead {
        $leadData = [
            'organization_id' => $organizationId,
            'client_id' => $clientId,
            'source' => $source,
            'status' => 'new',
            'priority' => 'medium',
            'created_via' => 'chatbot',
            'description' => 'Lead criado via ChatBot WhatsApp',
        ];

        // Map conversation data to lead fields
        if (isset($conversationData['service_interest'])) {
            $leadData['service_type'] = $conversationData['service_interest'];
        }

        if (isset($conversationData['budget'])) {
            $leadData['budget_range'] = $conversationData['budget'];
        }

        if (isset($conversationData['timeline'])) {
            $leadData['timeline'] = $conversationData['timeline'];
        }

        if (isset($conversationData['company_name'])) {
            $leadData['company'] = $conversationData['company_name'];
        }

        if (isset($conversationData['additional_notes'])) {
            $leadData['notes'] = $conversationData['additional_notes'];
        }

        // Set title based on service type or default
        if (isset($leadData['service_type'])) {
            $leadData['title'] = "Interesse em {$leadData['service_type']}";
        } else {
            $leadData['title'] = 'Lead via ChatBot';
        }

        // Store original conversation data in custom fields
        $leadData['custom_fields'] = [
            'chatbot_conversation_data' => $conversationData,
            'created_via_chatbot' => true,
        ];

        return $this->buildFromArray($leadData);
    }

    /**
     * Build minimal Lead for quick creation
     */
    public function buildMinimal(
        int $organizationId,
        int $clientId,
        string $source = 'manual',
        string $status = 'new'
    ): Lead {
        return new Lead(
            id: null,
            organization_id: $organizationId,
            client_id: $clientId,
            source: $source,
            status: $status,
            priority: 'medium',
            created_via: $source === 'manual' ? 'manual' : 'system'
        );
    }

    /**
     * Build Lead with custom fields
     */
    public function buildWithCustomFields(
        int $organizationId,
        int $clientId,
        array $customFields,
        array $baseData = []
    ): Lead {
        $leadData = array_merge([
            'organization_id' => $organizationId,
            'client_id' => $clientId,
            'source' => 'manual',
            'status' => 'new',
            'priority' => 'medium',
            'created_via' => 'manual',
            'custom_fields' => $customFields,
        ], $baseData);

        return $this->buildFromArray($leadData);
    }

    /**
     * Clone Lead with new data
     */
    public function cloneLead(Lead $originalLead, array $newData = []): Lead
    {
        $leadData = $originalLead->toArray();

        // Remove ID to create new lead
        unset($leadData['id']);

        // Reset timestamps
        unset($leadData['created_at'], $leadData['updated_at'], $leadData['deleted_at']);

        // Reset status-specific timestamps
        unset($leadData['contacted_at'], $leadData['qualified_at'], $leadData['closed_at']);

        // Apply new data
        $leadData = array_merge($leadData, $newData);

        // Set default status for cloned lead
        $leadData['status'] = $newData['status'] ?? 'new';
        $leadData['created_via'] = 'cloned';

        return $this->buildFromArray($leadData);
    }
}
