<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\CampaignAnalytics as CampaignAnalyticsDomain;
use App\Models\CampaignAnalytics as CampaignAnalyticsModel;

class CampaignAnalyticsFactory
{
    public function buildFromModel(
        ?CampaignAnalyticsModel $model,
        bool $loadCampaign = false
    ): ?CampaignAnalyticsDomain {
        if (!$model) {
            return null;
        }
        $campaign = null;

        if ($loadCampaign && $model->relationLoaded('campaign')) {
            /** @var CampaignFactory $campaignFactory */
            $campaignFactory = app()->make(CampaignFactory::class);
            $campaign = $campaignFactory->buildFromModel($model->campaign, false, false, false, false, false, false);
        }

        return new CampaignAnalyticsDomain(
            id: $model->id,
            campaign_id: $model->campaign_id,
            total_messages: $model->total_messages,
            sent_count: $model->sent_count,
            delivered_count: $model->delivered_count,
            failed_count: $model->failed_count,
            read_count: $model->read_count,
            response_count: $model->response_count,
            avg_delivery_time: $model->avg_delivery_time,
            delivery_rate: $model->delivery_rate,
            read_rate: $model->read_rate,
            response_rate: $model->response_rate,
            failure_rate: $model->failure_rate,
            calculated_at: $model->calculated_at,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
            campaign: $campaign
        );
    }

    public function buildFromArray(array $data): CampaignAnalyticsDomain
    {
        return new CampaignAnalyticsDomain(
            id: $data['id'] ?? null,
            campaign_id: $data['campaign_id'] ?? null,
            total_messages: $data['total_messages'] ?? 0,
            sent_count: $data['sent_count'] ?? 0,
            delivered_count: $data['delivered_count'] ?? 0,
            failed_count: $data['failed_count'] ?? 0,
            read_count: $data['read_count'] ?? 0,
            response_count: $data['response_count'] ?? 0,
            avg_delivery_time: $data['avg_delivery_time'] ?? null,
            delivery_rate: $data['delivery_rate'] ?? 0.0,
            read_rate: $data['read_rate'] ?? 0.0,
            response_rate: $data['response_rate'] ?? 0.0,
            failure_rate: $data['failure_rate'] ?? 0.0,
            calculated_at: isset($data['calculated_at']) ? \Carbon\Carbon::parse($data['calculated_at']) : null,
            created_at: isset($data['created_at']) ? \Carbon\Carbon::parse($data['created_at']) : null,
            updated_at: isset($data['updated_at']) ? \Carbon\Carbon::parse($data['updated_at']) : null
        );
    }

    public function buildCollection(
        iterable $models,
        bool $loadCampaign = false
    ): array {
        $analytics = [];
        foreach ($models as $model) {
            $analytics[] = $this->buildFromModel($model, $loadCampaign);
        }
        return $analytics;
    }
}
