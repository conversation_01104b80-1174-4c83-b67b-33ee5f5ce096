<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Tag as TagDomain;
use App\Models\Tag as TagModel;

class TagFactory
{
    public function buildFromModel(?TagModel $model, bool $loadCampaigns = false): ?TagDomain
    {
        if (!$model) {
            return null;
        }
        $campaigns = null;
        if ($loadCampaigns && $model->relationLoaded('campaigns')) {
            /** @var CampaignFactory $campaignFactory */
            $campaignFactory = app()->make(CampaignFactory::class);
            $campaigns = [];
            foreach ($model->campaigns as $campaign) {
                $campaigns[] = $campaignFactory->buildFromModel($campaign, false, false, false, false, false, false);
            }
        }

        return new TagDomain(
            id: $model->id,
            organization_id: $model->organization_id,
            name: $model->name,
            usage_count: $model->usage_count,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
            campaigns: $campaigns
        );
    }

    public function buildFromArray(array $data): TagDomain
    {
        return new TagDomain(
            id: $data['id'] ?? null,
            organization_id: $data['organization_id'] ?? null,
            name: $data['name'] ?? null,
            usage_count: $data['usage_count'] ?? 0,
            created_at: isset($data['created_at']) ? \Carbon\Carbon::parse($data['created_at']) : null,
            updated_at: isset($data['updated_at']) ? \Carbon\Carbon::parse($data['updated_at']) : null
        );
    }

    public function buildCollection(iterable $models, bool $loadCampaigns = false): array
    {
        $tags = [];
        foreach ($models as $model) {
            $tags[] = $this->buildFromModel($model, $loadCampaigns);
        }
        return $tags;
    }
}
