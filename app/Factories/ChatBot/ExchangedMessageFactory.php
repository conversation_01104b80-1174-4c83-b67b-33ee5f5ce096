<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\ExchangedMessage;
use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\ExchangedMessage\StoreRequest;
use App\Http\Requests\ExchangedMessage\UpdateRequest;
use App\Models\ExchangedMessage as ExchangedMessageModel;
use Carbon\Carbon;

class ExchangedMessageFactory
{
    public ClientFactory $clientFactory;
    public PhoneNumberFactory $phoneNumberFactory;

    public function __construct(
        ClientFactory $clientFactory,
        PhoneNumberFactory $phoneNumberFactory
    ) {
        $this->clientFactory = $clientFactory;
        $this->phoneNumberFactory = $phoneNumberFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request): ExchangedMessage
    {
        return new ExchangedMessage(
            id: null,
            organization_id: $request->organization_id ?? null,
            client_id: $request->client_id ?? null,
            phone_number_id: $request->phone_number_id ?? null,
            conversation_id: $request->conversation_id ?? null,
            webhook_log_id: $request->webhook_log_id ?? null,
            message_id: $request->message_id ?? null,
            inbound: $request->inbound ?? false,
            outbound: $request->outbound ?? false,
            message: $request->message ?? null,
            json: $request->json ?? null,
            sent_at: isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            created_at: null,
            updated_at: null,
            client: null,
            phone_number: null,
            conversation: null,
            message_object: null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request): ExchangedMessage
    {
        return new ExchangedMessage(
            id: null,
            organization_id: null,
            client_id: $request->client_id ?? null,
            phone_number_id: $request->phone_number_id ?? null,
            conversation_id: $request->conversation_id ?? null,
            webhook_log_id: $request->webhook_log_id ?? null,
            message_id: $request->message_id ?? null,
            inbound: $request->inbound ?? false,
            outbound: $request->outbound ?? false,
            message: $request->message ?? null,
            json: $request->json ?? null,
            sent_at: isset($request->sent_at) ? Carbon::parse($request->sent_at) : null,
            created_at: null,
            updated_at: null,
            client: null,
            phone_number: null,
            conversation: null,
            message_object: null
        );
    }

    public function buildFromModel(
        ?ExchangedMessageModel $exchangedMessage,
        bool $with_client = false,
        bool $with_phone_number = false,
        bool $with_conversation = false,
        bool $with_message = false
    ): ?ExchangedMessage {
        if (!$exchangedMessage) {
            return null;
        }

        $client = null;
        $phone_number = null;
        $conversation = null;
        $message_object = null;

        if ($with_client && $exchangedMessage->client) {
            $client = $this->clientFactory->buildFromModel($exchangedMessage->client);
        }

        if ($with_phone_number && $exchangedMessage->phoneNumber) {
            $phone_number = $this->phoneNumberFactory->buildFromModel($exchangedMessage->phoneNumber);
        }

        if ($with_conversation && $exchangedMessage->conversation) {
            $conversation = app()->make(ConversationFactory::class)->buildFromModel($exchangedMessage->conversation);
        }

        if ($with_message && $exchangedMessage->relationLoaded('message') && $exchangedMessage->getRelation('message')) {
            $message_object = app()->make(MessageFactory::class)->buildFromModel($exchangedMessage->getRelation('message'));
        }

        return new ExchangedMessage(
            id: $exchangedMessage->id,
            organization_id: $exchangedMessage->organization_id,
            client_id: $exchangedMessage->client_id,
            phone_number_id: $exchangedMessage->phone_number_id,
            conversation_id: $exchangedMessage->conversation_id,
            webhook_log_id: $exchangedMessage->webhook_log_id,
            message_id: $exchangedMessage->message_id,
            inbound: $exchangedMessage->inbound,
            outbound: $exchangedMessage->outbound,
            message: $exchangedMessage->message,
            json: $exchangedMessage->json,
            sent_at: $exchangedMessage->sent_at ? Carbon::parse($exchangedMessage->sent_at) : null,
            created_at: $exchangedMessage->created_at ? Carbon::parse($exchangedMessage->created_at) : null,
            updated_at: $exchangedMessage->updated_at ? Carbon::parse($exchangedMessage->updated_at) : null,
            client: $client,
            phone_number: $phone_number,
            conversation: $conversation,
            message_object: $message_object
        );
    }

    /**
     * Build ExchangedMessage domain from webhook inbound message
     */
    public function buildFromWebhookInbound(
        array $webhookData,
        \App\Domains\ChatBot\PhoneNumber $phoneNumber,
        \App\Domains\Inventory\Client $client,
        ?\App\Domains\ChatBot\Conversation $conversation = null,
        ?int $webhookLogId = null
    ): ExchangedMessage {
        $messageData = $webhookData['message'] ?? [];
        $messageText = $this->extractMessageText($messageData);
        $sentAt = isset($messageData['timestamp']) ? Carbon::createFromTimestamp($messageData['timestamp']) : Carbon::now();

        return new ExchangedMessage(
            id: null,
            organization_id: $phoneNumber->organization_id,
            client_id: $client->id,
            phone_number_id: $phoneNumber->id,
            conversation_id: $conversation?->id,
            webhook_log_id: $webhookLogId,
            message_id: null,
            inbound: true,
            outbound: false,
            message: $messageText,
            json: $webhookData,
            sent_at: $sentAt,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            client: $client,
            phone_number: $phoneNumber,
            conversation: $conversation,
            message_object: null
        );
    }

    /**
     * Extract message text from webhook message data
     */
    private function extractMessageText(array $messageData): string
    {
        $type = $messageData['type'] ?? 'unknown';

        switch ($type) {
            case 'text':
                return $messageData['text']['body'] ?? '';
            case 'button':
                return $messageData['button']['text'] ?? $messageData['button']['payload'] ?? '';
            case 'interactive':
                $interactive = $messageData['interactive'] ?? [];
                return $interactive['button_reply']['title'] ??
                       $interactive['list_reply']['title'] ??
                       $interactive['body']['text'] ?? '';
            case 'image':
            case 'video':
            case 'audio':
            case 'document':
                return $messageData[$type]['caption'] ?? "[{$type}]";
            case 'location':
                return '[Location shared]';
            case 'contacts':
                return '[Contact shared]';
            default:
                return "[{$type} message]";
        }
    }

    public function buildFromOutboundMessage(
        Message $message,
        PhoneNumber $phoneNumber,
        Carbon $sentAt
    ): ExchangedMessage {
        return new ExchangedMessage(
            id: null,
            organization_id: $message->organization_id,
            client_id: $message->client_id,
            phone_number_id: $phoneNumber->id,
            conversation_id: null,
            webhook_log_id: null,
            message_id: $message->id,
            inbound: false,
            outbound: true,
            message: $message->message,
            json: $message->toWhatsAppPayload(),
            sent_at: $sentAt,
            created_at: null,
            updated_at: null,
            client: null,
            phone_number: null,
            conversation: null,
            message_object: null
        );
    }
}
