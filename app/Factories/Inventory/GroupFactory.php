<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Group;
use App\Http\Requests\Group\StoreRequest;
use App\Http\Requests\Group\UpdateRequest;
use App\Models\Group as GroupModel;

class GroupFactory
{

    public function buildFromStoreRequest(StoreRequest $request) : Group {
        return new Group(
            null,
            $request->organization_id ?? null,
            $request->name ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Group {
        return new Group(
            null,
            null,
            $request->name ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(?GroupModel $group) : ?Group {
        if (!$group) { return null; }
        return new Group(
            $group->id ?? null,
            $group->organization_id ?? null,
            $group->name ?? null,
            $group->description ?? null,
            $group->created_at ?? null,
            $group->updated_at ?? null,
        );
    }
}
