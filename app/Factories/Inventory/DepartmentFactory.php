<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Department;
use App\Http\Requests\Department\StoreRequest;
use App\Http\Requests\Department\UpdateRequest;
use App\Models\Department as DepartmentModel;
use Illuminate\Http\Request;

class DepartmentFactory
{

    public function buildFromStoreRequest(StoreRequest $request) : Department {
        return new Department(
            null,
            $request->organization_id ?? null,
            $request->name ?? null,
            $request->is_active ?? null
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Department {
        return new Department(
            null,
            null,
            $request->name ?? null,
            $request->is_active ?? null
        );
    }

    public function buildFromModel(?DepartmentModel $department) : ?Department {
        if(!$department){ return null; }

        return new Department(
            $department->id ?? null,
            $department->organization_id ?? null,
            $department->name ?? null,
            $department->is_active ?? null,
            $department->created_at ?? null,
            $department->updated_at ?? null
        );
    }
}
