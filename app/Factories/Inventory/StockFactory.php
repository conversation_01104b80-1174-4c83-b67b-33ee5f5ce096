<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Stock;
use App\Domains\Inventory\StockEntry;
use App\Http\Requests\Stock\StoreRequest;
use App\Http\Requests\Stock\UpdateRequest;
use App\Models\Stock as StockModel;

class StockFactory
{

    public BrandFactory $brandFactory;
    public ProductFactory $productFactory;
    public ShopFactory $shopFactory;

    public function __construct(
        BrandFactory $brandFactory,
        ProductFactory $productFactory,
        ShopFactory $shopFactory
    ) {
        $this->brandFactory = $brandFactory;
        $this->productFactory = $productFactory;
        $this->shopFactory = $shopFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Stock {
        return new Stock(
            null,
            $request->organization_id ?? null,
            $request->shop_id ?? null,
            $request->brand_id ?? null,
            $request->product_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Stock {
        return new Stock(
            null,
            null,
            null,
            null,
            null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(?StockModel $stock, bool $with_shop = true) : ?Stock {
        if(!$stock){ return null; }

        if($with_shop){ $shop = $this->shopFactory->buildFromModel($stock->shop ?? null); }

        return new Stock(
            $stock->id ?? null,
            $stock->organization_id ?? null,
            $stock->shop_id ?? null,
            $stock->brand_id ?? null,
            $stock->product_id ?? null,
            $stock->quantity ?? null,
            $stock->value ?? null,
            $stock->description ?? null,
            $stock->created_at ?? null,
            $stock->updated_at ?? null,
            $this->productFactory->buildFromModel($stock->product()->first() ?? null) ?? null,
            $shop ?? null
        );
    }

    public function buildFromStockEntry(StockEntry $stockEntry): Stock {
        return new Stock(
            null,
            $stockEntry->organization_id ?? null,
            $stockEntry->product->shop_id ?? null,
            $stockEntry->product->brand_id ?? null,
            $stockEntry->product_id ?? null,
            $stockEntry->quantity ?? null,
            $stockEntry->value ?? null,
            $stockEntry->description ?? null,
            null,
            null,
            $stockEntry->product ?? null,
            $stockEntry->shop ?? null
        );
    }
}
