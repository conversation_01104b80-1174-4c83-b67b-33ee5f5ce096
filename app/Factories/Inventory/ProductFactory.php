<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Product;
use App\Http\Requests\Product\StoreRequest;
use App\Http\Requests\Product\UpdateRequest;
use App\Models\Product as ProductModel;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ProductFactory
{
    public BrandFactory $brandFactory;
    public function __construct(BrandFactory $brandFactory){
        $this->brandFactory = $brandFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : Product {
        return new Product(
            null,
            $request->organization_id ?? null,
            $request->brand_id ?? null,
            $request->name ?? "Unnamed Product",
            $request->barcode ?? null,
            $request->description ?? null,
            $request->price ?? null,
            $request->unity ?? null,
            Carbon::now(),
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Product {
        return new Product(
            null,
            $request->organization_id ?? null,
            $request->brand_id ?? null,
            $request->name ?? "Unnamed Product",
            $request->barcode ?? null,
            $request->description ?? null,
            $request->price ?? null,
            $request->unity ?? null,
            null,
        );
    }

    public function buildFromModel(?ProductModel $product, bool $with_brand = true) : ?Product {
        if(!$product) { return null; }

        return new Product(
            $product->id ?? null,
            $product->organization_id ?? null,
            $product->brand_id ?? null,
            $product->name ?? "Unnamed Product",
            $product->barcode ?? null,
            $product->description ?? null,
            $product->price ?? null,
            is_numeric($product->unity) ? (int)$product->unity : null,
            $product->last_priced_at ? Carbon::parse($product->last_priced_at) : null,
            $product->created_at ?? null,
            $product->updated_at ?? null,
            ($with_brand) ? $this->brandFactory->buildFromModel($product->brand ?? null) : null,
        );
    }

    public function buildFromModelArray(Collection $productModels) : array {
        $products = [];
        foreach ($productModels as $product){
            $products[] = $this->buildFromModel($product);
        }
        return $products;
    }
}
