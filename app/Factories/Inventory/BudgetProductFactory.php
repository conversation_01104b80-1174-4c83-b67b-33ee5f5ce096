<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\BudgetProduct;
use App\Http\Requests\BudgetProduct\StoreRequest;
use App\Http\Requests\BudgetProduct\UpdateRequest;
use App\Models\Budget;
use App\Models\BudgetProduct as BudgetProductModel;

class BudgetProductFactory
{

    public BudgetFactory $budgetFactory;
    public ProductFactory $productFactory;

    public function __construct(
        BudgetFactory $budgetFactory,
        ProductFactory $productFactory,
    ) {
        $this->budgetFactory = $budgetFactory;
        $this->productFactory = $productFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : BudgetProduct {
        return new BudgetProduct(
            null,
            $request->budget_id ?? null,
            $request->product_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : BudgetProduct {
        return new BudgetProduct(
            null,
            $request->budget_id ?? null,
            $request->product_id ?? null,
            $request->quantity ?? null,
            $request->value ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(?BudgetProductModel $budgetProduct) : ?BudgetProduct {
        if (!$budgetProduct) { return null; }
        return new BudgetProduct(
            $budgetProduct->id ?? null,
            $budgetProduct->budget_id ?? null,
            $budgetProduct->product_id ?? null,
            $budgetProduct->quantity ?? null,
            $budgetProduct->value ?? null,
            $budgetProduct->description ?? null,
            $budgetProduct->created_at ?? null,
            $budgetProduct->updated_at ?? null,
            $this->budgetFactory->buildFromModel($budgetProduct->budget ?? null) ?? null,
            $this->productFactory->buildFromModel($budgetProduct->product ?? null) ?? null,
        );
    }

    public function buildFromBudget(Budget $budget) : array {
        $products = [];
        foreach ($budget->products as $product){
            $products[] = new BudgetProduct(
                $product->pivot->id ?? null,
                $budget->id ?? null,
                $product->id ?? null,
                $product->pivot->quantity ?? null,
                $product->pivot->value ?? null,
                $product->pivot->description ?? null,
                $product->pivot->created_at ?? null,
                $product->pivot->updated_at ?? null,
                null,
                $this->productFactory->buildFromModel($product ?? null) ?? null,
            );
        }
        return $products;
    }
}
