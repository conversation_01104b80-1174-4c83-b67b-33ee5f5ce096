<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\GroupProduct;
use App\Http\Requests\GroupProduct\StoreRequest;
use App\Http\Requests\GroupProduct\UpdateRequest;
use App\Models\GroupProduct as GroupProductModel;

class GroupProductFactory
{

    public GroupFactory $groupFactory;
    public ProductFactory $productFactory;

    public function __construct(
        GroupFactory $groupFactory,
        ProductFactory $productFactory,
    ) {
        $this->groupFactory = $groupFactory;
        $this->productFactory = $productFactory;
    }

    public function buildFromStoreRequest(StoreRequest $request) : GroupProduct {
        return new GroupProduct(
            null,
            $request->group_id ?? null,
            $request->product_id ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : GroupProduct {
        return new GroupProduct(
            null,
            $request->group_id ?? null,
            $request->product_id ?? null,
        );
    }

    public function buildFromModel(?GroupProductModel $groupProduct) : ?GroupProduct {
        if (!$groupProduct) { return null; }
        return new GroupProduct(
            $groupProduct->id ?? null,
            $groupProduct->group_id ?? null,
            $groupProduct->product_id ?? null,
            $groupProduct->created_at ?? null,
            $groupProduct->updated_at ?? null,
            $this->groupFactory->buildFromModel($groupProduct->group()->first() ?? null) ?? null,
            $this->productFactory->buildFromModel($groupProduct->product()->first() ?? null) ?? null
        );
    }
}
