<?php

namespace App\Factories;

use App\Domains\Profile;
use App\Http\Requests\Research\StoreRequest;
use App\Http\Requests\Research\UpdateRequest;
use App\Models\Profile as ProfileModel;

class ProfileFactory
{
    public function buildFromStoreRequest(StoreRequest $request) : Profile {
        return new Profile(
            null,
            $request->name ?? "",
            $request->slug ?? "",
            $request->description ?? null,
            $request->is_admin ?? null,
            $request->is_super_admin ?? null,
        );
    }
    public function buildFromUpdateRequest(UpdateRequest $request) : Profile {
        return new Profile(
            null,
            $request->name ?? "",
            $request->slug ?? "",
            $request->description ?? null,
            $request->is_admin ?? false,
            $request->is_super_admin ?? false,
        );
    }

    public function buildFromModel(ProfileModel $profile) : Profile {
        return new Profile(
            $profile->id ?? null,
            $profile->name ?? "",
            $profile->slug ?? "",
            $profile->description ?? null,
            $profile->is_admin ?? false,
            $profile->is_super_admin ?? false,
            $profile->created_at ?? null,
            $profile->updated_at ?? null,
        );
    }
}
