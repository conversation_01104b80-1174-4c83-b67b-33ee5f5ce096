#!/bin/bash
set -e

echo "Deployment started ..."

# Enter maintenance mode or return true
# if already is in maintenance mode
#(php artisan down) || true
#(php artisan down --message 'The app is being (quickly!) updated. Please try again in a minute.') || true

    # Pull the latest version of the app
    #git pull origin production
    git pull

    # Install composer dependencies
    #composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader
    composer install --no-interaction

    # Clear the old cache
    #php artisan clear-compiled

    # Recreate cache
    #php artisan optimize

    # Compile npm assets
    #npm run prod

    # Run database migrations
    php artisan migrate --force
    php artisan db:seed --force
    # php artisan cache:clear
    # php artisan config:clear
    # php artisan route:clear
# Exit maintenance mode
#php artisan up

echo "Deployment finished!"
