# Melhorias no Parser da Primavera

## Resumo das Melhorias

O código do parser da Primavera foi significativamente melhorado para extrair informações de forma mais precisa e robusta dos documentos OCR. As principais melhorias incluem:

## 1. Estrutura de Dados Mais Completa

### Antes:
- Extração limitada de apenas alguns campos básicos
- Estrutura de dados inconsistente
- Muitos campos importantes não eram capturados

### Depois:
```php
$dados = [
    'paciente' => '',
    'data_nascimento' => '',
    'idade' => '',
    'sexo' => '',
    'convenio' => '',
    'codigo_usuario' => '',
    'atendimento' => '',
    'prontuario' => '',
    'data_entrada' => '',
    'setor' => '',
    'cirurgia_realizada' => '',
    'cirurgiao' => '',
    'crm_cirurgiao' => '',
    'anestesista' => '',
    'tipo_anestesia' => '',
    'inicio_cirurgia' => '',
    'fim_cirurgia' => '',
    'classificacao' => '',
    'tipo_cirurgia' => '',
    'antibioticoprofilaxia' => '',
    'procedimentos' => [],
    'equipe_cirurgica' => [],
    'diagnostico_pre_operatorio' => '',
    'diagnostico_pos_operatorio' => '',
    'descricao_ato_cirurgico' => '',
    'materiais' => [],
    'opme' => []
];
```

## 2. Método de Extração Avançado

### Nova Abordagem:
- **`extractPrimaveraDataAdvanced()`**: Método especializado para o formato Primavera
- **Mapeamento de linhas**: Cria um mapa indexado do documento para navegação mais eficiente
- **Busca por padrões específicos**: Usa regex e posicionamento relativo para encontrar dados

### Métodos Auxiliares:
- **`findValueAfterLabel()`**: Encontra valores após um rótulo específico com offset configurável
- **`findValueBeforeLabel()`**: Encontra valores antes de um rótulo (útil para convênio)
- **`extractDiagnosis()`**: Extração especializada para diagnósticos

## 3. Melhorias Específicas por Campo

### Informações do Paciente:
- **Nome**: Extração mais precisa usando offset de 2 linhas após "Paciente"
- **Data de Nascimento**: Busca específica após "Data Nascto"
- **Idade**: Regex para capturar padrão "XX anos"
- **Sexo**: Busca direta por "Masculino" ou "Feminino"

### Informações Médicas:
- **Convênio**: Extração antes do rótulo (padrão específico da Primavera)
- **Cirurgião**: Regex melhorado para capturar nome e CRM: `(.+?)\s*\(CRM\s*(\d+)\)`
- **Procedimentos**: Captura códigos e descrições no formato "XXXXXXXX-Descrição"

### Equipe Cirúrgica:
- **Cirurgião Principal**: Extração direta após o rótulo
- **Primeiro Auxiliar**: Captura do assistente principal

### Materiais e OPME:
- **Materiais**: Regex para capturar "Item XX unidade"
- **OPME**: Extração específica de materiais especiais

## 4. Tratamento de Erros Melhorado

### Antes:
```php
return ['msg' => 'Error when parsing the code.'];
```

### Depois:
```php
return [
    'status' => 'error',
    'msg' => 'Erro ao processar documento da Primavera: ' . $e->getMessage()
];
```

## 5. Compatibilidade Retroativa

O novo parser mantém compatibilidade com o formato anterior:

```php
return [
    'source' => 'Primavera',
    'status' => 'success',
    'data' => $dados,
    // Backward compatibility
    'paciente' => $dados['paciente'],
    'birthdate' => $dados['data_nascimento'],
    'covenant' => $dados['convenio'],
    'entry_date' => $dados['data_entrada'],
    'surgeon' => $dados['cirurgiao'],
    'procedure' => $dados['cirurgia_realizada'],
];
```

## 6. Resultados do Teste

Com o documento OCR fornecido, o parser agora extrai corretamente:

- ✅ **Paciente**: "Carlos Jose Gomes da Silva"
- ✅ **Data de Nascimento**: "20/09/1973"
- ✅ **Idade**: "51 anos"
- ✅ **Sexo**: "Masculino"
- ✅ **Cirurgião**: "Rafael Moraes Tavares"
- ✅ **CRM**: "5473"
- ✅ **Data de Entrada**: "26/11/2024 07:45"
- ✅ **Setor**: "HP-Centro Cirúrgico"
- ✅ **Procedimentos**: Código e descrição completos
- ✅ **Equipe Cirúrgica**: Cirurgião principal e auxiliar
- ✅ **Materiais**: Lista completa com quantidades
- ✅ **OPME**: Materiais especiais

## 7. Próximos Passos Sugeridos

1. **Testes Adicionais**: Testar com mais documentos da Primavera para validar robustez
2. **Validação de Dados**: Adicionar validação de formatos (datas, CPF, etc.)
3. **Normalização**: Padronizar formatos de data e hora
4. **Logs Detalhados**: Melhorar logging para debug
5. **Tratamento de Variações**: Lidar com pequenas variações no OCR

## 8. Como Usar

O parser melhorado é usado da mesma forma que antes:

```php
$parser = new MessageToDataArray($telegram);
$result = $parser->parseFromText($ocrText);
```

A diferença está na qualidade e completude dos dados extraídos, especialmente para documentos da Rede Primavera Saúde.
