<?php

/**
 * WhatsApp Service Test Runner
 *
 * This script runs all WhatsApp-related tests in order of priority
 * and provides a comprehensive test report.
 */

require_once __DIR__ . '/../vendor/autoload.php';

class WhatsAppTestRunner
{
    private array $testSuites = [
        'Core Services (Critical)' => [
            'tests/Unit/Services/Meta/WhatsApp/ChatBotServiceTest.php',
            'tests/Unit/Services/Meta/WhatsApp/WhatsAppServiceTest.php',
            'tests/Unit/Services/Meta/WhatsApp/TemplateServiceTest.php',
            'tests/Unit/Services/Meta/WhatsApp/MessageServiceTest.php',
        ],
        'ChatBot Services (Critical - NEW)' => [
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/Services/ConditionalNavigationServiceTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/Services/DynamicInputServiceTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/Services/ChatBotMessageServiceTest.php',
        ],
        'Factories (High Priority - NEW)' => [
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/Factories/WhatsAppConversationFactoryTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/Factories/WhatsAppInteractionFactoryTest.php',
            'tests/Unit/Services/Meta/WhatsApp/Factories/WhatsAppTemplateFactoryTest.php',
        ],
        'Repositories (High Priority - NEW)' => [
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/Repositories/StepRepositoryTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/Repositories/WhatsAppConversationRepositoryTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/Repositories/WhatsAppInteractionRepositoryTest.php',
            'tests/Unit/Services/Meta/WhatsApp/Repositories/WhatsAppTemplateRepositoryTest.php',
        ],
        'Use Cases (High Priority)' => [
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessWebhookMessageTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/UseCases/FindOrCreateClientTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/UseCases/FindOrCreateConversationTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessFlowStepTest.php',
            'tests/Unit/Services/Meta/WhatsApp/ChatBot/UseCases/SendWhatsAppResponseTest.php',
            'tests/Unit/Services/Meta/WhatsApp/UseCases/Template/PublishTest.php',
        ],
        'Domains (Medium-High Priority)' => [
            'tests/Unit/Services/Meta/WhatsApp/Domains/WhatsAppTemplateTest.php',
            'tests/Unit/Services/Meta/WhatsApp/Domains/WhatsAppConversationTest.php',
            'tests/Unit/Services/Meta/WhatsApp/Domains/WhatsAppInteractionTest.php',
        ],
        'Feature Tests (Integration - NEW)' => [
            'tests/Feature/Services/Meta/WhatsApp/TemplateManagementTest.php',
            'tests/Feature/Services/Meta/WhatsApp/MessageSendingTest.php',
            'tests/Feature/Services/Meta/WhatsApp/ErrorHandlingTest.php',
            'tests/Feature/Services/Meta/WhatsApp/ChatBot/ChatBotWebhookTest.php',
        ],
    ];

    private array $results = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function run(): void
    {
        $this->printHeader();

        foreach ($this->testSuites as $suiteName => $testFiles) {
            $this->runTestSuite($suiteName, $testFiles);
        }

        $this->printSummary();
    }

    private function printHeader(): void
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════════════════════╗\n";
        echo "║                          WhatsApp Service Test Suite                        ║\n";
        echo "║                                                                              ║\n";
        echo "║  Comprehensive testing for Meta WhatsApp ChatBot service components         ║\n";
        echo "║  Tests are organized by priority: Core Services → Use Cases → Domains      ║\n";
        echo "╚══════════════════════════════════════════════════════════════════════════════╝\n";
        echo "\n";
    }

    private function runTestSuite(string $suiteName, array $testFiles): void
    {
        echo "🔍 Running {$suiteName}\n";
        echo str_repeat("─", 80) . "\n";

        $suiteResults = [];

        foreach ($testFiles as $testFile) {
            $result = $this->runSingleTest($testFile);
            $suiteResults[] = $result;
            $this->results[] = $result;
        }

        $this->printSuiteResults($suiteResults);
        echo "\n";
    }

    private function runSingleTest(string $testFile): array
    {
        $testName = basename($testFile, '.php');
        echo "  ├─ Running {$testName}... ";

        // Check if test file exists
        if (!file_exists($testFile)) {
            echo "❌ FILE NOT FOUND\n";
            return [
                'name' => $testName,
                'file' => $testFile,
                'status' => 'missing',
                'output' => 'Test file not found',
                'tests' => 0,
                'assertions' => 0,
                'failures' => 1,
                'errors' => 0
            ];
        }

        // Run PHPUnit for this specific test
        $command = "cd " . dirname(__DIR__) . " && ./vendor/bin/phpunit {$testFile} --testdox 2>&1";
        $output = shell_exec($command);

        // Parse PHPUnit output
        $result = $this->parsePhpUnitOutput($output, $testName, $testFile);

        // Print result
        if ($result['status'] === 'passed') {
            echo "✅ PASSED ({$result['tests']} tests, {$result['assertions']} assertions)\n";
            $this->passedTests += $result['tests'];
        } elseif ($result['status'] === 'failed') {
            echo "❌ FAILED ({$result['failures']} failures, {$result['errors']} errors)\n";
            $this->failedTests += $result['failures'] + $result['errors'];
        } else {
            echo "⚠️  SKIPPED\n";
        }

        $this->totalTests += $result['tests'];

        return $result;
    }

    private function parsePhpUnitOutput(string $output, string $testName, string $testFile): array
    {
        $result = [
            'name' => $testName,
            'file' => $testFile,
            'status' => 'unknown',
            'output' => $output,
            'tests' => 0,
            'assertions' => 0,
            'failures' => 0,
            'errors' => 0
        ];

        // Parse test counts
        if (preg_match('/Tests: (\d+), Assertions: (\d+)/', $output, $matches)) {
            $result['tests'] = (int)$matches[1];
            $result['assertions'] = (int)$matches[2];
        }

        // Parse failures and errors
        if (preg_match('/Failures: (\d+)/', $output, $matches)) {
            $result['failures'] = (int)$matches[1];
        }

        if (preg_match('/Errors: (\d+)/', $output, $matches)) {
            $result['errors'] = (int)$matches[1];
        }

        // Determine status
        if (strpos($output, 'OK (') !== false) {
            $result['status'] = 'passed';
        } elseif ($result['failures'] > 0 || $result['errors'] > 0) {
            $result['status'] = 'failed';
        } elseif (strpos($output, 'No tests executed') !== false) {
            $result['status'] = 'skipped';
        }

        return $result;
    }

    private function printSuiteResults(array $suiteResults): void
    {
        $suitePassed = 0;
        $suiteFailed = 0;
        $suiteSkipped = 0;

        foreach ($suiteResults as $result) {
            if ($result['status'] === 'passed') {
                $suitePassed++;
            } elseif ($result['status'] === 'failed') {
                $suiteFailed++;
            } else {
                $suiteSkipped++;
            }
        }

        echo "  └─ Suite Results: ";
        if ($suitePassed > 0) echo "✅ {$suitePassed} passed ";
        if ($suiteFailed > 0) echo "❌ {$suiteFailed} failed ";
        if ($suiteSkipped > 0) echo "⚠️ {$suiteSkipped} skipped ";
        echo "\n";
    }

    private function printSummary(): void
    {
        echo "╔══════════════════════════════════════════════════════════════════════════════╗\n";
        echo "║                                TEST SUMMARY                                  ║\n";
        echo "╠══════════════════════════════════════════════════════════════════════════════╣\n";

        $totalTestFiles = count($this->results);
        $passedFiles = count(array_filter($this->results, fn($r) => $r['status'] === 'passed'));
        $failedFiles = count(array_filter($this->results, fn($r) => $r['status'] === 'failed'));
        $skippedFiles = count(array_filter($this->results, fn($r) => $r['status'] === 'skipped'));

        printf("║ Test Files:    %3d total, %3d passed, %3d failed, %3d skipped            ║\n",
               $totalTestFiles, $passedFiles, $failedFiles, $skippedFiles);
        printf("║ Test Methods:  %3d total, %3d passed, %3d failed                         ║\n",
               $this->totalTests, $this->passedTests, $this->failedTests);

        echo "╠══════════════════════════════════════════════════════════════════════════════╣\n";

        if ($failedFiles === 0) {
            echo "║                            🎉 ALL TESTS PASSED! 🎉                          ║\n";
            echo "║                                                                              ║\n";
            echo "║  The Meta WhatsApp service is fully tested and ready for production!       ║\n";
        } else {
            echo "║                          ⚠️  SOME TESTS FAILED ⚠️                           ║\n";
            echo "║                                                                              ║\n";
            echo "║  Please review the failed tests and fix any issues before deployment.      ║\n";
        }

        echo "╚══════════════════════════════════════════════════════════════════════════════╝\n";

        // Print failed test details
        if ($failedFiles > 0) {
            echo "\n";
            echo "🔍 FAILED TEST DETAILS:\n";
            echo str_repeat("─", 80) . "\n";

            foreach ($this->results as $result) {
                if ($result['status'] === 'failed') {
                    echo "\n❌ {$result['name']}\n";
                    echo "   File: {$result['file']}\n";
                    echo "   Failures: {$result['failures']}, Errors: {$result['errors']}\n";

                    // Show relevant output lines
                    $lines = explode("\n", $result['output']);
                    $errorLines = array_filter($lines, function($line) {
                        return strpos($line, 'FAIL') !== false ||
                               strpos($line, 'ERROR') !== false ||
                               strpos($line, 'Exception') !== false;
                    });

                    if (!empty($errorLines)) {
                        echo "   Output:\n";
                        foreach (array_slice($errorLines, 0, 3) as $line) {
                            echo "     " . trim($line) . "\n";
                        }
                    }
                }
            }
        }

        echo "\n";
        echo "📋 COVERAGE REPORT:\n";
        echo str_repeat("─", 80) . "\n";
        echo "✅ Core Services:     4/4 test files created\n";
        echo "✅ Use Cases:         2/5 test files created (40% - ProcessWebhookMessage, FindOrCreateClient)\n";
        echo "✅ Domains:           2/3 test files created (67% - WhatsAppTemplate, WhatsAppConversation)\n";
        echo "✅ Repositories:      1/3 test files created (33% - WhatsAppTemplateRepository)\n";
        echo "✅ Factories:         1/3 test files created (33% - WhatsAppTemplateFactory)\n";
        echo "✅ Feature Tests:     1/1 test files created (100% - ChatBot webhook integration)\n";
        echo "\n";
        echo "🎯 NEXT STEPS TO COMPLETE COVERAGE:\n";
        echo "   • Create tests for remaining Use Cases (FindOrCreateConversation, ProcessFlowStep, SendWhatsAppResponse)\n";
        echo "   • Create tests for WhatsAppInteraction domain\n";
        echo "   • Create tests for remaining repositories (WhatsAppConversationRepository, WhatsAppInteractionRepository)\n";
        echo "   • Create tests for remaining factories (WhatsAppConversationFactory, WhatsAppInteractionFactory)\n";
        echo "   • Create tests for supporting services (ConditionalNavigationService, DynamicInputService)\n";
        echo "\n";
    }
}

// Run the test suite
$runner = new WhatsAppTestRunner();
$runner->run();
