<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Models\PasswordResetToken;
use App\Models\Organization;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Auth\ValidateTokenRequest;
use App\UseCases\Auth\RequestPasswordReset;
use App\UseCases\Auth\ResetPassword;
use App\UseCases\Auth\ValidateResetToken;
use App\UseCases\Auth\CleanExpiredTokens;
use App\Services\Resend\ResendService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class PasswordResetTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the Resend service to avoid sending real emails
        $this->mock(ResendService::class, function ($mock) {
            $mock->shouldReceive('send')->andReturn(['id' => 'test-email-id']);
        });

        $this->organization = Organization::create([
            'name' => 'Test Organization',
            'slug' => 'test-org'
        ]);

        $this->user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'username' => 'testuser',
            'password' => bcrypt('password123'),
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_forgot_password_creates_token()
    {
        $response = $this->postJson('/api/auth/password/forgot', [
            'email' => $this->user->email
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Se o email estiver cadastrado, você receberá instruções para redefinir sua senha.'
        ]);

        // Verify token was created in database
        $this->assertDatabaseHas('password_reset_tokens', [
            'email' => $this->user->email,
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_forgot_password_returns_success_for_nonexistent_email()
    {
        // For security, we don't reveal if email exists or not
        $response = $this->postJson('/api/auth/password/forgot', [
            'email' => '<EMAIL>'
        ]);



        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Se o email estiver cadastrado, você receberá instruções para redefinir sua senha.'
        ]);

        // But no token should be created
        $this->assertDatabaseMissing('password_reset_tokens', [
            'email' => '<EMAIL>'
        ]);
    }

    public function test_validate_token_works_for_valid_token()
    {
        // Create a token first
        $token = PasswordResetToken::create([
            'email' => $this->user->email,
            'token' => 'test-token',
            'hashed_token' => \App\Domains\Auth\PasswordResetToken::hashToken('test-token'),
            'organization_id' => $this->organization->id,
            'expires_at' => Carbon::now()->addHour(),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        $response = $this->postJson('/api/auth/password/validate-token', [
            'email' => $this->user->email,
            'token' => 'test-token'
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Token válido'
        ]);
    }

    public function test_validate_token_fails_for_invalid_token()
    {
        $response = $this->postJson('/api/auth/password/validate-token', [
            'email' => $this->user->email,
            'token' => 'invalid-token'
        ]);



        $response->assertStatus(400);
        $response->assertJsonStructure(['message']);
    }

    public function test_validate_token_fails_for_expired_token()
    {
        // Create an expired token
        $token = PasswordResetToken::create([
            'email' => $this->user->email,
            'token' => 'test-token',
            'hashed_token' => \App\Domains\Auth\PasswordResetToken::hashToken('test-token'),
            'organization_id' => $this->organization->id,
            'expires_at' => Carbon::now()->subHour(),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        $response = $this->postJson('/api/auth/password/validate-token', [
            'email' => $this->user->email,
            'token' => 'test-token'
        ]);

        $response->assertStatus(400);
        $response->assertJsonStructure(['message']);
    }

    public function test_reset_password_works_with_valid_token()
    {
        // Create a token first
        $token = PasswordResetToken::create([
            'email' => $this->user->email,
            'token' => 'test-token',
            'hashed_token' => \App\Domains\Auth\PasswordResetToken::hashToken('test-token'),
            'organization_id' => $this->organization->id,
            'expires_at' => Carbon::now()->addHour(),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        $response = $this->postJson('/api/auth/password/reset', [
            'email' => $this->user->email,
            'token' => 'test-token',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123'
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Senha redefinida com sucesso'
        ]);

        // Verify password was changed
        $this->user->refresh();
        $this->assertTrue(\Hash::check('newpassword123', $this->user->password));

        // Verify token was marked as used
        $this->assertDatabaseHas('password_reset_tokens', [
            'email' => $this->user->email
        ]);

        $updatedToken = PasswordResetToken::where('email', $this->user->email)->first();
        $this->assertNotNull($updatedToken->used_at);
    }

    public function test_reset_password_fails_with_invalid_token()
    {
        $response = $this->postJson('/api/auth/password/reset', [
            'email' => $this->user->email,
            'token' => 'invalid-token',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123'
        ]);

        $response->assertStatus(400);
        $response->assertJsonStructure(['message']);
    }

    public function test_reset_password_fails_with_mismatched_passwords()
    {
        // Create a token first
        $token = PasswordResetToken::create([
            'email' => $this->user->email,
            'token' => 'test-token',
            'hashed_token' => \App\Domains\Auth\PasswordResetToken::hashToken('test-token'),
            'organization_id' => $this->organization->id,
            'expires_at' => Carbon::now()->addHour(),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        $response = $this->postJson('/api/auth/password/reset', [
            'email' => $this->user->email,
            'token' => 'test-token',
            'password' => 'newpassword123',
            'password_confirmation' => 'differentpassword'
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['password']);
    }

    public function test_clean_expired_tokens_removes_old_tokens()
    {
        // Create expired tokens
        PasswordResetToken::create([
            'email' => $this->user->email,
            'token' => 'expired-token-1',
            'hashed_token' => \App\Domains\Auth\PasswordResetToken::hashToken('expired-token-1'),
            'organization_id' => $this->organization->id,
            'expires_at' => Carbon::now()->subDay(),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        // Create used token older than 7 days
        PasswordResetToken::create([
            'email' => $this->user->email,
            'token' => 'used-token',
            'hashed_token' => \App\Domains\Auth\PasswordResetToken::hashToken('used-token'),
            'organization_id' => $this->organization->id,
            'expires_at' => Carbon::now()->addHour(),
            'used_at' => Carbon::now()->subDays(8),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'created_at' => Carbon::now()->subDays(8),
            'updated_at' => Carbon::now()->subDays(8)
        ]);

        // Create valid token (should not be deleted)
        PasswordResetToken::create([
            'email' => $this->user->email,
            'token' => 'valid-token',
            'hashed_token' => \App\Domains\Auth\PasswordResetToken::hashToken('valid-token'),
            'organization_id' => $this->organization->id,
            'expires_at' => Carbon::now()->addHour(),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        $cleanUseCase = app()->make(CleanExpiredTokens::class);
        $result = $cleanUseCase->perform();

        $this->assertEquals(1, $result['expired_tokens_deleted']);
        $this->assertEquals(1, $result['used_tokens_deleted']);
        $this->assertEquals(2, $result['total_cleaned']);

        // Verify only valid token remains
        $this->assertEquals(1, PasswordResetToken::count());
        $this->assertDatabaseHas('password_reset_tokens', [
            'token' => 'valid-token'
        ]);
    }

    public function test_token_cannot_be_reused_after_password_reset()
    {
        // Create a token and use it
        $token = PasswordResetToken::create([
            'email' => $this->user->email,
            'token' => 'test-token',
            'hashed_token' => \App\Domains\Auth\PasswordResetToken::hashToken('test-token'),
            'organization_id' => $this->organization->id,
            'expires_at' => Carbon::now()->addHour(),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);

        // Use the token to reset password
        $response = $this->postJson('/api/auth/password/reset', [
            'email' => $this->user->email,
            'token' => 'test-token',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123'
        ]);

        $response->assertStatus(200);

        // Try to use the same token again
        $response2 = $this->postJson('/api/auth/password/reset', [
            'email' => $this->user->email,
            'token' => 'test-token',
            'password' => 'anotherpassword',
            'password_confirmation' => 'anotherpassword'
        ]);

        $response2->assertStatus(400);
        $response2->assertJsonStructure(['message']);
    }
}
