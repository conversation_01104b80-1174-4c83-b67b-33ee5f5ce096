<?php

namespace Tests\Feature\UseCases\ChatBot;

use App\UseCases\ChatBot\Button\SaveFullButton;
use App\Repositories\ButtonRepository;
use App\Factories\ChatBot\ButtonFactory;
use App\Domains\ChatBot\Component;
use App\Enums\ComponentFormat;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SaveFullButtonWithInternalDataTest extends TestCase
{
    use RefreshDatabase;

    private SaveFullButton $saveFullButton;
    private ButtonRepository $buttonRepository;
    private ButtonFactory $buttonFactory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->buttonFactory = new ButtonFactory();
        $this->buttonRepository = new ButtonRepository($this->buttonFactory);
        $this->saveFullButton = new SaveFullButton($this->buttonRepository, $this->buttonFactory);
    }

    public function test_button_factory_with_url_type_extracts_internal_data()
    {
        $buttonData = [
            'type' => 'URL',
            'text' => '📱 Ver Cardápio',
            'url' => 'https://pizzaexpress.com.br/cardapio',
            'callback_data' => ['action' => 'view_menu']
        ];

        $organizationId = 1;
        $json = json_encode($buttonData);

        $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

        $this->assertNotNull($button);
        $this->assertEquals('https://pizzaexpress.com.br/cardapio', $button->internal_data);
        $this->assertEquals('URL', $button->type);
        $this->assertEquals('📱 Ver Cardápio', $button->text);
        $this->assertEquals($organizationId, $button->organization_id);
    }

    public function test_button_factory_with_phone_number_type_extracts_internal_data()
    {
        $buttonData = [
            'type' => 'PHONE_NUMBER',
            'text' => '📞 Ligar Agora',
            'phone_number' => '+5511999887766',
            'callback_data' => ['action' => 'call_now']
        ];

        $organizationId = 1;
        $json = json_encode($buttonData);

        $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

        $this->assertNotNull($button);
        $this->assertEquals('+5511999887766', $button->internal_data);
        $this->assertEquals('PHONE_NUMBER', $button->type);
        $this->assertEquals('📞 Ligar Agora', $button->text);
        $this->assertEquals($organizationId, $button->organization_id);
    }

    public function test_button_factory_with_case_insensitive_type_and_keys()
    {
        $buttonData = [
            'type' => 'url', // lowercase type
            'text' => 'Visit Website',
            'URL' => 'https://example.com', // uppercase key
            'callback_data' => ['action' => 'visit']
        ];

        $organizationId = 1;
        $json = json_encode($buttonData);

        $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

        $this->assertNotNull($button);
        $this->assertEquals('https://example.com', $button->internal_data);
        $this->assertEquals('url', $button->type); // Preserves original case
        $this->assertEquals('Visit Website', $button->text);
    }

    public function test_button_factory_with_phone_number_alternative_keys()
    {
        $buttonData = [
            'type' => 'PHONE_NUMBER',
            'text' => 'Call Support',
            'phoneNumber' => '+1234567890', // camelCase key
            'callback_data' => ['action' => 'call_support']
        ];

        $organizationId = 1;
        $json = json_encode($buttonData);

        $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

        $this->assertNotNull($button);
        $this->assertEquals('+1234567890', $button->internal_data);
        $this->assertEquals('PHONE_NUMBER', $button->type);
        $this->assertEquals('Call Support', $button->text);
    }

    public function test_button_factory_with_quick_reply_type_no_internal_data()
    {
        $buttonData = [
            'type' => 'QUICK_REPLY',
            'text' => 'Yes, I agree',
            'callback_data' => ['response' => 'yes']
        ];

        $organizationId = 1;
        $json = json_encode($buttonData);

        $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

        $this->assertNotNull($button);
        $this->assertNull($button->internal_data);
        $this->assertEquals('QUICK_REPLY', $button->type);
        $this->assertEquals('Yes, I agree', $button->text);
    }

    public function test_button_factory_with_missing_url_key()
    {
        $buttonData = [
            'type' => 'URL',
            'text' => 'Broken Link',
            // Missing 'url' key
            'callback_data' => ['action' => 'broken']
        ];

        $organizationId = 1;
        $json = json_encode($buttonData);

        $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

        $this->assertNotNull($button);
        $this->assertNull($button->internal_data);
        $this->assertEquals('URL', $button->type);
        $this->assertEquals('Broken Link', $button->text);
    }

    public function test_button_factory_with_empty_url_value()
    {
        $buttonData = [
            'type' => 'URL',
            'text' => 'Empty URL',
            'url' => '', // Empty URL
            'callback_data' => ['action' => 'empty']
        ];

        $organizationId = 1;
        $json = json_encode($buttonData);

        $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

        $this->assertNotNull($button);
        $this->assertNull($button->internal_data); // Empty values should return null
        $this->assertEquals('URL', $button->type);
        $this->assertEquals('Empty URL', $button->text);
    }

    public function test_button_factory_with_unknown_type()
    {
        $buttonData = [
            'type' => 'UNKNOWN_TYPE',
            'text' => 'Unknown Button',
            'some_data' => 'some_value',
            'callback_data' => ['action' => 'unknown']
        ];

        $organizationId = 1;
        $json = json_encode($buttonData);

        $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

        $this->assertNotNull($button);
        $this->assertNull($button->internal_data);
        $this->assertEquals('UNKNOWN_TYPE', $button->type);
        $this->assertEquals('Unknown Button', $button->text);
    }

    public function test_button_factory_with_complex_phone_number_scenarios()
    {
        // Test with different phone number key variations
        $scenarios = [
            ['phone_number', '+5511999887766'],
            ['PHONE_NUMBER', '+5511888776655'],
            ['Phone_Number', '+5511777665544'],
            ['phoneNumber', '+5511666554433'],
            ['phone', '+5511555443322'],
        ];

        foreach ($scenarios as [$key, $phoneNumber]) {
            $buttonData = [
                'type' => 'PHONE_NUMBER',
                'text' => 'Call Us',
                $key => $phoneNumber,
                'callback_data' => ['action' => 'call']
            ];

            $organizationId = 1;
            $json = json_encode($buttonData);

            $button = $this->buttonFactory->buildFromSaveFullButton($buttonData, $json, $organizationId, null);

            $this->assertNotNull($button);
            $this->assertEquals($phoneNumber, $button->internal_data, "Failed for key: $key");
            $this->assertEquals('PHONE_NUMBER', $button->type);
        }
    }
}
