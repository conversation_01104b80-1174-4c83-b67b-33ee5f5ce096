<?php

namespace Tests\Feature\UseCases\ChatBot\ExchangedMessage;

use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Factories\ChatBot\MessageFactory;
use App\Factories\ChatBot\PhoneNumberFactory;
use App\Factories\OrganizationFactory;
use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\Message;
use App\Repositories\ExchangedMessageRepository;
use App\UseCases\ChatBot\ExchangedMessage\SaveOutboundFromStatusUpdate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaveOutboundFromStatusUpdateTest extends TestCase
{
    use RefreshDatabase;

    private SaveOutboundFromStatusUpdate $useCase;
    private ExchangedMessageFactory $exchangedMessageFactory;
    private ExchangedMessageRepository $exchangedMessageRepository;
    private OrganizationFactory $organizationFactory;
    private PhoneNumberFactory $phoneNumberFactory;
    private MessageFactory $messageFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->exchangedMessageFactory = app()->make(ExchangedMessageFactory::class);
        $this->exchangedMessageRepository = app()->make(ExchangedMessageRepository::class);
        $this->organizationFactory = app()->make(OrganizationFactory::class);
        $this->phoneNumberFactory = app()->make(PhoneNumberFactory::class);
        $this->messageFactory = app()->make(MessageFactory::class);

        $this->useCase = new SaveOutboundFromStatusUpdate(
            $this->exchangedMessageFactory,
            $this->exchangedMessageRepository
        );
    }

    public function test_should_create_exchanged_message_for_delivered_status()
    {
        // Arrange
        $organizationModel = Organization::factory()->create();
        $phoneNumberModel = PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $clientModel = \App\Models\Client::factory()->create(['organization_id' => $organizationModel->id]);
        $messageModel = Message::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id
        ]);

        $organization = $this->organizationFactory->buildFromModel($organizationModel);
        $phoneNumber = $this->phoneNumberFactory->buildFromModel($phoneNumberModel);
        $message = $this->messageFactory->buildFromModel($messageModel, true);

        $statusData = [
            'status' => 'delivered',
            'timestamp' => time(),
            'id' => 'wamid.test123'
        ];

        // Act
        $result = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['processed']);
        $this->assertArrayHasKey('exchanged_message_id', $result);

        // Verify the ExchangedMessage was created in database
        $this->assertDatabaseHas('exchanged_messages', [
            'message_id' => $message->id,
            'organization_id' => $organization->id,
            'outbound' => true,
            'inbound' => false
        ]);
    }

    public function test_should_not_create_exchanged_message_for_non_delivered_status()
    {
        // Arrange
        $organizationModel = Organization::factory()->create();
        $phoneNumberModel = PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $clientModel = \App\Models\Client::factory()->create(['organization_id' => $organizationModel->id]);
        $messageModel = Message::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id
        ]);

        $organization = $this->organizationFactory->buildFromModel($organizationModel);
        $phoneNumber = $this->phoneNumberFactory->buildFromModel($phoneNumberModel);
        $message = $this->messageFactory->buildFromModel($messageModel, true);

        $statusData = [
            'status' => 'sent',
            'timestamp' => time(),
            'id' => 'wamid.test123'
        ];

        // Act
        $result = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        // Assert
        $this->assertFalse($result['success']);
        $this->assertEquals(0, $result['processed']);
        $this->assertEquals('Status is not delivered', $result['reason']);

        // Verify no ExchangedMessage was created
        $this->assertDatabaseMissing('exchanged_messages', [
            'message_id' => $message->id,
            'organization_id' => $organization->id
        ]);
    }

    public function test_should_prevent_duplicate_exchanged_messages()
    {
        // Arrange
        $organizationModel = Organization::factory()->create();
        $phoneNumberModel = PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $clientModel = \App\Models\Client::factory()->create(['organization_id' => $organizationModel->id]);
        $messageModel = Message::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id
        ]);

        $organization = $this->organizationFactory->buildFromModel($organizationModel);
        $phoneNumber = $this->phoneNumberFactory->buildFromModel($phoneNumberModel);
        $message = $this->messageFactory->buildFromModel($messageModel, true);

        $statusData = [
            'status' => 'delivered',
            'timestamp' => time(),
            'id' => 'wamid.test123'
        ];

        // Act - First call should succeed
        $result1 = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        // Act - Second call should detect duplicate
        $result2 = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        // Assert - First call succeeded
        $this->assertTrue($result1['success']);
        $this->assertEquals(1, $result1['processed']);

        // Assert - Second call detected duplicate
        $this->assertFalse($result2['success']);
        $this->assertEquals(0, $result2['processed']);
        $this->assertEquals('ExchangedMessage already exists for this message', $result2['reason']);

        // Verify only one ExchangedMessage exists
        $this->assertEquals(1, \App\Models\ExchangedMessage::where('message_id', $message->id)->count());
    }

    public function test_should_handle_race_condition_with_unique_constraint()
    {
        // Arrange
        $organizationModel = Organization::factory()->create();
        $phoneNumberModel = PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $clientModel = \App\Models\Client::factory()->create(['organization_id' => $organizationModel->id]);
        $messageModel = Message::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id
        ]);

        $organization = $this->organizationFactory->buildFromModel($organizationModel);
        $phoneNumber = $this->phoneNumberFactory->buildFromModel($phoneNumberModel);
        $message = $this->messageFactory->buildFromModel($messageModel, true);

        $statusData = [
            'status' => 'delivered',
            'timestamp' => time(),
            'id' => 'wamid.test123'
        ];

        // Simulate race condition by creating ExchangedMessage directly in database
        // This simulates what would happen if another webhook processed the same message
        // between our check and insert
        $exchangedMessage = $this->exchangedMessageFactory->buildFromOutboundMessage(
            $message,
            $phoneNumber,
            now()
        );
        $this->exchangedMessageRepository->store($exchangedMessage);

        // Act - This should detect the existing ExchangedMessage
        $result = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        // Assert - Should detect existing and return success with duplicate flag
        $this->assertFalse($result['success']);
        $this->assertEquals(0, $result['processed']);
        $this->assertEquals('ExchangedMessage already exists for this message', $result['reason']);

        // Verify only one ExchangedMessage exists
        $this->assertEquals(1, \App\Models\ExchangedMessage::where('message_id', $message->id)->count());
    }
}
