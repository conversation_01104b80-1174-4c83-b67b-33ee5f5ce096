<?php

namespace Tests\Feature\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\UseCases\ChatBot\ExchangedMessage\SaveExchangedMessagesFromWebhook;
use App\UseCases\ChatBot\ExchangedMessage\SaveFromWebhook;
use Tests\TestCase;
use Mockery;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SaveExchangedMessagesFromWebhookTest extends TestCase
{
    use RefreshDatabase;
    private SaveExchangedMessagesFromWebhook $saveExchangedMessagesFromWebhook;
    private $mockSaveFromWebhook;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockSaveFromWebhook = Mockery::mock(SaveFromWebhook::class);
        $this->saveExchangedMessagesFromWebhook = new SaveExchangedMessagesFromWebhook($this->mockSaveFromWebhook);
    }

    public function test_perform_processes_incoming_messages_successfully()
    {
        // Arrange
        $organizationModel = \App\Models\Organization::factory()->create();
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);

        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);

        $changeValueData = [
            'messaging_product' => 'whatsapp',
            'metadata' => ['display_phone_number' => '1234567890'],
            'contacts' => [['profile' => ['name' => 'Test User']]],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '5511999999999',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Mock SaveFromWebhook to return success
        $this->mockSaveFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->andReturn([
                'success' => true,
                'processed' => 1,
                'exchanged_message_id' => 456,
                'client_id' => 789
            ]);

        // Act
        $result = $this->saveExchangedMessagesFromWebhook->perform(
            $changeValue,
            $organization,
            $phoneNumber,
            123
        );

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['processed']);
        $this->assertEquals(1, $result['total_messages']);
        $this->assertCount(1, $result['results']);
        $this->assertEmpty($result['errors']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
