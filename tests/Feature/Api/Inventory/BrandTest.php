<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Brand;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class BrandTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_brand()
    {
        $brandData = [
            'name' => 'Test Brand',
            'description' => 'Test brand description'
        ];

        $response = $this->postJson('/api/brands', $brandData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals('Test Brand', $responseData['name']);
        $this->assertEquals('Test brand description', $responseData['description']);

        $this->assertDatabaseHas('brands', [
            'name' => 'Test Brand',
            'organization_id' => $this->organization->id,
            'description' => 'Test brand description'
        ]);
    }

    public function test_can_get_brand_by_id()
    {
        $brand = Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Get Brand',
            'description' => 'Brand for get test'
        ]);

        $response = $this->getJson("/api/brands/{$brand->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($brand->id, $responseData['id']);
        $this->assertEquals('Get Brand', $responseData['name']);
        $this->assertEquals('Brand for get test', $responseData['description']);
    }

    public function test_cannot_get_brand_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $brand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/brands/{$brand->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_brand()
    {
        $brand = Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Brand',
            'description' => 'Old description'
        ]);

        $updateData = [
            'name' => 'Updated Brand',
            'description' => 'Updated description'
        ];

        $response = $this->putJson("/api/brands/{$brand->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('brands', [
            'id' => $brand->id,
            'name' => 'Updated Brand',
            'description' => 'Updated description',
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Updated Brand', $responseData['name']);
        $this->assertEquals('Updated description', $responseData['description']);
    }

    public function test_cannot_update_brand_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $brand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'name' => 'Unauthorized Update'
        ];

        $response = $this->putJson("/api/brands/{$brand->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This brand don't belong to this organization."
                ]);
    }

    public function test_can_delete_brand()
    {
        $brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/brands/{$brand->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('brands', [
            'id' => $brand->id
        ]);
    }

    public function test_cannot_delete_brand_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $brand = Brand::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/brands/{$brand->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This brand don't belong to this organization."
                ]);
    }

    public function test_can_get_all_brands_with_pagination()
    {
        Brand::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/brands?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_brands_by_name()
    {
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Brand'
        ]);
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Brand'
        ]);

        $response = $this->getJson('/api/brands?name=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha Brand', $responseData['data'][0]['name']);
    }

    public function test_can_filter_brands_by_description()
    {
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Brand 1',
            'description' => 'Alpha description'
        ]);
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Brand 2',
            'description' => 'Beta description'
        ]);

        $response = $this->getJson('/api/brands?description=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha description', $responseData['data'][0]['description']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/brands', [
            'name' => '', // Empty name should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_validation_errors_on_update()
    {
        $brand = Brand::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->putJson("/api/brands/{$brand->id}", [
            'name' => '' // Empty name should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_brand_soft_delete_behavior()
    {
        $brand = Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'To Be Deleted'
        ]);

        // Delete the brand
        $response = $this->deleteJson("/api/brands/{$brand->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/brands');
        $responseData = $response->json('data.data');
        $brandNames = collect($responseData)->pluck('name')->toArray();
        $this->assertNotContains('To Be Deleted', $brandNames);

        // Should not be accessible by ID
        $response = $this->getJson("/api/brands/{$brand->id}");
        $response->assertStatus(404);
    }

    public function test_brand_ordering_and_filtering_combinations()
    {
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Brand',
            'description' => 'Alpha description',
            'created_at' => now()->subDays(3)
        ]);
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Brand',
            'description' => 'Beta description',
            'created_at' => now()->subDays(1)
        ]);
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Charlie Brand',
            'description' => 'Charlie description',
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by name ascending
        $response = $this->getJson('/api/brands?order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Alpha Brand', $responseData[0]['name']);
        $this->assertEquals('Beta Brand', $responseData[1]['name']);
        $this->assertEquals('Charlie Brand', $responseData[2]['name']);

        // Test ordering by name descending
        $response = $this->getJson('/api/brands?order=name&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Charlie Brand', $responseData[0]['name']);
        $this->assertEquals('Beta Brand', $responseData[1]['name']);
        $this->assertEquals('Alpha Brand', $responseData[2]['name']);

        // Test filtering with ordering
        $response = $this->getJson('/api/brands?name=a&order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Alpha and Charlie contain 'a'
        $this->assertEquals('Alpha Brand', $responseData[0]['name']);
        $this->assertEquals('Charlie Brand', $responseData[1]['name']);
    }

    public function test_brand_with_special_characters()
    {
        $brandData = [
            'name' => 'Brand & Co. (™)',
            'description' => 'Description with special chars: @#$%^&*()'
        ];

        $response = $this->postJson('/api/brands', $brandData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Brand & Co. (™)', $responseData['name']);
        $this->assertEquals('Description with special chars: @#$%^&*()', $responseData['description']);

        $this->assertDatabaseHas('brands', [
            'name' => 'Brand & Co. (™)',
            'description' => 'Description with special chars: @#$%^&*()'
        ]);
    }

    public function test_brand_with_unicode_characters()
    {
        $brandData = [
            'name' => 'Bränd Ñamé 中文',
            'description' => 'Descripción con caracteres especiales: ñáéíóú'
        ];

        $response = $this->postJson('/api/brands', $brandData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('Bränd Ñamé 中文', $responseData['name']);
        $this->assertEquals('Descripción con caracteres especiales: ñáéíóú', $responseData['description']);

        $this->assertDatabaseHas('brands', [
            'name' => 'Bränd Ñamé 中文',
            'description' => 'Descripción con caracteres especiales: ñáéíóú'
        ]);
    }

    public function test_brand_with_empty_description()
    {
        $brandData = [
            'name' => 'No Description Brand',
            'description' => ''
        ];

        $response = $this->postJson('/api/brands', $brandData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals('No Description Brand', $responseData['name']);
        $this->assertEquals('', $responseData['description']);
    }

    public function test_brand_with_long_name_and_description()
    {
        $longName = str_repeat('A', 255);
        $longDescription = str_repeat('B', 1000);

        $brandData = [
            'name' => $longName,
            'description' => $longDescription
        ];

        $response = $this->postJson('/api/brands', $brandData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals($longName, $responseData['name']);
        $this->assertEquals($longDescription, $responseData['description']);
    }

    public function test_brand_data_consistency_across_operations()
    {
        $brand = Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Consistency Brand',
            'description' => 'Original description'
        ]);

        // Get brand and verify data
        $getResponse = $this->getJson("/api/brands/{$brand->id}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals('Consistency Brand', $getData['name']);
        $this->assertEquals('Original description', $getData['description']);

        // Update brand
        $updateData = [
            'name' => 'Updated Brand',
            'description' => 'Updated description'
        ];

        $updateResponse = $this->putJson("/api/brands/{$brand->id}", $updateData);
        $updateResponse->assertStatus(200);

        // Verify update
        $getUpdatedResponse = $this->getJson("/api/brands/{$brand->id}");
        $getUpdatedData = $getUpdatedResponse->json('data');

        $this->assertEquals('Updated Brand', $getUpdatedData['name']);
        $this->assertEquals('Updated description', $getUpdatedData['description']);

        // Verify data consistency in list view
        $listResponse = $this->getJson('/api/brands');
        $listData = $listResponse->json('data.data');

        $brandInList = collect($listData)->firstWhere('id', $brand->id);
        $this->assertNotNull($brandInList);
        $this->assertEquals($getUpdatedData['name'], $brandInList['name']);
        $this->assertEquals($getUpdatedData['description'], $brandInList['description']);
    }

    public function test_brand_organization_isolation()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $user1 = User::factory()->create(['organization_id' => $org1->id]);
        $user2 = User::factory()->create(['organization_id' => $org2->id]);

        $brand1 = Brand::factory()->create(['organization_id' => $org1->id]);
        $brand2 = Brand::factory()->create(['organization_id' => $org2->id]);

        // User 1 should only see brand 1
        Sanctum::actingAs($user1);
        $response = $this->getJson('/api/brands');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($brand1->id, $responseData[0]['id']);

        // User 2 should only see brand 2
        Sanctum::actingAs($user2);
        $response = $this->getJson('/api/brands');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($brand2->id, $responseData[0]['id']);
    }

    public function test_brand_complex_filtering_combinations()
    {
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Complex Brand',
            'description' => 'Alpha complex description'
        ]);
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Simple Brand',
            'description' => 'Beta simple description'
        ]);

        // Complex filter: name + description
        $response = $this->getJson('/api/brands?name=Alpha&description=complex');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Alpha Complex Brand', $responseData[0]['name']);
    }

    public function test_brand_case_insensitive_filtering()
    {
        Brand::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'CaseSensitive Brand',
            'description' => 'CaseSensitive Description'
        ]);

        // Test case insensitive name filtering
        $response = $this->getJson('/api/brands?name=casesensitive');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('CaseSensitive Brand', $responseData[0]['name']);

        // Test case insensitive description filtering
        $response = $this->getJson('/api/brands?description=casesensitive');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('CaseSensitive Description', $responseData[0]['description']);
    }
}
