<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Batch;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class BatchTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_batch()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);

        $batchData = [
            'shop_id' => $shop->id,
            'product_id' => $product->id,
            'batch_number' => 'BATCH-001',
            'name' => 'Test Batch',
            'description' => 'Test batch description',
            'quantity' => 100,
            'produced_at' => '2024-01-01',
            'expired_at' => '2024-12-31',
            'is_processed_at_stock' => false
        ];

        $response = $this->postJson('/api/batches', $batchData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'shop_id',
                        'product_id',
                        'batch_number',
                        'name',
                        'description',
                        'quantity',
                        'produced_at',
                        'expired_at',
                        'processed_at',
                        'is_processed_at_stock',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals('BATCH-001', $responseData['batch_number']);
        $this->assertEquals(100, $responseData['quantity']);

        $this->assertDatabaseHas('batches', [
            'batch_number' => 'BATCH-001',
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);
    }

    public function test_can_get_batch_by_id()
    {
        $batch = Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'GET-001',
            'quantity' => 150
        ]);

        $response = $this->getJson("/api/batches/{$batch->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'shop_id',
                        'product_id',
                        'batch_number',
                        'name',
                        'description',
                        'quantity',
                        'produced_at',
                        'expired_at',
                        'processed_at',
                        'is_processed_at_stock',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($batch->id, $responseData['id']);
        $this->assertEquals('GET-001', $responseData['batch_number']);
        $this->assertEquals(150, $responseData['quantity']);
    }

    public function test_cannot_get_batch_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $batch = Batch::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/batches/{$batch->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_batch()
    {
        $batch = Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'OLD-001',
            'quantity' => 50
        ]);

        $updateData = [
            'batch_number' => 'NEW-001',
            'name' => 'Updated Batch',
            'description' => 'Updated description',
            'quantity' => 200,
            'is_processed_at_stock' => true
        ];

        $response = $this->putJson("/api/batches/{$batch->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('batches', [
            'id' => $batch->id,
            'batch_number' => 'NEW-001',
            'quantity' => 200,
            'is_processed_at_stock' => true,
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('NEW-001', $responseData['batch_number']);
        $this->assertEquals(200, $responseData['quantity']);
        $this->assertTrue($responseData['is_processed_at_stock']);
    }

    public function test_cannot_update_batch_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $batch = Batch::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'batch_number' => 'UNAUTHORIZED-001'
        ];

        $response = $this->putJson("/api/batches/{$batch->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This batch don't belong to this organization."
                ]);
    }

    public function test_can_delete_batch()
    {
        $batch = Batch::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/batches/{$batch->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('batches', [
            'id' => $batch->id
        ]);
    }

    public function test_cannot_delete_batch_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $batch = Batch::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/batches/{$batch->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This batch don't belong to this organization."
                ]);
    }

    public function test_can_get_all_batches_with_pagination()
    {
        Batch::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/batches?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_batches_by_product()
    {
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product1->id,
            'batch_number' => 'PRODUCT1-001'
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product2->id,
            'batch_number' => 'PRODUCT2-001'
        ]);

        $response = $this->getJson("/api/batches?product_id={$product1->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals($product1->id, $responseData['data'][0]['product_id']);
        $this->assertEquals('PRODUCT1-001', $responseData['data'][0]['batch_number']);
    }

    public function test_can_filter_batches_by_shop()
    {
        $shop1 = Shop::factory()->create(['organization_id' => $this->organization->id]);
        $shop2 = Shop::factory()->create(['organization_id' => $this->organization->id]);

        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop1->id,
            'batch_number' => 'SHOP1-001'
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop2->id,
            'batch_number' => 'SHOP2-001'
        ]);

        $response = $this->getJson("/api/batches?shop_id={$shop1->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals($shop1->id, $responseData['data'][0]['shop_id']);
        $this->assertEquals('SHOP1-001', $responseData['data'][0]['batch_number']);
    }

    public function test_can_get_batches_with_product_relationship()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $batch = Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product->id
        ]);

        $response = $this->getJson('/api/batches?with_product=true');

        $response->assertStatus(200);

        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertNotNull($responseData[0]['product']);
        $this->assertEquals($product->name, $responseData[0]['product']['name']);
    }

    public function test_can_get_batches_with_shop_relationship()
    {
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);
        $batch = Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop->id
        ]);

        $response = $this->getJson('/api/batches?with_shop=true');

        $response->assertStatus(200);

        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertNotNull($responseData[0]['shop']);
        $this->assertEquals($shop->name, $responseData[0]['shop']['name']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/batches', [
            'batch_number' => '', // Empty batch number should fail validation
            'quantity' => -1 // Negative quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['batch_number', 'quantity']);
    }

    public function test_validation_errors_on_update()
    {
        $batch = Batch::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->putJson("/api/batches/{$batch->id}", [
            'quantity' => -1 // Negative quantity should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['quantity']);
    }

    public function test_batch_soft_delete_behavior()
    {
        $batch = Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'TO-DELETE-001'
        ]);

        // Delete the batch
        $response = $this->deleteJson("/api/batches/{$batch->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/batches');
        $responseData = $response->json('data.data');
        $batchNumbers = collect($responseData)->pluck('batch_number')->toArray();
        $this->assertNotContains('TO-DELETE-001', $batchNumbers);

        // Should not be accessible by ID
        $response = $this->getJson("/api/batches/{$batch->id}");
        $response->assertStatus(404);
    }

    public function test_batch_ordering_and_filtering_combinations()
    {
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'ALPHA-001',
            'quantity' => 100,
            'created_at' => now()->subDays(3)
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'BETA-001',
            'quantity' => 200,
            'created_at' => now()->subDays(1)
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'CHARLIE-001',
            'quantity' => 300,
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by batch_number ascending
        $response = $this->getJson('/api/batches?order=batch_number&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals('ALPHA-001', $responseData[0]['batch_number']);
        $this->assertEquals('BETA-001', $responseData[1]['batch_number']);
        $this->assertEquals('CHARLIE-001', $responseData[2]['batch_number']);

        // Test ordering by quantity descending
        $response = $this->getJson('/api/batches?order=quantity&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals('CHARLIE-001', $responseData[0]['batch_number']); // Highest quantity
    }

    public function test_batch_quantity_range_filtering()
    {
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'LOW-001',
            'quantity' => 50
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'MEDIUM-001',
            'quantity' => 150
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'HIGH-001',
            'quantity' => 300
        ]);

        // Test minimum quantity filter
        $response = $this->getJson('/api/batches?quantity_min=100');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // MEDIUM and HIGH

        // Test maximum quantity filter
        $response = $this->getJson('/api/batches?quantity_max=200');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // LOW and MEDIUM

        // Test quantity range filter
        $response = $this->getJson('/api/batches?quantity_min=100&quantity_max=200');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData)); // Only MEDIUM
        $this->assertEquals('MEDIUM-001', $responseData[0]['batch_number']);
    }

    public function test_batch_processing_status_filtering()
    {
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'PROCESSED-001',
            'is_processed_at_stock' => true,
            'processed_at' => now()
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'UNPROCESSED-001',
            'is_processed_at_stock' => false,
            'processed_at' => null
        ]);

        // Test processed filter
        $response = $this->getJson('/api/batches?is_processed_at_stock=true');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('PROCESSED-001', $responseData[0]['batch_number']);
        $this->assertTrue($responseData[0]['is_processed_at_stock']);

        // Test unprocessed filter
        $response = $this->getJson('/api/batches?is_processed_at_stock=false');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('UNPROCESSED-001', $responseData[0]['batch_number']);
        $this->assertFalse($responseData[0]['is_processed_at_stock']);
    }

    public function test_batch_name_filtering()
    {
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Batch'
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Batch'
        ]);

        $response = $this->getJson('/api/batches?name=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha Batch', $responseData['data'][0]['name']);
    }

    public function test_batch_number_filtering()
    {
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'SEARCH-001'
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'OTHER-001'
        ]);

        $response = $this->getJson('/api/batches?batch_number=SEARCH');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('SEARCH-001', $responseData['data'][0]['batch_number']);
    }

    public function test_batch_complex_filtering_combinations()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);

        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product->id,
            'shop_id' => $shop->id,
            'batch_number' => 'COMPLEX-001',
            'name' => 'Alpha Complex',
            'quantity' => 150,
            'is_processed_at_stock' => false
        ]);
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product->id,
            'shop_id' => $shop->id,
            'batch_number' => 'COMPLEX-002',
            'name' => 'Beta Complex',
            'quantity' => 50,
            'is_processed_at_stock' => true
        ]);

        // Complex filter: product + shop + name + quantity range + processing status
        $response = $this->getJson("/api/batches?product_id={$product->id}&shop_id={$shop->id}&name=Alpha&quantity_min=100&is_processed_at_stock=false");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('COMPLEX-001', $responseData[0]['batch_number']);
    }

    public function test_batch_with_null_shop()
    {
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => null,
            'batch_number' => 'NO-SHOP-001'
        ]);

        $response = $this->getJson('/api/batches');
        $responseData = $response->json('data.data');

        $this->assertEquals(1, count($responseData));
        $this->assertNull($responseData[0]['shop_id']);
        $this->assertEquals('NO-SHOP-001', $responseData[0]['batch_number']);
    }

    public function test_batch_with_zero_quantity()
    {
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 0,
            'batch_number' => 'ZERO-001'
        ]);

        $response = $this->getJson('/api/batches');
        $responseData = $response->json('data.data');

        $this->assertEquals(1, count($responseData));
        $this->assertEquals(0, $responseData[0]['quantity']);
        $this->assertEquals('ZERO-001', $responseData[0]['batch_number']);
    }

    public function test_batch_with_high_quantity()
    {
        Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 999999,
            'batch_number' => 'HIGH-001'
        ]);

        $response = $this->getJson('/api/batches');
        $responseData = $response->json('data.data');

        $this->assertEquals(1, count($responseData));
        $this->assertEquals(999999, $responseData[0]['quantity']);
        $this->assertEquals('HIGH-001', $responseData[0]['batch_number']);
    }

    public function test_batch_data_consistency_across_operations()
    {
        $batch = Batch::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'CONSISTENCY-001',
            'quantity' => 100,
            'name' => 'Consistency Batch',
            'description' => 'Original description'
        ]);

        // Get batch and verify data
        $getResponse = $this->getJson("/api/batches/{$batch->id}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals('CONSISTENCY-001', $getData['batch_number']);
        $this->assertEquals(100, $getData['quantity']);
        $this->assertEquals('Consistency Batch', $getData['name']);
        $this->assertEquals('Original description', $getData['description']);

        // Update batch
        $updateData = [
            'batch_number' => 'UPDATED-001',
            'quantity' => 200,
            'name' => 'Updated Batch',
            'description' => 'Updated description'
        ];

        $updateResponse = $this->putJson("/api/batches/{$batch->id}", $updateData);
        $updateResponse->assertStatus(200);

        // Verify update
        $getUpdatedResponse = $this->getJson("/api/batches/{$batch->id}");
        $getUpdatedData = $getUpdatedResponse->json('data');

        $this->assertEquals('UPDATED-001', $getUpdatedData['batch_number']);
        $this->assertEquals(200, $getUpdatedData['quantity']);
        $this->assertEquals('Updated Batch', $getUpdatedData['name']);
        $this->assertEquals('Updated description', $getUpdatedData['description']);
    }

    public function test_batch_organization_isolation()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $user1 = User::factory()->create(['organization_id' => $org1->id]);
        $user2 = User::factory()->create(['organization_id' => $org2->id]);

        $batch1 = Batch::factory()->create(['organization_id' => $org1->id]);
        $batch2 = Batch::factory()->create(['organization_id' => $org2->id]);

        // User 1 should only see batch 1
        Sanctum::actingAs($user1);
        $response = $this->getJson('/api/batches');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($batch1->id, $responseData[0]['id']);

        // User 2 should only see batch 2
        Sanctum::actingAs($user2);
        $response = $this->getJson('/api/batches');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($batch2->id, $responseData[0]['id']);
    }
}
