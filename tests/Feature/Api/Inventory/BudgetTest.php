<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Budget;
use App\Models\BudgetCustomProduct;
use App\Models\BudgetProduct;
use App\Models\Client;
use App\Models\CustomProduct;
use App\Models\Organization;
use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class BudgetTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_get_budget_by_id()
    {
        $budget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 5000.00,
            'cost' => 3000.00
        ]);

        $response = $this->getJson("/api/budgets/{$budget->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'client_id',
                        'value',
                        'cost',
                        'name',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($budget->id, $responseData['id']);
        $this->assertEquals(5000.00, $responseData['value']);
        $this->assertEquals(3000.00, $responseData['cost']);
    }

    public function test_cannot_get_budget_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $budget = Budget::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/budgets/{$budget->id}");

        $response->assertStatus(404);
    }

    public function test_can_delete_budget()
    {
        $budget = Budget::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/budgets/{$budget->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('budgets', [
            'id' => $budget->id
        ]);
    }

    public function test_cannot_delete_budget_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $budget = Budget::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/budgets/{$budget->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This budget don't belong to this organization."
                ]);
    }

    public function test_can_get_all_budgets_with_pagination()
    {
        Budget::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/budgets?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_budgets_by_client()
    {
        $client1 = Client::factory()->create(['organization_id' => $this->organization->id]);
        $client2 = Client::factory()->create(['organization_id' => $this->organization->id]);

        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client1->id,
            'name' => 'Client 1 Budget'
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client2->id,
            'name' => 'Client 2 Budget'
        ]);

        $response = $this->getJson("/api/budgets?client_id={$client1->id}");

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals($client1->id, $responseData['data'][0]['client_id']);
        $this->assertEquals('Client 1 Budget', $responseData['data'][0]['name']);
    }

    public function test_can_filter_budgets_by_name()
    {
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Budget'
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Budget'
        ]);

        $response = $this->getJson('/api/budgets?name=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha Budget', $responseData['data'][0]['name']);
    }

    public function test_can_get_budgets_with_client_relationship()
    {
        $client = Client::factory()->create(['organization_id' => $this->organization->id]);
        $budget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client->id
        ]);

        $response = $this->getJson('/api/budgets?with_client=true');

        $response->assertStatus(200);

        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertNotNull($responseData[0]['client']);
        $this->assertEquals($client->name, $responseData[0]['client']['name']);
    }

    public function test_budget_soft_delete_behavior()
    {
        $budget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'To Be Deleted'
        ]);

        // Delete the budget
        $response = $this->deleteJson("/api/budgets/{$budget->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/budgets');
        $responseData = $response->json('data.data');
        $budgetNames = collect($responseData)->pluck('name')->toArray();
        $this->assertNotContains('To Be Deleted', $budgetNames);

        // Should not be accessible by ID
        $response = $this->getJson("/api/budgets/{$budget->id}");
        $response->assertStatus(404);
    }

    public function test_budget_ordering_and_filtering_combinations()
    {
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Budget',
            'value' => 1000.00,
            'cost' => 600.00,
            'created_at' => now()->subDays(3)
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Budget',
            'value' => 2000.00,
            'cost' => 1200.00,
            'created_at' => now()->subDays(1)
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Charlie Budget',
            'value' => 3000.00,
            'cost' => 1800.00,
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by name ascending
        $response = $this->getJson('/api/budgets?order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Alpha Budget', $responseData[0]['name']);
        $this->assertEquals('Beta Budget', $responseData[1]['name']);
        $this->assertEquals('Charlie Budget', $responseData[2]['name']);

        // Test ordering by value descending
        $response = $this->getJson('/api/budgets?order=value&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Charlie Budget', $responseData[0]['name']); // Highest value

        // Test filtering with ordering
        $response = $this->getJson('/api/budgets?name=a&order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Alpha and Charlie contain 'a'
        $this->assertEquals('Alpha Budget', $responseData[0]['name']);
        $this->assertEquals('Charlie Budget', $responseData[1]['name']);
    }

    public function test_budget_value_range_filtering()
    {
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Low Budget',
            'value' => 1000.00
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Medium Budget',
            'value' => 5000.00
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'High Budget',
            'value' => 10000.00
        ]);

        // Test minimum value filter
        $response = $this->getJson('/api/budgets?value_min=2500');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Medium and High

        // Test maximum value filter
        $response = $this->getJson('/api/budgets?value_max=7500');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Low and Medium

        // Test value range filter
        $response = $this->getJson('/api/budgets?value_min=2500&value_max=7500');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData)); // Only Medium
        $this->assertEquals('Medium Budget', $responseData[0]['name']);
    }

    public function test_budget_cost_range_filtering()
    {
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Low Cost Budget',
            'cost' => 500.00
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Medium Cost Budget',
            'cost' => 2500.00
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'High Cost Budget',
            'cost' => 5000.00
        ]);

        // Test minimum cost filter
        $response = $this->getJson('/api/budgets?cost_min=1250');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Medium and High

        // Test maximum cost filter
        $response = $this->getJson('/api/budgets?cost_max=3750');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Low and Medium

        // Test cost range filter
        $response = $this->getJson('/api/budgets?cost_min=1250&cost_max=3750');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData)); // Only Medium
        $this->assertEquals('Medium Cost Budget', $responseData[0]['name']);
    }

    public function test_budget_complex_filtering_combinations()
    {
        $client = Client::factory()->create(['organization_id' => $this->organization->id]);

        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client->id,
            'name' => 'Alpha High Budget',
            'value' => 8000.00,
            'cost' => 4800.00
        ]);
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client->id,
            'name' => 'Beta Low Budget',
            'value' => 2000.00,
            'cost' => 1200.00
        ]);

        // Complex filter: client + name + value range + cost range
        $response = $this->getJson("/api/budgets?client_id={$client->id}&name=Alpha&value_min=5000&cost_min=3000");
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals('Alpha High Budget', $responseData[0]['name']);
    }

    public function test_budget_with_null_client()
    {
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => null,
            'name' => 'No Client Budget'
        ]);

        $response = $this->getJson('/api/budgets');
        $responseData = $response->json('data.data');

        $this->assertEquals(1, count($responseData));
        $this->assertNull($responseData[0]['client_id']);
        $this->assertEquals('No Client Budget', $responseData[0]['name']);
    }

    public function test_budget_with_zero_values()
    {
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 0.0,
            'cost' => 0.0,
            'name' => 'Zero Budget'
        ]);

        $response = $this->getJson('/api/budgets');
        $responseData = $response->json('data.data');

        $this->assertEquals(1, count($responseData));
        $this->assertEquals(0.0, $responseData[0]['value']);
        $this->assertEquals(0.0, $responseData[0]['cost']);
        $this->assertEquals('Zero Budget', $responseData[0]['name']);
    }

    public function test_budget_with_high_values()
    {
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 999999.99,
            'cost' => 888888.88,
            'name' => 'High Value Budget'
        ]);

        $response = $this->getJson('/api/budgets');
        $responseData = $response->json('data.data');

        $this->assertEquals(1, count($responseData));
        $this->assertEquals(999999.99, $responseData[0]['value']);
        $this->assertEquals(888888.88, $responseData[0]['cost']);
        $this->assertEquals('High Value Budget', $responseData[0]['name']);
    }

    public function test_budget_profit_calculation()
    {
        Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 10000.00,
            'cost' => 6000.00,
            'name' => 'Profit Budget'
        ]);

        $response = $this->getJson('/api/budgets');
        $responseData = $response->json('data.data');

        $this->assertEquals(1, count($responseData));
        $budget = $responseData[0];

        // Calculate profit (Value - Cost)
        $expectedProfit = 4000.00;
        $actualProfit = $budget['value'] - $budget['cost'];
        $this->assertEquals($expectedProfit, $actualProfit);
    }

    public function test_budget_data_consistency_across_operations()
    {
        $budget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 5000.00,
            'cost' => 3000.00,
            'name' => 'Consistency Budget',
            'description' => 'Original description'
        ]);

        // Get budget and verify data
        $getResponse = $this->getJson("/api/budgets/{$budget->id}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals(5000.00, $getData['value']);
        $this->assertEquals(3000.00, $getData['cost']);
        $this->assertEquals('Consistency Budget', $getData['name']);
        $this->assertEquals('Original description', $getData['description']);

        // Verify data consistency in list view
        $listResponse = $this->getJson('/api/budgets');
        $listData = $listResponse->json('data.data');

        $budgetInList = collect($listData)->firstWhere('id', $budget->id);
        $this->assertNotNull($budgetInList);
        $this->assertEquals($getData['value'], $budgetInList['value']);
        $this->assertEquals($getData['cost'], $budgetInList['cost']);
        $this->assertEquals($getData['name'], $budgetInList['name']);
    }

    public function test_budget_with_products_and_custom_products()
    {
        $budget = Budget::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Add regular product to budget
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        BudgetProduct::factory()->create([
            'budget_id' => $budget->id,
            'product_id' => $product->id,
            'quantity' => 10,
            'value' => 1000.00
        ]);

        // Add custom product to budget
        $customProduct = CustomProduct::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        BudgetCustomProduct::factory()->create([
            'budget_id' => $budget->id,
            'custom_product_id' => $customProduct->id,
            'quantity' => 5,
            'value' => 500.00
        ]);

        // Verify budget exists and can be retrieved
        $response = $this->getJson("/api/budgets/{$budget->id}");
        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals($budget->id, $responseData['id']);

        // Verify products and custom products exist in database
        $this->assertDatabaseHas('budget_products', [
            'budget_id' => $budget->id,
            'product_id' => $product->id,
            'quantity' => 10,
            'value' => 1000.00
        ]);

        $this->assertDatabaseHas('budget_custom_products', [
            'budget_id' => $budget->id,
            'custom_product_id' => $customProduct->id,
            'quantity' => 5,
            'value' => 500.00
        ]);
    }

    public function test_budget_organization_isolation()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $user1 = User::factory()->create(['organization_id' => $org1->id]);
        $user2 = User::factory()->create(['organization_id' => $org2->id]);

        $budget1 = Budget::factory()->create(['organization_id' => $org1->id]);
        $budget2 = Budget::factory()->create(['organization_id' => $org2->id]);

        // User 1 should only see budget 1
        Sanctum::actingAs($user1);
        $response = $this->getJson('/api/budgets');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($budget1->id, $responseData[0]['id']);

        // User 2 should only see budget 2
        Sanctum::actingAs($user2);
        $response = $this->getJson('/api/budgets');
        $responseData = $response->json('data.data');
        $this->assertEquals(1, count($responseData));
        $this->assertEquals($budget2->id, $responseData[0]['id']);
    }
}
