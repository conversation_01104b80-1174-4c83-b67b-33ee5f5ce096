<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Budget;
use App\Models\Client;
use App\Models\CustomProduct;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Project;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ProjectTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_project()
    {
        $client = Client::factory()->create(['organization_id' => $this->organization->id]);
        $budget = Budget::factory()->create(['organization_id' => $this->organization->id]);

        $projectData = [
            'client_id' => $client->id,
            'budget_id' => $budget->id,
            'name' => 'Test Project',
            'description' => 'Test project description',
            'value' => 10000.50,
            'cost' => 7500.25
        ];

        $response = $this->postJson('/api/projects', $projectData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'client_id',
                        'budget_id',
                        'name',
                        'description',
                        'value',
                        'cost',
                        'created_at',
                        'updated_at',
                        'client',
                        'budget',
                        'products',
                        'customProducts'
                    ]
                ]);

        $this->assertDatabaseHas('projects', [
            'name' => 'Test Project',
            'organization_id' => $this->organization->id,
            'client_id' => $client->id,
            'budget_id' => $budget->id,
            'value' => 10000.50,
            'cost' => 7500.25
        ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals('Test Project', $responseData['name']);
        $this->assertEquals($client->id, $responseData['client_id']);
        $this->assertEquals($budget->id, $responseData['budget_id']);
    }

    public function test_can_create_minimal_project()
    {
        $projectData = [
            'name' => 'Minimal Project'
        ];

        $response = $this->postJson('/api/projects', $projectData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('projects', [
            'name' => 'Minimal Project',
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Minimal Project', $responseData['name']);
        $this->assertNull($responseData['client_id']);
        $this->assertNull($responseData['budget_id']);
        $this->assertNull($responseData['value']);
        $this->assertNull($responseData['cost']);
    }

    public function test_can_get_project_by_id()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Test Project'
        ]);

        $response = $this->getJson("/api/projects/{$project->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'description',
                        'value',
                        'cost',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($project->id, $responseData['id']);
        $this->assertEquals('Test Project', $responseData['name']);
    }

    public function test_cannot_get_project_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $project = Project::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/projects/{$project->id}");

        $response->assertStatus(404);
    }

    public function test_can_update_project()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'value' => 15000.75,
            'cost' => 12000.50
        ];

        $response = $this->putJson("/api/projects/{$project->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'value' => 15000.75,
            'cost' => 12000.50,
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Updated Name', $responseData['name']);
        $this->assertEquals('Updated description', $responseData['description']);
    }

    public function test_cannot_update_project_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $project = Project::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'name' => 'Updated Name'
        ];

        $response = $this->putJson("/api/projects/{$project->id}", $updateData);

        $response->assertStatus(404);
    }

    public function test_can_delete_project()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/projects/{$project->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('projects', [
            'id' => $project->id
        ]);
    }

    public function test_cannot_delete_project_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $project = Project::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/projects/{$project->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This project don't belong to this organization."
                ]);
    }

    public function test_can_get_all_projects_with_pagination()
    {
        Project::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/projects?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data',
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals(5, $responseData['count']);
        $this->assertEquals(15, $responseData['total']);
        $this->assertEquals(3, $responseData['lastPage']);
    }

    public function test_can_filter_projects_by_name()
    {
        Project::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Project'
        ]);
        Project::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Project'
        ]);

        $response = $this->getJson('/api/projects?name=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals(1, $responseData['count']);
        $this->assertEquals('Alpha Project', $responseData['data'][0]['name']);
    }

    public function test_can_create_project_from_budget()
    {
        $budget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Source Budget'
        ]);

        $requestData = [
            'name' => 'Custom Project Name',
            'description' => 'Custom project description'
        ];

        $response = $this->postJson("/api/budgets/{$budget->id}/projects", $requestData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('projects', [
            'name' => 'Custom Project Name',
            'description' => 'Custom project description',
            'budget_id' => $budget->id,
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Custom Project Name', $responseData['name']);
        $this->assertEquals($budget->id, $responseData['budget_id']);
    }

    public function test_can_create_project_from_budget_with_default_values()
    {
        $budget = Budget::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Source Budget',
            'description' => 'Source budget description'
        ]);

        $response = $this->postJson("/api/budgets/{$budget->id}/projects", []);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertEquals("Project from budget #{$budget->id}", $responseData['name']);
        $this->assertEquals('Source budget description', $responseData['description']);
        $this->assertEquals($budget->id, $responseData['budget_id']);
    }

    public function test_cannot_create_project_from_budget_of_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $budget = Budget::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->postJson("/api/budgets/{$budget->id}/projects", []);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This budget don't belong to this organization."
                ]);
    }

    public function test_can_attach_products_to_project()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        $attachData = [
            'products' => [
                $product1->id => ['quantity' => 5, 'value' => 100.00, 'description' => 'Product 1'],
                $product2->id => ['quantity' => 3, 'value' => 200.00, 'description' => 'Product 2']
            ],
            'custom_products' => [
                ['name' => 'Custom Product 1', 'description' => 'Description 1', 'quantity' => 2, 'value' => 150.00],
                ['name' => 'Custom Product 2', 'description' => 'Description 2', 'quantity' => 1, 'value' => 300.00]
            ]
        ];

        $response = $this->postJson("/api/projects/{$project->id}/attach-products", $attachData);

        $response->assertStatus(200);

        // Verify products were attached
        $this->assertDatabaseHas('projects_products', [
            'project_id' => $project->id,
            'product_id' => $product1->id,
            'quantity' => 5,
            'value' => 100.00
        ]);
        $this->assertDatabaseHas('projects_products', [
            'project_id' => $project->id,
            'product_id' => $product2->id,
            'quantity' => 3,
            'value' => 200.00
        ]);

        // Verify custom products were attached
        $this->assertDatabaseHas('custom_products', [
            'project_id' => $project->id,
            'name' => 'Custom Product 1',
            'quantity' => 2,
            'value' => 150.00
        ]);
        $this->assertDatabaseHas('custom_products', [
            'project_id' => $project->id,
            'name' => 'Custom Product 2',
            'quantity' => 1,
            'value' => 300.00
        ]);
    }

    public function test_cannot_attach_products_to_project_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $project = Project::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $attachData = [
            'products' => [],
            'custom_products' => []
        ];

        $response = $this->postJson("/api/projects/{$project->id}/attach-products", $attachData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This project don't belong to this organization."
                ]);
    }

    public function test_can_get_projects_with_relationships()
    {
        $client = Client::factory()->create(['organization_id' => $this->organization->id]);
        $budget = Budget::factory()->create(['organization_id' => $this->organization->id]);
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $client->id,
            'budget_id' => $budget->id
        ]);

        $response = $this->getJson('/api/projects?with_client=true&with_budget=true');

        $response->assertStatus(200);

        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertNotNull($responseData[0]['client']);
        $this->assertNotNull($responseData[0]['budget']);
        $this->assertEquals($client->name, $responseData[0]['client']['name']);
        $this->assertEquals($budget->name, $responseData[0]['budget']['name']);
    }

    public function test_validation_errors_on_create()
    {
        $response = $this->postJson('/api/projects', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_validation_errors_on_update()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->putJson("/api/projects/{$project->id}", [
            'name' => '' // Empty name should fail validation
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_project_creation_with_zero_values()
    {
        $projectData = [
            'name' => 'Zero Value Project',
            'description' => 'Project with zero values',
            'value' => 0.0,
            'cost' => 0.0
        ];

        $response = $this->postJson('/api/projects', $projectData);

        $response->assertStatus(201);

        $this->assertDatabaseHas('projects', [
            'name' => 'Zero Value Project',
            'value' => 0.0,
            'cost' => 0.0
        ]);

        $responseData = $response->json('data');
        $this->assertEquals(0.0, $responseData['value']);
        $this->assertEquals(0.0, $responseData['cost']);
    }

    public function test_project_soft_delete_behavior()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'To Be Deleted'
        ]);

        // Delete the project
        $response = $this->deleteJson("/api/projects/{$project->id}");
        $response->assertStatus(200);

        // Should not appear in normal queries
        $response = $this->getJson('/api/projects');
        $responseData = $response->json('data.data');
        $projectNames = collect($responseData)->pluck('name')->toArray();
        $this->assertNotContains('To Be Deleted', $projectNames);

        // Should not be accessible by ID
        $response = $this->getJson("/api/projects/{$project->id}");
        $response->assertStatus(404);
    }

    public function test_project_ordering_and_filtering_combinations()
    {
        Project::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Project',
            'value' => 1000.00,
            'created_at' => now()->subDays(3)
        ]);
        Project::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Project',
            'value' => 2000.00,
            'created_at' => now()->subDays(1)
        ]);
        Project::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Charlie Project',
            'value' => 3000.00,
            'created_at' => now()->subDays(2)
        ]);

        // Test ordering by name ascending
        $response = $this->getJson('/api/projects?order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Alpha Project', $responseData[0]['name']);
        $this->assertEquals('Beta Project', $responseData[1]['name']);
        $this->assertEquals('Charlie Project', $responseData[2]['name']);

        // Test ordering by value descending
        $response = $this->getJson('/api/projects?order=value&by=desc');
        $responseData = $response->json('data.data');
        $this->assertEquals('Charlie Project', $responseData[0]['name']); // Highest value

        // Test filtering with ordering
        $response = $this->getJson('/api/projects?name=a&order=name&by=asc');
        $responseData = $response->json('data.data');
        $this->assertEquals(2, count($responseData)); // Alpha and Charlie contain 'a'
        $this->assertEquals('Alpha Project', $responseData[0]['name']);
        $this->assertEquals('Charlie Project', $responseData[1]['name']);
    }

    public function test_project_data_consistency_across_operations()
    {
        // Create project
        $projectData = [
            'name' => 'Consistency Test',
            'description' => 'Test project consistency',
            'value' => 50000.00,
            'cost' => 37500.00
        ];

        $createResponse = $this->postJson('/api/projects', $projectData);
        $createResponse->assertStatus(201);
        $projectId = $createResponse->json('data.id');

        // Get project and verify data
        $getResponse = $this->getJson("/api/projects/{$projectId}");
        $getResponse->assertStatus(200);
        $getData = $getResponse->json('data');

        $this->assertEquals($projectData['name'], $getData['name']);
        $this->assertEquals($projectData['description'], $getData['description']);
        $this->assertEquals($projectData['value'], $getData['value']);
        $this->assertEquals($projectData['cost'], $getData['cost']);

        // Update project
        $updateData = [
            'name' => 'Updated Consistency Test',
            'description' => 'Updated description',
            'value' => 60000.00
        ];

        $updateResponse = $this->putJson("/api/projects/{$projectId}", $updateData);
        $updateResponse->assertStatus(200);

        // Verify update
        $getUpdatedResponse = $this->getJson("/api/projects/{$projectId}");
        $getUpdatedData = $getUpdatedResponse->json('data');

        $this->assertEquals($updateData['name'], $getUpdatedData['name']);
        $this->assertEquals($updateData['description'], $getUpdatedData['description']);
        $this->assertEquals($updateData['value'], $getUpdatedData['value']);
        // Cost should remain unchanged
        $this->assertEquals($projectData['cost'], $getUpdatedData['cost']);
    }

    public function test_attach_products_clears_existing_products()
    {
        $project = Project::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        // First attach some products
        $project->products()->attach($product1->id, [
            'quantity' => 10,
            'value' => 50.00,
            'description' => 'Initial product'
        ]);
        CustomProduct::factory()->create([
            'project_id' => $project->id,
            'name' => 'Initial Custom Product'
        ]);

        // Now attach different products
        $attachData = [
            'products' => [
                $product2->id => ['quantity' => 5, 'value' => 100.00, 'description' => 'New product']
            ],
            'custom_products' => [
                ['name' => 'New Custom Product', 'description' => 'New description', 'quantity' => 1, 'value' => 200.00]
            ]
        ];

        $response = $this->postJson("/api/projects/{$project->id}/attach-products", $attachData);
        $response->assertStatus(200);

        // Old products should be cleared
        $this->assertDatabaseMissing('projects_products', [
            'project_id' => $project->id,
            'product_id' => $product1->id
        ]);
        $this->assertDatabaseMissing('custom_products', [
            'project_id' => $project->id,
            'name' => 'Initial Custom Product'
        ]);

        // New products should be attached
        $this->assertDatabaseHas('projects_products', [
            'project_id' => $project->id,
            'product_id' => $product2->id,
            'quantity' => 5,
            'value' => 100.00
        ]);
        $this->assertDatabaseHas('custom_products', [
            'project_id' => $project->id,
            'name' => 'New Custom Product',
            'quantity' => 1,
            'value' => 200.00
        ]);
    }
}
