<?php

namespace Tests\Feature\Api\Inventory;

use App\Models\Group;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class GroupTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_group()
    {
        $groupData = [
            'name' => 'Test Group',
            'description' => 'Test group description'
        ];

        $response = $this->postJson('/api/groups', $groupData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->organization->id, $responseData['organization_id']);
        $this->assertEquals('Test Group', $responseData['name']);
        $this->assertEquals('Test group description', $responseData['description']);

        $this->assertDatabaseHas('groups', [
            'name' => 'Test Group',
            'organization_id' => $this->organization->id,
            'description' => 'Test group description'
        ]);
    }

    public function test_can_create_group_without_description()
    {
        $groupData = [
            'name' => 'Group Without Description'
        ];

        $response = $this->postJson('/api/groups', $groupData);

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals('Group Without Description', $responseData['name']);
        $this->assertNull($responseData['description']);

        $this->assertDatabaseHas('groups', [
            'name' => 'Group Without Description',
            'description' => null
        ]);
    }

    public function test_cannot_create_group_without_name()
    {
        $groupData = [
            'description' => 'Group without name'
        ];

        $response = $this->postJson('/api/groups', $groupData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_can_get_group_by_id()
    {
        $group = Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Get Group',
            'description' => 'Get group description'
        ]);

        $response = $this->getJson("/api/groups/{$group->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertEquals($group->id, $responseData['id']);
        $this->assertEquals('Get Group', $responseData['name']);
        $this->assertEquals('Get group description', $responseData['description']);
    }

    public function test_cannot_get_group_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $group = Group::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson("/api/groups/{$group->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This group don't belong to this organization."
                ]);
    }

    public function test_can_update_group()
    {
        $group = Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Group',
            'description' => 'Old description'
        ]);

        $updateData = [
            'name' => 'Updated Group',
            'description' => 'Updated description'
        ];

        $response = $this->putJson("/api/groups/{$group->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('groups', [
            'id' => $group->id,
            'name' => 'Updated Group',
            'description' => 'Updated description',
            'organization_id' => $this->organization->id
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Updated Group', $responseData['name']);
        $this->assertEquals('Updated description', $responseData['description']);
    }

    public function test_can_update_group_partially()
    {
        $group = Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name',
            'description' => 'Original description'
        ]);

        $updateData = [
            'name' => 'Partially Updated'
        ];

        $response = $this->putJson("/api/groups/{$group->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('groups', [
            'id' => $group->id,
            'name' => 'Partially Updated',
            'description' => 'Original description' // Should remain unchanged
        ]);
    }

    public function test_can_update_group_description_to_null()
    {
        $group = Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Group With Description',
            'description' => 'Original description'
        ]);

        $updateData = [
            'name' => 'Group Without Description',
            'description' => null
        ];

        $response = $this->putJson("/api/groups/{$group->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('groups', [
            'id' => $group->id,
            'name' => 'Group Without Description',
            'description' => null
        ]);
    }

    public function test_cannot_update_group_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $group = Group::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $updateData = [
            'name' => 'Unauthorized Update'
        ];

        $response = $this->putJson("/api/groups/{$group->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error'
                ]);
    }

    public function test_cannot_update_group_without_name()
    {
        $group = Group::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $updateData = [
            'name' => ''
        ];

        $response = $this->putJson("/api/groups/{$group->id}", $updateData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    public function test_can_delete_group()
    {
        $group = Group::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/groups/{$group->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('groups', [
            'id' => $group->id
        ]);
    }

    public function test_cannot_delete_group_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $group = Group::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->deleteJson("/api/groups/{$group->id}");

        $response->assertStatus(403)
                ->assertJson([
                    'status' => 'error',
                    'message' => "This group don't belong to this organization."
                ]);
    }

    public function test_can_get_all_groups_with_pagination()
    {
        Group::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/groups?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'organization_id',
                            'name',
                            'description',
                            'created_at',
                            'updated_at'
                        ]
                    ],
                    'meta' => [
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertCount(5, $responseData);

        $meta = $response->json('meta');
        $this->assertEquals(5, $meta['count']);
        $this->assertEquals(15, $meta['total']);
    }

    public function test_can_filter_groups_by_name()
    {
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Group'
        ]);
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Group'
        ]);

        $response = $this->getJson('/api/groups?name=Alpha');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Alpha Group', $responseData[0]['name']);
    }

    public function test_can_filter_groups_by_description()
    {
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Group 1',
            'description' => 'Special description'
        ]);
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Group 2',
            'description' => 'Normal description'
        ]);

        $response = $this->getJson('/api/groups?description=Special');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Group 1', $responseData[0]['name']);
        $this->assertStringContainsString('Special', $responseData[0]['description']);
    }

    public function test_groups_are_organization_scoped()
    {
        $otherOrganization = Organization::factory()->create();

        // Create groups for current organization
        Group::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);

        // Create groups for other organization
        Group::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $response = $this->getJson('/api/groups');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);

        // Verify all returned groups belong to the current organization
        foreach ($responseData as $group) {
            $this->assertEquals($this->organization->id, $group['organization_id']);
        }
    }

    public function test_handles_nonexistent_group_gracefully()
    {
        $response = $this->getJson('/api/groups/999');

        $response->assertStatus(404);
    }

    public function test_handles_invalid_group_id_gracefully()
    {
        $response = $this->getJson('/api/groups/invalid');

        $response->assertStatus(404);
    }

    public function test_api_returns_proper_error_format()
    {
        $response = $this->postJson('/api/groups', []);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'code',
                    'data',
                    'errors' => [
                        'name'
                    ]
                ]);
    }

    public function test_api_returns_proper_success_format()
    {
        $groupData = [
            'name' => 'Success Format Group',
            'description' => 'Success format description'
        ];

        $response = $this->postJson('/api/groups', $groupData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Group created successfully'
                ]);
    }

    public function test_update_returns_proper_format()
    {
        $group = Group::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $updateData = [
            'name' => 'Updated Format Group'
        ];

        $response = $this->putJson("/api/groups/{$group->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Group updated successfully'
                ]);
    }

    public function test_delete_returns_proper_format()
    {
        $group = Group::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/groups/{$group->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data'
                ])
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Group deleted successfully'
                ]);
    }

    public function test_can_create_group_with_long_description()
    {
        $longDescription = str_repeat('This is a very long description. ', 20);
        $groupData = [
            'name' => 'Group With Long Description',
            'description' => $longDescription
        ];

        $response = $this->postJson('/api/groups', $groupData);

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals($longDescription, $responseData['description']);

        $this->assertDatabaseHas('groups', [
            'name' => 'Group With Long Description',
            'description' => $longDescription
        ]);
    }

    public function test_can_create_group_with_special_characters()
    {
        $specialName = 'Group with Special Characters: @#$%^&*()';
        $specialDescription = 'Description with special chars: <>&"\'';

        $groupData = [
            'name' => $specialName,
            'description' => $specialDescription
        ];

        $response = $this->postJson('/api/groups', $groupData);

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals($specialName, $responseData['name']);
        $this->assertEquals($specialDescription, $responseData['description']);
    }

    public function test_can_create_group_with_unicode_characters()
    {
        $unicodeName = 'Grupo de Prueba 测试组 グループ';
        $unicodeDescription = 'Descripción de prueba 测试描述 説明';

        $groupData = [
            'name' => $unicodeName,
            'description' => $unicodeDescription
        ];

        $response = $this->postJson('/api/groups', $groupData);

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals($unicodeName, $responseData['name']);
        $this->assertEquals($unicodeDescription, $responseData['description']);
    }

    public function test_can_order_groups_by_name()
    {
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Zebra Group'
        ]);
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Group'
        ]);

        $response = $this->getJson('/api/groups?order=name&by=asc');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals('Alpha Group', $responseData[0]['name']);
        $this->assertEquals('Zebra Group', $responseData[1]['name']);
    }

    public function test_can_order_groups_by_created_at_desc()
    {
        $firstGroup = Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'First Group'
        ]);

        sleep(1); // Ensure different timestamps

        $secondGroup = Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Second Group'
        ]);

        $response = $this->getJson('/api/groups?order=created_at&by=desc');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEquals('Second Group', $responseData[0]['name']);
        $this->assertEquals('First Group', $responseData[1]['name']);
    }

    public function test_can_combine_filters_and_ordering()
    {
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Test Group',
            'description' => 'Test description'
        ]);
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Test Group',
            'description' => 'Test description'
        ]);
        Group::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Gamma Other Group',
            'description' => 'Other description'
        ]);

        $response = $this->getJson('/api/groups?description=Test&order=name&by=asc');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData);
        $this->assertEquals('Alpha Test Group', $responseData[0]['name']);
        $this->assertEquals('Beta Test Group', $responseData[1]['name']);
    }

    public function test_pagination_works_correctly()
    {
        Group::factory()->count(7)->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson('/api/groups?limit=3');

        $response->assertStatus(200);

        $meta = $response->json('meta');
        $this->assertEquals(3, $meta['count']);
        $this->assertEquals(7, $meta['total']);
        $this->assertEquals(1, $meta['currentPage']);
        $this->assertEquals(3, $meta['lastPage']);
    }

    public function test_empty_results_handled_correctly()
    {
        $response = $this->getJson('/api/groups?name=NonExistentGroup');

        $response->assertStatus(200);

        $responseData = $response->json('data');
        $this->assertEmpty($responseData);

        $meta = $response->json('meta');
        $this->assertEquals(0, $meta['count']);
        $this->assertEquals(0, $meta['total']);
    }
}
