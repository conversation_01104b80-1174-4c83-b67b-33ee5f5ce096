<?php

namespace Tests\Feature\Services\ASAAS\Commands;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Console\Commands\ASAAS\SyncPaymentsStatus;
use App\Models\Organization;
use App\Models\Client;
use App\Models\Sale;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Services\ASAAS\Models\AsaasClient;
use App\Services\ASAAS\Models\AsaasSale;
use App\Enums\PaymentStatus;
use App\Enums\AsaasEnvironment;
use Carbon\Carbon;

class SyncPaymentsStatusTest extends TestCase
{
    use RefreshDatabase;

    private Organization $organization;
    private Client $client;
    private Sale $sale;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        
        // Create ASAAS organization
        AsaasOrganization::factory()->create([
            'organization_id' => $this->organization->id,
            'asaas_account_id' => 'acc_123',
            'asaas_api_key' => 'key_456',
            'asaas_environment' => AsaasEnvironment::SANDBOX,
        ]);

        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id,
        ]);

        // Create ASAAS client
        AsaasClient::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'asaas_customer_id' => 'cus_123',
        ]);

        $this->sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
        ]);
    }

    public function test_command_exists_and_can_be_instantiated()
    {
        $command = new SyncPaymentsStatus();
        $this->assertInstanceOf(SyncPaymentsStatus::class, $command);
    }

    public function test_sync_status_action_processes_pending_payments()
    {
        // Create ASAAS sale with pending payment
        AsaasSale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'sale_id' => $this->sale->id,
            'asaas_payment_id' => 'pay_123',
            'payment_status' => PaymentStatus::PENDING,
            'asaas_synced_at' => Carbon::now()->subHours(2), // Needs sync
        ]);

        $this->artisan('asaas:sync-payments sync-status --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_sync_status_with_organization_filter()
    {
        // Create ASAAS sale
        AsaasSale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'sale_id' => $this->sale->id,
            'asaas_payment_id' => 'pay_123',
            'payment_status' => PaymentStatus::PENDING,
        ]);

        $this->artisan("asaas:sync-payments sync-status --organization={$this->organization->id} --dry-run")
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_sync_status_with_sale_filter()
    {
        // Create ASAAS sale
        AsaasSale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'sale_id' => $this->sale->id,
            'asaas_payment_id' => 'pay_123',
            'payment_status' => PaymentStatus::PENDING,
        ]);

        $this->artisan("asaas:sync-payments sync-status --sale={$this->sale->id} --dry-run")
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_create_customers_action()
    {
        // Create client without ASAAS customer
        $clientWithoutAsaas = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Client Without ASAAS',
            'email' => '<EMAIL>',
            'cpf' => '12345678901',
        ]);

        $this->artisan('asaas:sync-payments create-customers --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_create_customers_with_organization_filter()
    {
        $this->artisan("asaas:sync-payments create-customers --organization={$this->organization->id} --dry-run")
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_sync_overdue_action()
    {
        // Create overdue payment
        AsaasSale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'sale_id' => $this->sale->id,
            'asaas_payment_id' => 'pay_overdue',
            'payment_status' => PaymentStatus::PENDING,
            'due_date' => Carbon::now()->subDays(5),
        ]);

        $this->artisan('asaas:sync-payments sync-overdue --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_command_with_limit_option()
    {
        // Create multiple sales
        for ($i = 0; $i < 5; $i++) {
            $sale = Sale::factory()->create([
                'organization_id' => $this->organization->id,
                'client_id' => $this->client->id,
            ]);

            AsaasSale::factory()->create([
                'organization_id' => $this->organization->id,
                'client_id' => $this->client->id,
                'sale_id' => $sale->id,
                'asaas_payment_id' => "pay_$i",
                'payment_status' => PaymentStatus::PENDING,
            ]);
        }

        $this->artisan('asaas:sync-payments sync-status --limit=3 --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_command_with_all_organizations_flag()
    {
        // Create another organization
        $otherOrg = Organization::factory()->create();
        AsaasOrganization::factory()->create([
            'organization_id' => $otherOrg->id,
        ]);

        $this->artisan('asaas:sync-payments sync-status --all --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_command_shows_help_for_unknown_action()
    {
        $this->artisan('asaas:sync-payments unknown-action')
            ->expectsOutput('Unknown action: unknown-action')
            ->assertExitCode(1);
    }

    public function test_command_validates_organization_exists()
    {
        $this->artisan('asaas:sync-payments sync-status --organization=999 --dry-run')
            ->assertExitCode(1);
    }

    public function test_command_validates_sale_exists()
    {
        $this->artisan('asaas:sync-payments sync-status --sale=999 --dry-run')
            ->assertExitCode(1);
    }

    public function test_command_validates_client_exists()
    {
        $this->artisan('asaas:sync-payments create-customers --client=999 --dry-run')
            ->assertExitCode(1);
    }

    public function test_command_without_dry_run_processes_real_data()
    {
        // Create ASAAS sale that needs sync
        AsaasSale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'sale_id' => $this->sale->id,
            'asaas_payment_id' => 'pay_123',
            'payment_status' => PaymentStatus::PENDING,
            'asaas_synced_at' => Carbon::now()->subHours(2),
        ]);

        // Since we don't have real ASAAS API, this will likely fail
        // but we can verify the command attempts to process
        $this->artisan('asaas:sync-payments sync-status --limit=1')
            ->assertExitCode(0);
    }

    public function test_command_handles_organizations_without_asaas_integration()
    {
        // Create organization without ASAAS
        $orgWithoutAsaas = Organization::factory()->create();

        $this->artisan("asaas:sync-payments sync-status --organization={$orgWithoutAsaas->id} --dry-run")
            ->assertExitCode(0);
    }

    public function test_command_shows_statistics()
    {
        // Create various payment statuses
        $statuses = [PaymentStatus::PENDING, PaymentStatus::RECEIVED, PaymentStatus::OVERDUE];
        
        foreach ($statuses as $index => $status) {
            $sale = Sale::factory()->create([
                'organization_id' => $this->organization->id,
                'client_id' => $this->client->id,
            ]);

            AsaasSale::factory()->create([
                'organization_id' => $this->organization->id,
                'client_id' => $this->client->id,
                'sale_id' => $sale->id,
                'asaas_payment_id' => "pay_$index",
                'payment_status' => $status,
            ]);
        }

        $this->artisan('asaas:sync-payments sync-status --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }

    public function test_command_handles_empty_results()
    {
        // No ASAAS sales exist
        $this->artisan('asaas:sync-payments sync-status --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No changes will be made')
            ->assertExitCode(0);
    }
}
