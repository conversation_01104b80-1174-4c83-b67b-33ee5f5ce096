<?php

namespace Tests\Feature\Services\ASAAS\Organizations;

use App\Models\Organization;
use App\Models\User;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CheckSubscriptionStatus;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSubaccount;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\CreateSystemSubscription;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\GetBillingDetails;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AsaasOrganizationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected Organization $organization;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create([
            'name' => 'Test Organization',
        ]);

        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_can_instantiate_create_subaccount_use_case()
    {
        $createSubaccount = app(CreateSubaccount::class);
        $this->assertInstanceOf(CreateSubaccount::class, $createSubaccount);
    }

    public function test_can_instantiate_is_allowed_to_use_system_use_case()
    {
        $isAllowed = app(IsAllowedToUseSystem::class);
        $this->assertInstanceOf(IsAllowedToUseSystem::class, $isAllowed);
    }

    public function test_can_check_organization_access_when_active()
    {
        $isAllowed = app(IsAllowedToUseSystem::class);

        // Test active organization
        $result = $isAllowed->perform($this->organization);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('allowed', $result);
        $this->assertArrayHasKey('reason', $result);
        $this->assertArrayHasKey('message', $result);
    }

    public function test_can_check_organization_access_when_inactive()
    {
        $isAllowed = app(IsAllowedToUseSystem::class);

        $this->organization->update(['is_active' => false]);

        $result = $isAllowed->perform($this->organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('organization_inactive', $result['reason']);
    }

    public function test_can_check_organization_access_when_suspended()
    {
        $isAllowed = app(IsAllowedToUseSystem::class);

        $this->organization->update(['is_suspended' => true]);

        $result = $isAllowed->perform($this->organization);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('organization_suspended', $result['reason']);
    }

    public function test_can_instantiate_get_billing_details_use_case()
    {
        $getBillingDetails = app(GetBillingDetails::class);
        $this->assertInstanceOf(GetBillingDetails::class, $getBillingDetails);
    }

    public function test_can_instantiate_create_system_subscription_use_case()
    {
        $createSubscription = app(CreateSystemSubscription::class);
        $this->assertInstanceOf(CreateSystemSubscription::class, $createSubscription);
    }

    public function test_can_instantiate_check_subscription_status_use_case()
    {
        $checkStatus = app(CheckSubscriptionStatus::class);
        $this->assertInstanceOf(CheckSubscriptionStatus::class, $checkStatus);
    }

    public function test_can_get_available_plans()
    {
        $createSubscription = app(CreateSystemSubscription::class);

        $plans = $createSubscription->getAvailablePlans();

        $this->assertArrayHasKey('basic', $plans);
        $this->assertArrayHasKey('professional', $plans);
        $this->assertArrayHasKey('enterprise', $plans);

        $this->assertEquals(29.90, $plans['basic']['value']);
        $this->assertEquals(59.90, $plans['professional']['value']);
        $this->assertEquals(99.90, $plans['enterprise']['value']);
    }

    public function test_can_calculate_subscription_value()
    {
        $createSubscription = app(CreateSystemSubscription::class);

        // Test with default user count (should be base price)
        $value = $createSubscription->calculateSubscriptionValue($this->organization);
        $this->assertEquals(29.90, $value);

        // Create additional users to test scaling (we already have 1 user from setUp)
        User::factory()->count(15)->create(['organization_id' => $this->organization->id]);

        $value = $createSubscription->calculateSubscriptionValue($this->organization->fresh());
        // Base price (29.90) + 6 additional users * 2.50 = 44.90 (16 total users - 10 free = 6 additional)
        $this->assertEquals(44.90, $value);
    }
}
