<?php

namespace Tests\Feature\Services\ASAAS\Integration;

use App\Enums\AsaasEnvironment;
use App\Enums\PaymentStatus;
use App\Enums\SubscriptionStatus;
use App\Models\Client;
use App\Models\Organization;
use App\Models\Sale;
use App\Services\ASAAS\Models\AsaasClient;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Services\ASAAS\Models\AsaasSale;
use App\Services\ASAAS\UseCases\Deprecated\Organizations\IsAllowedToUseSystem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Full workflow integration test for ASAAS refactored architecture
 *
 * This test validates the complete flow:
 * 1. Organization creation and ASAAS integration
 * 2. Client creation and ASAAS customer sync
 * 3. Sale creation and payment processing
 * 4. Status synchronization and validation
 */
class FullWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected Organization $organization;
    protected Client $client;
    protected Sale $sale;

    protected function setUp(): void
    {
        parent::setUp();

        // Create base models
        $this->organization = Organization::factory()->create([
            'name' => 'Test Organization',
            'is_active' => true,
        ]);

        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'cpf' => '12345678901',
        ]);

        $this->sale = Sale::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'total_value' => 150.00,
        ]);
    }

    public function test_complete_asaas_workflow()
    {
        // Step 1: Verify initial state - no ASAAS integration
        $this->assertFalse($this->organization->hasAsaasIntegration());
        $this->assertFalse($this->client->hasAsaasIntegration());
        $this->assertFalse($this->sale->hasAsaasPayment());

        // Step 2: Create ASAAS organization (simulating subaccount creation)
        $asaasOrganization = AsaasOrganization::factory()->active()->create([
            'organization_id' => $this->organization->id,
            'asaas_account_id' => 'acc_test_123',
            'asaas_api_key' => 'test_api_key_123',
            'asaas_environment' => AsaasEnvironment::SANDBOX,
            'subscription_status' => SubscriptionStatus::ACTIVE,
            'subscription_value' => 99.90,
        ]);

        // Verify organization now has ASAAS integration
        $freshOrganization = $this->organization->fresh();
        $this->assertTrue($freshOrganization->hasAsaasIntegration());
        $this->assertEquals('acc_test_123', $freshOrganization->getAsaasAccountId());
        $this->assertEquals('test_api_key_123', $freshOrganization->getAsaasApiKey());
        $this->assertTrue($asaasOrganization->hasActiveSubscription());

        // Step 3: Check system access permissions
        $isAllowedUseCase = app(IsAllowedToUseSystem::class);
        $accessResult = $isAllowedUseCase->perform($freshOrganization);

        $this->assertTrue($accessResult['allowed']);
        $this->assertEquals('subscription_active', $accessResult['reason']);

        // Step 4: Create ASAAS client (simulating customer creation)
        $asaasClient = AsaasClient::factory()->create([
            'client_id' => $this->client->id,
            'organization_id' => $this->organization->id,
            'asaas_customer_id' => 'cus_test_456',
        ]);

        // Verify client now has ASAAS integration
        $freshClient = $this->client->fresh();
        $this->assertTrue($freshClient->hasAsaasIntegration());
        $this->assertEquals('cus_test_456', $freshClient->getAsaasCustomerId());

        // Step 5: Create ASAAS sale (simulating payment creation)
        $asaasSale = AsaasSale::factory()->pending()->boleto()->create([
            'sale_id' => $this->sale->id,
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'asaas_payment_id' => 'pay_test_789',
            'original_value' => 150.00,
            'net_value' => 145.50,
            'due_date' => now()->addDays(7),
        ]);

        // Verify sale now has ASAAS payment
        $this->assertTrue($this->sale->fresh()->hasAsaasPayment());
        $this->assertEquals('pay_test_789', $this->sale->getAsaasPaymentId());
        $this->assertTrue($this->sale->isAsaasPending());

        // Step 6: Test payment status methods
        $this->assertTrue($this->sale->isPending());
        $this->assertFalse($this->sale->isPaid());
        $this->assertFalse($this->sale->isOverdue());

        // Step 7: Simulate payment received
        $asaasSale->update([
            'payment_status' => PaymentStatus::RECEIVED,
            'payment_date' => now(),
        ]);

        // Verify payment status updated
        $this->assertTrue($this->sale->fresh()->isPaid());
        $this->assertFalse($this->sale->fresh()->isPending());

        // Step 8: Test payment summary
        $summary = $this->sale->fresh()->getPaymentSummary();

        $this->assertEquals('received', $summary['status']);
        $this->assertEquals(150.00, $summary['value']);
        $this->assertEquals('R$ 150,00', $summary['formatted_value']);
        $this->assertTrue($summary['has_asaas_payment']);

        // Step 9: Test billing type labels
        $this->assertEquals('Boleto', $this->sale->fresh()->billing_type_label);
        $this->assertEquals('Recebido', $this->sale->fresh()->payment_status_label);

        // Step 10: Test relationships and eager loading
        $organizationWithAsaas = Organization::with('asaas')->find($this->organization->id);
        $this->assertNotNull($organizationWithAsaas->asaas);
        $this->assertEquals('acc_test_123', $organizationWithAsaas->asaas->asaas_account_id);

        $clientWithAsaas = Client::with('asaas')->find($this->client->id);
        $this->assertNotNull($clientWithAsaas->asaas);
        $this->assertEquals('cus_test_456', $clientWithAsaas->asaas->asaas_customer_id);

        $saleWithAsaas = Sale::with('asaas')->find($this->sale->id);
        $this->assertNotNull($saleWithAsaas->asaas);
        $this->assertEquals('pay_test_789', $saleWithAsaas->asaas->asaas_payment_id);

        // Step 11: Test sync status tracking
        $this->assertFalse($asaasClient->needsSync());
        $this->assertFalse($asaasSale->needsSync());

        // Step 12: Test organization subscription management
        $subscriptionSummary = $asaasOrganization->getSubscriptionSummary();
        $this->assertEquals('active', $subscriptionSummary['status']);
        $this->assertEquals(99.90, $subscriptionSummary['value']);
        $this->assertTrue($subscriptionSummary['has_active_subscription']);

        // Step 13: Test access summary
        $accessSummary = $asaasOrganization->getAccessSummary();
        $this->assertTrue($accessSummary['can_access_system']);
        $this->assertTrue($accessSummary['has_asaas_integration']);
        $this->assertFalse($accessSummary['is_courtesy']);

        $this->assertTrue(true); // Test completed successfully
    }

    public function test_workflow_with_courtesy_access()
    {
        // Create organization with courtesy access
        $asaasOrganization = AsaasOrganization::factory()->inCourtesy(30)->create([
            'organization_id' => $this->organization->id,
            'asaas_account_id' => 'acc_courtesy_123',
            'asaas_api_key' => 'courtesy_api_key',
            'subscription_status' => SubscriptionStatus::INACTIVE,
            'courtesy_expires_at' => now()->addDays(30),
            'courtesy_reason' => 'Trial period',
        ]);

        // Test courtesy access
        $isAllowedUseCase = app(IsAllowedToUseSystem::class);
        $accessResult = $isAllowedUseCase->perform($this->organization);

        $this->assertTrue($accessResult['allowed']);
        $this->assertEquals('courtesy_active', $accessResult['reason']);
        $this->assertStringContainsString('Courtesy access valid until', $accessResult['message']);

        // Test access summary with courtesy
        $accessSummary = $asaasOrganization->getAccessSummary();
        $this->assertTrue($accessSummary['can_access_system']);
        $this->assertTrue($accessSummary['has_asaas_integration']);
        $this->assertTrue($accessSummary['is_courtesy']);
    }

    public function test_workflow_without_asaas_integration()
    {
        // Test organization without ASAAS integration
        $isAllowedUseCase = app(IsAllowedToUseSystem::class);
        $accessResult = $isAllowedUseCase->perform($this->organization);

        $this->assertFalse($accessResult['allowed']);
        $this->assertEquals('no_asaas_integration', $accessResult['reason']);

        // Test client and sale methods without ASAAS
        $this->assertFalse($this->client->needsAsaasSync());
        $this->assertFalse($this->sale->needsAsaasSync());

        $summary = $this->sale->getPaymentSummary();
        $this->assertEquals('no_payment', $summary['status']);
        $this->assertFalse($summary['has_asaas_payment']);
    }
}
