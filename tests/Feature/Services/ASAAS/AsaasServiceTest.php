<?php

namespace Tests\Feature\Services\ASAAS;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\ASAAS\AsaasService;
use App\Services\ASAAS\UseCases\CallEndpoint;
use App\Services\ASAAS\UseCases\Http\CreateHttpClient;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\Models\AsaasLog;
use App\Enums\AsaasEnvironment;
use App\Models\Organization;
use App\Models\User;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;

class AsaasServiceTest extends TestCase
{
    use RefreshDatabase;

    protected AsaasService $asaasService;
    protected Organization $organization;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->asaasService = app(AsaasService::class);
    }

    public function test_can_create_asaas_service()
    {
        $this->assertInstanceOf(AsaasService::class, $this->asaasService);
    }

    public function test_service_has_required_dependencies()
    {
        $this->assertInstanceOf(CallEndpoint::class, app(CallEndpoint::class));
        $this->assertInstanceOf(CreateHttpClient::class, app(CreateHttpClient::class));
    }

    public function test_can_create_http_client_with_token()
    {
        $client = $this->asaasService->createClient('test_token');

        $this->assertInstanceOf(\App\Services\ASAAS\Domains\AsaasHttpClient::class, $client);
        $this->assertEquals('test_token', $client->getToken());
        $this->assertEquals(AsaasEnvironment::SANDBOX, $client->getEnvironment()); // Default environment
    }

    public function test_can_create_http_client_with_environment()
    {
        $sandboxClient = $this->asaasService->createSandboxClient('sandbox_token');
        $this->assertEquals(AsaasEnvironment::SANDBOX, $sandboxClient->getEnvironment());
        $this->assertEquals('sandbox_token', $sandboxClient->getToken());

        $productionClient = $this->asaasService->createProductionClient('production_token');
        $this->assertEquals(AsaasEnvironment::PRODUCTION, $productionClient->getEnvironment());
        $this->assertEquals('production_token', $productionClient->getToken());
    }

    public function test_can_create_client_for_organization()
    {
        $client = $this->asaasService->createClientForOrganization(
            $this->organization->id,
            'org_token',
            AsaasEnvironment::SANDBOX,
            $this->user->id
        );

        $this->assertEquals($this->organization->id, $client->getOrganizationId());
        $this->assertEquals($this->user->id, $client->getUserId());
        $this->assertEquals(AsaasEnvironment::SANDBOX, $client->getEnvironment());
        $this->assertEquals('org_token', $client->getToken());
    }

    public function test_asaas_environment_enum_works()
    {
        $sandbox = AsaasEnvironment::SANDBOX;
        $production = AsaasEnvironment::PRODUCTION;

        $this->assertEquals('sandbox', $sandbox->value);
        $this->assertEquals('production', $production->value);
        $this->assertEquals('https://api-sandbox.asaas.com', $sandbox->getBaseUrl());
        $this->assertEquals('https://api.asaas.com', $production->getBaseUrl());
        $this->assertEquals('asaas.token_sandbox', $sandbox->getTokenConfigKey());
        $this->assertEquals('asaas.token_production', $production->getTokenConfigKey());
    }

    public function test_asaas_configuration_is_loaded()
    {
        $this->assertNotNull(config('asaas.environment'));
        $this->assertNotNull(config('asaas.timeout'));
        $this->assertNotNull(config('asaas.retry_attempts'));
        $this->assertIsBool(config('asaas.log_requests'));
        $this->assertIsBool(config('asaas.log_responses'));
        $this->assertIsBool(config('asaas.log_errors_only'));
    }

    public function test_asaas_log_model_exists()
    {
        $this->assertTrue(class_exists(\App\Services\ASAAS\Models\AsaasLog::class));

        // Test that we can create an AsaasLog instance
        $log = new \App\Services\ASAAS\Models\AsaasLog();
        $this->assertInstanceOf(\App\Services\ASAAS\Models\AsaasLog::class, $log);

        // Test fillable attributes
        $fillable = $log->getFillable();
        $expectedFillable = [
            'organization_id', 'user_id', 'method', 'endpoint', 'request_data',
            'response_data', 'response_status', 'execution_time', 'is_error',
            'error_message', 'asaas_error_code', 'environment'
        ];

        foreach ($expectedFillable as $field) {
            $this->assertContains($field, $fillable);
        }
    }

    public function test_asaas_exception_class_exists()
    {
        $this->assertTrue(class_exists(\App\Services\ASAAS\Exceptions\AsaasException::class));

        // Test that we can create an AsaasException instance
        $exception = new \App\Services\ASAAS\Exceptions\AsaasException('Test message', 400);
        $this->assertInstanceOf(\App\Services\ASAAS\Exceptions\AsaasException::class, $exception);
        $this->assertEquals('Test message', $exception->getMessage());
        $this->assertEquals(400, $exception->getCode());
    }


}
