<?php

namespace Tests\Feature\Services\ASAAS;

use App\Models\Organization;
use App\Services\ASAAS\Domains\AsaasOrganizationCustomer;
use App\Services\ASAAS\Factories\AsaasOrganizationCustomerFactory;
use App\Services\ASAAS\Models\AsaasOrganizationCustomer as AsaasOrganizationCustomerModel;
use App\Services\ASAAS\Repositories\AsaasOrganizationCustomerRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AsaasOrganizationCustomerTest extends TestCase
{
    use RefreshDatabase;

    private AsaasOrganizationCustomerFactory $factory;
    private AsaasOrganizationCustomerRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new AsaasOrganizationCustomerFactory();
        $this->repository = new AsaasOrganizationCustomerRepository($this->factory);
    }

    /** @test */
    public function it_can_create_asaas_organization_customer_model()
    {
        $organization = Organization::factory()->create();

        $customer = AsaasOrganizationCustomerModel::factory()
            ->forOrganization($organization)
            ->create();

        $this->assertDatabaseHas('asaas_organization_customers', [
            'id' => $customer->id,
            'organization_id' => $organization->id,
            'asaas_customer_id' => $customer->asaas_customer_id,
        ]);
    }

    /** @test */
    public function it_can_build_domain_from_model()
    {
        $organization = Organization::factory()->create();
        $model = AsaasOrganizationCustomerModel::factory()
            ->forOrganization($organization)
            ->synced()
            ->create();

        $domain = $this->factory->buildFromModel($model);

        $this->assertInstanceOf(AsaasOrganizationCustomer::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->asaas_customer_id, $domain->asaas_customer_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->email, $domain->email);
        $this->assertTrue($domain->isSynced());
    }

    /** @test */
    public function it_can_build_domain_from_organization()
    {
        $organization = Organization::factory()->create([
            'name' => 'Test Organization',
            'description' => 'Test Description'
        ]);

        $domain = $this->factory->buildFromOrganization($organization->toDomain());

        $this->assertInstanceOf(AsaasOrganizationCustomer::class, $domain);
        $this->assertEquals($organization->id, $domain->organization_id);
        $this->assertEquals('Test Organization', $domain->name);
        $this->assertEquals('Test Description', $domain->observations);
        $this->assertEquals('JURIDICA', $domain->person_type);
        $this->assertEquals("org_{$organization->id}", $domain->external_reference);
        $this->assertTrue($domain->isPending());
    }

    /** @test */
    public function it_can_build_domain_from_asaas_response()
    {
        $asaasResponse = [
            'id' => 'cus_123456789',
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'phone' => '11999999999',
            'mobilePhone' => '11888888888',
            'address' => 'Test Address',
            'addressNumber' => '123',
            'complement' => 'Apt 1',
            'province' => 'Test Province',
            'cityName' => 'Test City',
            'state' => 'SP',
            'country' => 'Brasil',
            'postalCode' => '01234567',
            'cpfCnpj' => '12345678901234',
            'personType' => 'JURIDICA',
            'externalReference' => 'org_1',
            'notificationDisabled' => false,
            'additionalEmails' => '<EMAIL>',
            'observations' => 'Test observations',
            'foreignCustomer' => false,
            'deleted' => false,
        ];

        $domain = $this->factory->buildFromAsaasResponse($asaasResponse, 1);

        $this->assertInstanceOf(AsaasOrganizationCustomer::class, $domain);
        $this->assertEquals('cus_123456789', $domain->asaas_customer_id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Test Customer', $domain->name);
        $this->assertEquals('<EMAIL>', $domain->email);
        $this->assertEquals('JURIDICA', $domain->person_type);
        $this->assertTrue($domain->isSynced());
    }

    /** @test */
    public function it_can_save_customer_through_repository()
    {
        $organization = Organization::factory()->create();

        $domain = $this->factory->buildFromOrganization($organization->toDomain(), 'cus_test123');

        $savedDomain = $this->repository->save($domain);

        $this->assertInstanceOf(AsaasOrganizationCustomer::class, $savedDomain);
        $this->assertNotNull($savedDomain->id);
        $this->assertEquals('cus_test123', $savedDomain->asaas_customer_id);

        $this->assertDatabaseHas('asaas_organization_customers', [
            'organization_id' => $organization->id,
            'asaas_customer_id' => 'cus_test123',
        ]);
    }

    /** @test */
    public function it_can_find_customer_by_organization_id()
    {
        $organization = Organization::factory()->create();
        $model = AsaasOrganizationCustomerModel::factory()
            ->forOrganization($organization)
            ->create();

        $domain = $this->repository->findByOrganizationId($organization->id);

        $this->assertInstanceOf(AsaasOrganizationCustomer::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($organization->id, $domain->organization_id);
    }

    /** @test */
    public function it_can_find_customer_by_asaas_customer_id()
    {
        $model = AsaasOrganizationCustomerModel::factory()->create([
            'asaas_customer_id' => 'cus_findme123'
        ]);

        $domain = $this->repository->findByAsaasCustomerId('cus_findme123');

        $this->assertInstanceOf(AsaasOrganizationCustomer::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals('cus_findme123', $domain->asaas_customer_id);
    }

    /** @test */
    public function it_can_find_customers_needing_sync()
    {
        // Create customers with different sync statuses
        $pending = AsaasOrganizationCustomerModel::factory()->pending()->create();
        $needsSync = AsaasOrganizationCustomerModel::factory()->needsSync()->create();

        // Create a recently synced customer (should not need sync)
        $synced = AsaasOrganizationCustomerModel::factory()->create([
            'sync_status' => 'synced',
            'asaas_synced_at' => now()->subMinutes(30), // Recently synced
            'asaas_sync_errors' => null,
        ]);

        $customers = $this->repository->findNeedingSync();

        // Should find at least the pending and needsSync customers
        $this->assertGreaterThanOrEqual(2, $customers->count());

        // Check that pending and needsSync customers are included
        $customerIds = $customers->pluck('id')->toArray();
        $this->assertContains($pending->id, $customerIds);
        $this->assertContains($needsSync->id, $customerIds);

        // The recently synced customer should not be included
        $this->assertNotContains($synced->id, $customerIds);
    }

    /** @test */
    public function it_can_convert_domain_to_asaas_payload()
    {
        $domain = new AsaasOrganizationCustomer(
            id: null,
            organization_id: 1,
            asaas_customer_id: 'cus_123',
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            name: 'Test Company',
            email: '<EMAIL>',
            phone: '11999999999',
            mobile_phone: '11888888888',
            address: 'Test Street',
            address_number: '123',
            complement: 'Suite 1',
            province: 'Downtown',
            city_name: 'São Paulo',
            state: 'SP',
            country: 'Brasil',
            postal_code: '01234567',
            cpf_cnpj: '12345678901234',
            person_type: 'JURIDICA',
            external_reference: 'org_1',
            notification_disabled: false,
            additional_emails: '<EMAIL>',
            observations: 'Test observations',
            foreign_customer: false,
            deleted: false,
        );

        $payload = $domain->toAsaasPayload();

        $expectedPayload = [
            'name' => 'Test Company',
            'email' => '<EMAIL>',
            'phone' => '11999999999',
            'mobilePhone' => '11888888888',
            'address' => 'Test Street',
            'addressNumber' => '123',
            'complement' => 'Suite 1',
            'province' => 'Downtown',
            'postalCode' => '01234567',
            'cpfCnpj' => '12345678901234',
            'externalReference' => 'org_1',
            'notificationDisabled' => false,
            'additionalEmails' => '<EMAIL>',
            'observations' => 'Test observations',
            'foreignCustomer' => false,
        ];

        $this->assertEquals($expectedPayload, $payload);
    }

    /** @test */
    public function organization_model_has_asaas_customer_relationship()
    {
        $organization = Organization::factory()->create();
        $customer = AsaasOrganizationCustomerModel::factory()
            ->forOrganization($organization)
            ->create();

        $organization->refresh();

        $this->assertInstanceOf(AsaasOrganizationCustomerModel::class, $organization->asaasCustomer);
        $this->assertEquals($customer->id, $organization->asaasCustomer->id);
        $this->assertTrue($organization->hasAsaasCustomerIntegration());
    }

    /** @test */
    public function it_validates_data_completeness()
    {
        // Complete data
        $completeDomain = new AsaasOrganizationCustomer(
            id: null,
            organization_id: 1,
            asaas_customer_id: 'cus_123',
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            name: 'Test Company',
            email: '<EMAIL>',
            phone: null,
            mobile_phone: null,
            address: null,
            address_number: null,
            complement: null,
            province: null,
            city_name: null,
            state: null,
            country: null,
            postal_code: null,
            cpf_cnpj: '12345678901234',
            person_type: 'JURIDICA',
            external_reference: null,
            notification_disabled: false,
            additional_emails: null,
            observations: null,
            foreign_customer: false,
            deleted: false,
        );

        $this->assertTrue($completeDomain->isDataComplete());
        $this->assertEmpty($completeDomain->getValidationErrors());

        // Incomplete data
        $incompleteDomain = new AsaasOrganizationCustomer(
            id: null,
            organization_id: 1,
            asaas_customer_id: 'cus_123',
            asaas_synced_at: null,
            asaas_sync_errors: null,
            sync_status: 'pending',
            name: null, // Missing required field
            email: null, // Missing required field
            phone: null,
            mobile_phone: null,
            address: null,
            address_number: null,
            complement: null,
            province: null,
            city_name: null,
            state: null,
            country: null,
            postal_code: null,
            cpf_cnpj: null, // Missing required field
            person_type: 'JURIDICA',
            external_reference: null,
            notification_disabled: false,
            additional_emails: null,
            observations: null,
            foreign_customer: false,
            deleted: false,
        );

        $this->assertFalse($incompleteDomain->isDataComplete());
        $errors = $incompleteDomain->getValidationErrors();
        $this->assertContains('Nome é obrigatório', $errors);
        $this->assertContains('Email é obrigatório', $errors);
        $this->assertContains('CPF/CNPJ é obrigatório', $errors);
    }
}
