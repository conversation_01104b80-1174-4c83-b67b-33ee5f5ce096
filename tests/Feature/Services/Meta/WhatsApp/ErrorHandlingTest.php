<?php

namespace Tests\Feature\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use App\Services\Meta\WhatsApp\TemplateService;
use App\Services\Meta\WhatsApp\MessageService;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessWebhookMessage;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateClient;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendWhatsAppResponse;
use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Models\Organization;
use App\Models\User;
use App\Models\PhoneNumber as PhoneNumberModel;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Psr7\Response;
use Exception;

class ErrorHandlingTest extends TestCase
{
    use RefreshDatabase;

    protected ChatBotService $chatBotService;
    protected TemplateService $templateService;
    protected MessageService $messageService;
    protected Organization $organization;
    protected User $user;
    protected PhoneNumber $phoneNumber;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        
        $phoneNumberModel = PhoneNumberModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'whatsapp_phone_number_id' => 'test_phone_123',
            'whatsapp_access_token' => 'test_token_123'
        ]);

        $this->phoneNumber = new PhoneNumber(
            id: $phoneNumberModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            phone_number: $phoneNumberModel->phone_number,
            whatsapp_phone_number_id: $phoneNumberModel->whatsapp_phone_number_id,
            whatsapp_access_token: $phoneNumberModel->whatsapp_access_token
        );

        // Create services with mocked dependencies
        $this->createChatBotServiceWithMocks();
        $this->templateService = new TemplateService($this->phoneNumber);
        $this->messageService = new MessageService($this->phoneNumber);
    }

    private function createChatBotServiceWithMocks(): void
    {
        $processWebhookMessage = $this->createMock(ProcessWebhookMessage::class);
        $findOrCreateClient = $this->createMock(FindOrCreateClient::class);
        $findOrCreateConversation = $this->createMock(FindOrCreateConversation::class);
        $processFlowStep = $this->createMock(ProcessFlowStep::class);
        $sendWhatsAppResponse = $this->createMock(SendWhatsAppResponse::class);

        $this->chatBotService = new ChatBotService(
            $processWebhookMessage,
            $findOrCreateClient,
            $findOrCreateConversation,
            $processFlowStep,
            $sendWhatsAppResponse
        );
    }

    public function test_handles_whatsapp_api_rate_limiting()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: 1,
            phone_number_id: $this->phoneNumber->id,
            to: '+**********',
            message: 'Test message',
            type: 'text'
        );

        $rateLimitResponse = new Response(429, [], json_encode([
            'error' => [
                'message' => 'Rate limit exceeded',
                'type' => 'OAuthException',
                'code' => 4,
                'error_subcode' => 2018109
            ]
        ]));

        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException(new ClientException('Rate limit exceeded', 
                $this->createMock(\Psr\Http\Message\RequestInterface::class), 
                $rateLimitResponse
            ));

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act & Assert
        $this->expectException(ClientException::class);
        $this->expectExceptionMessage('Rate limit exceeded');
        
        $this->messageService->sendMessage($message);
    }

    public function test_handles_invalid_phone_number_error()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: 1,
            phone_number_id: $this->phoneNumber->id,
            to: 'invalid_phone',
            message: 'Test message',
            type: 'text'
        );

        $invalidPhoneResponse = new Response(400, [], json_encode([
            'error' => [
                'message' => 'Invalid phone number',
                'type' => 'OAuthException',
                'code' => 100,
                'error_subcode' => 2018001
            ]
        ]));

        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException(new ClientException('Invalid phone number', 
                $this->createMock(\Psr\Http\Message\RequestInterface::class), 
                $invalidPhoneResponse
            ));

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act & Assert
        $this->expectException(ClientException::class);
        $this->expectExceptionMessage('Invalid phone number');
        
        $this->messageService->sendMessage($message);
    }

    public function test_handles_whatsapp_server_error()
    {
        // Arrange
        $template = new Template(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: 'Test Template',
            description: 'Test Description',
            language: 'en',
            category: 'MARKETING',
            json: json_encode([])
        );

        $serverErrorResponse = new Response(500, [], json_encode([
            'error' => [
                'message' => 'Internal server error',
                'type' => 'OAuthException',
                'code' => 1
            ]
        ]));

        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException(new ServerException('Internal server error', 
                $this->createMock(\Psr\Http\Message\RequestInterface::class), 
                $serverErrorResponse
            ));

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->templateService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->templateService, $mockClient);

        // Act & Assert
        $this->expectException(ServerException::class);
        $this->expectExceptionMessage('Internal server error');
        
        $this->templateService->register($template);
    }

    public function test_handles_malformed_webhook_data()
    {
        // Arrange
        $malformedWebhookData = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => '*********',
                    'changes' => [
                        [
                            'value' => [
                                'messaging_product' => 'whatsapp',
                                // Missing required 'messages' field
                                'metadata' => [
                                    'display_phone_number' => '+**********',
                                    'phone_number_id' => 'test_phone_123'
                                ]
                            ],
                            'field' => 'messages'
                        ]
                    ]
                ]
            ]
        ];

        // Mock the ProcessWebhookMessage use case to throw an exception
        $processWebhookMessage = $this->createMock(ProcessWebhookMessage::class);
        $processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->with($malformedWebhookData)
            ->willThrowException(new Exception('Invalid webhook data: missing messages field'));

        $findOrCreateClient = $this->createMock(FindOrCreateClient::class);
        $findOrCreateConversation = $this->createMock(FindOrCreateConversation::class);
        $processFlowStep = $this->createMock(ProcessFlowStep::class);
        $sendWhatsAppResponse = $this->createMock(SendWhatsAppResponse::class);

        $chatBotService = new ChatBotService(
            $processWebhookMessage,
            $findOrCreateClient,
            $findOrCreateConversation,
            $processFlowStep,
            $sendWhatsAppResponse
        );

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid webhook data: missing messages field');
        
        $chatBotService->processWebhook($malformedWebhookData);
    }

    public function test_handles_database_connection_error()
    {
        // Arrange
        $webhookData = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => '*********',
                    'changes' => [
                        [
                            'value' => [
                                'messaging_product' => 'whatsapp',
                                'messages' => [
                                    [
                                        'id' => 'msg_123',
                                        'from' => '+**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Hello']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Mock the FindOrCreateClient use case to throw a database exception
        $processWebhookMessage = $this->createMock(ProcessWebhookMessage::class);
        $processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->willReturn($webhookData['entry'][0]['changes'][0]['value']['messages'][0]);

        $findOrCreateClient = $this->createMock(FindOrCreateClient::class);
        $findOrCreateClient
            ->expects($this->once())
            ->method('perform')
            ->willThrowException(new Exception('Database connection failed'));

        $findOrCreateConversation = $this->createMock(FindOrCreateConversation::class);
        $processFlowStep = $this->createMock(ProcessFlowStep::class);
        $sendWhatsAppResponse = $this->createMock(SendWhatsAppResponse::class);

        $chatBotService = new ChatBotService(
            $processWebhookMessage,
            $findOrCreateClient,
            $findOrCreateConversation,
            $processFlowStep,
            $sendWhatsAppResponse
        );

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database connection failed');
        
        $chatBotService->processWebhook($webhookData);
    }

    public function test_handles_invalid_template_validation_error()
    {
        // Arrange
        $invalidTemplate = new Template(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: '', // Invalid: empty name
            description: 'Test Description',
            language: 'invalid_lang', // Invalid language code
            category: 'INVALID_CATEGORY', // Invalid category
            json: 'invalid json' // Invalid JSON
        );

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Template is not valid for WhatsApp');
        
        $this->templateService->register($invalidTemplate);
    }

    public function test_handles_network_timeout_error()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: 1,
            phone_number_id: $this->phoneNumber->id,
            to: '+**********',
            message: 'Test message',
            type: 'text'
        );

        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException(new GuzzleException('Connection timeout'));

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act & Assert
        $this->expectException(GuzzleException::class);
        $this->expectExceptionMessage('Connection timeout');
        
        $this->messageService->sendMessage($message);
    }

    public function test_handles_authentication_error()
    {
        // Arrange
        $message = new Message(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            client_id: 1,
            phone_number_id: $this->phoneNumber->id,
            to: '+**********',
            message: 'Test message',
            type: 'text'
        );

        $authErrorResponse = new Response(401, [], json_encode([
            'error' => [
                'message' => 'Invalid access token',
                'type' => 'OAuthException',
                'code' => 190
            ]
        ]));

        $mockClient = $this->createMock(GuzzleClient::class);
        $mockClient
            ->expects($this->once())
            ->method('post')
            ->willThrowException(new ClientException('Invalid access token', 
                $this->createMock(\Psr\Http\Message\RequestInterface::class), 
                $authErrorResponse
            ));

        // Replace the client in the service
        $reflection = new \ReflectionClass($this->messageService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->messageService, $mockClient);

        // Act & Assert
        $this->expectException(ClientException::class);
        $this->expectExceptionMessage('Invalid access token');
        
        $this->messageService->sendMessage($message);
    }

    public function test_handles_flow_step_processing_error()
    {
        // Arrange
        $webhookData = [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => '*********',
                    'changes' => [
                        [
                            'value' => [
                                'messaging_product' => 'whatsapp',
                                'messages' => [
                                    [
                                        'id' => 'msg_123',
                                        'from' => '+**********',
                                        'type' => 'text',
                                        'text' => ['body' => 'Hello']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Mock successful initial steps but failure in flow processing
        $processWebhookMessage = $this->createMock(ProcessWebhookMessage::class);
        $processWebhookMessage
            ->expects($this->once())
            ->method('perform')
            ->willReturn($webhookData['entry'][0]['changes'][0]['value']['messages'][0]);

        $findOrCreateClient = $this->createMock(FindOrCreateClient::class);
        $findOrCreateClient
            ->expects($this->once())
            ->method('perform')
            ->willReturn($this->createMock(\App\Domains\Inventory\Client::class));

        $findOrCreateConversation = $this->createMock(FindOrCreateConversation::class);
        $findOrCreateConversation
            ->expects($this->once())
            ->method('perform')
            ->willReturn($this->createMock(\App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation::class));

        $processFlowStep = $this->createMock(ProcessFlowStep::class);
        $processFlowStep
            ->expects($this->once())
            ->method('perform')
            ->willThrowException(new Exception('Flow step processing failed: invalid step configuration'));

        $sendWhatsAppResponse = $this->createMock(SendWhatsAppResponse::class);

        $chatBotService = new ChatBotService(
            $processWebhookMessage,
            $findOrCreateClient,
            $findOrCreateConversation,
            $processFlowStep,
            $sendWhatsAppResponse
        );

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Flow step processing failed: invalid step configuration');
        
        $chatBotService->processWebhook($webhookData);
    }
}
