<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppInteractionRepository;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppInteractionFactory;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConditionalNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Services\Meta\WhatsApp\ChatBot\Services\ErrorHandlerService;
use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\Flow;
use App\Models\Organization;
use Mockery;

class ProcessFlowStepIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected ProcessFlowStep $processFlowStep;
    protected $mockInteractionRepository;
    protected $mockInteractionFactory;
    protected $mockConditionalNavigationService;
    protected $mockDynamicInputService;
    protected $mockErrorHandlerService;
    protected Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();

        $this->mockInteractionRepository = Mockery::mock(WhatsAppInteractionRepository::class);
        $this->mockInteractionFactory = Mockery::mock(WhatsAppInteractionFactory::class);
        $this->mockConditionalNavigationService = Mockery::mock(ConditionalNavigationService::class);
        $this->mockDynamicInputService = Mockery::mock(DynamicInputService::class);
        $this->mockErrorHandlerService = Mockery::mock(ErrorHandlerService::class);

        $this->processFlowStep = new ProcessFlowStep(
            $this->mockInteractionRepository,
            $this->mockInteractionFactory,
            $this->mockConditionalNavigationService,
            $this->mockDynamicInputService,
            $this->mockErrorHandlerService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_processes_message_step_successfully()
    {
        $flow = new Flow(1, $this->organization->id, 'Test Flow', 'Description', 1, null, true);

        $step = new Step(
            1, $this->organization->id, 1, 'welcome_message', 'message', \App\Enums\StepType::MESSAGE, 1, 2, null,
            true, false, ['text' => 'Welcome message'], null, null, '{}'
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 1);
        $conversation->current_step = $step;

        $messageData = [
            'type' => 'text',
            'text' => ['body' => 'Hello'],
            'from' => '5511999999999'
        ];

        $mockInteraction = Mockery::mock();
        $mockInteraction->shouldReceive('setAttribute')->andReturnSelf();
        $mockInteraction->result = null;

        $this->mockInteractionFactory
            ->shouldReceive('buildFromWhatsAppMessage')
            ->once()
            ->andReturn($mockInteraction);

        $this->mockInteractionRepository
            ->shouldReceive('save')
            ->once()
            ->with($mockInteraction)
            ->andReturn($mockInteraction);

        $this->mockConditionalNavigationService
            ->shouldReceive('shouldApplyConditionalNavigation')
            ->once()
            ->andReturn(false);

        $result = $this->processFlowStep->perform($conversation, $messageData);

        $this->assertEquals('message', $result['type']);
        $this->assertEquals(1, $result['step_id']);
        $this->assertTrue($result['move_to_next']);
        $this->assertEquals(2, $result['next_step']);
    }

    public function test_perform_processes_command_step_successfully()
    {
        $commandConfig = [
            'command_type' => 'update_client',
            'command_config' => ['field' => 'name'],
            'success_message' => 'Updated successfully!',
            'error_message' => 'Update failed!'
        ];

        $step = new Step(
            2, $this->organization->id, 1, 'update_client_name', 'command', \App\Enums\StepType::COMMAND, 2, 3, 1,
            false, false, ['command' => 'update_client', 'parameters' => []], null, null, json_encode($commandConfig)
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 2);
        $conversation->current_step = $step;

        $messageData = [
            'type' => 'text',
            'text' => ['body' => 'João Silva'],
            'from' => '5511999999999'
        ];

        $mockInteraction = Mockery::mock();
        $mockInteraction->shouldReceive('setAttribute')->andReturnSelf();
        $mockInteraction->result = null;

        $this->mockInteractionFactory
            ->shouldReceive('buildFromWhatsAppMessage')
            ->once()
            ->andReturn($mockInteraction);

        $this->mockInteractionRepository
            ->shouldReceive('save')
            ->once()
            ->with($mockInteraction)
            ->andReturn($mockInteraction);

        $this->mockConditionalNavigationService
            ->shouldReceive('shouldApplyConditionalNavigation')
            ->once()
            ->andReturn(false);

        // Mock the ExecuteCommand service to be injected
        $mockExecuteCommand = Mockery::mock();
        $mockExecuteCommand
            ->shouldReceive('perform')
            ->once()
            ->andReturn([
                'success' => true,
                'command_type' => 'update_client',
                'result' => ['field_updated' => 'name', 'new_value' => 'João Silva'],
                'message' => 'Updated successfully!'
            ]);

        $this->app->instance(
            \App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand::class,
            $mockExecuteCommand
        );

        $result = $this->processFlowStep->perform($conversation, $messageData);

        $this->assertEquals('command', $result['type']);
        $this->assertEquals('command_executed', $result['action']);
        $this->assertTrue($result['success']);
        $this->assertEquals('Updated successfully!', $result['message']);
        $this->assertTrue($result['move_to_next']);
        $this->assertEquals(3, $result['next_step']);
    }

    public function test_perform_processes_input_step_with_valid_input()
    {
        $inputConfig = [
            'input_config' => [
                'field_name' => 'client.name',
                'type' => 'text',
                'validation' => 'required|string|min:2'
            ]
        ];

        $step = new Step(
            3, $this->organization->id, 1, 'collect_name', 'input', \App\Enums\StepType::INPUT, 3, 4, 2,
            false, false, ['prompt' => 'Enter your name', 'field_mapping' => 'client.name'], null, null, json_encode($inputConfig)
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 3);
        $conversation->current_step = $step;

        $messageData = [
            'type' => 'text',
            'text' => ['body' => 'João Silva'],
            'from' => '5511999999999'
        ];

        $mockInteraction = Mockery::mock();
        $mockInteraction->shouldReceive('setAttribute')->andReturnSelf();
        $mockInteraction->shouldReceive('getInputText')->andReturn('João Silva');
        $mockInteraction->result = null;

        $this->mockInteractionFactory
            ->shouldReceive('buildFromWhatsAppMessage')
            ->once()
            ->andReturn($mockInteraction);

        $this->mockInteractionRepository
            ->shouldReceive('save')
            ->once()
            ->with($mockInteraction)
            ->andReturn($mockInteraction);

        $this->mockDynamicInputService
            ->shouldReceive('processInput')
            ->once()
            ->with('client.name', 'João Silva', $conversation)
            ->andReturn(['success' => true, 'updated_field' => 'client.name']);

        $this->mockConditionalNavigationService
            ->shouldReceive('shouldApplyConditionalNavigation')
            ->once()
            ->andReturn(false);

        // Mock InputValidationService
        $mockInputValidationService = Mockery::mock();
        $mockInputValidationService
            ->shouldReceive('validateInput')
            ->once()
            ->with('João Silva', 'text', 'required|string|min:2', 3)
            ->andReturn([
                'valid' => true,
                'sanitized_input' => 'João Silva',
                'input_type' => 'text'
            ]);

        $this->app->instance(
            \App\Services\Meta\WhatsApp\ChatBot\Services\InputValidationService::class,
            $mockInputValidationService
        );

        $result = $this->processFlowStep->perform($conversation, $messageData);

        $this->assertEquals('input', $result['type']);
        $this->assertEquals('input_processed', $result['action']);
        $this->assertTrue($result['input_valid']);
        $this->assertTrue($result['move_to_next']);
        $this->assertEquals(4, $result['next_step']);
    }

    public function test_perform_processes_input_step_with_invalid_input()
    {
        $inputConfig = [
            'input_config' => [
                'field_name' => 'client.email',
                'type' => 'email',
                'validation' => 'required|email'
            ]
        ];

        $step = new Step(
            4, $this->organization->id, 1, 'collect_email', 'input', \App\Enums\StepType::INPUT, 4, 5, 3,
            false, false, ['prompt' => 'Enter your email', 'field_mapping' => 'client.email'], null, null, json_encode($inputConfig)
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 4);
        $conversation->current_step = $step;

        $messageData = [
            'type' => 'text',
            'text' => ['body' => 'invalid-email'],
            'from' => '5511999999999'
        ];

        $mockInteraction = Mockery::mock();
        $mockInteraction->shouldReceive('setAttribute')->andReturnSelf();
        $mockInteraction->shouldReceive('getInputText')->andReturn('invalid-email');
        $mockInteraction->result = null;

        $this->mockInteractionFactory
            ->shouldReceive('buildFromWhatsAppMessage')
            ->once()
            ->andReturn($mockInteraction);

        $this->mockInteractionRepository
            ->shouldReceive('save')
            ->once()
            ->with($mockInteraction)
            ->andReturn($mockInteraction);

        $this->mockConditionalNavigationService
            ->shouldReceive('shouldApplyConditionalNavigation')
            ->once()
            ->andReturn(false);

        // Mock InputValidationService to return validation error
        $mockInputValidationService = Mockery::mock();
        $mockInputValidationService
            ->shouldReceive('validateInput')
            ->once()
            ->with('invalid-email', 'email', 'required|email', 3)
            ->andReturn([
                'valid' => false,
                'error' => 'Por favor, digite um email válido.',
                'error_code' => 'VALIDATION_FAILED'
            ]);

        $this->app->instance(
            \App\Services\Meta\WhatsApp\ChatBot\Services\InputValidationService::class,
            $mockInputValidationService
        );

        $result = $this->processFlowStep->perform($conversation, $messageData);

        $this->assertEquals('input', $result['type']);
        $this->assertEquals('input_validation_failed', $result['action']);
        $this->assertFalse($result['input_valid']);
        $this->assertFalse($result['move_to_next']);
        $this->assertEquals('Por favor, digite um email válido.', $result['validation_error']);
    }

    public function test_perform_handles_errors_with_error_handler_service()
    {
        $step = new Step(
            5, $this->organization->id, 1, 'error_step', 'command', \App\Enums\StepType::COMMAND, 5, null, 4,
            false, true, ['command' => 'error_command'], null, null, '{"invalid": "json"}'
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 5);
        $conversation->current_step = $step;

        $messageData = [
            'type' => 'text',
            'text' => ['body' => 'test'],
            'from' => '5511999999999'
        ];

        $mockInteraction = Mockery::mock();
        $mockInteraction->shouldReceive('setAttribute')->andReturnSelf();
        $mockInteraction->result = null;

        $this->mockInteractionFactory
            ->shouldReceive('buildFromWhatsAppMessage')
            ->once()
            ->andReturn($mockInteraction);

        // Mock ExecuteCommand to throw an exception
        $mockExecuteCommand = Mockery::mock();
        $mockExecuteCommand
            ->shouldReceive('perform')
            ->once()
            ->andThrow(new \Exception('Invalid command configuration'));

        $this->app->instance(
            \App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand::class,
            $mockExecuteCommand
        );

        $this->mockErrorHandlerService
            ->shouldReceive('handleError')
            ->once()
            ->andReturn([
                'strategy' => 'retry',
                'action' => 'retry_current_step',
                'message' => 'Ocorreu um erro. Tente novamente.',
                'stay_on_current_step' => true
            ]);

        $result = $this->processFlowStep->perform($conversation, $messageData);

        $this->assertEquals('error', $result['type']);
        $this->assertEquals('retry_current_step', $result['action']);
        $this->assertEquals('retry', $result['error_strategy']);
        $this->assertFalse($result['move_to_next']);
        $this->assertTrue($result['error_handled']);
        $this->assertEquals('Invalid command configuration', $result['original_error']);
    }

    public function test_perform_applies_conditional_navigation()
    {
        $step = new Step(
            6, $this->organization->id, 1, 'interactive_step', 'interactive', \App\Enums\StepType::INTERACTIVE, 6, 7, 5,
            false, false, ['text' => 'Choose an option', 'buttons' => []], null, null, '{}'
        );

        $conversation = new WhatsAppConversation(1, $this->organization->id, 1, 1, 1, 6);
        $conversation->current_step = $step;

        $messageData = [
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button_reply',
                'button_reply' => ['id' => 'btn_yes', 'title' => 'Yes']
            ],
            'from' => '5511999999999'
        ];

        $mockInteraction = Mockery::mock();
        $mockInteraction->shouldReceive('setAttribute')->andReturnSelf();
        $mockInteraction->result = null;

        $this->mockInteractionFactory
            ->shouldReceive('buildFromWhatsAppMessage')
            ->once()
            ->andReturn($mockInteraction);

        $this->mockInteractionRepository
            ->shouldReceive('save')
            ->once()
            ->with($mockInteraction)
            ->andReturn($mockInteraction);

        $this->mockConditionalNavigationService
            ->shouldReceive('shouldApplyConditionalNavigation')
            ->once()
            ->andReturn(true);

        $this->mockConditionalNavigationService
            ->shouldReceive('getConditionalNextStep')
            ->once()
            ->andReturn(10); // Different step due to conditional navigation

        $result = $this->processFlowStep->perform($conversation, $messageData);

        $this->assertEquals('interactive', $result['type']);
        $this->assertTrue($result['move_to_next']);
        $this->assertEquals(10, $result['next_step']); // Should be the conditional target
    }
}
