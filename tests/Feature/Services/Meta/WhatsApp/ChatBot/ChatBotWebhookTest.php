<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use App\Models\Organization;
use App\Models\User;
use App\Models\Client;
use App\Models\PhoneNumber;
use App\Models\Flow;
use App\Models\Step;
use App\Models\Conversation;
use App\Models\Template;
use App\Models\Component;
use App\Services\Meta\WhatsApp\Models\WhatsAppTemplate;

class ChatBotWebhookTest extends TestCase
{
    use RefreshDatabase;

    protected Organization $organization;
    protected User $user;
    protected PhoneNumber $phoneNumber;
    protected Flow $flow;
    protected Step $firstStep;
    protected Step $secondStep;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test organization and user
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);

        // Create test phone number
        $this->phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'whatsapp_phone_number_id' => 'test_phone_123',
            'whatsapp_access_token' => 'test_token_123',
            'is_active' => true
        ]);

        // Create test flow
        $this->flow = Flow::factory()->create([
            'organization_id' => $this->organization->id,
            'phone_number_id' => $this->phoneNumber->id,
            'name' => 'Welcome Flow',
            'is_active' => true
        ]);

        // Create test steps
        $this->firstStep = Step::factory()->create([
            'organization_id' => $this->organization->id,
            'flow_id' => $this->flow->id,
            'name' => 'Welcome Step',
            'type' => 'message',
            'message' => 'Welcome to our service!',
            'step' => 1,
            'is_first' => true
        ]);

        $this->secondStep = Step::factory()->create([
            'organization_id' => $this->organization->id,
            'flow_id' => $this->flow->id,
            'name' => 'Second Step',
            'type' => 'message',
            'message' => 'How can we help you?',
            'step' => 2,
            'is_first' => false
        ]);

        // Link steps
        $this->firstStep->update(['next_step' => $this->secondStep->id]);

        // Update flow to set first step
        $this->flow->update(['first_step_id' => $this->firstStep->id]);

        // Update phone number to use this flow
        $this->phoneNumber->update(['flow_id' => $this->flow->id]);
    }

    public function test_webhook_processing_creates_new_client_and_conversation()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'id' => 'wamid.123456789',
                'text' => [
                    'body' => 'Hello'
                ],
                'type' => 'text',
                'timestamp' => '1234567890'
            ],
            'metadata' => [
                'display_phone_number' => $this->phoneNumber->phone_number,
                'phone_number_id' => $this->phoneNumber->whatsapp_phone_number_id
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'John Doe'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
        ];

        // Act
        $chatBotService = app(ChatBotService::class);
        $result = $chatBotService->processWebhook($webhookData);

        // Assert
        $this->assertEquals('success', $result['status']);
        $this->assertArrayHasKey('step_result', $result);
        $this->assertArrayHasKey('response_result', $result);

        // Verify client was created
        $this->assertDatabaseHas('clients', [
            'phone' => '5511999999999',
            'name' => 'John Doe',
            'organization_id' => $this->organization->id
        ]);

        // Verify conversation was created
        $this->assertDatabaseHas('conversations', [
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $this->firstStep->id,
            'is_finished' => false
        ]);

        // Verify interaction was created
        $this->assertDatabaseHas('interactions', [
            'organization_id' => $this->organization->id,
            'flow_id' => $this->flow->id,
            'step_id' => $this->firstStep->id,
            'message' => 'Hello'
        ]);
    }

    public function test_webhook_processing_finds_existing_client()
    {
        // Arrange
        $existingClient = Client::factory()->create([
            'phone' => '5511888888888',
            'name' => 'Jane Smith',
            'organization_id' => $this->organization->id
        ]);

        $webhookData = [
            'message' => [
                'from' => '5511888888888',
                'id' => 'wamid.987654321',
                'text' => [
                    'body' => 'Hi again'
                ],
                'type' => 'text',
                'timestamp' => '1234567891'
            ],
            'metadata' => [
                'display_phone_number' => $this->phoneNumber->phone_number,
                'phone_number_id' => $this->phoneNumber->whatsapp_phone_number_id
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'Jane Smith Updated'
                    ],
                    'wa_id' => '5511888888888'
                ]
            ]
        ];

        // Act
        $chatBotService = app(ChatBotService::class);
        $result = $chatBotService->processWebhook($webhookData);

        // Assert
        $this->assertEquals('success', $result['status']);

        // Verify existing client was used (not duplicated)
        $clientCount = Client::where('phone', '5511888888888')->count();
        $this->assertEquals(1, $clientCount);

        // Verify client name was updated
        $this->assertDatabaseHas('clients', [
            'id' => $existingClient->id,
            'phone' => '5511888888888',
            'name' => 'Jane Smith Updated',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_webhook_processing_finds_existing_active_conversation()
    {
        // Arrange
        $client = Client::factory()->create([
            'phone' => '5511777777777',
            'organization_id' => $this->organization->id
        ]);

        $existingConversation = Conversation::factory()->create([
            'client_id' => $client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $this->secondStep->id,
            'is_finished' => false
        ]);

        $webhookData = [
            'message' => [
                'from' => '5511777777777',
                'id' => 'wamid.555666777',
                'text' => [
                    'body' => 'Continue conversation'
                ],
                'type' => 'text',
                'timestamp' => '1234567892'
            ],
            'metadata' => [
                'display_phone_number' => $this->phoneNumber->phone_number,
                'phone_number_id' => $this->phoneNumber->whatsapp_phone_number_id
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => $client->name
                    ],
                    'wa_id' => '5511777777777'
                ]
            ]
        ];

        // Act
        $chatBotService = app(ChatBotService::class);
        $result = $chatBotService->processWebhook($webhookData);

        // Assert
        $this->assertEquals('success', $result['status']);

        // Verify existing conversation was used (not duplicated)
        $conversationCount = Conversation::where('client_id', $client->id)
            ->where('phone_number_id', $this->phoneNumber->id)
            ->where('is_finished', false)
            ->count();
        $this->assertEquals(1, $conversationCount);

        // Verify conversation is still on the second step
        $this->assertDatabaseHas('conversations', [
            'id' => $existingConversation->id,
            'current_step_id' => $this->secondStep->id,
            'is_finished' => false
        ]);
    }

    public function test_webhook_processing_handles_interactive_message()
    {
        // Arrange
        $client = Client::factory()->create([
            'phone' => '5511666666666',
            'organization_id' => $this->organization->id
        ]);

        // Create an interactive step with buttons
        $interactiveStep = Step::factory()->create([
            'organization_id' => $this->organization->id,
            'flow_id' => $this->flow->id,
            'name' => 'Interactive Step',
            'type' => 'interactive',
            'message' => 'Choose an option:',
            'step' => 3
        ]);

        $conversation = Conversation::factory()->create([
            'client_id' => $client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'flow_id' => $this->flow->id,
            'current_step_id' => $interactiveStep->id,
            'is_finished' => false
        ]);

        $webhookData = [
            'message' => [
                'from' => '5511666666666',
                'id' => 'wamid.interactive123',
                'interactive' => [
                    'type' => 'button_reply',
                    'button_reply' => [
                        'id' => 'option_1',
                        'title' => 'Yes'
                    ]
                ],
                'type' => 'interactive',
                'timestamp' => '1234567893'
            ],
            'metadata' => [
                'display_phone_number' => $this->phoneNumber->phone_number,
                'phone_number_id' => $this->phoneNumber->whatsapp_phone_number_id
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => $client->name
                    ],
                    'wa_id' => '5511666666666'
                ]
            ]
        ];

        // Act
        $chatBotService = app(ChatBotService::class);
        $result = $chatBotService->processWebhook($webhookData);

        // Assert
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('interactive', $result['step_result']['type']);

        // Verify interaction was created with interactive data
        $this->assertDatabaseHas('interactions', [
            'organization_id' => $this->organization->id,
            'conversation_id' => $conversation->id,
            'step_id' => $interactiveStep->id
        ]);
    }

    public function test_webhook_processing_handles_invalid_phone_number()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511999999999',
                'id' => 'wamid.123456789',
                'text' => [
                    'body' => 'Hello'
                ],
                'type' => 'text'
            ],
            'metadata' => [
                'display_phone_number' => '15551234567',
                'phone_number_id' => 'invalid_phone_id'
            ],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Phone number not found');

        $chatBotService = app(ChatBotService::class);
        $chatBotService->processWebhook($webhookData);
    }

    public function test_webhook_processing_handles_missing_message_data()
    {
        // Arrange
        $webhookData = [
            'metadata' => [
                'phone_number_id' => $this->phoneNumber->whatsapp_phone_number_id
            ],
            'contacts' => []
        ];

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Missing sender phone number');

        $chatBotService = app(ChatBotService::class);
        $chatBotService->processWebhook($webhookData);
    }

    public function test_webhook_processing_creates_proper_whatsapp_metadata()
    {
        // Arrange
        $webhookData = [
            'message' => [
                'from' => '5511555555555',
                'id' => 'wamid.metadata_test',
                'text' => [
                    'body' => 'Test metadata'
                ],
                'type' => 'text',
                'timestamp' => '1234567894'
            ],
            'metadata' => [
                'display_phone_number' => $this->phoneNumber->phone_number,
                'phone_number_id' => $this->phoneNumber->whatsapp_phone_number_id
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'Metadata Test User'
                    ],
                    'wa_id' => '5511555555555'
                ]
            ]
        ];

        // Act
        $chatBotService = app(ChatBotService::class);
        $result = $chatBotService->processWebhook($webhookData);

        // Assert
        $this->assertEquals('success', $result['status']);

        // Verify conversation has WhatsApp metadata
        $conversation = Conversation::where('phone_number_id', $this->phoneNumber->id)
            ->where('is_finished', false)
            ->first();

        $this->assertNotNull($conversation);
        $this->assertNotNull($conversation->json);

        $metadata = json_decode($conversation->json, true);
        $this->assertArrayHasKey('whatsapp_contact_name', $metadata);
        $this->assertEquals('Metadata Test User', $metadata['whatsapp_contact_name']);
    }
}
