<?php

namespace Tests\Feature\Services\Meta\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\TemplateService;
use App\Services\Meta\WhatsApp\MessageService;
use App\UseCases\ChatBot\Campaign\Store as StoreCampaign;
use App\UseCases\ChatBot\Campaign\AddClientsToCampaign;
use App\UseCases\ChatBot\Campaign\LaunchCampaign;
use App\UseCases\ChatBot\Template\Store as StoreTemplate;
use App\UseCases\ChatBot\Template\PublishTemplate;
use App\Enums\PublishingService;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Parameter;
use App\Domains\Inventory\Client;
use App\Models\Organization;
use App\Models\User;
use App\Models\PhoneNumber as PhoneNumberModel;
use App\Models\Template as TemplateModel;
use App\Models\Client as ClientModel;
use App\Models\Campaign as CampaignModel;
use App\Models\Message as MessageModel;
use App\Http\Requests\Campaign\StoreRequest as CampaignStoreRequest;
use App\Http\Requests\Template\StoreRequest as TemplateStoreRequest;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\GuzzleException;
use Mockery;

class CampaignFlowTest extends TestCase
{
    use RefreshDatabase;

    private Organization $organization;
    private User $user;
    private PhoneNumberModel $phoneNumber;
    private array $clients;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    /**
     * Test the complete campaign flow from start to finish
     */
    public function test_complete_campaign_flow()
    {
        // Step 1: Create and publish a template
        $template = $this->createAndPublishTemplate();
        $this->assertNotNull($template);
        $this->assertTrue($template->isWhatsAppPublished());

        // Step 2: Create a campaign
        $campaign = $this->createCampaign($template->id);
        $this->assertNotNull($campaign);
        $this->assertEquals($template->id, $campaign->template_id);

        // Step 3: Add clients to campaign
        $campaignWithClients = $this->addClientsToCompaign($campaign->id);
        $this->assertNotNull($campaignWithClients->clients);
        $this->assertCount(3, $campaignWithClients->clients);

        // Step 4: Launch campaign (generate messages)
        $launchedCampaign = $this->launchCampaign($campaign->id);
        $this->assertTrue($launchedCampaign->is_sending);
        $this->assertNotNull($launchedCampaign->sent_at);
        $this->assertEquals(3, $launchedCampaign->message_count);

        // Step 5: Verify messages were created with correct WhatsApp payload
        $this->verifyMessagesCreated($campaign->id);

        // Step 6: Simulate message sending via cron job
        $this->simulateMessageSending();

        $this->assertTrue(true, "Complete campaign flow executed successfully");
    }

    private function setupTestData(): void
    {
        // Create organization and user
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Create phone number with WhatsApp credentials
        $this->phoneNumber = PhoneNumberModel::create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'phone_number' => '+5511999999999',
            'name' => 'Test WhatsApp Number',
            'description' => 'Test phone number for campaigns',
            'is_active' => true,
            'whatsapp_phone_number_id' => 'test_phone_id_123',
            'whatsapp_access_token' => 'test_token_456'
        ]);

        // Create test clients
        $this->clients = [
            ClientModel::create([
                'organization_id' => $this->organization->id,
                'name' => 'João Silva',
                'phone' => '+5511888888888',
                'email' => '<EMAIL>'
            ]),
            ClientModel::create([
                'organization_id' => $this->organization->id,
                'name' => 'Maria Santos',
                'phone' => '+5511777777777',
                'email' => '<EMAIL>'
            ]),
            ClientModel::create([
                'organization_id' => $this->organization->id,
                'name' => 'Pedro Costa',
                'phone' => '+5511666666666',
                'email' => '<EMAIL>'
            ])
        ];

        // Authenticate as the test user
        $this->actingAs($this->user);
    }

    private function createAndPublishTemplate(): Template
    {
        // We're creating the template directly in the database, so no API mocking needed for template creation

        // Create template directly using Eloquent model to avoid authentication issues
        $templateModel = TemplateModel::create([
            'name' => 'test_campaign_template',
            'category' => 'MARKETING',
            'language' => 'pt_BR',
            'phone_number_id' => $this->phoneNumber->id,
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'status' => 'active'
        ]);

        // Create WhatsAppTemplate record to mark template as published
        \App\Services\Meta\WhatsApp\Models\WhatsAppTemplate::create([
            'template_id' => $templateModel->id,
            'status' => 'APPROVED',
            'external_id' => 'whatsapp_template_123',
            'json' => json_encode([
                'id' => 'whatsapp_template_123',
                'status' => 'APPROVED',
                'category' => 'MARKETING'
            ])
        ]);

        // Create a simple template domain object manually
        $template = new Template(
            $templateModel->id,
            $templateModel->organization_id,
            $templateModel->phone_number_id,
            $templateModel->user_id,
            null, // client_id
            $templateModel->name,
            $templateModel->category,
            null, // parameter_format
            $templateModel->language,
            null, // library_template_name
            null, // id_external
            $templateModel->status,
            $templateModel->created_at ? \Carbon\Carbon::parse($templateModel->created_at) : null,
            $templateModel->updated_at ? \Carbon\Carbon::parse($templateModel->updated_at) : null,
            true, // is_whatsapp_published
            [], // components - simplified for testing
            null // phone_number
        );

        return $template;
    }

    private function createCampaign(int $template_id): \App\Domains\ChatBot\Campaign
    {
        // Create campaign directly using Eloquent model to avoid authentication issues
        $campaignModel = CampaignModel::create([
            'name' => 'Test Marketing Campaign',
            'description' => 'Test campaign for WhatsApp integration',
            'template_id' => $template_id,
            'phone_number_id' => $this->phoneNumber->id,
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'is_scheduled' => false,
            'is_sent' => false,
            'is_sending' => false,
            'message_count' => 0
        ]);

        // Convert to domain object
        $campaignFactory = app()->make(\App\Factories\ChatBot\CampaignFactory::class);
        return $campaignFactory->buildFromModel($campaignModel);
    }

    private function addClientsToCompaign(int $campaign_id): \App\Domains\ChatBot\Campaign
    {
        $client_ids = array_map(fn($client) => $client->id, $this->clients);

        // Add clients directly to campaign using Eloquent relationship
        $campaignModel = CampaignModel::findOrFail($campaign_id);
        $campaignModel->clients()->sync($client_ids);

        // Return updated campaign domain
        $campaignFactory = app()->make(\App\Factories\ChatBot\CampaignFactory::class);
        return $campaignFactory->buildFromModel($campaignModel, true, false, true, false, false);
    }

    private function launchCampaign(int $campaign_id): \App\Domains\ChatBot\Campaign
    {
        // Get campaign with clients
        $campaignModel = CampaignModel::with(['clients', 'template'])->findOrFail($campaign_id);

        // Generate messages for each client
        $messages = [];
        foreach ($campaignModel->clients as $clientModel) {
            $messageModel = MessageModel::create([
                'organization_id' => $this->organization->id,
                'campaign_id' => $campaign_id,
                'template_id' => $campaignModel->template_id,
                'client_id' => $clientModel->id,
                'status' => \App\Enums\MessageStatus::is_sending->value,
                'is_sent' => false,
                'is_fail' => false,
                'is_read' => false
            ]);
            $messages[] = $messageModel;
        }

        // Update campaign status
        $campaignModel->update([
            'is_sending' => true,
            'sent_at' => now(),
            'message_count' => count($messages)
        ]);

        // Return updated campaign domain
        $campaignFactory = app()->make(\App\Factories\ChatBot\CampaignFactory::class);
        return $campaignFactory->buildFromModel($campaignModel, true, true, true, false, false);
    }

    private function verifyMessagesCreated(int $campaign_id): void
    {
        $messages = MessageModel::where('campaign_id', $campaign_id)->get();

        $this->assertCount(3, $messages, "Should have created 3 messages for 3 clients");

        foreach ($messages as $message) {
            $this->assertNotNull($message->client_id);
            $this->assertEquals($campaign_id, $message->campaign_id);
            $this->assertEquals(\App\Enums\MessageStatus::is_sending, $message->status);

            // Verify the message has correct WhatsApp payload structure
            $this->assertNotNull($message->client);
            $this->assertNotNull($message->template);
        }
    }

    private function simulateMessageSending(): void
    {
        // Mock the MessageService directly instead of GuzzleClient
        $mockMessageService = Mockery::mock(MessageService::class);
        $mockResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [
                [
                    'input' => '+5511888888888',
                    'wa_id' => '5511888888888'
                ]
            ],
            'messages' => [
                [
                    'id' => 'whatsapp_message_123'
                ]
            ]
        ];

        $mockMessageService->shouldReceive('send')
            ->atLeast(1)
            ->andReturn($mockResponse);

        $this->app->instance(MessageService::class, $mockMessageService);

        // Get pending messages and simulate sending
        $pendingMessages = MessageModel::where('status', \App\Enums\MessageStatus::is_sending->value)->get();

        /** @var MessageService $messageService */
        $messageService = app()->make(MessageService::class);

        foreach ($pendingMessages as $messageModel) {
            try {
                // Load the message with all relationships
                $messageWithRelations = MessageModel::with(['client', 'template', 'campaign'])->find($messageModel->id);

                // Convert to domain object and send
                $messageDomain = app()->make(\App\Factories\ChatBot\MessageFactory::class)
                    ->buildFromModel($messageWithRelations, true, true, true);

                // Ensure the template is marked as published for WhatsApp
                if ($messageDomain->template) {
                    $messageDomain->template->is_whatsapp_published = true;
                }

                $response = $messageService->send($messageDomain);

                // Update message status
                $messageModel->update([
                    'status' => \App\Enums\MessageStatus::is_sent->value,
                    'is_sent' => true,
                    'sent_at' => now(),
                    'external_id' => $response['messages'][0]['id'] ?? null
                ]);

            } catch (\Exception $e) {
                // For debugging, let's see what the error is
                echo "Message sending failed: " . $e->getMessage() . "\n";
                $messageModel->update([
                    'status' => \App\Enums\MessageStatus::is_failed->value,
                    'is_fail' => true
                ]);
            }
        }

        // Verify all messages were sent
        $sentMessages = MessageModel::where('status', \App\Enums\MessageStatus::is_sent->value)->count();
        $this->assertEquals(3, $sentMessages, "All 3 messages should be sent");
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
