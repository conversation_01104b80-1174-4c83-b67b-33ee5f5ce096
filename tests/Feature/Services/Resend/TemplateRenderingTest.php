<?php

namespace Tests\Feature\Services\Resend;

use Tests\TestCase;
use App\Services\Resend\Domains\PasswordResetEmail;
use App\Services\Resend\Domains\CampaignReportEmail;
use App\Services\Resend\Domains\WelcomeEmail;
use App\Services\Resend\Domains\InvoiceEmail;
use App\Domains\User;
use App\Domains\Organization;
use App\Domains\ChatBot\Campaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class TemplateRenderingTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Campaign $campaign;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = new Organization(
            1,
            'Test Organization',
            'Test Description',
            true,
            false,
            null,
            Carbon::now(),
            Carbon::now()
        );

        $this->user = new User(
            1,
            1,
            1,
            'John',
            'Doe',
            'johndoe',
            '<EMAIL>',
            'password123',
            '12345678901',
            '+1234567890',
            'test-token',
            $this->organization
        );

        $this->campaign = new Campaign(
            1,
            1,
            1,
            1,
            1,
            'Test Campaign',
            'Test Campaign Description'
        );
    }

    public function test_password_reset_template_renders_correctly()
    {
        $email = new PasswordResetEmail($this->user, 'reset-token-123', 60);
        $templateData = $email->getTemplateData();

        $html = view($email->getTemplatePath(), $templateData)->render();

        // Check that template renders without errors
        $this->assertIsString($html);
        $this->assertNotEmpty($html);

        // Check that user data is present
        $this->assertStringContainsString('John Doe', $html);
        $this->assertStringContainsString('john%40example.com', $html); // URL encoded email

        // Check that reset URL is present and correctly formatted
        $this->assertStringContainsString('password/reset/reset-token-123', $html);
        $this->assertStringContainsString('email=john%40example.com', $html);

        // Check that expiry time is present
        $this->assertStringContainsString('60 minutos', $html);

        // Check that security information is present
        $this->assertStringContainsString('Redefinir Senha', $html);
        $this->assertStringContainsString('motivos de segurança', $html);

        // Check that HTML structure is valid
        $this->assertStringContainsString('<html', $html);
        $this->assertStringContainsString('</html>', $html);
        $this->assertStringContainsString('<body', $html);
        $this->assertStringContainsString('</body>', $html);
    }

    public function test_campaign_report_template_renders_correctly()
    {
        $metrics = [
            'Mensagens Enviadas' => 1500,
            'Taxa de Entrega' => '98.5%',
            'Respostas Recebidas' => 45,
            'Taxa de Conversão' => '3.2%'
        ];

        $email = new CampaignReportEmail(
            $this->campaign,
            $metrics,
            '01/01/2024 - 31/01/2024',
            $this->organization,
            $this->user,
            Carbon::parse('2024-01-31 15:30:00')
        );

        $templateData = $email->getTemplateData();
        $html = view($email->getTemplatePath(), $templateData)->render();

        // Check basic rendering
        $this->assertIsString($html);
        $this->assertNotEmpty($html);

        // Check campaign information
        $this->assertStringContainsString('Test Campaign', $html);
        $this->assertStringContainsString('01/01/2024 - 31/01/2024', $html);
        $this->assertStringContainsString('Test Organization', $html);
        $this->assertStringContainsString('31/01/2024 15:30:00', $html);

        // Check metrics table
        $this->assertStringContainsString('Mensagens Enviadas', $html);
        $this->assertStringContainsString('1500', $html);
        $this->assertStringContainsString('Taxa de Entrega', $html);
        $this->assertStringContainsString('98.5%', $html);
        $this->assertStringContainsString('Respostas Recebidas', $html);
        $this->assertStringContainsString('45', $html);
        $this->assertStringContainsString('Taxa de Conversão', $html);
        $this->assertStringContainsString('3.2%', $html);

        // Check table structure
        $this->assertStringContainsString('<table', $html);
        $this->assertStringContainsString('<thead>', $html);
        $this->assertStringContainsString('<tbody>', $html);
        $this->assertStringContainsString('Métrica', $html);
        $this->assertStringContainsString('Valor', $html);
    }

    public function test_welcome_template_renders_correctly()
    {
        $nextSteps = [
            'Configurar seu perfil pessoal',
            'Explorar o dashboard principal',
            'Criar sua primeira campanha'
        ];

        $email = new WelcomeEmail(
            $this->user,
            $this->organization,
            $nextSteps,
            '<EMAIL>',
            'https://example.com/getting-started'
        );

        $templateData = $email->getTemplateData();
        $html = view($email->getTemplatePath(), $templateData)->render();

        // Check basic rendering
        $this->assertIsString($html);
        $this->assertNotEmpty($html);

        // Check user and organization information
        $this->assertStringContainsString('John Doe', $html);
        $this->assertStringContainsString('Test Organization', $html);

        // Check welcome message
        $this->assertStringContainsString('Bem-vindo', $html);
        $this->assertStringContainsString('prazer tê-lo', $html);

        // Check next steps
        $this->assertStringContainsString('Configurar seu perfil pessoal', $html);
        $this->assertStringContainsString('Explorar o dashboard principal', $html);
        $this->assertStringContainsString('Criar sua primeira campanha', $html);

        // Check links
        $this->assertStringContainsString('https://example.com/getting-started', $html);
        $this->assertStringContainsString('<EMAIL>', $html);

        // Check call-to-action
        $this->assertStringContainsString('Começar Agora', $html);
    }

    public function test_invoice_template_renders_correctly()
    {
        $invoice = (object) ['id' => 12345];
        $client = (object) ['name' => 'Test Client', 'email' => '<EMAIL>'];
        $dueDate = Carbon::parse('2024-02-15');
        $paymentLink = 'https://payment.example.com/invoice/12345';
        $items = [
            [
                'description' => 'Serviço de Consultoria',
                'quantity' => 10,
                'unit_price' => 150.00,
                'total' => 1500.00
            ],
            [
                'description' => 'Taxa de Setup',
                'quantity' => 1,
                'unit_price' => 200.00,
                'total' => 200.00
            ]
        ];

        $email = new InvoiceEmail(
            $invoice,
            $client,
            $dueDate,
            $paymentLink,
            $items,
            1700.00,
            $this->organization
        );

        $templateData = $email->getTemplateData();
        $html = view($email->getTemplatePath(), $templateData)->render();

        // Check basic rendering
        $this->assertIsString($html);
        $this->assertNotEmpty($html);

        // Check invoice information
        $this->assertStringContainsString('Test Client', $html);
        $this->assertStringContainsString('#12345', $html);
        $this->assertStringContainsString('15/02/2024', $html);
        $this->assertStringContainsString('Test Organization', $html);

        // Check total amount formatting
        $this->assertStringContainsString('R$ 1.700,00', $html);

        // Check items table
        $this->assertStringContainsString('Serviço de Consultoria', $html);
        $this->assertStringContainsString('Taxa de Setup', $html);
        $this->assertStringContainsString('R$ 150,00', $html);
        $this->assertStringContainsString('R$ 200,00', $html);
        $this->assertStringContainsString('R$ 1.500,00', $html);

        // Check payment link
        $this->assertStringContainsString('https://payment.example.com/invoice/12345', $html);
        $this->assertStringContainsString('Pagar Agora', $html);

        // Check table structure
        $this->assertStringContainsString('<table', $html);
        $this->assertStringContainsString('Descrição', $html);
        $this->assertStringContainsString('Quantidade', $html);
        $this->assertStringContainsString('Valor Unitário', $html);
        $this->assertStringContainsString('Total', $html);
    }

    public function test_all_templates_extend_base_layout()
    {
        $emails = [
            new PasswordResetEmail($this->user, 'token'),
            new CampaignReportEmail($this->campaign, [], 'period', $this->organization, $this->user),
            new WelcomeEmail($this->user, $this->organization),
            new InvoiceEmail(
                (object) ['id' => 1],
                (object) ['name' => 'Client', 'email' => '<EMAIL>'],
                Carbon::now(),
                'https://pay.example.com',
                [],
                0.0,
                $this->organization
            )
        ];

        foreach ($emails as $email) {
            $templateData = $email->getTemplateData();
            $html = view($email->getTemplatePath(), $templateData)->render();

            // Check that all templates include base layout elements
            $this->assertStringContainsString('<!DOCTYPE html>', $html);
            $this->assertStringContainsString('<meta charset="utf-8">', $html);
            $this->assertStringContainsString('<meta name="viewport"', $html);
            $this->assertStringContainsString('Obvio', $html);
            $this->assertStringContainsString('Todos os direitos reservados', $html);

            // Check CSS styling is present
            $this->assertStringContainsString('<style>', $html);
            $this->assertStringContainsString('font-family:', $html);
            $this->assertStringContainsString('.container', $html);
            $this->assertStringContainsString('.header', $html);
            $this->assertStringContainsString('.content', $html);
            $this->assertStringContainsString('.footer', $html);
        }
    }

    public function test_templates_handle_missing_optional_data()
    {
        // Test campaign report without description
        $campaignWithoutDescription = new Campaign(1, 1, 1, 1, 1, 'Test Campaign', null);
        $email = new CampaignReportEmail(
            $campaignWithoutDescription,
            ['test' => 'value'],
            'period',
            $this->organization,
            $this->user
        );

        $templateData = $email->getTemplateData();
        $html = view($email->getTemplatePath(), $templateData)->render();

        $this->assertIsString($html);
        $this->assertNotEmpty($html);

        // Test invoice without items
        $invoiceEmail = new InvoiceEmail(
            (object) ['id' => 1],
            (object) ['name' => 'Client', 'email' => '<EMAIL>'],
            Carbon::now(),
            'https://pay.example.com',
            [], // empty items
            0.0,
            $this->organization
        );

        $templateData = $invoiceEmail->getTemplateData();
        $html = view($invoiceEmail->getTemplatePath(), $templateData)->render();

        $this->assertIsString($html);
        $this->assertNotEmpty($html);
    }
}
