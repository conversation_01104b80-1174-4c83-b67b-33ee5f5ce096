<?php

namespace Tests\Feature\Services\Resend;

use Tests\TestCase;
use App\Services\Resend\UseCases\Send;
use App\Services\Resend\Domains\PasswordResetEmail;
use App\Services\Resend\Domains\CampaignReportEmail;
use App\Services\Resend\Domains\WelcomeEmail;
use App\Services\Resend\Domains\InvoiceEmail;
use App\Domains\User;
use App\Domains\Organization;
use App\Domains\ChatBot\Campaign;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class EmailTypesTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Campaign $campaign;

    protected function setUp(): void
    {
        parent::setUp();

        Config::set('resend.api_key', 'test-api-key');
        Config::set('resend.sandbox_mode', true);

        $this->organization = new Organization(
            1,
            'Test Organization',
            'Test Description',
            true,
            false,
            null,
            Carbon::now(),
            Carbon::now()
        );

        $this->user = new User(
            1,
            1,
            1,
            'John',
            'Doe',
            'johndoe',
            '<EMAIL>',
            'password123',
            '12345678901',
            '+1234567890',
            'test-token',
            $this->organization
        );

        $this->campaign = new Campaign(
            1,
            1,
            1,
            1,
            1,
            'Test Campaign',
            'Test Campaign Description'
        );
    }

    public function test_password_reset_email_type()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'password-reset-id'], 200)
        ]);

        $sendUseCase = app()->make(Send::class);
        $email = new PasswordResetEmail($this->user, 'reset-token-123', 60);

        $result = $sendUseCase->perform($email);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $html = $data['html'];

            return $data['subject'] === 'Redefinir sua senha - ' . config('app.name') &&
                   $data['tags']['type'] === 'auth' &&
                   $data['tags']['priority'] === 'high' &&
                   $data['tags']['user_id'] === 1 &&
                   strpos($html, 'John Doe') !== false &&
                   strpos($html, 'reset-token-123') !== false &&
                   strpos($html, '60 minutos') !== false;
        });

        $this->assertEquals('password-reset-id', $result['id']);
    }

    public function test_campaign_report_email_type()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'campaign-report-id'], 200)
        ]);

        $metrics = [
            'Mensagens Enviadas' => 1500,
            'Taxa de Entrega' => '98.5%',
            'Respostas Recebidas' => 45,
            'Taxa de Conversão' => '3.2%'
        ];

        $sendUseCase = app()->make(Send::class);
        $email = new CampaignReportEmail(
            $this->campaign,
            $metrics,
            '01/01/2024 - 31/01/2024',
            $this->organization,
            $this->user
        );

        $result = $sendUseCase->perform($email);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $html = $data['html'];

            return strpos($data['subject'], 'Test Campaign') !== false &&
                   $data['tags']['type'] === 'report' &&
                   $data['tags']['priority'] === 'medium' &&
                   $data['tags']['campaign_id'] === 1 &&
                   $data['tags']['organization_id'] === 1 &&
                   strpos($html, 'Test Campaign') !== false &&
                   strpos($html, '01/01/2024 - 31/01/2024') !== false &&
                   strpos($html, '1500') !== false &&
                   strpos($html, '98.5%') !== false;
        });

        $this->assertEquals('campaign-report-id', $result['id']);
    }

    public function test_welcome_email_type()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'welcome-id'], 200)
        ]);

        $customNextSteps = [
            'Configurar seu perfil',
            'Criar primeira campanha',
            'Convidar equipe'
        ];

        $sendUseCase = app()->make(Send::class);
        $email = new WelcomeEmail(
            $this->user,
            $this->organization,
            $customNextSteps,
            '<EMAIL>',
            'https://example.com/getting-started'
        );

        $result = $sendUseCase->perform($email);

        Http::assertSent(function ($request) {
            $data = $request->data();
            $html = $data['html'];

            return $data['subject'] === 'Bem-vindo ao ' . config('app.name') . '!' &&
                   $data['tags']['type'] === 'onboarding' &&
                   $data['tags']['priority'] === 'medium' &&
                   $data['tags']['user_id'] === 1 &&
                   $data['tags']['organization_id'] === 1 &&
                   $data['reply_to'] === '<EMAIL>' &&
                   strpos($html, 'John Doe') !== false &&
                   strpos($html, 'Test Organization') !== false &&
                   strpos($html, 'Configurar seu perfil') !== false &&
                   strpos($html, 'https://example.com/getting-started') !== false;
        });

        $this->assertEquals('welcome-id', $result['id']);
    }

    public function test_invoice_email_type()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'invoice-id'], 200)
        ]);

        $invoice = (object) ['id' => 12345];
        $client = (object) ['name' => 'Test Client', 'email' => '<EMAIL>'];
        $dueDate = Carbon::now()->addDays(30);
        $paymentLink = 'https://payment.example.com/invoice/12345';
        $items = [
            [
                'description' => 'Serviço de Consultoria',
                'quantity' => 10,
                'unit_price' => 150.00,
                'total' => 1500.00
            ],
            [
                'description' => 'Taxa de Setup',
                'quantity' => 1,
                'unit_price' => 200.00,
                'total' => 200.00
            ]
        ];

        $sendUseCase = app()->make(Send::class);
        $email = new InvoiceEmail(
            $invoice,
            $client,
            $dueDate,
            $paymentLink,
            $items,
            1700.00,
            $this->organization
        );

        $result = $sendUseCase->perform($email);

        Http::assertSent(function ($request) use ($dueDate) {
            $data = $request->data();
            $html = $data['html'];

            return $data['subject'] === 'Fatura #12345 - ' . config('app.name') &&
                   $data['tags']['type'] === 'billing' &&
                   $data['tags']['priority'] === 'high' &&
                   $data['tags']['invoice_id'] === 12345 &&
                   $data['tags']['organization_id'] === 1 &&
                   $data['to'][0]['email'] === '<EMAIL>' && // sandbox mode
                   strpos($html, 'Test Client') !== false &&
                   strpos($html, '#12345') !== false &&
                   strpos($html, 'R$ 1.700,00') !== false &&
                   strpos($html, 'Serviço de Consultoria') !== false &&
                   strpos($html, 'https://payment.example.com/invoice/12345') !== false &&
                   strpos($html, $dueDate->format('d/m/Y')) !== false;
        });

        $this->assertEquals('invoice-id', $result['id']);
    }

    public function test_all_email_types_have_required_fields()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $sendUseCase = app()->make(Send::class);

        $emails = [
            new PasswordResetEmail($this->user, 'token'),
            new CampaignReportEmail($this->campaign, [], 'period', $this->organization, $this->user),
            new WelcomeEmail($this->user, $this->organization),
            new InvoiceEmail(
                (object) ['id' => 1],
                (object) ['name' => 'Client', 'email' => '<EMAIL>'],
                Carbon::now(),
                'https://pay.example.com',
                [],
                0.0,
                $this->organization
            )
        ];

        foreach ($emails as $email) {
            $sendUseCase->perform($email);

            Http::assertSent(function ($request) {
                $data = $request->data();

                // All emails must have these required fields
                return isset($data['from']) &&
                       isset($data['to']) &&
                       isset($data['subject']) &&
                       isset($data['html']) &&
                       isset($data['tags']) &&
                       !empty($data['from']) &&
                       !empty($data['to']) &&
                       !empty($data['subject']) &&
                       !empty($data['html']) &&
                       is_array($data['tags']) &&
                       isset($data['tags']['type']) &&
                       isset($data['tags']['priority']);
            });
        }
    }

    public function test_email_templates_render_without_errors()
    {
        Http::fake([
            'https://api.resend.com/emails' => Http::response(['id' => 'test-id'], 200)
        ]);

        $sendUseCase = app()->make(Send::class);

        $emails = [
            new PasswordResetEmail($this->user, 'token'),
            new CampaignReportEmail($this->campaign, ['test' => 'value'], 'period', $this->organization, $this->user),
            new WelcomeEmail($this->user, $this->organization),
            new InvoiceEmail(
                (object) ['id' => 1],
                (object) ['name' => 'Client', 'email' => '<EMAIL>'],
                Carbon::now(),
                'https://pay.example.com',
                [['description' => 'Test', 'quantity' => 1, 'unit_price' => 100, 'total' => 100]],
                100.0,
                $this->organization
            )
        ];

        foreach ($emails as $email) {
            $result = $sendUseCase->perform($email);
            $this->assertIsArray($result);
            $this->assertEquals('test-id', $result['id']);
        }
    }
}
