<?php

namespace Tests\Feature\Migrations;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\PhoneNumber;
use App\Models\Organization;

class AddIsChatbotActivatedToPhoneNumbersTest extends TestCase
{
    use RefreshDatabase;

    public function test_migration_adds_is_chatbot_activated_column()
    {
        // Verify the column exists after migration
        $this->assertTrue(Schema::hasColumn('phone_numbers', 'is_chatbot_activated'));
    }

    public function test_migration_sets_default_value_for_existing_records()
    {
        // Create an organization first
        $organization = Organization::factory()->create();

        // Create a phone number record using raw database insert to simulate existing record
        DB::table('phone_numbers')->insert([
            'organization_id' => $organization->id,
            'phone_number' => '+5511999999999',
            'name' => 'Test Phone',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Get the record and verify that the default value is set to true
        $phoneNumber = PhoneNumber::where('phone_number', '+5511999999999')->first();
        $this->assertTrue($phoneNumber->is_chatbot_activated);
    }

    public function test_migration_column_is_boolean_type()
    {
        $columnType = Schema::getColumnType('phone_numbers', 'is_chatbot_activated');
        $this->assertEquals('boolean', $columnType);
    }

    public function test_migration_column_is_nullable()
    {
        $columns = Schema::getColumnListing('phone_numbers');
        $this->assertContains('is_chatbot_activated', $columns);

        // Test that we can insert null values (for compatibility)
        $organization = Organization::factory()->create();

        DB::table('phone_numbers')->insert([
            'organization_id' => $organization->id,
            'phone_number' => '+5511999999999',
            'name' => 'Test Phone',
            'is_active' => true,
            'is_chatbot_activated' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $record = DB::table('phone_numbers')->where('phone_number', '+5511999999999')->first();
        $this->assertNull($record->is_chatbot_activated);
    }

    public function test_migration_column_position()
    {
        $columns = Schema::getColumnListing('phone_numbers');
        $isActiveIndex = array_search('is_active', $columns);
        $isChatbotActivatedIndex = array_search('is_chatbot_activated', $columns);

        // Verify that is_chatbot_activated comes after is_active
        $this->assertGreaterThan($isActiveIndex, $isChatbotActivatedIndex);
    }

    public function test_model_casts_is_chatbot_activated_as_boolean()
    {
        $organization = Organization::factory()->create();
        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'is_chatbot_activated' => 1
        ]);

        $this->assertIsBool($phoneNumber->is_chatbot_activated);
        $this->assertTrue($phoneNumber->is_chatbot_activated);
    }

    public function test_model_fillable_includes_is_chatbot_activated()
    {
        $phoneNumber = new PhoneNumber();
        $fillable = $phoneNumber->getFillable();

        $this->assertContains('is_chatbot_activated', $fillable);
    }

    public function test_database_factory_sets_default_true()
    {
        $organization = Organization::factory()->create();
        $phoneNumber = PhoneNumber::factory()->create(['organization_id' => $organization->id]);

        $this->assertTrue($phoneNumber->is_chatbot_activated);
    }

    public function test_database_factory_can_override_default()
    {
        $organization = Organization::factory()->create();
        $phoneNumber = PhoneNumber::factory()->create([
            'organization_id' => $organization->id,
            'is_chatbot_activated' => false
        ]);

        $this->assertFalse($phoneNumber->is_chatbot_activated);
    }
}
