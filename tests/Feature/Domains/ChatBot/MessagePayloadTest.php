<?php

namespace Tests\Feature\Domains\ChatBot;

use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use App\Enums\ComponentFormat;
use Tests\TestCase;

class MessagePayloadTest extends TestCase
{
    private function createClient(): Client
    {
        return new Client(
            id: 1,
            organization_id: 1,
            name: '<PERSON>',
            phone: '+5511999887766',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: '123 Main St',
            number: null,
            neighborhood: 'Centro',
            cep: '01234-567',
            complement: null,
            civil_state: null,
            description: null
        );
    }

    private function createPhoneNumber(): PhoneNumber
    {
        return new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511987654321',
            name: 'Test Phone',
            description: null,
            is_active: true,
            is_chatbot_activated: true,
            whatsapp_phone_number_id: 'test_phone_id',
            whatsapp_business_id: 'test_business_id',
            whatsapp_access_token: 'test_token'
        );
    }

    private function createTemplate(): Template
    {
        $headerComponent = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Header',
            type: 'HEADER',
            sub_type: null,
            index: 0,
            text: 'Welcome {{client.name}}!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $bodyComponent = new Component(
            id: 2,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body',
            type: 'BODY',
            sub_type: null,
            index: 1,
            text: 'Hello {{client.name}}, your email is {{client.email}}. Thank you for choosing us!',
            format: ComponentFormat::TEXT,
            json: null
        );

        $footerComponent = new Component(
            id: 3,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Footer',
            type: 'FOOTER',
            sub_type: null,
            index: 2,
            text: 'Contact us at {{client.phone}}',
            format: ComponentFormat::TEXT,
            json: null
        );

        return new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: null,
            client_id: null,
            name: 'welcome_template',
            category: 'UTILITY',
            parameter_format: null,
            language: 'en_US',
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            components: [$headerComponent, $bodyComponent, $footerComponent],
            phone_number: $this->createPhoneNumber()
        );
    }

    public function test_message_to_whatsapp_template_payload()
    {
        $client = $this->createClient();
        $template = $this->createTemplate();

        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: 1,
            client_id: 1,
            message: 'Template message',
            client: $client,
            template: $template
        );

        $payload = $message->toWhatsAppTemplatePayload();

        $this->assertIsArray($payload);
        $this->assertEquals('whatsapp', $payload['messaging_product']);
        $this->assertEquals($client->phone, $payload['to']);
        $this->assertEquals('template', $payload['type']);

        $this->assertArrayHasKey('template', $payload);
        $templatePayload = $payload['template'];

        $this->assertEquals('welcome_template', $templatePayload['name']);
        $this->assertEquals('en_US', $templatePayload['language']['code']); // Template language
        $this->assertArrayHasKey('components', $templatePayload);
        $this->assertCount(3, $templatePayload['components']);

        // Check header component
        $headerComponent = $templatePayload['components'][0];
        $this->assertEquals('header', $headerComponent['type']);
        $this->assertArrayHasKey('parameters', $headerComponent);
        $this->assertCount(1, $headerComponent['parameters']);
        $this->assertEquals('text', $headerComponent['parameters'][0]['type']);
        $this->assertEquals('John Doe', $headerComponent['parameters'][0]['text']);

        // Check body component
        $bodyComponent = $templatePayload['components'][1];
        $this->assertEquals('body', $bodyComponent['type']);
        $this->assertArrayHasKey('parameters', $bodyComponent);
        $this->assertCount(2, $bodyComponent['parameters']);
        $this->assertEquals('John Doe', $bodyComponent['parameters'][0]['text']);
        $this->assertEquals('<EMAIL>', $bodyComponent['parameters'][1]['text']);

        // Check footer component
        $footerComponent = $templatePayload['components'][2];
        $this->assertEquals('footer', $footerComponent['type']);
        $this->assertArrayHasKey('parameters', $footerComponent);
        $this->assertCount(1, $footerComponent['parameters']);
        $this->assertEquals('+5511999887766', $footerComponent['parameters'][0]['text']);
    }

    public function test_message_to_whatsapp_template_payload_with_custom_language()
    {
        $client = $this->createClient();
        $template = $this->createTemplate();
        $template->language = 'en_US';

        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: 1,
            client_id: 1,
            message: 'Template message',
            client: $client,
            template: $template
        );

        $payload = $message->toWhatsAppTemplatePayload();

        $this->assertEquals('en_US', $payload['template']['language']['code']);
    }

    public function test_message_to_whatsapp_text_payload()
    {
        $client = $this->createClient();

        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: 1,
            message: 'Hello {{client.name}}, this is a text message!',
            client: $client
        );

        $payload = $message->toWhatsAppTextPayload();

        $this->assertIsArray($payload);
        $this->assertEquals('whatsapp', $payload['messaging_product']);
        $this->assertEquals($client->phone, $payload['to']);
        $this->assertEquals('text', $payload['type']);

        $this->assertArrayHasKey('text', $payload);
        $this->assertArrayHasKey('body', $payload['text']);
        $this->assertEquals('Hello John Doe, this is a text message!', $payload['text']['body']);
    }

    public function test_message_to_whatsapp_payload_chooses_correct_type()
    {
        $client = $this->createClient();
        $template = $this->createTemplate();

        // Message with template should use template payload
        $templateMessage = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: 1,
            client_id: 1,
            message: 'Template message',
            client: $client,
            template: $template
        );

        $templatePayload = $templateMessage->toWhatsAppPayload();
        $this->assertEquals('template', $templatePayload['type']);

        // Message without template should use text payload
        $textMessage = new Message(
            id: 2,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: 1,
            message: 'Hello world!',
            client: $client
        );

        $textPayload = $textMessage->toWhatsAppPayload();
        $this->assertEquals('text', $textPayload['type']);
    }

    public function test_message_template_payload_without_template_throws_exception()
    {
        $client = $this->createClient();

        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: null,
            client_id: 1,
            message: 'Template message',
            client: $client
        );

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Template is required for template message');

        $message->toWhatsAppTemplatePayload();
    }

    public function test_message_template_payload_with_no_variables()
    {
        $client = $this->createClient();

        $bodyComponent = new Component(
            id: 1,
            organization_id: 1,
            step_id: null,
            template_id: 1,
            name: 'Body',
            type: 'BODY',
            sub_type: null,
            index: 0,
            text: 'Welcome to our service! We are glad to have you.',
            format: ComponentFormat::TEXT,
            json: null
        );

        $template = new Template(
            id: 1,
            organization_id: 1,
            phone_number_id: 1,
            user_id: null,
            client_id: null,
            name: 'static_welcome',
            category: 'UTILITY',
            parameter_format: null,
            language: 'en_US',
            library_template_name: null,
            id_external: null,
            status: 'PENDING',
            components: [$bodyComponent],
            phone_number: $this->createPhoneNumber()
        );

        $message = new Message(
            id: 1,
            organization_id: 1,
            campaign_id: null,
            template_id: 1,
            client_id: 1,
            message: 'Template message',
            client: $client,
            template: $template
        );

        $payload = $message->toWhatsAppTemplatePayload();

        $this->assertIsArray($payload);
        $this->assertCount(1, $payload['template']['components']);

        $bodyComponent = $payload['template']['components'][0];
        $this->assertEquals('body', $bodyComponent['type']);
        $this->assertArrayNotHasKey('parameters', $bodyComponent);
    }
}
