<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaveFullTemplateTest extends TestCase
{
    use RefreshDatabase;

    public function test_save_full_template_endpoint()
    {
        // Create a user and organization for testing
        $organization = Organization::factory()->create();
        $user = User::factory()->create(['organization_id' => $organization->id]);

        // Sample WhatsApp template JSON
        $templateJson = [
            "messaging_product" => "whatsapp",
            "to" => "5599999999999",
            "type" => "template",
            "template" => [
                "name" => "appointment_reminder",
                "language" => [
                    "code" => "en_US"
                ],
                "components" => [
                    [
                        "type" => "body",
                        "parameters" => [
                            ["type" => "text", "text" => "John"],
                            ["type" => "text", "text" => "Monday at 10 AM"]
                        ]
                    ]
                ]
            ]
        ];

        // Make authenticated request
        $response = $this->actingAs($user)
            ->postJson('/api/template/save', [
                'template_json' => $templateJson
            ]);

        // Assert successful response
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Template created successfully'
            ]);

        // Assert template was created in database
        $this->assertDatabaseHas('templates', [
            'name' => 'appointment_reminder',
            'language' => 'en_US',
            'organization_id' => $organization->id
        ]);

        // Assert component was created
        $this->assertDatabaseHas('components', [
            'type' => 'body',
            'organization_id' => $organization->id
        ]);

        // Assert parameters were created
        $this->assertDatabaseHas('parameters', [
            'type' => 'text',
            'value' => 'John',
            'organization_id' => $organization->id
        ]);

        $this->assertDatabaseHas('parameters', [
            'type' => 'text',
            'value' => 'Monday at 10 AM',
            'organization_id' => $organization->id
        ]);
    }
}
