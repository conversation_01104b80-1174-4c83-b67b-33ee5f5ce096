<?php

namespace Tests\Feature\ChatBot;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Campaign;
use App\Models\Message;
use App\Models\Client;
use App\Models\Template;
use App\Models\Organization;
use App\Models\User;
use App\Enums\CampaignStatus;
use App\Enums\MessageStatus;
use Illuminate\Support\Facades\DB;

class CampaignPerformanceTest extends TestCase
{
    use RefreshDatabase;

    private Organization $organization;
    private User $user;
    private Template $template;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->template = Template::factory()->create(['organization_id' => $this->organization->id]);
    }

    public function test_campaign_list_performance_with_large_dataset()
    {
        // Create a large number of campaigns
        Campaign::factory()->count(1000)->create([
            'organization_id' => $this->organization->id
        ]);

        $startTime = microtime(true);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/campaigns?per_page=50');

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

        $response->assertStatus(200);
        
        // Should complete within 2 seconds
        $this->assertLessThan(2000, $executionTime, 'Campaign list took too long to load');
        
        // Should return paginated results
        $this->assertArrayHasKey('data', $response->json());
        $this->assertArrayHasKey('meta', $response->json());
    }

    public function test_campaign_with_many_messages_loads_efficiently()
    {
        // Create campaign with many messages
        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Message::factory()->count(5000)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $this->organization->id
        ]);

        $startTime = microtime(true);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/campaign/{$campaign->id}/messages");

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);
        
        // Should complete within 3 seconds even with many messages
        $this->assertLessThan(3000, $executionTime, 'Campaign messages took too long to load');
    }

    public function test_bulk_client_addition_performance()
    {
        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Create many clients
        $clients = Client::factory()->count(1000)->create([
            'organization_id' => $this->organization->id
        ]);

        $clientIds = $clients->pluck('id')->toArray();

        $startTime = microtime(true);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/campaign/add-clients/{$campaign->id}", [
                             'client_ids' => $clientIds
                         ]);

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);
        
        // Should complete within 5 seconds for 1000 clients
        $this->assertLessThan(5000, $executionTime, 'Bulk client addition took too long');
        
        // Verify all clients were added
        $this->assertEquals(1000, $campaign->clients()->count());
    }

    public function test_message_generation_performance()
    {
        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id,
            'template_id' => $this->template->id
        ]);

        // Add many clients to campaign
        $clients = Client::factory()->count(2000)->create([
            'organization_id' => $this->organization->id
        ]);
        $campaign->clients()->attach($clients->pluck('id'));

        $startTime = microtime(true);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/message/generate-messages/{$campaign->id}");

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);
        
        // Should complete within 10 seconds for 2000 messages
        $this->assertLessThan(10000, $executionTime, 'Message generation took too long');
        
        // Verify messages were created
        $this->assertEquals(2000, $campaign->messages()->count());
    }

    public function test_analytics_calculation_performance()
    {
        // Create campaign with many messages and varied statuses
        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);

        // Create messages with different statuses
        Message::factory()->count(1000)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $this->organization->id,
            'status' => MessageStatus::is_sent,
            'is_sent' => true
        ]);

        Message::factory()->count(200)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $this->organization->id,
            'status' => MessageStatus::is_failed,
            'is_fail' => true
        ]);

        $startTime = microtime(true);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/analytics/campaign/{$campaign->id}?force_recalculate=true");

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);
        
        // Should complete within 3 seconds
        $this->assertLessThan(3000, $executionTime, 'Analytics calculation took too long');
    }

    public function test_database_query_optimization()
    {
        // Enable query logging
        DB::enableQueryLog();

        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Clear query log
        DB::flushQueryLog();

        // Fetch campaign with relationships
        $response = $this->actingAs($this->user)
                         ->getJson("/api/campaigns/{$campaign->id}");

        $queries = DB::getQueryLog();
        $queryCount = count($queries);

        $response->assertStatus(200);
        
        // Should not have N+1 query problems - limit to reasonable number of queries
        $this->assertLessThan(10, $queryCount, 'Too many database queries executed');
        
        DB::disableQueryLog();
    }

    public function test_concurrent_campaign_operations()
    {
        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::DRAFT
        ]);

        // Simulate concurrent operations
        $responses = [];
        
        // Multiple users trying to update the same campaign
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->actingAs($this->user)
                                ->putJson("/api/campaigns/{$campaign->id}", [
                                    'name' => "Updated Name {$i}",
                                    'description' => "Updated by process {$i}"
                                ]);
        }

        // At least one should succeed
        $successCount = collect($responses)->filter(fn($r) => $r->status() === 200)->count();
        $this->assertGreaterThan(0, $successCount);
        
        // Verify campaign is in consistent state
        $campaign->refresh();
        $this->assertNotNull($campaign->name);
        $this->assertNotNull($campaign->updated_at);
    }

    public function test_memory_usage_with_large_datasets()
    {
        $initialMemory = memory_get_usage(true);

        // Create campaign with many related records
        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $clients = Client::factory()->count(1000)->create([
            'organization_id' => $this->organization->id
        ]);

        $messages = Message::factory()->count(1000)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $this->organization->id
        ]);

        // Load campaign with all relationships
        $response = $this->actingAs($this->user)
                         ->getJson("/api/campaigns/{$campaign->id}");

        $finalMemory = memory_get_usage(true);
        $memoryIncrease = $finalMemory - $initialMemory;

        $response->assertStatus(200);
        
        // Memory increase should be reasonable (less than 50MB)
        $this->assertLessThan(50 * 1024 * 1024, $memoryIncrease, 'Memory usage too high');
    }

    public function test_search_performance_with_large_dataset()
    {
        // Create many campaigns with searchable content
        for ($i = 0; $i < 1000; $i++) {
            Campaign::factory()->create([
                'organization_id' => $this->organization->id,
                'name' => "Campaign {$i}",
                'description' => "Description for campaign number {$i}"
            ]);
        }

        $startTime = microtime(true);

        $response = $this->actingAs($this->user)
                         ->getJson('/api/campaigns?search=Campaign 500');

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);
        
        // Search should complete quickly
        $this->assertLessThan(1000, $executionTime, 'Search took too long');
        
        // Should find the specific campaign
        $campaigns = $response->json('data.campaigns');
        $this->assertGreaterThan(0, count($campaigns));
    }

    public function test_pagination_performance()
    {
        // Create many campaigns
        Campaign::factory()->count(10000)->create([
            'organization_id' => $this->organization->id
        ]);

        $startTime = microtime(true);

        // Test pagination at different pages
        $response1 = $this->actingAs($this->user)
                          ->getJson('/api/campaigns?page=1&per_page=50');

        $response2 = $this->actingAs($this->user)
                          ->getJson('/api/campaigns?page=100&per_page=50');

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $response1->assertStatus(200);
        $response2->assertStatus(200);
        
        // Both requests should complete quickly
        $this->assertLessThan(2000, $executionTime, 'Pagination took too long');
    }

    public function test_bulk_message_status_update_performance()
    {
        $campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Create many failed messages
        $messages = Message::factory()->count(1000)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $this->organization->id,
            'status' => MessageStatus::is_failed,
            'is_fail' => true
        ]);

        $startTime = microtime(true);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/campaign/{$campaign->id}/messages/resend-failed", [
                             'reset_retry_count' => true
                         ]);

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $response->assertStatus(200);
        
        // Should complete within 5 seconds for 1000 messages
        $this->assertLessThan(5000, $executionTime, 'Bulk message update took too long');
    }
}
