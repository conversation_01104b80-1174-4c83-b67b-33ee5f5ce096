<?php

namespace Tests\Feature\ChatBot;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Campaign;
use App\Models\Message;
use App\Models\Template;
use App\Models\Client;
use App\Models\Organization;
use App\Models\User;
use App\Enums\CampaignStatus;

class CampaignSecurityTest extends TestCase
{
    use RefreshDatabase;

    private Organization $organization1;
    private Organization $organization2;
    private User $user1;
    private User $user2;
    private Campaign $campaign1;
    private Campaign $campaign2;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create two separate organizations
        $this->organization1 = Organization::factory()->create();
        $this->organization2 = Organization::factory()->create();
        
        // Create users for each organization
        $this->user1 = User::factory()->create(['organization_id' => $this->organization1->id]);
        $this->user2 = User::factory()->create(['organization_id' => $this->organization2->id]);
        
        // Create campaigns for each organization
        $this->campaign1 = Campaign::factory()->create(['organization_id' => $this->organization1->id]);
        $this->campaign2 = Campaign::factory()->create(['organization_id' => $this->organization2->id]);
    }

    public function test_user_cannot_access_other_organization_campaigns()
    {
        // User from org1 trying to access org2's campaign
        $response = $this->actingAs($this->user1)
                         ->getJson("/api/campaigns/{$this->campaign2->id}");

        $response->assertStatus(422);
    }

    public function test_user_cannot_update_other_organization_campaigns()
    {
        $updateData = [
            'name' => 'Hacked Campaign Name',
            'description' => 'This should not work'
        ];

        $response = $this->actingAs($this->user1)
                         ->putJson("/api/campaigns/{$this->campaign2->id}", $updateData);

        $response->assertStatus(422);
        
        // Verify campaign was not updated
        $this->campaign2->refresh();
        $this->assertNotEquals('Hacked Campaign Name', $this->campaign2->name);
    }

    public function test_user_cannot_delete_other_organization_campaigns()
    {
        $response = $this->actingAs($this->user1)
                         ->deleteJson("/api/campaigns/{$this->campaign2->id}");

        $response->assertStatus(422);
        
        // Verify campaign still exists
        $this->assertDatabaseHas('campaigns', ['id' => $this->campaign2->id]);
    }

    public function test_user_cannot_launch_other_organization_campaigns()
    {
        $response = $this->actingAs($this->user1)
                         ->postJson("/api/campaign/launch/{$this->campaign2->id}");

        $response->assertStatus(422);
    }

    public function test_user_cannot_cancel_other_organization_campaigns()
    {
        $this->campaign2->update(['status' => CampaignStatus::SENDING]);

        $response = $this->actingAs($this->user1)
                         ->postJson("/api/campaign/{$this->campaign2->id}/cancel", [
                             'reason' => 'Unauthorized cancellation'
                         ]);

        $response->assertStatus(422);
        
        // Verify campaign was not cancelled
        $this->campaign2->refresh();
        $this->assertEquals(CampaignStatus::SENDING, $this->campaign2->status);
    }

    public function test_user_cannot_add_clients_from_other_organization()
    {
        $otherOrgClient = Client::factory()->create(['organization_id' => $this->organization2->id]);

        $response = $this->actingAs($this->user1)
                         ->postJson("/api/campaign/add-clients/{$this->campaign1->id}", [
                             'client_ids' => [$otherOrgClient->id]
                         ]);

        $response->assertStatus(422);
    }

    public function test_user_cannot_access_other_organization_messages()
    {
        $otherOrgMessage = Message::factory()->create([
            'campaign_id' => $this->campaign2->id,
            'organization_id' => $this->organization2->id
        ]);

        $response = $this->actingAs($this->user1)
                         ->getJson("/api/message/{$otherOrgMessage->id}/delivery-status");

        $response->assertStatus(422);
    }

    public function test_user_cannot_resend_other_organization_messages()
    {
        $otherOrgMessage = Message::factory()->create([
            'campaign_id' => $this->campaign2->id,
            'organization_id' => $this->organization2->id,
            'is_fail' => true
        ]);

        $response = $this->actingAs($this->user1)
                         ->postJson("/api/message/{$otherOrgMessage->id}/resend");

        $response->assertStatus(422);
    }

    public function test_user_cannot_access_other_organization_analytics()
    {
        $response = $this->actingAs($this->user1)
                         ->getJson("/api/analytics/campaign/{$this->campaign2->id}");

        $response->assertStatus(422);
    }

    public function test_user_cannot_sync_other_organization_campaigns()
    {
        $response = $this->actingAs($this->user1)
                         ->postJson("/api/whatsapp/sync/campaign/{$this->campaign2->id}");

        $response->assertStatus(422);
    }

    public function test_campaign_list_only_shows_organization_campaigns()
    {
        // Create additional campaigns for both organizations
        Campaign::factory()->count(3)->create(['organization_id' => $this->organization1->id]);
        Campaign::factory()->count(2)->create(['organization_id' => $this->organization2->id]);

        $response = $this->actingAs($this->user1)
                         ->getJson('/api/campaigns');

        $response->assertStatus(200);
        
        $campaigns = $response->json('data.campaigns');
        
        // Should only see campaigns from user1's organization (4 total: 1 from setup + 3 created)
        $this->assertCount(4, $campaigns);
        
        foreach ($campaigns as $campaign) {
            $this->assertEquals($this->organization1->id, $campaign['organization_id']);
        }
    }

    public function test_template_access_is_organization_scoped()
    {
        $otherOrgTemplate = Template::factory()->create(['organization_id' => $this->organization2->id]);

        $campaignData = [
            'name' => 'Test Campaign',
            'template_id' => $otherOrgTemplate->id,
            'phone_number_id' => 1
        ];

        $response = $this->actingAs($this->user1)
                         ->postJson('/api/campaigns', $campaignData);

        $response->assertStatus(422);
    }

    public function test_sql_injection_protection_in_campaign_name()
    {
        $maliciousName = "'; DROP TABLE campaigns; --";
        
        $campaignData = [
            'name' => $maliciousName,
            'template_id' => Template::factory()->create(['organization_id' => $this->organization1->id])->id,
            'phone_number_id' => 1
        ];

        $response = $this->actingAs($this->user1)
                         ->postJson('/api/campaigns', $campaignData);

        // Should either succeed with escaped data or fail validation
        if ($response->status() === 201) {
            $campaign = Campaign::find($response->json('data.campaign.id'));
            $this->assertEquals($maliciousName, $campaign->name); // Should be stored as-is, not executed
        }
        
        // Verify campaigns table still exists
        $this->assertDatabaseHas('campaigns', ['id' => $this->campaign1->id]);
    }

    public function test_xss_protection_in_campaign_description()
    {
        $xssPayload = '<script>alert("XSS")</script>';
        
        $campaignData = [
            'name' => 'Test Campaign',
            'description' => $xssPayload,
            'template_id' => Template::factory()->create(['organization_id' => $this->organization1->id])->id,
            'phone_number_id' => 1
        ];

        $response = $this->actingAs($this->user1)
                         ->postJson('/api/campaigns', $campaignData);

        if ($response->status() === 201) {
            $campaign = Campaign::find($response->json('data.campaign.id'));
            // Description should be stored but will be escaped when displayed
            $this->assertEquals($xssPayload, $campaign->description);
        }
    }

    public function test_mass_assignment_protection()
    {
        $campaignData = [
            'name' => 'Test Campaign',
            'template_id' => Template::factory()->create(['organization_id' => $this->organization1->id])->id,
            'phone_number_id' => 1,
            'organization_id' => $this->organization2->id, // Trying to assign to different org
            'user_id' => $this->user2->id, // Trying to assign to different user
            'is_sent' => true, // Trying to set protected field
            'status' => CampaignStatus::COMPLETED // Trying to set protected field
        ];

        $response = $this->actingAs($this->user1)
                         ->postJson('/api/campaigns', $campaignData);

        if ($response->status() === 201) {
            $campaign = Campaign::find($response->json('data.campaign.id'));
            
            // Should use authenticated user's organization and ID
            $this->assertEquals($this->organization1->id, $campaign->organization_id);
            $this->assertEquals($this->user1->id, $campaign->user_id);
            
            // Protected fields should have default values
            $this->assertFalse($campaign->is_sent);
            $this->assertEquals(CampaignStatus::DRAFT, $campaign->status);
        }
    }

    public function test_rate_limiting_on_campaign_creation()
    {
        $template = Template::factory()->create(['organization_id' => $this->organization1->id]);
        
        // Attempt to create many campaigns quickly
        $responses = [];
        for ($i = 0; $i < 10; $i++) {
            $responses[] = $this->actingAs($this->user1)
                                ->postJson('/api/campaigns', [
                                    'name' => "Campaign {$i}",
                                    'template_id' => $template->id,
                                    'phone_number_id' => 1
                                ]);
        }

        // Most should succeed, but if rate limiting is in place, some might fail
        $successCount = collect($responses)->filter(fn($r) => $r->status() === 201)->count();
        $this->assertGreaterThan(0, $successCount);
    }

    public function test_unauthorized_access_returns_401()
    {
        $response = $this->getJson('/api/campaigns');
        $response->assertStatus(401);
    }

    public function test_invalid_campaign_id_returns_404()
    {
        $response = $this->actingAs($this->user1)
                         ->getJson('/api/campaigns/99999');

        $response->assertStatus(422);
    }
}
