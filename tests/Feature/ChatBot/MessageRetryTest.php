<?php

namespace Tests\Feature\ChatBot;

use App\Models\Campaign;
use App\Models\Message;
use App\Models\MessageDeliveryAttempt;
use App\Models\User;
use App\Models\Organization;
use App\Enums\MessageStatus;
use App\Enums\CampaignStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MessageRetryTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Campaign $campaign;
    private Message $message;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->campaign = Campaign::factory()->create([
            'organization_id' => $this->organization->id,
            'status' => CampaignStatus::SENDING
        ]);
        $this->message = Message::factory()->create([
            'campaign_id' => $this->campaign->id,
            'organization_id' => $this->organization->id,
            'status' => MessageStatus::is_failed,
            'delivery_attempts' => 1,
            'max_retries' => 3
        ]);
    }

    public function test_can_get_campaign_messages()
    {
        Message::factory()->count(3)->create([
            'campaign_id' => $this->campaign->id,
            'organization_id' => $this->organization->id
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/campaign/{$this->campaign->id}/messages");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'campaign_id',
                         'statistics'
                     ]
                 ]);
    }

    public function test_can_get_failed_messages()
    {
        Message::factory()->create([
            'campaign_id' => $this->campaign->id,
            'organization_id' => $this->organization->id,
            'status' => MessageStatus::is_failed
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/campaign/{$this->campaign->id}/messages/failed");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'failed_messages',
                         'permanently_failed_messages',
                         'summary'
                     ]
                 ]);
    }

    public function test_can_resend_single_message()
    {
        $response = $this->actingAs($this->user)
                         ->postJson("/api/message/{$this->message->id}/resend", [
                             'reset_retry_count' => false
                         ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'message_id',
                         'action',
                         'retry_info'
                     ]
                 ]);
    }

    public function test_can_resend_with_retry_reset()
    {
        $this->message->update(['delivery_attempts' => 3]); // Max retries reached

        $response = $this->actingAs($this->user)
                         ->postJson("/api/message/{$this->message->id}/resend", [
                             'reset_retry_count' => true
                         ]);

        $response->assertStatus(200);
        
        $this->message->refresh();
        $this->assertEquals(0, $this->message->delivery_attempts);
    }

    public function test_can_resend_failed_messages_in_campaign()
    {
        Message::factory()->count(2)->create([
            'campaign_id' => $this->campaign->id,
            'organization_id' => $this->organization->id,
            'status' => MessageStatus::is_failed,
            'delivery_attempts' => 1
        ]);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/campaign/{$this->campaign->id}/messages/resend-failed", [
                             'reset_retry_count' => false
                         ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'messages_processed',
                         'messages_queued_for_retry'
                     ]
                 ]);
    }

    public function test_can_get_delivery_status()
    {
        MessageDeliveryAttempt::factory()->create([
            'message_id' => $this->message->id,
            'attempt_number' => 1,
            'status' => MessageStatus::is_failed
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson("/api/message/{$this->message->id}/delivery-status");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'message_id',
                         'retry_info',
                         'delivery_attempts',
                         'statistics'
                     ]
                 ]);
    }

    public function test_can_get_campaign_statistics()
    {
        $response = $this->actingAs($this->user)
                         ->getJson("/api/campaign/{$this->campaign->id}/messages/statistics");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'campaign_id',
                         'statistics' => [
                             'total',
                             'sent',
                             'failed',
                             'success_rate'
                         ]
                     ]
                 ]);
    }

    public function test_cannot_resend_sent_message_without_force()
    {
        $this->message->update(['status' => MessageStatus::is_sent]);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/message/{$this->message->id}/resend");

        $response->assertStatus(422);
    }

    public function test_can_force_resend_sent_message()
    {
        $this->message->update(['status' => MessageStatus::is_sent]);

        $response = $this->actingAs($this->user)
                         ->postJson("/api/message/{$this->message->id}/resend", [
                             'force_resend' => true,
                             'reset_retry_count' => true
                         ]);

        $response->assertStatus(200);
    }
}
