<?php

namespace Tests\Feature\Http\Requests\Flow;

use App\Http\Requests\Flow\SaveFullFlowRequest;
use App\Enums\ChatBot\FlowStatus;
use App\Enums\ChatBot\StepType;
use App\Enums\ChatBot\ComponentFormat;
use App\Enums\ChatBot\WhatsAppButtonType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class SaveFullFlowRequestTest extends TestCase
{
    use RefreshDatabase;

    private function getValidFlowData(): array
    {
        return [
            'flow' => [
                'name' => 'Test Flow',
                'description' => 'Test flow description',
                'is_default_flow' => false,
                'inactivity_minutes' => 60,
                'ending_conversation_message' => 'Goodbye!',
                'version' => '1.0',
                'status' => FlowStatus::DRAFT->value,
                'variables' => ['client' => ['name', 'email']]
            ],
            'steps' => [
                [
                    'step' => 'welcome',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 0,
                    'is_initial_step' => true,
                    'is_ending_step' => false,
                    'component' => [
                        'name' => 'Welcome Component',
                        'type' => 'message',
                        'text' => 'Welcome to our service!',
                        'format' => ComponentFormat::TEXT->value,
                        'buttons' => [
                            [
                                'text' => 'Start',
                                'type' => WhatsAppButtonType::REPLY->value,
                                'internal_type' => 'navigation',
                                'internal_data' => 'next_step'
                            ]
                        ]
                    ]
                ],
                [
                    'step' => 'end',
                    'step_type' => StepType::MESSAGE->value,
                    'position' => 1,
                    'is_initial_step' => false,
                    'is_ending_step' => true,
                    'component' => [
                        'name' => 'End Component',
                        'type' => 'message',
                        'text' => 'Thank you!',
                        'format' => ComponentFormat::TEXT->value
                    ]
                ]
            ]
        ];
    }

    public function test_valid_flow_data_passes_validation()
    {
        $data = $this->getValidFlowData();
        
        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_missing_flow_fails_validation()
    {
        $data = $this->getValidFlowData();
        unset($data['flow']);

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('flow', $validator->errors()->toArray());
    }

    public function test_missing_flow_name_fails_validation()
    {
        $data = $this->getValidFlowData();
        unset($data['flow']['name']);

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('flow.name', $validator->errors()->toArray());
    }

    public function test_invalid_flow_status_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['flow']['status'] = 'invalid_status';

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('flow.status', $validator->errors()->toArray());
    }

    public function test_missing_steps_fails_validation()
    {
        $data = $this->getValidFlowData();
        unset($data['steps']);

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps', $validator->errors()->toArray());
    }

    public function test_empty_steps_array_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'] = [];

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps', $validator->errors()->toArray());
    }

    public function test_invalid_step_type_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['step_type'] = 'invalid_type';

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps.0.step_type', $validator->errors()->toArray());
    }

    public function test_invalid_component_format_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['format'] = 'invalid_format';

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps.0.component.format', $validator->errors()->toArray());
    }

    public function test_invalid_button_type_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['buttons'][0]['type'] = 'invalid_button_type';

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps.0.component.buttons.0.type', $validator->errors()->toArray());
    }

    public function test_button_text_too_long_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['buttons'][0]['text'] = str_repeat('a', 21); // 21 characters

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps.0.component.buttons.0.text', $validator->errors()->toArray());
    }

    public function test_component_text_too_long_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['text'] = str_repeat('a', 4097); // 4097 characters

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps.0.component.text', $validator->errors()->toArray());
    }

    public function test_inactivity_minutes_too_low_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['flow']['inactivity_minutes'] = 0;

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('flow.inactivity_minutes', $validator->errors()->toArray());
    }

    public function test_inactivity_minutes_too_high_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['flow']['inactivity_minutes'] = 1441; // More than 24 hours

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('flow.inactivity_minutes', $validator->errors()->toArray());
    }

    public function test_timeout_seconds_too_low_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['timeout_seconds'] = 0;

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps.0.timeout_seconds', $validator->errors()->toArray());
    }

    public function test_timeout_seconds_too_high_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['timeout_seconds'] = 3601; // More than 1 hour

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps.0.timeout_seconds', $validator->errors()->toArray());
    }

    public function test_action_buttons_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['action'] = [
            'buttons' => [
                [
                    'text' => 'Action Button',
                    'type' => WhatsAppButtonType::REPLY->value,
                    'internal_type' => 'action',
                    'internal_data' => 'action_data'
                ]
            ]
        ];

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_parameters_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['parameters'] = [
            [
                'type' => 'text',
                'text' => 'Parameter text',
                'index' => 0
            ]
        ];

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->passes());
    }

    public function test_parameter_index_negative_fails_validation()
    {
        $data = $this->getValidFlowData();
        $data['steps'][0]['component']['parameters'] = [
            [
                'type' => 'text',
                'text' => 'Parameter text',
                'index' => -1
            ]
        ];

        $request = new SaveFullFlowRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('steps.0.component.parameters.0.index', $validator->errors()->toArray());
    }
}
