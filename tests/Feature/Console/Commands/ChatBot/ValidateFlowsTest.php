<?php

namespace Tests\Feature\Console\Commands\ChatBot;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Console\Commands\ChatBot\ValidateFlows;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ValidateFlow;
use App\Repositories\FlowRepository;
use App\Domains\ChatBot\Flow;
use Mockery;

class ValidateFlowsTest extends TestCase
{
    use RefreshDatabase;

    protected $mockValidateFlow;
    protected $mockFlowRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockValidateFlow = Mockery::mock(ValidateFlow::class);
        $this->mockFlowRepository = Mockery::mock(FlowRepository::class);

        $this->app->instance(ValidateFlow::class, $this->mockValidateFlow);
        $this->app->instance(FlowRepository::class, $this->mockFlowRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_validate_single_flow_successfully()
    {
        $flow = new Flow(1, 1, 'Test Flow', 'Description', 1, null, true);

        $this->mockFlowRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($flow);

        $this->mockValidateFlow
            ->shouldReceive('perform')
            ->once()
            ->with($flow, true)
            ->andReturn([
                'valid' => true,
                'errors' => [],
                'warnings' => [],
                'flow_id' => 1,
                'flow_name' => 'Test Flow',
                'total_steps' => 3,
                'from_cache' => false
            ]);

        $this->artisan('chatbot:validate-flows --flow-id=1')
            ->expectsOutput('ChatBot Flow Validation')
            ->expectsOutput('Validating flow ID: 1')
            ->expectsOutput('Flow: Test Flow (ID: 1)')
            ->expectsOutput('Steps: 3')
            ->expectsOutput('✓ Flow is valid')
            ->assertExitCode(0);
    }

    public function test_validate_single_flow_with_errors()
    {
        $flow = new Flow(1, 1, 'Invalid Flow', 'Description', 1, null, true);

        $this->mockFlowRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($flow);

        $this->mockValidateFlow
            ->shouldReceive('perform')
            ->once()
            ->with($flow, true)
            ->andReturn([
                'valid' => false,
                'errors' => [
                    'No initial step found',
                    'No ending step found'
                ],
                'warnings' => [
                    'Step has no type flags set'
                ],
                'flow_id' => 1,
                'flow_name' => 'Invalid Flow',
                'total_steps' => 2,
                'from_cache' => false
            ]);

        $this->artisan('chatbot:validate-flows --flow-id=1')
            ->expectsOutput('✗ Flow has validation errors')
            ->expectsOutput('Errors:')
            ->expectsOutput('• No initial step found')
            ->expectsOutput('• No ending step found')
            ->expectsOutput('Warnings:')
            ->expectsOutput('• Step has no type flags set')
            ->assertExitCode(1);
    }

    public function test_validate_single_flow_with_cached_results()
    {
        $flow = new Flow(1, 1, 'Cached Flow', 'Description', 1, null, true);

        $this->mockFlowRepository
            ->shouldReceive('findById')
            ->once()
            ->with(1)
            ->andReturn($flow);

        $this->mockValidateFlow
            ->shouldReceive('perform')
            ->once()
            ->with($flow, true)
            ->andReturn([
                'valid' => true,
                'errors' => [],
                'warnings' => [],
                'flow_id' => 1,
                'flow_name' => 'Cached Flow',
                'total_steps' => 2,
                'from_cache' => true
            ]);

        $this->artisan('chatbot:validate-flows --flow-id=1')
            ->expectsOutput('✓ Flow is valid')
            ->expectsOutput('(Results from cache)')
            ->assertExitCode(0);
    }

    public function test_validate_single_flow_not_found()
    {
        $this->mockFlowRepository
            ->shouldReceive('findById')
            ->once()
            ->with(999)
            ->andReturn(null);

        $this->artisan('chatbot:validate-flows --flow-id=999')
            ->expectsOutput('Flow with ID 999 not found')
            ->assertExitCode(1);
    }

    public function test_validate_multiple_flows_successfully()
    {
        $flows = [
            new Flow(1, 1, 'Flow 1', 'Description', 1, null, true),
            new Flow(2, 1, 'Flow 2', 'Description', 1, null, true),
            new Flow(3, 1, 'Flow 3', 'Description', 1, null, true)
        ];

        $this->mockFlowRepository
            ->shouldReceive('findAll')
            ->once()
            ->andReturn($flows);

        $this->mockValidateFlow
            ->shouldReceive('validateMultiple')
            ->once()
            ->with($flows, true)
            ->andReturn([
                'summary' => [
                    'total_flows' => 3,
                    'valid_flows' => 2,
                    'invalid_flows' => 1,
                    'flows_with_warnings' => 1,
                    'total_errors' => 2,
                    'total_warnings' => 1
                ],
                'results' => [
                    1 => ['valid' => true, 'flow_name' => 'Flow 1', 'errors' => [], 'warnings' => []],
                    2 => ['valid' => true, 'flow_name' => 'Flow 2', 'errors' => [], 'warnings' => ['Warning']],
                    3 => ['valid' => false, 'flow_name' => 'Flow 3', 'errors' => ['Error 1', 'Error 2'], 'warnings' => []]
                ]
            ]);

        $this->artisan('chatbot:validate-flows')
            ->expectsOutput('Found 3 flows to validate')
            ->expectsOutput('Validation Summary:')
            ->expectsOutput('Total flows: 3')
            ->expectsOutput('Valid flows: 2')
            ->expectsOutput('Invalid flows: 1')
            ->expectsOutput('Flows with warnings: 1')
            ->expectsOutput('Total errors: 2')
            ->expectsOutput('Total warnings: 1')
            ->expectsOutput('Invalid Flows Details:')
            ->expectsOutput('Flow ID 3: Flow 3')
            ->expectsOutput('• Error 1')
            ->expectsOutput('• Error 2')
            ->assertExitCode(1);
    }

    public function test_validate_flows_for_specific_organization()
    {
        $flows = [
            new Flow(1, 2, 'Org Flow 1', 'Description', 1, null, true),
            new Flow(2, 2, 'Org Flow 2', 'Description', 1, null, true)
        ];

        $this->mockFlowRepository
            ->shouldReceive('findByOrganizationId')
            ->once()
            ->with(2)
            ->andReturn($flows);

        $this->mockValidateFlow
            ->shouldReceive('validateMultiple')
            ->once()
            ->with($flows, true)
            ->andReturn([
                'summary' => [
                    'total_flows' => 2,
                    'valid_flows' => 2,
                    'invalid_flows' => 0,
                    'flows_with_warnings' => 0,
                    'total_errors' => 0,
                    'total_warnings' => 0
                ],
                'results' => [
                    1 => ['valid' => true, 'flow_name' => 'Org Flow 1', 'errors' => [], 'warnings' => []],
                    2 => ['valid' => true, 'flow_name' => 'Org Flow 2', 'errors' => [], 'warnings' => []]
                ]
            ]);

        $this->artisan('chatbot:validate-flows --organization-id=2')
            ->expectsOutput('Found 2 flows to validate')
            ->expectsOutput('Valid flows: 2')
            ->expectsOutput('Invalid flows: 0')
            ->assertExitCode(0);
    }

    public function test_validate_flows_with_no_cache()
    {
        $flows = [
            new Flow(1, 1, 'Flow 1', 'Description', 1, null, true)
        ];

        $this->mockFlowRepository
            ->shouldReceive('findAll')
            ->once()
            ->andReturn($flows);

        $this->mockValidateFlow
            ->shouldReceive('validateMultiple')
            ->once()
            ->with($flows, false) // no-cache = false for useCache parameter
            ->andReturn([
                'summary' => [
                    'total_flows' => 1,
                    'valid_flows' => 1,
                    'invalid_flows' => 0,
                    'flows_with_warnings' => 0,
                    'total_errors' => 0,
                    'total_warnings' => 0
                ],
                'results' => [
                    1 => ['valid' => true, 'flow_name' => 'Flow 1', 'errors' => [], 'warnings' => []]
                ]
            ]);

        $this->artisan('chatbot:validate-flows --no-cache')
            ->expectsOutput('Valid flows: 1')
            ->assertExitCode(0);
    }

    public function test_validate_flows_with_no_flows_found()
    {
        $this->mockFlowRepository
            ->shouldReceive('findAll')
            ->once()
            ->andReturn([]);

        $this->artisan('chatbot:validate-flows')
            ->expectsOutput('No flows found to validate')
            ->assertExitCode(0);
    }

    public function test_validate_flows_handles_exception()
    {
        $this->mockFlowRepository
            ->shouldReceive('findAll')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->artisan('chatbot:validate-flows')
            ->expectsOutput('Validation failed: Database error')
            ->assertExitCode(1);
    }

    public function test_validate_flows_with_fix_option()
    {
        $flows = [
            new Flow(1, 1, 'Flow 1', 'Description', 1, null, true)
        ];

        $this->mockFlowRepository
            ->shouldReceive('findAll')
            ->once()
            ->andReturn($flows);

        $this->mockValidateFlow
            ->shouldReceive('validateMultiple')
            ->once()
            ->andReturn([
                'summary' => [
                    'total_flows' => 1,
                    'valid_flows' => 0,
                    'invalid_flows' => 1,
                    'flows_with_warnings' => 0,
                    'total_errors' => 1,
                    'total_warnings' => 0
                ],
                'results' => [
                    1 => ['valid' => false, 'flow_name' => 'Flow 1', 'errors' => ['Some error'], 'warnings' => []]
                ]
            ]);

        $this->artisan('chatbot:validate-flows --fix')
            ->expectsOutput('Attempting to fix issues for 1 flows...')
            ->expectsOutput('Automatic fixes not yet implemented')
            ->assertExitCode(1);
    }

    public function test_validate_flows_exports_to_json()
    {
        $flows = [
            new Flow(1, 1, 'Flow 1', 'Description', 1, null, true)
        ];

        $this->mockFlowRepository
            ->shouldReceive('findAll')
            ->once()
            ->andReturn($flows);

        $this->mockValidateFlow
            ->shouldReceive('validateMultiple')
            ->once()
            ->andReturn([
                'summary' => [
                    'total_flows' => 1,
                    'valid_flows' => 1,
                    'invalid_flows' => 0,
                    'flows_with_warnings' => 0,
                    'total_errors' => 0,
                    'total_warnings' => 0
                ],
                'results' => [
                    1 => ['valid' => true, 'flow_name' => 'Flow 1', 'errors' => [], 'warnings' => []]
                ]
            ]);

        $this->artisan('chatbot:validate-flows --export=json')
            ->expectsOutputToContain('Results exported to:')
            ->expectsOutputToContain('.json')
            ->assertExitCode(0);
    }

    public function test_validate_flows_exports_to_csv()
    {
        $flows = [
            new Flow(1, 1, 'Flow 1', 'Description', 1, null, true)
        ];

        $this->mockFlowRepository
            ->shouldReceive('findAll')
            ->once()
            ->andReturn($flows);

        $this->mockValidateFlow
            ->shouldReceive('validateMultiple')
            ->once()
            ->andReturn([
                'summary' => [
                    'total_flows' => 1,
                    'valid_flows' => 1,
                    'invalid_flows' => 0,
                    'flows_with_warnings' => 0,
                    'total_errors' => 0,
                    'total_warnings' => 0
                ],
                'results' => [
                    1 => ['valid' => true, 'flow_name' => 'Flow 1', 'errors' => [], 'warnings' => []]
                ]
            ]);

        $this->artisan('chatbot:validate-flows --export=csv')
            ->expectsOutputToContain('Results exported to:')
            ->expectsOutputToContain('.csv')
            ->assertExitCode(0);
    }
}
