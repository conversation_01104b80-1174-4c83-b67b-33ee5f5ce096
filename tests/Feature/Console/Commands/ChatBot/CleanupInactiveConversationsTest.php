<?php

namespace Tests\Feature\Console\Commands\ChatBot;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Console\Commands\ChatBot\CleanupInactiveConversations;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConversationTimeoutService;
use Mockery;

class CleanupInactiveConversationsTest extends TestCase
{
    use RefreshDatabase;

    protected $mockConversationTimeoutService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockConversationTimeoutService = Mockery::mock(ConversationTimeoutService::class);
        $this->app->instance(ConversationTimeoutService::class, $this->mockConversationTimeoutService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_cleanup_command_runs_successfully_with_dry_run()
    {
        config(['chatbot.cleanup.enabled' => true]);

        $this->mockConversationTimeoutService
            ->shouldReceive('cleanupInactiveConversations')
            ->once()
            ->with(true)
            ->andReturn([
                'total_found' => 5,
                'cleaned' => 0,
                'errors' => 0,
                'dry_run' => true
            ]);

        $this->artisan('chatbot:cleanup-conversations --dry-run')
            ->expectsOutput('ChatBot Conversation Cleanup')
            ->expectsOutput('DRY RUN MODE - No changes will be made')
            ->expectsOutput('Total conversations found: 5')
            ->expectsOutput('Successfully cleaned: 0')
            ->expectsOutput('This was a dry run - no actual changes were made')
            ->assertExitCode(0);
    }

    public function test_cleanup_command_runs_successfully_with_force()
    {
        config(['chatbot.cleanup.enabled' => true]);

        $this->mockConversationTimeoutService
            ->shouldReceive('cleanupInactiveConversations')
            ->once()
            ->with(false)
            ->andReturn([
                'total_found' => 3,
                'cleaned' => 3,
                'errors' => 0,
                'dry_run' => false
            ]);

        $this->artisan('chatbot:cleanup-conversations --force')
            ->expectsOutput('ChatBot Conversation Cleanup')
            ->expectsOutput('Total conversations found: 3')
            ->expectsOutput('Successfully cleaned: 3')
            ->expectsOutput('✓ Cleanup completed successfully')
            ->assertExitCode(0);
    }

    public function test_cleanup_command_shows_errors_when_present()
    {
        config(['chatbot.cleanup.enabled' => true]);

        $this->mockConversationTimeoutService
            ->shouldReceive('cleanupInactiveConversations')
            ->once()
            ->with(true)
            ->andReturn([
                'total_found' => 5,
                'cleaned' => 3,
                'errors' => 2,
                'dry_run' => true
            ]);

        $this->artisan('chatbot:cleanup-conversations --dry-run')
            ->expectsOutput('Total conversations found: 5')
            ->expectsOutput('Successfully cleaned: 3')
            ->expectsOutput('Errors encountered: 2')
            ->assertExitCode(0);
    }

    public function test_cleanup_command_fails_when_disabled()
    {
        config(['chatbot.cleanup.enabled' => false]);

        $this->artisan('chatbot:cleanup-conversations')
            ->expectsOutput('Conversation cleanup is disabled in configuration')
            ->assertExitCode(1);
    }

    public function test_cleanup_command_handles_service_exception()
    {
        config(['chatbot.cleanup.enabled' => true]);

        $this->mockConversationTimeoutService
            ->shouldReceive('cleanupInactiveConversations')
            ->once()
            ->andThrow(new \Exception('Service error'));

        $this->artisan('chatbot:cleanup-conversations --force')
            ->expectsOutput('Cleanup failed: Service error')
            ->assertExitCode(1);
    }

    public function test_cleanup_command_shows_configuration()
    {
        config([
            'chatbot.cleanup.enabled' => true,
            'chatbot.cleanup.soft_delete' => true
        ]);

        $this->mockConversationTimeoutService
            ->shouldReceive('cleanupInactiveConversations')
            ->once()
            ->andReturn([
                'total_found' => 0,
                'cleaned' => 0,
                'errors' => 0,
                'dry_run' => true
            ]);

        $this->artisan('chatbot:cleanup-conversations --dry-run --older-than=48')
            ->expectsOutput('Configuration:')
            ->expectsOutput('Cleanup conversations older than: 48 hours')
            ->expectsOutput('Soft delete: Yes')
            ->expectsOutput('Dry run: Yes')
            ->expectsOutput('No conversations needed cleanup')
            ->assertExitCode(0);
    }

    public function test_cleanup_command_with_custom_older_than_parameter()
    {
        config(['chatbot.cleanup.enabled' => true]);

        $this->mockConversationTimeoutService
            ->shouldReceive('cleanupInactiveConversations')
            ->once()
            ->with(false)
            ->andReturn([
                'total_found' => 2,
                'cleaned' => 2,
                'errors' => 0,
                'dry_run' => false
            ]);

        $this->artisan('chatbot:cleanup-conversations --force --older-than=72')
            ->expectsOutput('Cleanup conversations older than: 72 hours')
            ->assertExitCode(0);
    }

    public function test_cleanup_command_prompts_for_confirmation_without_force()
    {
        config(['chatbot.cleanup.enabled' => true]);

        $this->mockConversationTimeoutService
            ->shouldReceive('cleanupInactiveConversations')
            ->once()
            ->with(false)
            ->andReturn([
                'total_found' => 1,
                'cleaned' => 1,
                'errors' => 0,
                'dry_run' => false
            ]);

        $this->artisan('chatbot:cleanup-conversations')
            ->expectsQuestion('Do you want to proceed with the cleanup?', 'yes')
            ->expectsOutput('✓ Cleanup completed successfully')
            ->assertExitCode(0);
    }

    public function test_cleanup_command_cancels_when_user_declines()
    {
        config(['chatbot.cleanup.enabled' => true]);

        $this->artisan('chatbot:cleanup-conversations')
            ->expectsQuestion('Do you want to proceed with the cleanup?', 'no')
            ->expectsOutput('Cleanup cancelled')
            ->assertExitCode(0);
    }

    public function test_cleanup_command_shows_soft_delete_configuration()
    {
        config([
            'chatbot.cleanup.enabled' => true,
            'chatbot.cleanup.soft_delete' => false
        ]);

        $this->mockConversationTimeoutService
            ->shouldReceive('cleanupInactiveConversations')
            ->once()
            ->andReturn([
                'total_found' => 0,
                'cleaned' => 0,
                'errors' => 0,
                'dry_run' => true
            ]);

        $this->artisan('chatbot:cleanup-conversations --dry-run')
            ->expectsOutput('Soft delete: No')
            ->assertExitCode(0);
    }
}
