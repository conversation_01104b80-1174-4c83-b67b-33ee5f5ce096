<?php

namespace Tests\Feature\Controllers;

use Tests\TestCase;
use App\Models\Organization;
use App\Models\WhatsAppWebhookLog;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WhatsAppWebhookControllerVerifyTest extends TestCase
{
    use RefreshDatabase;

    public function test_verify_succeeds_with_organization_token()
    {
        $token = 'org_webhook_token_123';
        $challenge = '1234567890';

        $organization = Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => $token,
            'hub_challenge' => $challenge,
        ]));

        $response->assertStatus(200);
        $this->assertEquals((int) $challenge, $response->json());

        // Verify log was created
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization->id,
            'phone_number_id' => null,
            'event_type' => 'verification',
            'processing_status' => 'success',
        ]);

        $log = WhatsAppWebhookLog::where('organization_id', $organization->id)->first();
        $this->assertEquals('organization', $log->webhook_payload['type']);
    }

    public function test_verify_succeeds_with_global_token()
    {
        $globalToken = 'global_webhook_token_456';
        $challenge = '9876543210';

        config(['whatsapp.webhook_verify_token' => $globalToken]);

        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => $globalToken,
            'hub_challenge' => $challenge,
        ]));

        $response->assertStatus(200);
        $this->assertEquals((int) $challenge, $response->json());

        // Verify log was created
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'verification',
            'processing_status' => 'success',
        ]);

        $log = WhatsAppWebhookLog::whereNull('organization_id')->first();
        $this->assertEquals('global', $log->webhook_payload['type']);
    }

    public function test_verify_fails_with_invalid_token()
    {
        $invalidToken = 'invalid_token_123';
        $challenge = '1111111111';

        config(['whatsapp.webhook_verify_token' => 'different_global_token']);

        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => $invalidToken,
            'hub_challenge' => $challenge,
        ]));

        $response->assertStatus(403);
        $response->assertJson(['error' => 'Forbidden']);

        // Verify failure log was created
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'verification',
            'processing_status' => 'failed',
            'error_message' => 'Invalid token',
        ]);

        $log = WhatsAppWebhookLog::where('processing_status', 'failed')->first();
        $this->assertEquals('invalid_...', $log->webhook_payload['token']);
        $this->assertEquals('subscribe', $log->webhook_payload['mode']);
    }

    public function test_verify_fails_with_invalid_mode()
    {
        $token = 'any_token';
        $challenge = '2222222222';

        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'invalid_mode',
            'hub_verify_token' => $token,
            'hub_challenge' => $challenge,
        ]));

        $response->assertStatus(403);
        $response->assertJson(['error' => 'Forbidden']);

        // Verify failure log was created
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'verification',
            'processing_status' => 'failed',
            'error_message' => 'Invalid mode',
        ]);
    }

    public function test_verify_prioritizes_organization_token_over_global()
    {
        $sharedToken = 'shared_token_789';
        $challenge = '3333333333';

        // Set same token as global
        config(['whatsapp.webhook_verify_token' => $sharedToken]);

        $organization = Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $sharedToken,
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => $sharedToken,
            'hub_challenge' => $challenge,
        ]));

        $response->assertStatus(200);
        $this->assertEquals((int) $challenge, $response->json());

        // Verify organization log was created (not global)
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => $organization->id,
            'phone_number_id' => null,
            'event_type' => 'verification',
            'processing_status' => 'success',
        ]);

        $log = WhatsAppWebhookLog::where('organization_id', $organization->id)->first();
        $this->assertEquals('organization', $log->webhook_payload['type']);
    }

    public function test_verify_ignores_inactive_organization()
    {
        $token = 'inactive_org_token';
        $challenge = '4444444444';

        // Set same token as global
        config(['whatsapp.webhook_verify_token' => $token]);

        Organization::factory()->create([
            'whatsapp_webhook_verify_token' => $token,
            'is_active' => false,
            'is_suspended' => false,
        ]);

        $response = $this->get('/api/whatsapp/webhook?' . http_build_query([
            'hub_mode' => 'subscribe',
            'hub_verify_token' => $token,
            'hub_challenge' => $challenge,
        ]));

        $response->assertStatus(200);
        $this->assertEquals((int) $challenge, $response->json());

        // Verify global log was created (organization was ignored)
        $this->assertDatabaseHas('whatsapp_webhook_logs', [
            'organization_id' => null,
            'phone_number_id' => null,
            'event_type' => 'verification',
            'processing_status' => 'success',
        ]);

        $log = WhatsAppWebhookLog::whereNull('organization_id')->first();
        $this->assertEquals('global', $log->webhook_payload['type']);
    }
}
