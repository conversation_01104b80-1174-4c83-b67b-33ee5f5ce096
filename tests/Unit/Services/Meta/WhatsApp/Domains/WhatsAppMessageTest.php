<?php

namespace Tests\Unit\Services\Meta\WhatsApp\Domains;

use Tests\Unit\Domains\BaseDomainTest;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Domains\ChatBot\Message;
use Carbon\Carbon;

class WhatsAppMessageTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        // Arrange
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();
        $message = $this->createMock(Message::class);

        // Act
        $whatsAppMessage = new WhatsAppMessage(
            1,
            2,
            $message,
            'wamid.123456789',
            'accepted',
            '557991514957',
            '+5579991514957',
            'whatsapp',
            '{"test": "data"}',
            $createdAt,
            $updatedAt
        );

        // Assert
        $this->assertEquals(1, $whatsAppMessage->id);
        $this->assertEquals(2, $whatsAppMessage->message_id);
        $this->assertEquals($message, $whatsAppMessage->message);
        $this->assertEquals('wamid.123456789', $whatsAppMessage->whatsapp_message_id);
        $this->assertEquals('accepted', $whatsAppMessage->message_status);
        $this->assertEquals('557991514957', $whatsAppMessage->wa_id);
        $this->assertEquals('+5579991514957', $whatsAppMessage->input_phone);
        $this->assertEquals('whatsapp', $whatsAppMessage->messaging_product);
        $this->assertEquals('{"test": "data"}', $whatsAppMessage->json);
        $this->assertEquals($createdAt, $whatsAppMessage->created_at);
        $this->assertEquals($updatedAt, $whatsAppMessage->updated_at);
    }

    public function test_domain_instantiation_with_nulls()
    {
        // Act
        $whatsAppMessage = new WhatsAppMessage(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );

        // Assert
        $this->assertNull($whatsAppMessage->id);
        $this->assertNull($whatsAppMessage->message_id);
        $this->assertNull($whatsAppMessage->message);
        $this->assertNull($whatsAppMessage->whatsapp_message_id);
        $this->assertNull($whatsAppMessage->message_status);
        $this->assertNull($whatsAppMessage->wa_id);
        $this->assertNull($whatsAppMessage->input_phone);
        $this->assertNull($whatsAppMessage->messaging_product);
        $this->assertNull($whatsAppMessage->json);
        $this->assertNull($whatsAppMessage->created_at);
        $this->assertNull($whatsAppMessage->updated_at);
    }

    public function test_to_array_method()
    {
        // Arrange
        $message = $this->createMock(Message::class);
        $message->method('toArray')->willReturn([
            'id' => 1,
            'organization_id' => 1,
            'message' => 'Test message'
        ]);

        $whatsAppMessage = new WhatsAppMessage(
            1,
            2,
            $message,
            'wamid.123456789',
            'accepted',
            '557991514957',
            '+5579991514957',
            'whatsapp',
            '{"test": "data"}',
            Carbon::parse('2023-01-01 12:00:00'),
            Carbon::parse('2023-01-01 12:00:00')
        );

        // Act
        $array = $whatsAppMessage->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(2, $array['message_id']);
        $this->assertIsArray($array['message']);
        $this->assertEquals('wamid.123456789', $array['whatsapp_message_id']);
        $this->assertEquals('accepted', $array['message_status']);
        $this->assertEquals('557991514957', $array['wa_id']);
        $this->assertEquals('+5579991514957', $array['input_phone']);
        $this->assertEquals('whatsapp', $array['messaging_product']);
        $this->assertEquals('{"test": "data"}', $array['json']);
        $this->assertEquals('2023-01-01 12:00:00', $array['created_at']);
        $this->assertEquals('2023-01-01 12:00:00', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            1,
            2,
            null,
            'wamid.123456789',
            'accepted',
            '557991514957',
            '+5579991514957',
            'whatsapp',
            '{"test": "data"}',
            null,
            null
        );

        // Act
        $array = $whatsAppMessage->toStoreArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertEquals(2, $array['message_id']);
        $this->assertEquals('wamid.123456789', $array['whatsapp_message_id']);
        $this->assertEquals('accepted', $array['message_status']);
        $this->assertEquals('557991514957', $array['wa_id']);
        $this->assertEquals('+5579991514957', $array['input_phone']);
        $this->assertEquals('whatsapp', $array['messaging_product']);
        $this->assertEquals('{"test": "data"}', $array['json']);
        $this->assertArrayNotHasKey('id', $array);
        $this->assertArrayNotHasKey('created_at', $array);
        $this->assertArrayNotHasKey('updated_at', $array);
    }

    public function test_to_update_array_method()
    {
        // Arrange
        $whatsAppMessage = new WhatsAppMessage(
            1,
            2,
            null,
            'wamid.123456789',
            'delivered',
            '557991514957',
            '+5579991514957',
            'whatsapp',
            '{"test": "updated"}',
            null,
            null
        );

        // Act
        $array = $whatsAppMessage->toUpdateArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertEquals(2, $array['message_id']);
        $this->assertEquals('wamid.123456789', $array['whatsapp_message_id']);
        $this->assertEquals('delivered', $array['message_status']);
        $this->assertEquals('557991514957', $array['wa_id']);
        $this->assertEquals('+5579991514957', $array['input_phone']);
        $this->assertEquals('whatsapp', $array['messaging_product']);
        $this->assertEquals('{"test": "updated"}', $array['json']);
        $this->assertArrayNotHasKey('id', $array);
        $this->assertArrayNotHasKey('created_at', $array);
        $this->assertArrayNotHasKey('updated_at', $array);
    }

    protected function createDomainInstance()
    {
        return new WhatsAppMessage(
            1,
            2,
            null,
            'wamid.123456789',
            'accepted',
            '557991514957',
            '+5579991514957',
            'whatsapp',
            '{"test": "data"}',
            Carbon::now(),
            Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'message_id',
            'message',
            'whatsapp_message_id',
            'message_status',
            'wa_id',
            'input_phone',
            'messaging_product',
            'json',
            'created_at',
            'updated_at'
        ];
    }
}
