<?php

namespace Tests\Unit\Services\Meta\WhatsApp\Domains;

use Tests\Unit\Domains\BaseDomainTest;
use App\Services\Meta\WhatsApp\Domains\WhatsAppConversation;
use App\Domains\Inventory\Client;
use Carbon\Carbon;

class WhatsAppConversationTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        // Arrange
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();
        $client = $this->createMock(Client::class);
        $whatsappMetadata = ['contact_name' => '<PERSON>', 'profile_name' => '<PERSON>'];

        // Act
        $conversation = new WhatsAppConversation(
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            '{"test": "data"}',
            false,
            $createdAt,
            $updatedAt,
            '<PERSON>',
            'John',
            $whatsappMetadata,
            $client
        );

        // Assert
        $this->assertEquals(1, $conversation->id);
        $this->assertEquals(1, $conversation->organization_id);
        $this->assertEquals(1, $conversation->user_id);
        $this->assertEquals(1, $conversation->client_id);
        $this->assertEquals(1, $conversation->flow_id);
        $this->assertEquals(1, $conversation->phone_number_id);
        $this->assertEquals(1, $conversation->current_step_id);
        $this->assertEquals('{"test": "data"}', $conversation->json);
        $this->assertFalse($conversation->is_finished);
        $this->assertEquals($createdAt, $conversation->created_at);
        $this->assertEquals($updatedAt, $conversation->updated_at);
        $this->assertEquals('John Doe', $conversation->whatsapp_contact_name);
        $this->assertEquals('John', $conversation->whatsapp_profile_name);
        $this->assertEquals($whatsappMetadata, $conversation->whatsapp_metadata);
        $this->assertInstanceOf(Client::class, $conversation->client);
    }

    public function test_domain_instantiation_with_nulls()
    {
        // Act
        $conversation = new WhatsAppConversation();

        // Assert
        $this->assertNull($conversation->id);
        $this->assertNull($conversation->organization_id);
        $this->assertNull($conversation->user_id);
        $this->assertNull($conversation->client_id);
        $this->assertNull($conversation->flow_id);
        $this->assertNull($conversation->phone_number_id);
        $this->assertNull($conversation->current_step_id);
        $this->assertNull($conversation->json);
        $this->assertNull($conversation->is_finished);
        $this->assertNull($conversation->created_at);
        $this->assertNull($conversation->updated_at);
        $this->assertNull($conversation->whatsapp_contact_name);
        $this->assertNull($conversation->whatsapp_profile_name);
        $this->assertNull($conversation->whatsapp_metadata);
        $this->assertNull($conversation->client);
    }

    public function test_to_array_method()
    {
        // Arrange
        $client = $this->createMock(Client::class);
        $client->method('toArray')->willReturn([
            'id' => 1,
            'name' => 'John Doe',
            'phone' => '5511999999999'
        ]);

        $conversation = $this->createDomainInstance();
        $conversation->client = $client;

        // Act
        $array = $conversation->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals('John Doe', $array['whatsapp_contact_name']);
        $this->assertEquals('John', $array['whatsapp_profile_name']);
        $this->assertIsArray($array['whatsapp_metadata']);
        $this->assertIsArray($array['client']);
        $this->assertEquals('John Doe', $array['client']['name']);
    }

    public function test_to_array_method_with_null_client()
    {
        // Arrange
        $conversation = $this->createDomainInstance();
        $conversation->client = null;

        // Act
        $array = $conversation->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertNull($array['client']);
    }

    public function test_to_store_array_method()
    {
        // Arrange
        $conversation = $this->createDomainInstance();

        // Act
        $storeArray = $conversation->toStoreArray();

        // Assert
        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);
        $this->assertArrayNotHasKey('client', $storeArray);
        
        // Verify WhatsApp metadata is stored in JSON field
        $this->assertArrayHasKey('json', $storeArray);
        $jsonData = json_decode($storeArray['json'], true);
        $this->assertIsArray($jsonData);
        $this->assertEquals('John Doe', $jsonData['whatsapp_contact_name']);
        $this->assertEquals('John', $jsonData['whatsapp_profile_name']);
        $this->assertIsArray($jsonData['whatsapp_metadata']);
    }

    public function test_update_whatsapp_contact_info()
    {
        // Arrange
        $conversation = $this->createDomainInstance();

        // Act
        $conversation->updateWhatsAppContactInfo('Jane Smith', 'Jane');

        // Assert
        $this->assertEquals('Jane Smith', $conversation->whatsapp_contact_name);
        $this->assertEquals('Jane', $conversation->whatsapp_profile_name);
    }

    public function test_add_whatsapp_metadata()
    {
        // Arrange
        $conversation = $this->createDomainInstance();
        $newMetadata = ['last_message_time' => '2023-01-01 12:00:00'];

        // Act
        $conversation->addWhatsAppMetadata('last_message_time', '2023-01-01 12:00:00');

        // Assert
        $this->assertArrayHasKey('last_message_time', $conversation->whatsapp_metadata);
        $this->assertEquals('2023-01-01 12:00:00', $conversation->whatsapp_metadata['last_message_time']);
    }

    public function test_get_whatsapp_metadata()
    {
        // Arrange
        $conversation = $this->createDomainInstance();

        // Act
        $contactName = $conversation->getWhatsAppMetadata('contact_name');
        $nonExistent = $conversation->getWhatsAppMetadata('non_existent');

        // Assert
        $this->assertEquals('John Doe', $contactName);
        $this->assertNull($nonExistent);
    }

    public function test_get_whatsapp_metadata_with_default()
    {
        // Arrange
        $conversation = $this->createDomainInstance();

        // Act
        $result = $conversation->getWhatsAppMetadata('non_existent', 'default_value');

        // Assert
        $this->assertEquals('default_value', $result);
    }

    public function test_is_whatsapp_conversation()
    {
        // Arrange
        $conversation = $this->createDomainInstance();

        // Act & Assert
        $this->assertTrue($conversation->isWhatsAppConversation());
    }

    public function test_reset_to_start()
    {
        // Arrange
        $conversation = $this->createDomainInstance();
        $conversation->current_step_id = 5;
        $conversation->is_finished = true;
        $conversation->whatsapp_metadata = ['some' => 'data'];

        // Act
        $conversation->resetToStart();

        // Assert
        $this->assertNull($conversation->current_step_id);
        $this->assertNull($conversation->current_step);
        $this->assertFalse($conversation->is_finished);
        $this->assertEmpty($conversation->whatsapp_metadata);
    }

    public function test_finish_conversation()
    {
        // Arrange
        $conversation = $this->createDomainInstance();
        $conversation->is_finished = false;

        // Act
        $conversation->finishConversation();

        // Assert
        $this->assertTrue($conversation->is_finished);
    }

    public function test_to_store_array_preserves_whatsapp_metadata_structure()
    {
        // Arrange
        $complexMetadata = [
            'contact_name' => 'John Doe',
            'profile_name' => 'John',
            'last_seen' => '2023-01-01 12:00:00',
            'preferences' => [
                'language' => 'en',
                'notifications' => true
            ]
        ];

        $conversation = new WhatsAppConversation(
            1, 1, 1, 1, 1, 1, 1, null, false, null, null,
            'John Doe', 'John', $complexMetadata, null
        );

        // Act
        $storeArray = $conversation->toStoreArray();

        // Assert
        $jsonData = json_decode($storeArray['json'], true);
        $this->assertEquals('John Doe', $jsonData['whatsapp_contact_name']);
        $this->assertEquals('John', $jsonData['whatsapp_profile_name']);
        $this->assertIsArray($jsonData['whatsapp_metadata']);
        $this->assertEquals('en', $jsonData['whatsapp_metadata']['preferences']['language']);
        $this->assertTrue($jsonData['whatsapp_metadata']['preferences']['notifications']);
    }

    protected function createDomainInstance()
    {
        return new WhatsAppConversation(
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            '{"test": "data"}',
            false,
            Carbon::now(),
            Carbon::now(),
            'John Doe',
            'John',
            ['contact_name' => 'John Doe', 'profile_name' => 'John'],
            null
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'user_id',
            'client_id',
            'flow_id',
            'phone_number_id',
            'current_step_id',
            'json',
            'is_finished',
            'created_at',
            'updated_at',
            'whatsapp_contact_name',
            'whatsapp_profile_name',
            'whatsapp_metadata',
            'client'
        ];
    }
}
