<?php

namespace Tests\Unit\Services\Meta\WhatsApp\Domains;

use Tests\Unit\Domains\BaseDomainTest;
use App\Services\Meta\WhatsApp\Domains\WhatsAppTemplate;
use App\Domains\ChatBot\Template;
use Carbon\Carbon;

class WhatsAppTemplateTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        // Arrange
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();
        $template = $this->createMock(Template::class);

        // Act
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            2,
            $template,
            'APPROVED',
            'whatsapp_123',
            '{"status": "APPROVED"}',
            $createdAt,
            $updatedAt
        );

        // Assert
        $this->assertEquals(1, $whatsAppTemplate->id);
        $this->assertEquals(2, $whatsAppTemplate->template_id);
        $this->assertInstanceOf(Template::class, $whatsAppTemplate->template);
        $this->assertEquals('APPROVED', $whatsAppTemplate->status);
        $this->assertEquals('whatsapp_123', $whatsAppTemplate->external_id);
        $this->assertEquals('{"status": "APPROVED"}', $whatsAppTemplate->json);
        $this->assertEquals($createdAt, $whatsAppTemplate->created_at);
        $this->assertEquals($updatedAt, $whatsAppTemplate->updated_at);
    }

    public function test_domain_instantiation_with_nulls()
    {
        // Act
        $whatsAppTemplate = new WhatsAppTemplate(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );

        // Assert
        $this->assertNull($whatsAppTemplate->id);
        $this->assertNull($whatsAppTemplate->template_id);
        $this->assertNull($whatsAppTemplate->template);
        $this->assertNull($whatsAppTemplate->status);
        $this->assertNull($whatsAppTemplate->external_id);
        $this->assertNull($whatsAppTemplate->json);
        $this->assertNull($whatsAppTemplate->created_at);
        $this->assertNull($whatsAppTemplate->updated_at);
    }

    public function test_to_array_method()
    {
        // Arrange
        $template = $this->createMock(Template::class);
        $template->method('toArray')->willReturn([
            'id' => 1,
            'name' => 'test_template',
            'category' => 'UTILITY'
        ]);

        $whatsAppTemplate = $this->createDomainInstance();
        $whatsAppTemplate->template = $template;

        // Act
        $array = $whatsAppTemplate->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(2, $array['template_id']);
        $this->assertEquals('APPROVED', $array['status']);
        $this->assertEquals('whatsapp_123', $array['external_id']);
        $this->assertEquals('{"status": "APPROVED"}', $array['json']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        $this->assertIsArray($array['template']);
    }

    public function test_to_array_method_with_null_template()
    {
        // Arrange
        $whatsAppTemplate = $this->createDomainInstance();
        $whatsAppTemplate->template = null;

        // Act
        $array = $whatsAppTemplate->toArray();

        // Assert
        $this->assertIsArray($array);
        $this->assertNull($array['template']);
    }

    public function test_to_array_method_formats_dates()
    {
        // Arrange
        $createdAt = Carbon::create(2023, 1, 15, 10, 30, 45);
        $updatedAt = Carbon::create(2023, 2, 20, 14, 15, 30);

        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            2,
            null,
            'APPROVED',
            'whatsapp_123',
            '{"status": "APPROVED"}',
            $createdAt,
            $updatedAt
        );

        // Act
        $array = $whatsAppTemplate->toArray();

        // Assert
        $this->assertEquals('2023-01-15 10:30:45', $array['created_at']);
        $this->assertEquals('2023-02-20 14:15:30', $array['updated_at']);
    }

    public function test_to_array_method_with_null_dates_uses_current_time()
    {
        // Arrange
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            2,
            null,
            'APPROVED',
            'whatsapp_123',
            '{"status": "APPROVED"}',
            null,
            null
        );

        // Act
        $array = $whatsAppTemplate->toArray();

        // Assert
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        // Arrange
        $whatsAppTemplate = $this->createDomainInstance();

        // Act
        $storeArray = $whatsAppTemplate->toStoreArray();

        // Assert
        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);
        $this->assertArrayNotHasKey('template', $storeArray);
        
        $this->assertArrayHasKey('template_id', $storeArray);
        $this->assertArrayHasKey('status', $storeArray);
        $this->assertArrayHasKey('external_id', $storeArray);
        $this->assertArrayHasKey('json', $storeArray);
        
        $this->assertEquals(2, $storeArray['template_id']);
        $this->assertEquals('APPROVED', $storeArray['status']);
        $this->assertEquals('whatsapp_123', $storeArray['external_id']);
        $this->assertEquals('{"status": "APPROVED"}', $storeArray['json']);
    }

    public function test_to_update_array_method()
    {
        // Arrange
        $whatsAppTemplate = $this->createDomainInstance();

        // Act
        $updateArray = $whatsAppTemplate->toUpdateArray();

        // Assert
        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);
        $this->assertArrayNotHasKey('template', $updateArray);
        
        $this->assertArrayHasKey('template_id', $updateArray);
        $this->assertArrayHasKey('status', $updateArray);
        $this->assertArrayHasKey('external_id', $updateArray);
        $this->assertArrayHasKey('json', $updateArray);
        
        $this->assertEquals(2, $updateArray['template_id']);
        $this->assertEquals('APPROVED', $updateArray['status']);
        $this->assertEquals('whatsapp_123', $updateArray['external_id']);
        $this->assertEquals('{"status": "APPROVED"}', $updateArray['json']);
    }

    public function test_to_store_array_and_to_update_array_are_identical()
    {
        // Arrange
        $whatsAppTemplate = $this->createDomainInstance();

        // Act
        $storeArray = $whatsAppTemplate->toStoreArray();
        $updateArray = $whatsAppTemplate->toUpdateArray();

        // Assert
        $this->assertEquals($storeArray, $updateArray);
    }

    public function test_constructor_with_different_status_values()
    {
        // Test different status values
        $statuses = ['PENDING', 'APPROVED', 'REJECTED', 'DISABLED', null];

        foreach ($statuses as $status) {
            $whatsAppTemplate = new WhatsAppTemplate(
                1,
                2,
                null,
                $status,
                'whatsapp_123',
                '{"status": "' . $status . '"}',
                Carbon::now(),
                Carbon::now()
            );

            $this->assertEquals($status, $whatsAppTemplate->status);
        }
    }

    public function test_json_field_can_store_complex_data()
    {
        // Arrange
        $complexJson = json_encode([
            'status' => 'APPROVED',
            'components' => [
                ['type' => 'HEADER', 'format' => 'TEXT'],
                ['type' => 'BODY', 'text' => 'Hello {{1}}']
            ],
            'language' => 'en_US',
            'category' => 'UTILITY'
        ]);

        // Act
        $whatsAppTemplate = new WhatsAppTemplate(
            1,
            2,
            null,
            'APPROVED',
            'whatsapp_123',
            $complexJson,
            Carbon::now(),
            Carbon::now()
        );

        // Assert
        $this->assertEquals($complexJson, $whatsAppTemplate->json);
        
        $array = $whatsAppTemplate->toArray();
        $this->assertEquals($complexJson, $array['json']);
        
        $storeArray = $whatsAppTemplate->toStoreArray();
        $this->assertEquals($complexJson, $storeArray['json']);
    }

    protected function createDomainInstance()
    {
        return new WhatsAppTemplate(
            1,
            2,
            null,
            'APPROVED',
            'whatsapp_123',
            '{"status": "APPROVED"}',
            Carbon::now(),
            Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'template_id',
            'template',
            'status',
            'external_id',
            'json',
            'created_at',
            'updated_at'
        ];
    }
}
