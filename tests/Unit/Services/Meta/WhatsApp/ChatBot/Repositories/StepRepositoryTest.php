<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Repositories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\StepRepository;
use App\Factories\ChatBot\StepFactory;
use App\Domains\ChatBot\Step;
use App\Models\Step as StepModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Flow;

class StepRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected StepRepository $repository;
    protected StepFactory $stepFactory;
    protected Organization $organization;
    protected User $user;
    protected Flow $flow;

    protected function setUp(): void
    {
        parent::setUp();

        $this->stepFactory = $this->createMock(StepFactory::class);
        $this->repository = new StepRepository($this->stepFactory);

        // Create test data
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->flow = Flow::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);
    }

    public function test_finds_step_by_step_number_successfully()
    {
        // Arrange
        $stepModel = StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 2,
            'name' => 'Step 2',
            'message' => 'This is step 2'
        ]);

        $expectedStep = new Step(
            id: $stepModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            flow_id: $this->flow->id,
            step: 2,
            name: 'Step 2',
            message: 'This is step 2'
        );

        $this->stepFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($stepModel) {
                return $model->id === $stepModel->id && $model->step === 2;
            }))
            ->willReturn($expectedStep);

        // Act
        $result = $this->repository->findByStepNumber($this->flow->id, 2);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
        $this->assertEquals(2, $result->step);
        $this->assertEquals('Step 2', $result->name);
        $this->assertEquals($this->flow->id, $result->flow_id);
    }

    public function test_returns_null_when_step_not_found()
    {
        // Arrange
        $this->stepFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findByStepNumber($this->flow->id, 999);

        // Assert
        $this->assertNull($result);
    }

    public function test_finds_correct_step_when_multiple_steps_exist()
    {
        // Arrange
        StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 1,
            'name' => 'Step 1'
        ]);

        $targetStepModel = StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 3,
            'name' => 'Step 3'
        ]);

        StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 5,
            'name' => 'Step 5'
        ]);

        $expectedStep = new Step(
            id: $targetStepModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            flow_id: $this->flow->id,
            step: 3,
            name: 'Step 3'
        );

        $this->stepFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($targetStepModel) {
                return $model->id === $targetStepModel->id;
            }))
            ->willReturn($expectedStep);

        // Act
        $result = $this->repository->findByStepNumber($this->flow->id, 3);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
        $this->assertEquals(3, $result->step);
        $this->assertEquals('Step 3', $result->name);
    }

    public function test_finds_step_only_within_specified_flow()
    {
        // Arrange
        $otherFlow = Flow::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);

        // Create step in other flow with same step number
        StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $otherFlow->id,
            'step' => 2,
            'name' => 'Other Flow Step 2'
        ]);

        // Create step in target flow
        $targetStepModel = StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 2,
            'name' => 'Target Flow Step 2'
        ]);

        $expectedStep = new Step(
            id: $targetStepModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            flow_id: $this->flow->id,
            step: 2,
            name: 'Target Flow Step 2'
        );

        $this->stepFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($targetStepModel) {
                return $model->id === $targetStepModel->id && 
                       $model->flow_id === $this->flow->id;
            }))
            ->willReturn($expectedStep);

        // Act
        $result = $this->repository->findByStepNumber($this->flow->id, 2);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
        $this->assertEquals('Target Flow Step 2', $result->name);
        $this->assertEquals($this->flow->id, $result->flow_id);
    }

    public function test_finds_first_step_successfully()
    {
        // Arrange
        StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 3,
            'name' => 'Step 3'
        ]);

        $firstStepModel = StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 1,
            'name' => 'First Step'
        ]);

        StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 2,
            'name' => 'Step 2'
        ]);

        $expectedStep = new Step(
            id: $firstStepModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            flow_id: $this->flow->id,
            step: 1,
            name: 'First Step'
        );

        $this->stepFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($firstStepModel) {
                return $model->id === $firstStepModel->id && $model->step === 1;
            }))
            ->willReturn($expectedStep);

        // Act
        $result = $this->repository->findFirstStep($this->flow->id);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
        $this->assertEquals(1, $result->step);
        $this->assertEquals('First Step', $result->name);
    }

    public function test_returns_null_when_no_first_step_exists()
    {
        // Arrange
        $this->stepFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findFirstStep($this->flow->id);

        // Assert
        $this->assertNull($result);
    }

    public function test_finds_next_step_successfully()
    {
        // Arrange
        $currentStepModel = StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 2,
            'name' => 'Current Step'
        ]);

        $nextStepModel = StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 3,
            'name' => 'Next Step'
        ]);

        $currentStep = new Step(
            id: $currentStepModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            flow_id: $this->flow->id,
            step: 2,
            name: 'Current Step'
        );

        $expectedNextStep = new Step(
            id: $nextStepModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            flow_id: $this->flow->id,
            step: 3,
            name: 'Next Step'
        );

        $this->stepFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with($this->callback(function ($model) use ($nextStepModel) {
                return $model->id === $nextStepModel->id && $model->step === 3;
            }))
            ->willReturn($expectedNextStep);

        // Act
        $result = $this->repository->findNextStep($currentStep);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
        $this->assertEquals(3, $result->step);
        $this->assertEquals('Next Step', $result->name);
    }

    public function test_returns_null_when_no_next_step_exists()
    {
        // Arrange
        $lastStepModel = StepModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'flow_id' => $this->flow->id,
            'step' => 5,
            'name' => 'Last Step'
        ]);

        $lastStep = new Step(
            id: $lastStepModel->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            flow_id: $this->flow->id,
            step: 5,
            name: 'Last Step'
        );

        $this->stepFactory
            ->expects($this->once())
            ->method('buildFromModel')
            ->with(null)
            ->willReturn(null);

        // Act
        $result = $this->repository->findNextStep($lastStep);

        // Assert
        $this->assertNull($result);
    }
}
