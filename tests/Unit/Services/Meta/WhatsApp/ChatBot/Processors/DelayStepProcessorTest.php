<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\DelayStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class DelayStepProcessorTest extends TestCase
{
    protected DelayStepProcessor $processor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->processor = new DelayStepProcessor();
    }

    public function test_implements_step_processor_interface()
    {
        $this->assertInstanceOf(StepProcessorInterface::class, $this->processor);
    }

    public function test_get_supported_step_types()
    {
        $supportedTypes = $this->processor->getSupportedStepTypes();
        
        $this->assertIsArray($supportedTypes);
        $this->assertCount(1, $supportedTypes);
        $this->assertContains(StepType::DELAY->value, $supportedTypes);
    }

    public function test_can_process_delay_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::DELAY;
        
        $this->assertTrue($this->processor->canProcess($step));
    }

    public function test_cannot_process_non_delay_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;
        
        $this->assertFalse($this->processor->canProcess($step));
    }

    public function test_process_delay_step_without_configuration()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('delay', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('no_delay_config', $result['action']);
        $this->assertEquals(0, $result['delay_seconds']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_process_delay_step_with_valid_configuration()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'duration_seconds' => 30,
            'message' => 'Please wait while we process your request...'
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('delay', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('delay_processing', $result['action']);
        $this->assertEquals(30, $result['delay_seconds']);
        $this->assertEquals('Please wait while we process your request...', $result['delay_message']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
        $this->assertTrue($result['delay_scheduled']);
    }

    public function test_process_delay_step_with_configuration_without_message()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'duration_seconds' => 15
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals(15, $result['delay_seconds']);
        $this->assertNull($result['delay_message']);
        $this->assertTrue($result['delay_scheduled']);
    }

    public function test_process_delay_step_with_zero_duration()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'duration_seconds' => 0,
            'message' => 'Immediate processing'
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals(0, $result['delay_seconds']);
        $this->assertEquals('Immediate processing', $result['delay_message']);
        $this->assertTrue($result['delay_scheduled']);
    }

    public function test_process_delay_step_with_invalid_json()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = 'invalid json';
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should fall back to no delay config
        $this->assertEquals('no_delay_config', $result['action']);
        $this->assertEquals(0, $result['delay_seconds']);
    }

    public function test_process_delay_step_with_json_without_duration_seconds()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'message' => 'Some message',
            'other_field' => 'value'
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should fall back to no delay config when duration_seconds is missing
        $this->assertEquals('no_delay_config', $result['action']);
        $this->assertEquals(0, $result['delay_seconds']);
    }

    public function test_app_make_works()
    {
        $processor = $this->createMock(DelayStepProcessor::class);
        
        $this->assertInstanceOf(DelayStepProcessor::class, $processor);
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
    }
}
