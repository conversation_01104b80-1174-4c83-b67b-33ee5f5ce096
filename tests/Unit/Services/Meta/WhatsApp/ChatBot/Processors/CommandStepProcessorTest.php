<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\CommandStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand;
use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class CommandStepProcessorTest extends TestCase
{
    protected CommandStepProcessor $processor;
    protected ExecuteCommand $executeCommand;

    protected function setUp(): void
    {
        parent::setUp();
        $this->executeCommand = $this->createMock(ExecuteCommand::class);
        $this->processor = new CommandStepProcessor($this->executeCommand);
    }

    public function test_implements_step_processor_interface()
    {
        $this->assertInstanceOf(StepProcessorInterface::class, $this->processor);
    }

    public function test_get_supported_step_types()
    {
        $supportedTypes = $this->processor->getSupportedStepTypes();

        $this->assertIsArray($supportedTypes);
        $this->assertCount(1, $supportedTypes);
        $this->assertContains(StepType::COMMAND->value, $supportedTypes);
    }

    public function test_can_process_command_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::COMMAND;

        $this->assertTrue($this->processor->canProcess($step));
    }

    public function test_cannot_process_non_command_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;

        $this->assertFalse($this->processor->canProcess($step));
    }

    public function test_process_command_step_with_successful_execution()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'update_client_data';
        $step->next_step = 456;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->id = 789;

        // Mock successful command execution
        $commandResult = [
            'success' => true,
            'message' => 'Client data updated successfully',
            'data' => ['client_id' => 1, 'updated_fields' => ['name', 'email']]
        ];

        $this->executeCommand
            ->expects($this->once())
            ->method('perform')
            ->with($step, $interaction, $conversation)
            ->willReturn($commandResult);

        $result = $this->processor->process($step, $interaction, $conversation);

        // Verify successful execution result
        $this->assertEquals('command', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('command_executed', $result['action']);
        $this->assertEquals($commandResult, $result['result']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
        $this->assertTrue($result['success']);
        $this->assertEquals('Client data updated successfully', $result['message']);
    }

    public function test_process_command_step_with_failed_execution()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'failing_command';
        $step->next_step = 456;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->id = 789;

        // Mock command execution that returns failure
        $commandResult = [
            'success' => false,
            'error' => 'Database connection failed',
            'message' => 'Unable to process command'
        ];

        $this->executeCommand
            ->expects($this->once())
            ->method('perform')
            ->with($step, $interaction, $conversation)
            ->willReturn($commandResult);

        $result = $this->processor->process($step, $interaction, $conversation);

        // Verify failed execution result
        $this->assertEquals('command', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('command_executed', $result['action']);
        $this->assertEquals($commandResult, $result['result']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']); // Still moves to next even if command fails
        $this->assertFalse($result['success']);
        $this->assertEquals('Unable to process command', $result['message']);
    }

    public function test_process_command_step_with_exception()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'exception_command';
        $step->next_step = 456;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->id = 789;
        $conversation->organization_id = 1;

        // Mock command execution that throws exception
        $this->executeCommand
            ->expects($this->once())
            ->method('perform')
            ->with($step, $interaction, $conversation)
            ->willThrowException(new \Exception('Command execution failed'));

        $result = $this->processor->process($step, $interaction, $conversation);

        // Verify exception handling
        $this->assertEquals('command', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('command_failed', $result['action']);
        $this->assertFalse($result['result']['success']);
        $this->assertEquals('Command execution failed', $result['result']['error']);
        $this->assertEquals('Erro ao executar comando. Tente novamente.', $result['result']['message']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertFalse($result['move_to_next']); // Don't move to next step on exception
        $this->assertFalse($result['success']);
        $this->assertEquals('Command execution failed', $result['error']);
    }

    public function test_process_command_step_with_command_result_without_success_field()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);

        // Mock command execution that returns result without explicit success field
        $commandResult = [
            'data' => ['processed' => true],
            'message' => 'Command completed'
        ];

        $this->executeCommand
            ->expects($this->once())
            ->method('perform')
            ->willReturn($commandResult);

        $result = $this->processor->process($step, $interaction, $conversation);

        // Should default success to true
        $this->assertTrue($result['success']);
        $this->assertEquals('Command completed', $result['message']);
    }

    public function test_process_command_step_with_command_result_without_message_field()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;

        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);

        // Mock command execution that returns result without message field
        $commandResult = [
            'success' => true,
            'data' => ['processed' => true]
        ];

        $this->executeCommand
            ->expects($this->once())
            ->method('perform')
            ->willReturn($commandResult);

        $result = $this->processor->process($step, $interaction, $conversation);

        // Should handle null message
        $this->assertNull($result['message']);
    }

    public function test_app_make_works()
    {
        // Test that we can use app()->make() to instantiate the processor
        // Note: This would require proper service container binding in real app
        $executeCommand = $this->createMock(ExecuteCommand::class);
        $processor = new CommandStepProcessor($executeCommand);

        $this->assertInstanceOf(CommandStepProcessor::class, $processor);
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
    }
}
