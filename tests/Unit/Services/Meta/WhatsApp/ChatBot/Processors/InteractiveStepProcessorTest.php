<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\InteractiveStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class InteractiveStepProcessorTest extends TestCase
{
    protected InteractiveStepProcessor $processor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->processor = new InteractiveStepProcessor();
    }

    public function test_implements_step_processor_interface()
    {
        $this->assertInstanceOf(StepProcessorInterface::class, $this->processor);
    }

    public function test_get_supported_step_types()
    {
        $supportedTypes = $this->processor->getSupportedStepTypes();
        
        $this->assertIsArray($supportedTypes);
        $this->assertCount(1, $supportedTypes);
        $this->assertContains(StepType::INTERACTIVE->value, $supportedTypes);
    }

    public function test_can_process_interactive_step()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::INTERACTIVE;
        
        $this->assertTrue($this->processor->canProcess($step));
    }

    public function test_cannot_process_non_interactive_step()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;
        
        $this->assertFalse($this->processor->canProcess($step));
    }

    public function test_process_interactive_step_shows_options_when_no_selection()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->id = 123;
        $step->step = 'Choose an option';
        $step->next_step = 456;
        $step->json = null;
        $step->components = [];
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('isButtonInteraction')->willReturn(false);
        $interaction->method('isListInteraction')->willReturn(false);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should show options
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('show_options', $result['action']);
        $this->assertEquals('Choose an option', $result['message']);
        $this->assertArrayHasKey('options', $result);
        $this->assertFalse($result['move_to_next']);
    }

    public function test_process_interactive_step_processes_button_selection()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->id = 123;
        $step->next_step = 456;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('isButtonInteraction')->willReturn(true);
        $interaction->method('getButtonId')->willReturn('button_1');
        $interaction->method('isListInteraction')->willReturn(false);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should process selection
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('process_selection', $result['action']);
        $this->assertEquals('button_1', $result['selection']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_process_interactive_step_processes_list_selection()
    {
        // Create mock objects
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->id = 123;
        $step->next_step = 456;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('isButtonInteraction')->willReturn(false);
        $interaction->method('isListInteraction')->willReturn(true);
        $interaction->method('getListSelectionId')->willReturn('list_item_1');
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Should process selection
        $this->assertEquals('interactive', $result['type']);
        $this->assertEquals('process_selection', $result['action']);
        $this->assertEquals('list_item_1', $result['selection']);
        $this->assertTrue($result['move_to_next']);
    }

    public function test_get_step_message_from_json()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Fallback message';
        $step->json = json_encode(['message' => 'JSON message']);
        $step->components = [];
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('isButtonInteraction')->willReturn(false);
        $interaction->method('isListInteraction')->willReturn(false);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('JSON message', $result['message']);
    }

    public function test_get_step_message_from_text_field()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Fallback message';
        $step->json = json_encode(['text' => 'JSON text field']);
        $step->components = [];
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('isButtonInteraction')->willReturn(false);
        $interaction->method('isListInteraction')->willReturn(false);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('JSON text field', $result['message']);
    }

    public function test_get_step_message_fallback()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = null;
        $step->json = null;
        $step->components = [];
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('isButtonInteraction')->willReturn(false);
        $interaction->method('isListInteraction')->willReturn(false);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('Please select an option:', $result['message']);
    }

    public function test_get_options_from_json_buttons()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Choose option';
        $step->components = [];
        $step->json = json_encode([
            'buttons' => [
                ['id' => 'btn1', 'text' => 'Option 1'],
                ['text' => 'Option 2'] // No ID, should use text as ID
            ]
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('isButtonInteraction')->willReturn(false);
        $interaction->method('isListInteraction')->willReturn(false);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertCount(2, $result['options']);
        $this->assertEquals('btn1', $result['options'][0]['id']);
        $this->assertEquals('Option 1', $result['options'][0]['text']);
        $this->assertEquals('button', $result['options'][0]['type']);
        $this->assertEquals('Option 2', $result['options'][1]['id']);
        $this->assertEquals('Option 2', $result['options'][1]['text']);
    }

    public function test_get_options_from_json_list_items()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->step = 'Choose option';
        $step->components = [];
        $step->json = json_encode([
            'list_items' => [
                ['id' => 'item1', 'text' => 'List Item 1'],
                ['text' => 'List Item 2'] // No ID, should use text as ID
            ]
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $interaction->method('isButtonInteraction')->willReturn(false);
        $interaction->method('isListInteraction')->willReturn(false);
        
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertCount(2, $result['options']);
        $this->assertEquals('item1', $result['options'][0]['id']);
        $this->assertEquals('List Item 1', $result['options'][0]['text']);
        $this->assertEquals('list_item', $result['options'][0]['type']);
        $this->assertEquals('List Item 2', $result['options'][1]['id']);
        $this->assertEquals('List Item 2', $result['options'][1]['text']);
    }

    public function test_app_make_works()
    {
        $processor = $this->createMock(InteractiveStepProcessor::class);
        
        $this->assertInstanceOf(InteractiveStepProcessor::class, $processor);
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
    }
}
