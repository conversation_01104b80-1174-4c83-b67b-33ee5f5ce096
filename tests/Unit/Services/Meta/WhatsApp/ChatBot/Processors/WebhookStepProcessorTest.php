<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Services\Meta\WhatsApp\ChatBot\Processors\WebhookStepProcessor;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorInterface;
use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;
use PHPUnit\Framework\TestCase;

class WebhookStepProcessorTest extends TestCase
{
    protected WebhookStepProcessor $processor;

    protected function setUp(): void
    {
        parent::setUp();
        $this->processor = new WebhookStepProcessor();
    }

    public function test_implements_step_processor_interface()
    {
        $this->assertInstanceOf(StepProcessorInterface::class, $this->processor);
    }

    public function test_get_supported_step_types()
    {
        $supportedTypes = $this->processor->getSupportedStepTypes();
        
        $this->assertIsArray($supportedTypes);
        $this->assertCount(1, $supportedTypes);
        $this->assertContains(StepType::WEBHOOK->value, $supportedTypes);
    }

    public function test_can_process_webhook_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::WEBHOOK;
        
        $this->assertTrue($this->processor->canProcess($step));
    }

    public function test_cannot_process_non_webhook_step()
    {
        $step = $this->createMock(Step::class);
        $step->expects($this->once())->method('setStepTypeFromLegacyFields');
        $step->step_type = StepType::MESSAGE;
        
        $this->assertFalse($this->processor->canProcess($step));
    }

    public function test_process_webhook_step_without_configuration()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = null;
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('webhook', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('webhook_config_missing', $result['action']);
        $this->assertEquals('Webhook configuration not found', $result['error']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertFalse($result['move_to_next']);
        $this->assertFalse($result['success']);
    }

    public function test_process_webhook_step_with_valid_configuration()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'url' => 'https://api.example.com/webhook',
            'method' => 'POST',
            'headers' => ['Content-Type' => 'application/json']
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->id = 789;
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        $this->assertEquals('webhook', $result['type']);
        $this->assertEquals(123, $result['step_id']);
        $this->assertEquals('webhook_executed', $result['action']);
        $this->assertEquals(456, $result['next_step']);
        $this->assertTrue($result['move_to_next']);
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('webhook_result', $result);
        $this->assertArrayHasKey('response_data', $result);
    }

    public function test_process_webhook_step_placeholder_implementation()
    {
        $step = $this->createMock(Step::class);
        $step->id = 123;
        $step->next_step = 456;
        $step->json = json_encode([
            'url' => 'https://api.example.com/webhook',
            'method' => 'GET'
        ]);
        
        $interaction = $this->createMock(WhatsAppInteraction::class);
        $conversation = $this->createMock(WhatsAppConversation::class);
        
        $result = $this->processor->process($step, $interaction, $conversation);
        
        // Test placeholder implementation details
        $this->assertTrue($result['webhook_result']['success']);
        $this->assertStringContainsString('placeholder implementation', $result['webhook_result']['message']);
        $this->assertEquals('https://api.example.com/webhook', $result['webhook_result']['data']['url']);
        $this->assertEquals('GET', $result['webhook_result']['data']['method']);
        $this->assertEquals(200, $result['webhook_result']['data']['status_code']);
    }

    public function test_app_make_works()
    {
        $processor = $this->createMock(WebhookStepProcessor::class);
        
        $this->assertInstanceOf(WebhookStepProcessor::class, $processor);
        $this->assertInstanceOf(StepProcessorInterface::class, $processor);
    }
}
