<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\Services;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConditionalNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\StepRepository;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use Exception;

class ConditionalNavigationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ConditionalNavigationService $service;
    protected StepRepository $stepRepository;
    protected WhatsAppConversation $conversation;
    protected Step $currentStep;
    protected Step $nextStep;

    protected function setUp(): void
    {
        parent::setUp();

        $this->stepRepository = $this->createMock(StepRepository::class);
        $this->service = new ConditionalNavigationService($this->stepRepository);

        // Create mock conversation
        $client = $this->createMock(Client::class);
        $client->method('toArray')->willReturn(['id' => 1, 'name' => 'Test Client']);

        $this->conversation = new WhatsAppConversation(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number_id: 1,
            current_step_id: 1,
            client: $client
        );

        // Create mock current step
        $this->currentStep = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'Current Step',
            message: 'Choose an option',
            is_input: false,
            json: json_encode([
                'buttons' => [
                    [
                        'id' => 'btn1',
                        'title' => 'Option 1',
                        'internal_type' => 'condition',
                        'condition_value' => 'option1'
                    ],
                    [
                        'id' => 'btn2', 
                        'title' => 'Option 2',
                        'internal_type' => 'condition',
                        'condition_value' => 'option2'
                    ]
                ]
            ])
        );

        // Create mock next step
        $this->nextStep = new Step(
            id: 2,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 2,
            name: 'Next Step',
            message: 'You selected option 1'
        );
    }

    public function test_finds_next_step_by_button_condition()
    {
        // Arrange
        $messageData = [
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button_reply',
                'button_reply' => [
                    'id' => 'btn1'
                ]
            ]
        ];

        $this->stepRepository
            ->expects($this->once())
            ->method('findByStepNumber')
            ->with(1, 2) // flow_id, step_number
            ->willReturn($this->nextStep);

        // Act
        $result = $this->service->findNextStep($this->conversation, $this->currentStep, $messageData);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
        $this->assertEquals(2, $result->id);
        $this->assertEquals('Next Step', $result->name);
    }

    public function test_finds_next_step_by_text_condition()
    {
        // Arrange
        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'option1'
            ]
        ];

        $this->stepRepository
            ->expects($this->once())
            ->method('findByStepNumber')
            ->with(1, 2)
            ->willReturn($this->nextStep);

        // Act
        $result = $this->service->findNextStep($this->conversation, $this->currentStep, $messageData);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
        $this->assertEquals(2, $result->id);
    }

    public function test_returns_null_when_no_matching_condition()
    {
        // Arrange
        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'invalid_option'
            ]
        ];

        $this->stepRepository
            ->expects($this->never())
            ->method('findByStepNumber');

        // Act
        $result = $this->service->findNextStep($this->conversation, $this->currentStep, $messageData);

        // Assert
        $this->assertNull($result);
    }

    public function test_returns_null_when_step_has_no_conditions()
    {
        // Arrange
        $stepWithoutConditions = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'Step Without Conditions',
            message: 'No conditions here',
            json: json_encode(['buttons' => []])
        );

        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'anything'
            ]
        ];

        // Act
        $result = $this->service->findNextStep($this->conversation, $stepWithoutConditions, $messageData);

        // Assert
        $this->assertNull($result);
    }

    public function test_handles_list_reply_conditions()
    {
        // Arrange
        $stepWithListConditions = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'List Step',
            message: 'Choose from list',
            json: json_encode([
                'list' => [
                    'sections' => [
                        [
                            'rows' => [
                                [
                                    'id' => 'list_option1',
                                    'title' => 'List Option 1',
                                    'internal_type' => 'condition',
                                    'condition_value' => 'list1'
                                ]
                            ]
                        ]
                    ]
                ]
            ])
        );

        $messageData = [
            'type' => 'interactive',
            'interactive' => [
                'type' => 'list_reply',
                'list_reply' => [
                    'id' => 'list_option1'
                ]
            ]
        ];

        $this->stepRepository
            ->expects($this->once())
            ->method('findByStepNumber')
            ->with(1, 2)
            ->willReturn($this->nextStep);

        // Act
        $result = $this->service->findNextStep($this->conversation, $stepWithListConditions, $messageData);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
        $this->assertEquals(2, $result->id);
    }

    public function test_throws_exception_when_step_repository_fails()
    {
        // Arrange
        $messageData = [
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button_reply',
                'button_reply' => [
                    'id' => 'btn1'
                ]
            ]
        ];

        $this->stepRepository
            ->expects($this->once())
            ->method('findByStepNumber')
            ->willThrowException(new Exception('Database error'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');
        
        $this->service->findNextStep($this->conversation, $this->currentStep, $messageData);
    }

    public function test_handles_malformed_json_gracefully()
    {
        // Arrange
        $stepWithBadJson = new Step(
            id: 1,
            organization_id: 1,
            user_id: 1,
            flow_id: 1,
            step: 1,
            name: 'Bad JSON Step',
            message: 'Invalid JSON',
            json: 'invalid json'
        );

        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'anything'
            ]
        ];

        // Act
        $result = $this->service->findNextStep($this->conversation, $stepWithBadJson, $messageData);

        // Assert
        $this->assertNull($result);
    }

    public function test_case_insensitive_text_matching()
    {
        // Arrange
        $messageData = [
            'type' => 'text',
            'text' => [
                'body' => 'OPTION1' // uppercase
            ]
        ];

        $this->stepRepository
            ->expects($this->once())
            ->method('findByStepNumber')
            ->with(1, 2)
            ->willReturn($this->nextStep);

        // Act
        $result = $this->service->findNextStep($this->conversation, $this->currentStep, $messageData);

        // Assert
        $this->assertInstanceOf(Step::class, $result);
    }
}
