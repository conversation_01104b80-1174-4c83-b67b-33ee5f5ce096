<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\UseCases;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendWhatsAppResponse;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Services\Meta\WhatsApp\MessageService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Domains\ChatBot\Message;
use App\Factories\ChatBot\MessageFactory;
use Exception;

class SendWhatsAppResponseTest extends TestCase
{
    use RefreshDatabase;

    protected SendWhatsAppResponse $sendWhatsAppResponse;
    protected $mockConversationRepository;
    protected $mockMessageService;
    protected $mockMessageFactory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks
        $this->mockConversationRepository = $this->createMock(WhatsAppConversationRepository::class);
        $this->mockMessageService = $this->createMock(MessageService::class);
        $this->mockMessageFactory = $this->createMock(MessageFactory::class);

        $this->sendWhatsAppResponse = new SendWhatsAppResponse(
            $this->mockConversationRepository,
            $this->mockMessageService,
            $this->mockMessageFactory
        );
    }

    public function test_perform_sends_message_response()
    {
        // Arrange
        $stepResult = [
            'type' => 'message',
            'step_id' => 1,
            'action' => 'send_message',
            'message' => 'Welcome to our service!',
            'move_to_next' => true,
            'next_step' => 2
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->id = 1;
        $conversation->client_id = 1;
        $conversation->current_step_id = 1;

        $message = $this->createMock(Message::class);
        $message->id = 1;

        $whatsAppResponse = [
            'messaging_product' => 'whatsapp',
            'contacts' => [['wa_id' => '5511999999999']],
            'messages' => [['id' => 'wamid.response123']]
        ];

        $updatedConversation = $this->createMock(WhatsAppConversation::class);
        $updatedConversation->current_step_id = 2;

        // Set up mocks
        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->with($stepResult, $conversation)
            ->willReturn($message);

        $this->mockMessageService
            ->expects($this->once())
            ->method('send')
            ->with($message)
            ->willReturn($whatsAppResponse);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('updateCurrentStep')
            ->with($conversation, 2)
            ->willReturn($updatedConversation);

        // Act
        $result = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('message_sent', $result['action']);
        $this->assertEquals($whatsAppResponse, $result['whatsapp_response']);
        $this->assertEquals(2, $result['updated_step_id']);
        $this->assertFalse($result['conversation_finished']);
    }

    public function test_perform_sends_interactive_response()
    {
        // Arrange
        $stepResult = [
            'type' => 'interactive',
            'step_id' => 2,
            'action' => 'show_options',
            'message' => 'Choose an option:',
            'options' => [
                ['id' => 'option_1', 'title' => 'Yes'],
                ['id' => 'option_2', 'title' => 'No']
            ],
            'move_to_next' => false
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->id = 1;
        $conversation->current_step_id = 2;

        $message = $this->createMock(Message::class);
        $whatsAppResponse = [
            'messaging_product' => 'whatsapp',
            'messages' => [['id' => 'wamid.interactive123']]
        ];

        // Set up mocks
        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->willReturn($message);

        $this->mockMessageService
            ->expects($this->once())
            ->method('send')
            ->willReturn($whatsAppResponse);

        // Should not update step since move_to_next is false
        $this->mockConversationRepository
            ->expects($this->never())
            ->method('updateCurrentStep');

        // Act
        $result = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('interactive_sent', $result['action']);
        $this->assertEquals($whatsAppResponse, $result['whatsapp_response']);
        $this->assertEquals(2, $result['current_step_id']); // Should remain the same
        $this->assertFalse($result['conversation_finished']);
    }

    public function test_perform_finishes_conversation()
    {
        // Arrange
        $stepResult = [
            'type' => 'message',
            'step_id' => 5,
            'action' => 'send_message',
            'message' => 'Thank you! Conversation finished.',
            'finish_conversation' => true,
            'move_to_next' => false
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->id = 1;
        $conversation->current_step_id = 5;

        $message = $this->createMock(Message::class);
        $whatsAppResponse = [
            'messaging_product' => 'whatsapp',
            'messages' => [['id' => 'wamid.finish123']]
        ];

        $finishedConversation = $this->createMock(WhatsAppConversation::class);
        $finishedConversation->is_finished = true;

        // Set up mocks
        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->willReturn($message);

        $this->mockMessageService
            ->expects($this->once())
            ->method('send')
            ->willReturn($whatsAppResponse);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('finishConversation')
            ->with($conversation)
            ->willReturn($finishedConversation);

        // Act
        $result = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('conversation_finished', $result['action']);
        $this->assertEquals($whatsAppResponse, $result['whatsapp_response']);
        $this->assertTrue($result['conversation_finished']);
    }

    public function test_perform_handles_no_message_response()
    {
        // Arrange
        $stepResult = [
            'type' => 'processing',
            'step_id' => 3,
            'action' => 'process_data',
            'move_to_next' => true,
            'next_step' => 4,
            'send_message' => false // Explicitly no message to send
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step_id = 3;

        $updatedConversation = $this->createMock(WhatsAppConversation::class);
        $updatedConversation->current_step_id = 4;

        // Set up mocks - should not create or send message
        $this->mockMessageFactory
            ->expects($this->never())
            ->method('buildFromStepResult');

        $this->mockMessageService
            ->expects($this->never())
            ->method('send');

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('updateCurrentStep')
            ->with($conversation, 4)
            ->willReturn($updatedConversation);

        // Act
        $result = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('step_updated', $result['action']);
        $this->assertNull($result['whatsapp_response']);
        $this->assertEquals(4, $result['updated_step_id']);
        $this->assertFalse($result['conversation_finished']);
    }

    public function test_perform_handles_message_factory_exception()
    {
        // Arrange
        $stepResult = [
            'type' => 'message',
            'action' => 'send_message',
            'message' => 'Test message'
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);

        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->willThrowException(new Exception('Message creation failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Message creation failed');

        $this->sendWhatsAppResponse->perform($stepResult, $conversation);
    }

    public function test_perform_handles_message_service_exception()
    {
        // Arrange
        $stepResult = [
            'type' => 'message',
            'action' => 'send_message',
            'message' => 'Test message'
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $message = $this->createMock(Message::class);

        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->willReturn($message);

        $this->mockMessageService
            ->expects($this->once())
            ->method('send')
            ->willThrowException(new Exception('WhatsApp API error'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('WhatsApp API error');

        $this->sendWhatsAppResponse->perform($stepResult, $conversation);
    }

    public function test_perform_handles_conversation_update_exception()
    {
        // Arrange
        $stepResult = [
            'type' => 'message',
            'action' => 'send_message',
            'message' => 'Test message',
            'move_to_next' => true,
            'next_step' => 2
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $message = $this->createMock(Message::class);
        $whatsAppResponse = ['success' => true];

        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->willReturn($message);

        $this->mockMessageService
            ->expects($this->once())
            ->method('send')
            ->willReturn($whatsAppResponse);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('updateCurrentStep')
            ->willThrowException(new Exception('Database update failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database update failed');

        $this->sendWhatsAppResponse->perform($stepResult, $conversation);
    }

    public function test_perform_handles_conversation_finish_exception()
    {
        // Arrange
        $stepResult = [
            'type' => 'message',
            'action' => 'send_message',
            'message' => 'Goodbye',
            'finish_conversation' => true
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $message = $this->createMock(Message::class);
        $whatsAppResponse = ['success' => true];

        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->willReturn($message);

        $this->mockMessageService
            ->expects($this->once())
            ->method('send')
            ->willReturn($whatsAppResponse);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('finishConversation')
            ->willThrowException(new Exception('Conversation finish failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Conversation finish failed');

        $this->sendWhatsAppResponse->perform($stepResult, $conversation);
    }

    public function test_perform_with_input_collection_response()
    {
        // Arrange
        $stepResult = [
            'type' => 'input',
            'step_id' => 4,
            'action' => 'collect_input',
            'message' => 'Please enter your name:',
            'input_field' => 'client.name',
            'move_to_next' => false
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step_id = 4;

        $message = $this->createMock(Message::class);
        $whatsAppResponse = [
            'messaging_product' => 'whatsapp',
            'messages' => [['id' => 'wamid.input123']]
        ];

        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->willReturn($message);

        $this->mockMessageService
            ->expects($this->once())
            ->method('send')
            ->willReturn($whatsAppResponse);

        // Should not update step since we're waiting for input
        $this->mockConversationRepository
            ->expects($this->never())
            ->method('updateCurrentStep');

        // Act
        $result = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('input_requested', $result['action']);
        $this->assertEquals($whatsAppResponse, $result['whatsapp_response']);
        $this->assertEquals(4, $result['current_step_id']);
        $this->assertFalse($result['conversation_finished']);
    }

    public function test_perform_with_conditional_navigation()
    {
        // Arrange
        $stepResult = [
            'type' => 'message',
            'step_id' => 6,
            'action' => 'send_message',
            'message' => 'Conditional message',
            'move_to_next' => true,
            'conditional_next_step' => 10 // Conditional navigation result
        ];

        $conversation = $this->createMock(WhatsAppConversation::class);
        $conversation->current_step_id = 6;

        $message = $this->createMock(Message::class);
        $whatsAppResponse = ['success' => true];
        $updatedConversation = $this->createMock(WhatsAppConversation::class);

        $this->mockMessageFactory
            ->expects($this->once())
            ->method('buildFromStepResult')
            ->willReturn($message);

        $this->mockMessageService
            ->expects($this->once())
            ->method('send')
            ->willReturn($whatsAppResponse);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('updateCurrentStep')
            ->with($conversation, 10) // Should use conditional_next_step
            ->willReturn($updatedConversation);

        // Act
        $result = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals(10, $result['updated_step_id']);
    }
}
