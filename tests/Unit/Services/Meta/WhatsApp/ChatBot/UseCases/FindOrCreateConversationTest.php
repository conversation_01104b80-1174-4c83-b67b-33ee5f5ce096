<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot\UseCases;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppConversationFactory;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Repositories\PhoneNumberRepository;
use App\Repositories\FlowRepository;
use App\Domains\Inventory\Client;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Flow;
use Exception;

class FindOrCreateConversationTest extends TestCase
{
    use RefreshDatabase;

    protected FindOrCreateConversation $findOrCreateConversation;
    protected $mockConversationRepository;
    protected $mockConversationFactory;
    protected $mockPhoneNumberRepository;
    protected $mockFlowRepository;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks
        $this->mockConversationRepository = $this->createMock(WhatsAppConversationRepository::class);
        $this->mockConversationFactory = $this->createMock(WhatsAppConversationFactory::class);
        $this->mockPhoneNumberRepository = $this->createMock(PhoneNumberRepository::class);
        $this->mockFlowRepository = $this->createMock(FlowRepository::class);

        $this->findOrCreateConversation = new FindOrCreateConversation(
            $this->mockConversationRepository,
            $this->mockConversationFactory,
            $this->mockPhoneNumberRepository,
            $this->mockFlowRepository
        );
    }

    public function test_perform_returns_existing_active_conversation()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ]
        ];

        $client = $this->createMock(Client::class);
        $client->id = 1;

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->id = 1;
        $phoneNumber->flow_id = 1;

        $existingConversation = $this->createMock(WhatsAppConversation::class);
        $existingConversation->id = 1;
        $existingConversation->client_id = 1;
        $existingConversation->phone_number_id = 1;
        $existingConversation->is_finished = false;

        // Set up mocks
        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->with('whatsapp_phone_123')
            ->willReturn($phoneNumber);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('findActiveConversation')
            ->with($client, $phoneNumber)
            ->willReturn($existingConversation);

        // Act
        $result = $this->findOrCreateConversation->perform($messageData, $client);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertFalse($result->is_finished);
    }

    public function test_perform_creates_new_conversation_when_none_exists()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'John Doe'
                    ],
                    'wa_id' => '5511999999999'
                ]
            ]
        ];

        $client = $this->createMock(Client::class);
        $client->id = 1;

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->id = 1;
        $phoneNumber->flow_id = 1;

        $flow = $this->createMock(Flow::class);
        $flow->id = 1;
        $flow->first_step_id = 1;

        $newConversation = $this->createMock(WhatsAppConversation::class);
        $newConversation->id = null;
        $newConversation->client_id = 1;
        $newConversation->phone_number_id = 1;
        $newConversation->flow_id = 1;
        $newConversation->current_step_id = 1;

        $savedConversation = $this->createMock(WhatsAppConversation::class);
        $savedConversation->id = 2;
        $savedConversation->client_id = 1;
        $savedConversation->phone_number_id = 1;
        $savedConversation->flow_id = 1;
        $savedConversation->current_step_id = 1;

        // Set up mocks
        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->with('whatsapp_phone_123')
            ->willReturn($phoneNumber);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('findActiveConversation')
            ->with($client, $phoneNumber)
            ->willReturn(null);

        $this->mockFlowRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(1)
            ->willReturn($flow);

        $this->mockConversationFactory
            ->expects($this->once())
            ->method('buildFromWhatsAppData')
            ->with($messageData, $client, $phoneNumber, $flow)
            ->willReturn($newConversation);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('save')
            ->with($newConversation)
            ->willReturn($savedConversation);

        // Act
        $result = $this->findOrCreateConversation->perform($messageData, $client);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
        $this->assertEquals(2, $result->id);
        $this->assertEquals(1, $result->client_id);
        $this->assertEquals(1, $result->phone_number_id);
        $this->assertEquals(1, $result->flow_id);
        $this->assertEquals(1, $result->current_step_id);
    }

    public function test_perform_throws_exception_when_phone_number_not_found()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'invalid_phone_id'
            ]
        ];

        $client = $this->createMock(Client::class);

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->with('invalid_phone_id')
            ->willReturn(null);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Phone number not found for WhatsApp ID: invalid_phone_id');

        $this->findOrCreateConversation->perform($messageData, $client);
    }

    public function test_perform_throws_exception_when_flow_not_found()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ]
        ];

        $client = $this->createMock(Client::class);

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->id = 1;
        $phoneNumber->flow_id = 999; // Non-existent flow

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->willReturn($phoneNumber);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('findActiveConversation')
            ->willReturn(null);

        $this->mockFlowRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(999)
            ->willReturn(null);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Flow not found for phone number');

        $this->findOrCreateConversation->perform($messageData, $client);
    }

    public function test_perform_throws_exception_when_flow_has_no_first_step()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ]
        ];

        $client = $this->createMock(Client::class);

        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->id = 1;
        $phoneNumber->flow_id = 1;

        $flow = $this->createMock(Flow::class);
        $flow->id = 1;
        $flow->first_step_id = null; // No first step

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->willReturn($phoneNumber);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('findActiveConversation')
            ->willReturn(null);

        $this->mockFlowRepository
            ->expects($this->once())
            ->method('fetchById')
            ->willReturn($flow);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Flow has no first step configured');

        $this->findOrCreateConversation->perform($messageData, $client);
    }

    public function test_perform_handles_missing_metadata()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999'
            // Missing metadata
        ];

        $client = $this->createMock(Client::class);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing phone number ID in metadata');

        $this->findOrCreateConversation->perform($messageData, $client);
    }

    public function test_perform_handles_missing_phone_number_id_in_metadata()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'display_phone_number' => '15551234567'
                // Missing phone_number_id
            ]
        ];

        $client = $this->createMock(Client::class);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Missing phone number ID in metadata');

        $this->findOrCreateConversation->perform($messageData, $client);
    }

    public function test_perform_handles_conversation_factory_exception()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ]
        ];

        $client = $this->createMock(Client::class);
        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->flow_id = 1;
        $flow = $this->createMock(Flow::class);
        $flow->first_step_id = 1;

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->willReturn($phoneNumber);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('findActiveConversation')
            ->willReturn(null);

        $this->mockFlowRepository
            ->expects($this->once())
            ->method('fetchById')
            ->willReturn($flow);

        $this->mockConversationFactory
            ->expects($this->once())
            ->method('buildFromWhatsAppData')
            ->willThrowException(new Exception('Conversation creation failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Conversation creation failed');

        $this->findOrCreateConversation->perform($messageData, $client);
    }

    public function test_perform_handles_conversation_save_exception()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ]
        ];

        $client = $this->createMock(Client::class);
        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->flow_id = 1;
        $flow = $this->createMock(Flow::class);
        $flow->first_step_id = 1;
        $newConversation = $this->createMock(WhatsAppConversation::class);

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->willReturn($phoneNumber);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('findActiveConversation')
            ->willReturn(null);

        $this->mockFlowRepository
            ->expects($this->once())
            ->method('fetchById')
            ->willReturn($flow);

        $this->mockConversationFactory
            ->expects($this->once())
            ->method('buildFromWhatsAppData')
            ->willReturn($newConversation);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('save')
            ->willThrowException(new Exception('Database save failed'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database save failed');

        $this->findOrCreateConversation->perform($messageData, $client);
    }

    public function test_perform_with_empty_contacts_array()
    {
        // Arrange
        $messageData = [
            'from' => '5511999999999',
            'metadata' => [
                'phone_number_id' => 'whatsapp_phone_123'
            ],
            'contacts' => []
        ];

        $client = $this->createMock(Client::class);
        $phoneNumber = $this->createMock(PhoneNumber::class);
        $phoneNumber->flow_id = 1;
        $flow = $this->createMock(Flow::class);
        $flow->first_step_id = 1;
        $newConversation = $this->createMock(WhatsAppConversation::class);
        $savedConversation = $this->createMock(WhatsAppConversation::class);

        $this->mockPhoneNumberRepository
            ->expects($this->once())
            ->method('fetchByWhatsAppPhoneNumberId')
            ->willReturn($phoneNumber);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('findActiveConversation')
            ->willReturn(null);

        $this->mockFlowRepository
            ->expects($this->once())
            ->method('fetchById')
            ->willReturn($flow);

        $this->mockConversationFactory
            ->expects($this->once())
            ->method('buildFromWhatsAppData')
            ->willReturn($newConversation);

        $this->mockConversationRepository
            ->expects($this->once())
            ->method('save')
            ->willReturn($savedConversation);

        // Act
        $result = $this->findOrCreateConversation->perform($messageData, $client);

        // Assert
        $this->assertInstanceOf(WhatsAppConversation::class, $result);
    }
}
