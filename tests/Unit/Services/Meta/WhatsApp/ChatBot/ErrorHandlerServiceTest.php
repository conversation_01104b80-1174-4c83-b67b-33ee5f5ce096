<?php

namespace Tests\Unit\Services\Meta\WhatsApp\ChatBot;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\ChatBot\Services\ErrorHandlerService;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Domains\ChatBot\Step;
use App\Enums\ChatBot\ChatBotErrorType;
use Exception;

class ErrorHandlerServiceTest extends TestCase
{
    protected ErrorHandlerService $errorHandlerService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->errorHandlerService = new ErrorHandlerService();
    }

    public function test_handle_validation_error_returns_retry_strategy()
    {
        $exception = new Exception('Validation failed');
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        
        $result = $this->errorHandlerService->handleError(
            $exception,
            ChatBotErrorType::VALIDATION_ERROR,
            $conversation
        );

        $this->assertEquals('retry', $result['strategy']);
        $this->assertEquals('retry_current_step', $result['action']);
        $this->assertTrue($result['stay_on_current_step']);
        $this->assertTrue($result['increment_retry_count']);
        $this->assertArrayHasKey('max_attempts', $result);
        $this->assertArrayHasKey('delay_seconds', $result);
    }

    public function test_handle_flow_error_returns_fallback_strategy()
    {
        $exception = new Exception('Flow navigation error');
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        
        $result = $this->errorHandlerService->handleError(
            $exception,
            ChatBotErrorType::FLOW_ERROR,
            $conversation
        );

        $this->assertEquals('fallback', $result['strategy']);
        $this->assertEquals('goto_fallback_step', $result['action']);
        $this->assertTrue($result['preserve_conversation_data']);
        $this->assertArrayHasKey('fallback_step', $result);
        $this->assertArrayHasKey('message', $result);
    }

    public function test_handle_timeout_error_returns_reset_strategy()
    {
        $exception = new Exception('Conversation timeout');
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        
        $result = $this->errorHandlerService->handleError(
            $exception,
            ChatBotErrorType::TIMEOUT_ERROR,
            $conversation
        );

        $this->assertEquals('reset', $result['strategy']);
        $this->assertEquals('reset_conversation', $result['action']);
        $this->assertTrue($result['goto_initial_step']);
        $this->assertArrayHasKey('preserve_client_data', $result);
        $this->assertArrayHasKey('message', $result);
    }

    public function test_handle_external_api_error_returns_retry_strategy()
    {
        $exception = new Exception('API request failed');
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        
        $result = $this->errorHandlerService->handleError(
            $exception,
            ChatBotErrorType::EXTERNAL_API_ERROR,
            $conversation
        );

        $this->assertEquals('retry', $result['strategy']);
        $this->assertEquals('retry_current_step', $result['action']);
        $this->assertArrayHasKey('max_attempts', $result);
    }

    public function test_handle_command_execution_error_returns_configured_strategy()
    {
        // Mock config to return 'fallback' as default strategy
        config(['chatbot.error_handling.strategies.default' => 'fallback']);
        
        $exception = new Exception('Command execution failed');
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        
        $result = $this->errorHandlerService->handleError(
            $exception,
            ChatBotErrorType::COMMAND_EXECUTION_ERROR,
            $conversation
        );

        $this->assertEquals('fallback', $result['strategy']);
    }

    public function test_determine_error_type_from_validation_exception()
    {
        $exception = new Exception('Invalid input provided');
        
        $result = $this->errorHandlerService->handleError($exception);

        $this->assertEquals('retry', $result['strategy']);
    }

    public function test_determine_error_type_from_timeout_exception()
    {
        $exception = new Exception('Request timeout exceeded');
        
        $result = $this->errorHandlerService->handleError($exception);

        $this->assertEquals('reset', $result['strategy']);
    }

    public function test_determine_error_type_from_api_exception()
    {
        $exception = new Exception('HTTP API call failed');
        
        $result = $this->errorHandlerService->handleError($exception);

        $this->assertEquals('retry', $result['strategy']);
    }

    public function test_determine_error_type_from_flow_exception()
    {
        $exception = new Exception('Flow step navigation error');
        
        $result = $this->errorHandlerService->handleError($exception);

        $this->assertEquals('fallback', $result['strategy']);
    }

    public function test_determine_error_type_defaults_to_command_execution()
    {
        $exception = new Exception('Unknown error occurred');
        
        $result = $this->errorHandlerService->handleError($exception);

        // Should default to retry strategy for command execution errors
        $this->assertEquals('retry', $result['strategy']);
    }

    public function test_execute_escalation_strategy()
    {
        $exception = new Exception('Critical error');
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        
        // Force escalation strategy by using reflection to call protected method
        $reflection = new \ReflectionClass($this->errorHandlerService);
        $method = $reflection->getMethod('executeEscalationStrategy');
        $method->setAccessible(true);
        
        $result = $method->invoke(
            $this->errorHandlerService,
            ChatBotErrorType::FLOW_ERROR,
            $conversation
        );

        $this->assertEquals('escalate', $result['strategy']);
        $this->assertEquals('escalate_to_human', $result['action']);
        $this->assertTrue($result['mark_for_human_takeover']);
        $this->assertTrue($result['preserve_conversation_data']);
        $this->assertArrayHasKey('team_notified', $result);
    }

    public function test_execute_default_strategy()
    {
        $exception = new Exception('Some error');
        
        // Force default strategy by using reflection
        $reflection = new \ReflectionClass($this->errorHandlerService);
        $method = $reflection->getMethod('executeDefaultStrategy');
        $method->setAccessible(true);
        
        $result = $method->invoke(
            $this->errorHandlerService,
            ChatBotErrorType::VALIDATION_ERROR
        );

        $this->assertEquals('default', $result['strategy']);
        $this->assertEquals('send_error_message', $result['action']);
        $this->assertTrue($result['stay_on_current_step']);
        $this->assertArrayHasKey('message', $result);
    }

    public function test_handle_error_with_all_parameters()
    {
        $exception = new Exception('Test error');
        $conversation = new WhatsAppConversation(1, 1, 1, 1, 1, 1);
        $interaction = new WhatsAppInteraction(1, 1, null, 1, 1, 1, 1, 'test message');
        $step = new Step(1, 1, 1, 'test_step', 'message', 1, 2, null, false, false, true, false, false, false, '{}');
        
        $result = $this->errorHandlerService->handleError(
            $exception,
            ChatBotErrorType::VALIDATION_ERROR,
            $conversation,
            $interaction,
            $step
        );

        $this->assertIsArray($result);
        $this->assertArrayHasKey('strategy', $result);
        $this->assertArrayHasKey('action', $result);
        $this->assertArrayHasKey('message', $result);
    }

    public function test_error_type_descriptions()
    {
        $this->assertEquals('Erro de validação de entrada', ChatBotErrorType::VALIDATION_ERROR->getDescription());
        $this->assertEquals('Erro na execução de comando', ChatBotErrorType::COMMAND_EXECUTION_ERROR->getDescription());
        $this->assertEquals('Erro no fluxo de conversa', ChatBotErrorType::FLOW_ERROR->getDescription());
        $this->assertEquals('Erro em API externa', ChatBotErrorType::EXTERNAL_API_ERROR->getDescription());
        $this->assertEquals('Erro de timeout', ChatBotErrorType::TIMEOUT_ERROR->getDescription());
    }

    public function test_error_type_default_messages()
    {
        $this->assertStringContainsString('dados informados', ChatBotErrorType::VALIDATION_ERROR->getDefaultMessage());
        $this->assertStringContainsString('processar sua solicitação', ChatBotErrorType::COMMAND_EXECUTION_ERROR->getDefaultMessage());
        $this->assertStringContainsString('fluxo da conversa', ChatBotErrorType::FLOW_ERROR->getDefaultMessage());
        $this->assertStringContainsString('problemas técnicos', ChatBotErrorType::EXTERNAL_API_ERROR->getDefaultMessage());
        $this->assertStringContainsString('Tempo limite', ChatBotErrorType::TIMEOUT_ERROR->getDefaultMessage());
    }

    public function test_error_type_severity_levels()
    {
        $this->assertEquals('low', ChatBotErrorType::VALIDATION_ERROR->getSeverity());
        $this->assertEquals('medium', ChatBotErrorType::COMMAND_EXECUTION_ERROR->getSeverity());
        $this->assertEquals('high', ChatBotErrorType::FLOW_ERROR->getSeverity());
        $this->assertEquals('medium', ChatBotErrorType::EXTERNAL_API_ERROR->getSeverity());
        $this->assertEquals('low', ChatBotErrorType::TIMEOUT_ERROR->getSeverity());
    }

    public function test_error_type_escalation_flags()
    {
        $this->assertFalse(ChatBotErrorType::VALIDATION_ERROR->shouldEscalate());
        $this->assertFalse(ChatBotErrorType::COMMAND_EXECUTION_ERROR->shouldEscalate());
        $this->assertTrue(ChatBotErrorType::FLOW_ERROR->shouldEscalate());
        $this->assertFalse(ChatBotErrorType::EXTERNAL_API_ERROR->shouldEscalate());
        $this->assertFalse(ChatBotErrorType::TIMEOUT_ERROR->shouldEscalate());
    }
}
