<?php

namespace Tests\Unit\Jobs;

use App\Jobs\CalculateCampaignAnalytics;
use App\Models\Campaign;
use App\Models\Message;
use App\Models\CampaignAnalytics;
use App\Models\Organization;
use App\Enums\CampaignStatus;
use App\Enums\MessageStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CalculateCampaignAnalyticsTest extends TestCase
{
    use RefreshDatabase;

    public function test_job_calculates_analytics_for_eligible_campaigns()
    {
        $organization = Organization::factory()->create();
        
        // Create campaign with messages
        $campaign = Campaign::factory()->create([
            'organization_id' => $organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);

        Message::factory()->count(10)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $organization->id,
            'status' => MessageStatus::is_sent
        ]);

        // Execute job
        $job = new CalculateCampaignAnalytics();
        $job->handle();

        // Assert analytics were created
        $this->assertDatabaseHas('campaign_analytics', [
            'campaign_id' => $campaign->id
        ]);
    }

    public function test_job_skips_draft_campaigns()
    {
        $organization = Organization::factory()->create();
        
        $campaign = Campaign::factory()->create([
            'organization_id' => $organization->id,
            'status' => CampaignStatus::DRAFT
        ]);

        Message::factory()->count(5)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $organization->id
        ]);

        $job = new CalculateCampaignAnalytics();
        $job->handle();

        // Assert no analytics were created for draft campaign
        $this->assertDatabaseMissing('campaign_analytics', [
            'campaign_id' => $campaign->id
        ]);
    }

    public function test_job_creates_performance_snapshots()
    {
        $organization = Organization::factory()->create();
        
        $campaign = Campaign::factory()->create([
            'organization_id' => $organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);

        Message::factory()->count(5)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $organization->id
        ]);

        $job = new CalculateCampaignAnalytics();
        $job->handle();

        // Assert snapshot was created
        $this->assertDatabaseHas('campaign_performance_snapshots', [
            'campaign_id' => $campaign->id,
            'snapshot_date' => today()
        ]);
    }

    public function test_job_skips_existing_daily_snapshots()
    {
        $organization = Organization::factory()->create();
        
        $campaign = Campaign::factory()->create([
            'organization_id' => $organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);

        // Create existing snapshot for today
        \App\Models\CampaignPerformanceSnapshot::factory()->create([
            'campaign_id' => $campaign->id,
            'snapshot_date' => today()
        ]);

        Message::factory()->count(5)->create([
            'campaign_id' => $campaign->id,
            'organization_id' => $organization->id
        ]);

        $job = new CalculateCampaignAnalytics();
        $job->handle();

        // Assert only one snapshot exists (the existing one)
        $this->assertEquals(1, \App\Models\CampaignPerformanceSnapshot::where('campaign_id', $campaign->id)->count());
    }

    public function test_job_handles_campaigns_without_messages()
    {
        $organization = Organization::factory()->create();
        
        $campaign = Campaign::factory()->create([
            'organization_id' => $organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);

        // No messages created

        $job = new CalculateCampaignAnalytics();
        $job->handle();

        // Job should complete without errors
        $this->assertTrue(true);
    }

    public function test_job_processes_multiple_campaigns()
    {
        $organization = Organization::factory()->create();
        
        // Create multiple campaigns
        $campaigns = Campaign::factory()->count(3)->create([
            'organization_id' => $organization->id,
            'status' => CampaignStatus::COMPLETED
        ]);

        foreach ($campaigns as $campaign) {
            Message::factory()->count(5)->create([
                'campaign_id' => $campaign->id,
                'organization_id' => $organization->id
            ]);
        }

        $job = new CalculateCampaignAnalytics();
        $job->handle();

        // Assert analytics were created for all campaigns
        foreach ($campaigns as $campaign) {
            $this->assertDatabaseHas('campaign_analytics', [
                'campaign_id' => $campaign->id
            ]);
        }
    }
}
