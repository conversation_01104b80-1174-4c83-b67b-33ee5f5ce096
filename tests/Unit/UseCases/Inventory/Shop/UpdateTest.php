<?php

namespace Tests\Unit\UseCases\Inventory\Shop;

use App\Domains\Inventory\Shop;
use App\Factories\Inventory\ShopFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ShopRepository;
use App\UseCases\Inventory\Shop\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private ShopRepository $mockRepository;
    private ShopFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ShopRepository::class);
        $this->mockFactory = Mockery::mock(ShopFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_shop_successfully()
    {
        $shopId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Old Shop',
            description: 'Old description',
            is_active: true
        );

        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Updated Shop',
            description: 'Updated description',
            is_active: false
        );

        $updatedShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Updated Shop',
            description: 'Updated description',
            is_active: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($existingShop);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($shop) use ($shopId) {
                return $shop instanceof Shop &&
                       $shop->id === $shopId &&
                       $shop->organization_id === $this->organization->id &&
                       $shop->name === 'Updated Shop';
            }), $this->organization->id)
            ->andReturn($updatedShop);

        $result = $this->useCase->perform($shopId, $request);

        $this->assertInstanceOf(Shop::class, $result);
        $this->assertEquals($shopId, $result->id);
        $this->assertEquals('Updated Shop', $result->name);
        $this->assertEquals('Updated description', $result->description);
        $this->assertFalse($result->is_active);
    }

    public function test_perform_throws_exception_when_shop_belongs_to_different_organization()
    {
        $shopId = 1;
        $request = $this->createMockUpdateRequest();
        $otherOrganization = Organization::factory()->create();
        
        $existingShop = new Shop(
            id: $shopId,
            organization_id: $otherOrganization->id, // Different organization
            name: 'Other Shop',
            description: 'Other description',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($existingShop);

        $this->mockFactory
            ->shouldNotReceive('buildFromUpdateRequest');

        $this->mockRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This shop don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($shopId, $request);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $shopId = 999;
        $request = $this->createMockUpdateRequest();

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($shopId, $request);
    }

    public function test_perform_handles_factory_exception()
    {
        $shopId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Existing Shop',
            description: 'Existing description',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($existingShop);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($shopId, $request);
    }

    public function test_perform_handles_repository_update_exception()
    {
        $shopId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Existing Shop',
            description: 'Existing description',
            is_active: true
        );

        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Updated Shop',
            description: 'Updated description',
            is_active: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($existingShop);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($shopId, $request);
    }

    public function test_perform_preserves_original_id_and_organization()
    {
        $shopId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Preserve Shop',
            description: 'Preserve description',
            is_active: true
        );

        $domainFromRequest = new Shop(
            id: 999, // Different ID in request
            organization_id: 888, // Different organization in request
            name: 'Preserve Updated Shop',
            description: 'Preserve updated description',
            is_active: false
        );

        $updatedShop = new Shop(
            id: $shopId, // Should preserve original
            organization_id: $this->organization->id, // Should preserve original
            name: 'Preserve Updated Shop',
            description: 'Preserve updated description',
            is_active: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($existingShop);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($shop) use ($shopId) {
                return $shop->id === $shopId && 
                       $shop->organization_id === $this->organization->id;
            }), $this->organization->id)
            ->andReturn($updatedShop);

        $result = $this->useCase->perform($shopId, $request);

        $this->assertEquals($shopId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_with_special_characters()
    {
        $shopId = 1;
        $request = $this->createMockSpecialCharactersUpdateRequest();
        
        $existingShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Old Shop',
            description: 'Old description',
            is_active: true
        );

        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Shop & Co. (™)',
            description: 'Description with special chars: @#$%^&*()',
            is_active: true
        );

        $updatedShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Shop & Co. (™)',
            description: 'Description with special chars: @#$%^&*()',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($existingShop);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedShop);

        $result = $this->useCase->perform($shopId, $request);

        $this->assertEquals('Shop & Co. (™)', $result->name);
        $this->assertEquals('Description with special chars: @#$%^&*()', $result->description);
    }

    public function test_perform_with_unicode_characters()
    {
        $shopId = 1;
        $request = $this->createMockUnicodeUpdateRequest();

        $existingShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Old Shop',
            description: 'Old description',
            is_active: true
        );

        $domainFromRequest = new Shop(
            id: null,
            organization_id: null,
            name: 'Shöp Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú',
            is_active: true
        );

        $updatedShop = new Shop(
            id: $shopId,
            organization_id: $this->organization->id,
            name: 'Shöp Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($shopId)
            ->andReturn($existingShop);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedShop);

        $result = $this->useCase->perform($shopId, $request);

        $this->assertEquals('Shöp Ñamé 中文', $result->name);
        $this->assertEquals('Descripción con caracteres especiales: ñáéíóú', $result->description);
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\Shop\UpdateRequest {
            public $name = 'Updated Shop';
            public $description = 'Updated description';
            public $is_active = false;
        };
    }

    private function createMockSpecialCharactersUpdateRequest()
    {
        return new class extends \App\Http\Requests\Shop\UpdateRequest {
            public $name = 'Shop & Co. (™)';
            public $description = 'Description with special chars: @#$%^&*()';
            public $is_active = true;
        };
    }

    private function createMockUnicodeUpdateRequest()
    {
        return new class extends \App\Http\Requests\Shop\UpdateRequest {
            public $name = 'Shöp Ñamé 中文';
            public $description = 'Descripción con caracteres especiales: ñáéíóú';
            public $is_active = true;
        };
    }
}
