<?php

namespace Tests\Unit\UseCases\Inventory\ProductHistory;

use App\Domains\Inventory\ProductHistory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductHistoryRepository;
use App\UseCases\Inventory\ProductHistory\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private ProductHistoryRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductHistoryRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_product_history_successfully()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals($productHistoryId, $result->id);
        $this->assertEquals('price', $result->field);
        $this->assertEquals('Preço', $result->alias);
        $this->assertEquals('10.50', $result->old);
        $this->assertEquals('15.75', $result->new);
    }

    public function test_perform_handles_repository_exception()
    {
        $productHistoryId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($productHistoryId);
    }

    public function test_perform_with_different_field_types()
    {
        $fields = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Old Name', 'new' => 'New Name'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '10.50', 'new' => '15.75'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Old Description', 'new' => 'New Description'],
            ['field' => 'barcode', 'alias' => 'Código de Barras', 'old' => '1234567890', 'new' => '0987654321'],
            ['field' => 'unity', 'alias' => 'Unidade', 'old' => '1', 'new' => '2'],
        ];

        foreach ($fields as $index => $fieldData) {
            $productHistoryId = $index + 1;
            
            $productHistory = new ProductHistory(
                id: $productHistoryId,
                user_id: $this->user->id,
                product_id: 1,
                field: $fieldData['field'],
                alias: $fieldData['alias'],
                old: $fieldData['old'],
                new: $fieldData['new']
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($productHistoryId)
                ->andReturn($productHistory);

            $result = $this->useCase->perform($productHistoryId);

            $this->assertInstanceOf(ProductHistory::class, $result);
            $this->assertEquals($fieldData['field'], $result->field);
            $this->assertEquals($fieldData['alias'], $result->alias);
            $this->assertEquals($fieldData['old'], $result->old);
            $this->assertEquals($fieldData['new'], $result->new);
        }
    }

    public function test_perform_with_price_changes()
    {
        $priceChanges = [
            ['old' => '0.00', 'new' => '10.50'],
            ['old' => '10.50', 'new' => '15.75'],
            ['old' => '15.75', 'new' => '20.00'],
            ['old' => '20.00', 'new' => '0.00'],
            ['old' => '100.99', 'new' => '999.99'],
        ];

        foreach ($priceChanges as $index => $priceChange) {
            $productHistoryId = $index + 1;
            
            $productHistory = new ProductHistory(
                id: $productHistoryId,
                user_id: $this->user->id,
                product_id: 1,
                field: 'price',
                alias: 'Preço',
                old: $priceChange['old'],
                new: $priceChange['new']
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($productHistoryId)
                ->andReturn($productHistory);

            $result = $this->useCase->perform($productHistoryId);

            $this->assertInstanceOf(ProductHistory::class, $result);
            $this->assertEquals($priceChange['old'], $result->old);
            $this->assertEquals($priceChange['new'], $result->new);
        }
    }

    public function test_perform_with_long_values()
    {
        $productHistoryId = 1;
        $longOldValue = str_repeat('This is a very long old value. ', 10);
        $longNewValue = str_repeat('This is a very long new value. ', 10);
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: $longOldValue,
            new: $longNewValue
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals($longOldValue, $result->old);
        $this->assertEquals($longNewValue, $result->new);
    }

    public function test_perform_with_null_values()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'description',
            alias: 'Descrição',
            old: null,
            new: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertNull($result->old);
        $this->assertNull($result->new);
    }

    public function test_perform_returns_product_history_with_all_properties()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: 5,
            product_id: 3,
            field: 'price',
            alias: 'Preço',
            old: '100.50',
            new: '150.75',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals($productHistoryId, $result->id);
        $this->assertEquals(5, $result->user_id);
        $this->assertEquals(3, $result->product_id);
        $this->assertEquals('price', $result->field);
        $this->assertEquals('Preço', $result->alias);
        $this->assertEquals('100.50', $result->old);
        $this->assertEquals('150.75', $result->new);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_user_and_product_ids()
    {
        $testCases = [
            ['user_id' => 1, 'product_id' => 1],
            ['user_id' => 2, 'product_id' => 3],
            ['user_id' => 5, 'product_id' => 7],
            ['user_id' => 999, 'product_id' => 888],
        ];

        foreach ($testCases as $index => $testCase) {
            $productHistoryId = $index + 1;
            
            $productHistory = new ProductHistory(
                id: $productHistoryId,
                user_id: $testCase['user_id'],
                product_id: $testCase['product_id'],
                field: 'price',
                alias: 'Preço',
                old: '10.50',
                new: '15.75'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($productHistoryId)
                ->andReturn($productHistory);

            $result = $this->useCase->perform($productHistoryId);

            $this->assertInstanceOf(ProductHistory::class, $result);
            $this->assertEquals($testCase['user_id'], $result->user_id);
            $this->assertEquals($testCase['product_id'], $result->product_id);
        }
    }

    public function test_perform_with_decimal_values()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: $this->user->id,
            product_id: 1,
            field: 'price',
            alias: 'Preço',
            old: '123.456789',
            new: '987.654321'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals('123.456789', $result->old);
        $this->assertEquals('987.654321', $result->new);
    }

    public function test_perform_with_complete_product_history()
    {
        $productHistoryId = 1;
        
        $productHistory = new ProductHistory(
            id: $productHistoryId,
            user_id: 2,
            product_id: 3,
            field: 'name',
            alias: 'Nome',
            old: 'Old Product Name',
            new: 'New Product Name',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productHistoryId)
            ->andReturn($productHistory);

        $result = $this->useCase->perform($productHistoryId);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertEquals($productHistoryId, $result->id);
        $this->assertEquals(2, $result->user_id);
        $this->assertEquals(3, $result->product_id);
        $this->assertEquals('name', $result->field);
        $this->assertEquals('Nome', $result->alias);
        $this->assertEquals('Old Product Name', $result->old);
        $this->assertEquals('New Product Name', $result->new);
    }
}
