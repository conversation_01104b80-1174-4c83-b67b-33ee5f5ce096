<?php

namespace Tests\Unit\UseCases\Inventory\BudgetProduct;

use App\Domains\Inventory\BudgetProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BudgetProductRepository;
use App\UseCases\Inventory\BudgetProduct\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private BudgetProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BudgetProductRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_budget_product_successfully()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals($budgetProductId, $result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $budgetProductId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($budgetProductId);
    }

    public function test_perform_with_minimal_budget_product()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 1,
            value: 15.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(1, $result->quantity);
        $this->assertEquals(15.50, $result->value);
    }

    public function test_perform_with_complete_budget_product()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 200,
            value: 3000.75
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals($budgetProductId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(1, $result->budget_id);
        $this->assertEquals(1, $result->product_id);
        $this->assertEquals(200, $result->quantity);
        $this->assertEquals(3000.75, $result->value);
    }

    public function test_perform_with_zero_quantity()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(99999999.99, $result->value);
    }

    public function test_perform_with_decimal_values()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 33,
            value: 123.456789
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_returns_budget_product_with_all_properties()
    {
        $budgetProductId = 1;
        
        $budgetProduct = new BudgetProduct(
            id: $budgetProductId,
            organization_id: $this->organization->id,
            budget_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1500.50,
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetProductId)
            ->andReturn($budgetProduct);

        $result = $this->useCase->perform($budgetProductId);

        $this->assertInstanceOf(BudgetProduct::class, $result);
        $this->assertEquals($budgetProductId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(1, $result->budget_id);
        $this->assertEquals(1, $result->product_id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $index => $testCase) {
            $budgetProductId = $index + 1;
            
            $budgetProduct = new BudgetProduct(
                id: $budgetProductId,
                organization_id: $this->organization->id,
                budget_id: 1,
                product_id: 1,
                quantity: $testCase['quantity'],
                value: $testCase['value']
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($budgetProductId)
                ->andReturn($budgetProduct);

            $result = $this->useCase->perform($budgetProductId);

            $this->assertInstanceOf(BudgetProduct::class, $result);
            $this->assertEquals($testCase['quantity'], $result->quantity);
            $this->assertEquals($testCase['value'], $result->value);
        }
    }

    public function test_perform_with_different_budget_and_product_ids()
    {
        $testCases = [
            ['budget_id' => 1, 'product_id' => 1],
            ['budget_id' => 2, 'product_id' => 3],
            ['budget_id' => 5, 'product_id' => 10],
        ];

        foreach ($testCases as $index => $testCase) {
            $budgetProductId = $index + 1;
            
            $budgetProduct = new BudgetProduct(
                id: $budgetProductId,
                organization_id: $this->organization->id,
                budget_id: $testCase['budget_id'],
                product_id: $testCase['product_id'],
                quantity: 10,
                value: 150.50
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($budgetProductId)
                ->andReturn($budgetProduct);

            $result = $this->useCase->perform($budgetProductId);

            $this->assertInstanceOf(BudgetProduct::class, $result);
            $this->assertEquals($testCase['budget_id'], $result->budget_id);
            $this->assertEquals($testCase['product_id'], $result->product_id);
        }
    }
}
