<?php

namespace Tests\Unit\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\StockEntry;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockEntryRepository;
use App\UseCases\Inventory\StockEntry\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private StockEntryRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockEntryRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_stock_entry_successfully()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Get entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals($entryId, $result->id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertEquals('Get entry', $result->description);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $entryId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($entryId);
    }

    public function test_perform_with_minimal_entry()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: null,
            client_id: null,
            project_id: null,
            quantity: 10,
            value: 150.50,
            description: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertNull($result->batch_id);
        $this->assertNull($result->client_id);
        $this->assertNull($result->project_id);
        $this->assertNull($result->description);
    }

    public function test_perform_with_complete_entry()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 200,
            value: 3000.75,
            description: 'Complete entry with all fields'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals($entryId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(1, $result->shop_id);
        $this->assertEquals($this->user->id, $result->user_id);
        $this->assertEquals(1, $result->brand_id);
        $this->assertEquals(1, $result->product_id);
        $this->assertEquals(1, $result->batch_id);
        $this->assertEquals(1, $result->client_id);
        $this->assertEquals(1, $result->project_id);
        $this->assertEquals(200, $result->quantity);
        $this->assertEquals(3000.75, $result->value);
        $this->assertEquals('Complete entry with all fields', $result->description);
    }

    public function test_perform_with_special_characters_in_description()
    {
        $entryId = 1;
        $specialDescription = 'Entry with special chars: @#$%^&*()';
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: $specialDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals($specialDescription, $result->description);
    }

    public function test_perform_with_unicode_characters_in_description()
    {
        $entryId = 1;
        $unicodeDescription = 'Entrada con caracteres especiales: ñáéíóú 中文';
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 75,
            value: 1125.75,
            description: $unicodeDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals($unicodeDescription, $result->description);
    }

    public function test_perform_with_zero_quantity()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'High quantity entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(99999999.99, $result->value);
    }

    public function test_perform_with_decimal_values()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_returns_entry_with_all_properties()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Properties entry',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $result = $this->useCase->perform($entryId);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals($entryId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(1, $result->shop_id);
        $this->assertEquals($this->user->id, $result->user_id);
        $this->assertEquals(1, $result->brand_id);
        $this->assertEquals(1, $result->product_id);
        $this->assertEquals(1, $result->batch_id);
        $this->assertEquals(1, $result->client_id);
        $this->assertEquals(1, $result->project_id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertEquals('Properties entry', $result->description);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }
}
