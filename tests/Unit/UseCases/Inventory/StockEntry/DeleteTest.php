<?php

namespace Tests\Unit\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\StockEntry;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockEntryRepository;
use App\UseCases\Inventory\Stock\DecreaseStock;
use App\UseCases\Inventory\StockEntry\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private StockEntryRepository $mockRepository;
    private DecreaseStock $mockDecreaseStock;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockEntryRepository::class);
        $this->mockDecreaseStock = Mockery::mock(DecreaseStock::class);

        // Mock the app container to return our mock
        $this->app->instance(DecreaseStock::class, $this->mockDecreaseStock);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_stock_entry_successfully()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Delete entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($entry);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($entry)
            ->andReturn(true);

        $result = $this->useCase->perform($entryId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_entry_belongs_to_different_organization()
    {
        $entryId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $otherOrganization->id, // Different organization
            shop_id: 1,
            user_id: 1,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Other entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $this->mockDecreaseStock
            ->shouldNotReceive('perform');

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This stock entry don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($entryId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $entryId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($entryId);
    }

    public function test_perform_handles_decrease_stock_exception()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Decrease stock error entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($entry)
            ->andThrow(new Exception('Decrease stock error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Decrease stock error');

        $this->useCase->perform($entryId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Delete error entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($entry);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($entry)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($entryId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Fail entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($entry);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($entry)
            ->andReturn(false);

        $result = $this->useCase->perform($entryId);

        $this->assertFalse($result);
    }

    public function test_perform_calls_decrease_stock_before_delete()
    {
        $entryId = 1;
        
        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 50,
            value: 750.25,
            description: 'Decrease stock test'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        $this->mockDecreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($entry);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($entry)
            ->andReturn(true);

        $result = $this->useCase->perform($entryId);

        $this->assertTrue($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $entryId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $entry = new StockEntry(
            id: $entryId,
            organization_id: $this->organization->id, // Entry belongs to original org
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: 1,
            project_id: 1,
            quantity: 100,
            value: 1500.50,
            description: 'Auth test entry'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($entryId)
            ->andReturn($entry);

        // Should throw exception because entry belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This stock entry don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($entryId);
    }
}
