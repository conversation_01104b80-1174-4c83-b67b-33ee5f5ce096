<?php

namespace Tests\Unit\UseCases\Inventory\StockEntry;

use App\Domains\Inventory\StockEntry;
use App\Factories\Inventory\StockEntryFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockEntryRepository;
use App\UseCases\Inventory\Stock\IncreaseStock;
use App\UseCases\Inventory\StockEntry\CreateFromBatch;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class CreateFromBatchTest extends TestCase
{
    use RefreshDatabase;

    private CreateFromBatch $useCase;
    private StockEntryRepository $mockRepository;
    private StockEntryFactory $mockFactory;
    private IncreaseStock $mockIncreaseStock;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockEntryRepository::class);
        $this->mockFactory = Mockery::mock(StockEntryFactory::class);
        $this->mockIncreaseStock = Mockery::mock(IncreaseStock::class);

        // Mock the app container to return our mock
        $this->app->instance(IncreaseStock::class, $this->mockIncreaseStock);

        $this->useCase = new CreateFromBatch($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_creates_stock_entry_from_batch_successfully()
    {
        $batch = $this->createMockBatch();
        
        $domainFromBatch = new StockEntry(
            id: null,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 100,
            value: null,
            description: 'Batch entry'
        );

        $storedEntry = new StockEntry(
            id: 1,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 100,
            value: 1500.50,
            description: 'Batch entry'
        );

        $this->mockFactory
            ->shouldReceive('buildFromBatch')
            ->once()
            ->with($batch, $this->user->id)
            ->andReturn($domainFromBatch);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($entry) {
                return $entry instanceof StockEntry &&
                       $entry->organization_id === $this->organization->id &&
                       $entry->quantity === 100;
            }))
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry);

        $result = $this->useCase->perform($batch);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $batch = $this->createMockBatch();
        
        $domainFromBatch = new StockEntry(
            id: null,
            organization_id: null, // Will be assigned from user
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 50,
            value: null,
            description: 'Organization test'
        );

        $storedEntry = new StockEntry(
            id: 2,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 50,
            value: 750.25,
            description: 'Organization test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromBatch')
            ->once()
            ->with($batch, $this->user->id)
            ->andReturn($domainFromBatch);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($entry) {
                return $entry->organization_id === $this->organization->id;
            }))
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry);

        $result = $this->useCase->perform($batch);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_calls_calculate_value()
    {
        $batch = $this->createMockBatch();
        
        $domainFromBatch = Mockery::mock(StockEntry::class);
        $domainFromBatch->shouldReceive('calculateValue')->once();
        $domainFromBatch->organization_id = null;

        $storedEntry = new StockEntry(
            id: 3,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 75,
            value: 1125.75,
            description: 'Calculate value test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromBatch')
            ->once()
            ->with($batch, $this->user->id)
            ->andReturn($domainFromBatch);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry);

        $result = $this->useCase->perform($batch);

        $this->assertInstanceOf(StockEntry::class, $result);
    }

    public function test_perform_calls_increase_stock()
    {
        $batch = $this->createMockBatch();
        
        $domainFromBatch = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 25,
            value: null,
            description: 'Increase stock test'
        );

        $storedEntry = new StockEntry(
            id: 4,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 25,
            value: 375.25,
            description: 'Increase stock test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromBatch')
            ->once()
            ->with($batch, $this->user->id)
            ->andReturn($domainFromBatch);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry);

        $result = $this->useCase->perform($batch);

        $this->assertInstanceOf(StockEntry::class, $result);
        $this->assertEquals(25, $result->quantity);
    }

    public function test_perform_handles_factory_exception()
    {
        $batch = $this->createMockBatch();

        $this->mockFactory
            ->shouldReceive('buildFromBatch')
            ->once()
            ->with($batch, $this->user->id)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($batch);
    }

    public function test_perform_handles_repository_exception()
    {
        $batch = $this->createMockBatch();
        
        $domainFromBatch = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 100,
            value: null,
            description: 'Error test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromBatch')
            ->once()
            ->with($batch, $this->user->id)
            ->andReturn($domainFromBatch);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($batch);
    }

    public function test_perform_handles_increase_stock_exception()
    {
        $batch = $this->createMockBatch();
        
        $domainFromBatch = new StockEntry(
            id: null,
            organization_id: null,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 100,
            value: null,
            description: 'Increase stock error test'
        );

        $storedEntry = new StockEntry(
            id: 5,
            organization_id: $this->organization->id,
            shop_id: 1,
            user_id: $this->user->id,
            brand_id: 1,
            product_id: 1,
            batch_id: 1,
            client_id: null,
            project_id: null,
            quantity: 100,
            value: 1500.50,
            description: 'Increase stock error test'
        );

        $this->mockFactory
            ->shouldReceive('buildFromBatch')
            ->once()
            ->with($batch, $this->user->id)
            ->andReturn($domainFromBatch);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedEntry);

        $this->mockIncreaseStock
            ->shouldReceive('perform')
            ->once()
            ->with($storedEntry)
            ->andThrow(new Exception('Increase stock error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Increase stock error');

        $this->useCase->perform($batch);
    }

    private function createMockBatch()
    {
        return new class {
            public $id = 1;
            public $organization_id = 1;
            public $shop_id = 1;
            public $quantity = 100;
            public $description = 'Batch entry';
            public $product;
            public $shop;

            public function __construct()
            {
                $this->product = new class {
                    public $id = 1;
                    public $brand_id = 1;
                    public $price = 15.50;
                };
                
                $this->shop = new class {
                    public $id = 1;
                };
            }
        };
    }
}
