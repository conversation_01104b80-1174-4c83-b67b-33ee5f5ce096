<?php

namespace Tests\Unit\UseCases\Inventory\Group;

use App\Domains\Inventory\Group;
use App\Factories\Inventory\GroupFactory;
use App\Http\Requests\Group\UpdateRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupRepository;
use App\UseCases\Inventory\Group\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private GroupRepository $mockRepository;
    private GroupFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(GroupRepository::class);
        $this->mockFactory = Mockery::mock(GroupFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_group_successfully()
    {
        $groupId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Updated Group',
            description: 'Updated group description'
        );

        $updatedGroup = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Updated Group',
            description: 'Updated group description'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($group) use ($groupId) {
                    return $group instanceof Group &&
                           $group->id === $groupId &&
                           $group->name === 'Updated Group';
                }),
                $this->organization->id
            )
            ->andReturn($updatedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $groupId);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals($groupId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Updated Group', $result->name);
        $this->assertEquals('Updated group description', $result->description);
    }

    public function test_perform_assigns_id_to_domain()
    {
        $groupId = 5;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'ID Test Group',
            description: 'ID test description'
        );

        $updatedGroup = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'ID Test Group',
            description: 'ID test description'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($group) use ($groupId) {
                    return $group->id === $groupId;
                }),
                $this->organization->id
            )
            ->andReturn($updatedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $groupId);

        $this->assertEquals($groupId, $result->id);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $groupId = 3;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Org Test Group',
            description: 'Org test description'
        );

        $updatedGroup = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Org Test Group',
            description: 'Org test description'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $this->organization->id
            )
            ->andReturn($updatedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $groupId);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $groupId = 1;
        $request = $this->createMockUpdateRequest();

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $groupId);
    }

    public function test_perform_handles_repository_exception()
    {
        $groupId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Exception Group',
            description: 'Exception description'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request, $groupId);
    }

    public function test_perform_with_partial_update_data()
    {
        $groupId = 2;
        $request = $this->createMockUpdateRequest(['name' => 'Partial Update']);
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Partial Update',
            description: null
        );

        $updatedGroup = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Partial Update',
            description: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($group) {
                    return $group->name === 'Partial Update' &&
                           $group->description === null;
                }),
                $this->organization->id
            )
            ->andReturn($updatedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $groupId);

        $this->assertEquals('Partial Update', $result->name);
        $this->assertNull($result->description);
    }

    public function test_perform_with_description_only_update()
    {
        $groupId = 4;
        $request = $this->createMockUpdateRequest(['description' => 'Only description updated']);
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: null,
            description: 'Only description updated'
        );

        $updatedGroup = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: null,
            description: 'Only description updated'
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($group) {
                    return $group->name === null &&
                           $group->description === 'Only description updated';
                }),
                $this->organization->id
            )
            ->andReturn($updatedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $groupId);

        $this->assertNull($result->name);
        $this->assertEquals('Only description updated', $result->description);
    }

    public function test_perform_with_empty_description()
    {
        $groupId = 6;
        $request = $this->createMockUpdateRequest(['description' => '']);
        
        $domainFromRequest = new Group(
            id: null,
            organization_id: null,
            name: 'Group With Empty Description',
            description: ''
        );

        $updatedGroup = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group With Empty Description',
            description: ''
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($group) {
                    return $group->description === '';
                }),
                $this->organization->id
            )
            ->andReturn($updatedGroup);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $groupId);

        $this->assertEquals('', $result->description);
    }

    private function createMockUpdateRequest(array $data = []): UpdateRequest
    {
        $defaultData = [
            'name' => 'Updated Group',
            'description' => 'Updated group description',
        ];

        $requestData = array_merge($defaultData, $data);

        $request = Mockery::mock(UpdateRequest::class);
        foreach ($requestData as $key => $value) {
            $request->$key = $value;
        }

        return $request;
    }
}
