<?php

namespace Tests\Unit\UseCases\Inventory\Group;

use App\Domains\Inventory\Group;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\GroupRepository;
use App\UseCases\Inventory\Group\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private GroupRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(GroupRepository::class);
        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_group_successfully()
    {
        $groupId = 1;
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group to Delete',
            description: 'Group to delete description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($group)
            ->andReturn(true);

        $result = $this->useCase->perform($groupId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_group_belongs_to_different_organization()
    {
        $groupId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $group = new Group(
            id: $groupId,
            organization_id: $otherOrganization->id,
            name: 'Other Org Group',
            description: 'Other org description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($groupId);
    }

    public function test_perform_handles_fetch_exception()
    {
        $groupId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andThrow(new Exception('Group not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Group not found');

        $this->useCase->perform($groupId);
    }

    public function test_perform_handles_delete_exception()
    {
        $groupId = 1;
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group with Delete Error',
            description: 'Group with delete error description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($group)
            ->andThrow(new Exception('Delete operation failed'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Delete operation failed');

        $this->useCase->perform($groupId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $groupId = 2;
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group Delete Fail',
            description: 'Group delete fail description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($group)
            ->andReturn(false);

        $result = $this->useCase->perform($groupId);

        $this->assertFalse($result);
    }

    public function test_perform_validates_organization_ownership_before_delete()
    {
        $groupId = 3;
        
        // Create a group that belongs to the user's organization
        $validGroup = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Valid Group for Delete',
            description: 'Valid group for delete description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($validGroup);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($validGroup)
            ->andReturn(true);

        $result = $this->useCase->perform($groupId);

        $this->assertTrue($result);
    }

    public function test_perform_with_null_organization_id_throws_exception()
    {
        $groupId = 4;
        $group = new Group(
            id: $groupId,
            organization_id: null,
            name: 'No Org Group',
            description: 'No org description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This group don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($groupId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $groupId = 5;
        
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $group = new Group(
            id: $groupId,
            organization_id: $newOrganization->id,
            name: 'New Org Group',
            description: 'New org description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($group)
            ->andReturn(true);

        $result = $this->useCase->perform($groupId);

        $this->assertTrue($result);
    }

    public function test_perform_with_group_without_description()
    {
        $groupId = 6;
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group Without Description',
            description: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($group)
            ->andReturn(true);

        $result = $this->useCase->perform($groupId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_id()
    {
        $groupId = 0;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andThrow(new Exception('Invalid group ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid group ID');

        $this->useCase->perform($groupId);
    }

    public function test_perform_with_negative_id()
    {
        $groupId = -1;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andThrow(new Exception('Invalid group ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid group ID');

        $this->useCase->perform($groupId);
    }

    public function test_perform_with_group_with_empty_description()
    {
        $groupId = 7;
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group With Empty Description',
            description: ''
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($group)
            ->andReturn(true);

        $result = $this->useCase->perform($groupId);

        $this->assertTrue($result);
    }

    public function test_perform_with_group_with_long_description()
    {
        $groupId = 8;
        $longDescription = str_repeat('This is a very long description. ', 20);
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: 'Group With Long Description',
            description: $longDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($group)
            ->andReturn(true);

        $result = $this->useCase->perform($groupId);

        $this->assertTrue($result);
    }

    public function test_perform_with_special_characters_in_group()
    {
        $groupId = 9;
        $specialName = 'Group with Special Characters: @#$%^&*()';
        $specialDescription = 'Description with special chars: <>&"\'';
        
        $group = new Group(
            id: $groupId,
            organization_id: $this->organization->id,
            name: $specialName,
            description: $specialDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($groupId)
            ->andReturn($group);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($group)
            ->andReturn(true);

        $result = $this->useCase->perform($groupId);

        $this->assertTrue($result);
    }
}
