<?php

namespace Tests\Unit\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ItemRepository;
use App\UseCases\Inventory\Item\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private ItemRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ItemRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_items()
    {
        $items = [
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 1,
                quantity: 10,
                value: 150.50
            ),
            new Item(
                id: 2,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 2,
                quantity: 5,
                value: 75.25
            )
        ];

        $expectedResult = [
            'data' => $items,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $item) {
            $this->assertInstanceOf(Item::class, $item);
            $this->assertEquals($this->organization->id, $item->organization_id);
        }
    }

    public function test_perform_with_empty_result()
    {
        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
        $this->assertEmpty($result['data']);
    }

    public function test_perform_handles_repository_exception()
    {
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform();
    }

    public function test_perform_with_pagination()
    {
        $items = [];
        for ($i = 1; $i <= 5; $i++) {
            $items[] = new Item(
                id: $i,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: $i,
                quantity: $i * 10,
                value: $i * 150.50
            );
        }

        $expectedResult = [
            'data' => $items,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($differentOrg->id) // Should use the different organization
            ->andReturn($expectedResult);

        $this->useCase->perform();
    }

    public function test_perform_with_various_item_types()
    {
        $items = [
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 1,
                quantity: 0,
                value: 0.0
            ),
            new Item(
                id: 2,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 2,
                quantity: 999999,
                value: 99999999.99
            ),
            new Item(
                id: 3,
                organization_id: $this->organization->id,
                sale_id: 2,
                product_id: 3,
                quantity: 33,
                value: 123.456789
            )
        ];

        $expectedResult = [
            'data' => $items,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify different item types
        $this->assertEquals(0, $result['data'][0]->quantity);
        $this->assertEquals(0.0, $result['data'][0]->value);
        
        $this->assertEquals(999999, $result['data'][1]->quantity);
        $this->assertEquals(99999999.99, $result['data'][1]->value);
        
        $this->assertEquals(33, $result['data'][2]->quantity);
        $this->assertEquals(123.456789, $result['data'][2]->value);
    }

    public function test_perform_with_multiple_sales()
    {
        $items = [
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 1,
                quantity: 10,
                value: 150.50
            ),
            new Item(
                id: 2,
                organization_id: $this->organization->id,
                sale_id: 2,
                product_id: 2,
                quantity: 20,
                value: 300.75
            ),
            new Item(
                id: 3,
                organization_id: $this->organization->id,
                sale_id: 3,
                product_id: 3,
                quantity: 30,
                value: 450.25
            )
        ];

        $expectedResult = [
            'data' => $items,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify items from different sales
        $this->assertEquals(1, $result['data'][0]->sale_id);
        $this->assertEquals(2, $result['data'][1]->sale_id);
        $this->assertEquals(3, $result['data'][2]->sale_id);
    }

    public function test_perform_with_multiple_products()
    {
        $items = [
            new Item(
                id: 1,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 1,
                quantity: 10,
                value: 150.50
            ),
            new Item(
                id: 2,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 2,
                quantity: 15,
                value: 225.75
            ),
            new Item(
                id: 3,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 3,
                quantity: 20,
                value: 300.00
            )
        ];

        $expectedResult = [
            'data' => $items,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify items with different products
        $this->assertEquals(1, $result['data'][0]->product_id);
        $this->assertEquals(2, $result['data'][1]->product_id);
        $this->assertEquals(3, $result['data'][2]->product_id);
    }
}
