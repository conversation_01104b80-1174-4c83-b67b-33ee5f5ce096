<?php

namespace Tests\Unit\UseCases\Inventory\Item;

use App\Domains\Inventory\Item;
use App\Factories\Inventory\ItemFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ItemRepository;
use App\UseCases\Inventory\Item\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private ItemRepository $mockRepository;
    private ItemFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ItemRepository::class);
        $this->mockFactory = Mockery::mock(ItemFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_item_successfully()
    {
        $itemId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingItem = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 5,
            value: 75.25
        );

        $domainFromRequest = new Item(
            id: null,
            organization_id: null,
            sale_id: 1,
            product_id: 1,
            quantity: 15,
            value: 225.75
        );

        $updatedItem = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 15,
            value: 225.75
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($existingItem);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($item) use ($itemId) {
                return $item instanceof Item &&
                       $item->id === $itemId &&
                       $item->organization_id === $this->organization->id &&
                       $item->quantity === 15;
            }), $this->organization->id)
            ->andReturn($updatedItem);

        $result = $this->useCase->perform($itemId, $request);

        $this->assertInstanceOf(Item::class, $result);
        $this->assertEquals($itemId, $result->id);
        $this->assertEquals(15, $result->quantity);
        $this->assertEquals(225.75, $result->value);
    }

    public function test_perform_throws_exception_when_item_belongs_to_different_organization()
    {
        $itemId = 1;
        $request = $this->createMockUpdateRequest();
        $otherOrganization = Organization::factory()->create();
        
        $existingItem = new Item(
            id: $itemId,
            organization_id: $otherOrganization->id, // Different organization
            sale_id: 1,
            product_id: 1,
            quantity: 5,
            value: 75.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($existingItem);

        $this->mockFactory
            ->shouldNotReceive('buildFromUpdateRequest');

        $this->mockRepository
            ->shouldNotReceive('update');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This item don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($itemId, $request);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $itemId = 999;
        $request = $this->createMockUpdateRequest();

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($itemId, $request);
    }

    public function test_perform_handles_factory_exception()
    {
        $itemId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingItem = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 5,
            value: 75.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($existingItem);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($itemId, $request);
    }

    public function test_perform_handles_repository_update_exception()
    {
        $itemId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingItem = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 5,
            value: 75.25
        );

        $domainFromRequest = new Item(
            id: null,
            organization_id: null,
            sale_id: 1,
            product_id: 1,
            quantity: 15,
            value: 225.75
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($existingItem);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($itemId, $request);
    }

    public function test_perform_preserves_original_id_and_organization()
    {
        $itemId = 1;
        $request = $this->createMockUpdateRequest();
        
        $existingItem = new Item(
            id: $itemId,
            organization_id: $this->organization->id,
            sale_id: 1,
            product_id: 1,
            quantity: 5,
            value: 75.25
        );

        $domainFromRequest = new Item(
            id: 999, // Different ID in request
            organization_id: 888, // Different organization in request
            sale_id: 2,
            product_id: 2,
            quantity: 25,
            value: 375.50
        );

        $updatedItem = new Item(
            id: $itemId, // Should preserve original
            organization_id: $this->organization->id, // Should preserve original
            sale_id: 2,
            product_id: 2,
            quantity: 25,
            value: 375.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($itemId)
            ->andReturn($existingItem);

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(Mockery::on(function ($item) use ($itemId) {
                return $item->id === $itemId && 
                       $item->organization_id === $this->organization->id;
            }), $this->organization->id)
            ->andReturn($updatedItem);

        $result = $this->useCase->perform($itemId, $request);

        $this->assertEquals($itemId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_with_different_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $index => $testCase) {
            $itemId = $index + 1;
            $request = $this->createMockUpdateRequest();
            
            $existingItem = new Item(
                id: $itemId,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 1,
                quantity: 5,
                value: 75.25
            );

            $domainFromRequest = new Item(
                id: null,
                organization_id: null,
                sale_id: 1,
                product_id: 1,
                quantity: $testCase['quantity'],
                value: $testCase['value']
            );

            $updatedItem = new Item(
                id: $itemId,
                organization_id: $this->organization->id,
                sale_id: 1,
                product_id: 1,
                quantity: $testCase['quantity'],
                value: $testCase['value']
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($itemId)
                ->andReturn($existingItem);

            $this->mockFactory
                ->shouldReceive('buildFromUpdateRequest')
                ->once()
                ->with($request)
                ->andReturn($domainFromRequest);

            $this->mockRepository
                ->shouldReceive('update')
                ->once()
                ->andReturn($updatedItem);

            $result = $this->useCase->perform($itemId, $request);

            $this->assertEquals($testCase['quantity'], $result->quantity);
            $this->assertEquals($testCase['value'], $result->value);
        }
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\Item\UpdateRequest {
            public $sale_id = 1;
            public $product_id = 1;
            public $quantity = 15;
            public $value = 225.75;
        };
    }
}
