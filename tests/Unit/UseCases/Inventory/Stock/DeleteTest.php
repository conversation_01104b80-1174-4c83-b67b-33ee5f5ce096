<?php

namespace Tests\Unit\UseCases\Inventory\Stock;

use App\Domains\Inventory\Stock;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\StockRepository;
use App\UseCases\Inventory\Stock\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private StockRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(StockRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_stock_successfully()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($stock)
            ->andReturn(true);

        $result = $this->useCase->perform($stockId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_stock_belongs_to_different_organization()
    {
        $stockId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $otherOrganization->id, // Different organization
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This stock don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($stockId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $stockId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($stockId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($stock)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($stockId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($stock)
            ->andReturn(false);

        $result = $this->useCase->perform($stockId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $stockId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id, // Stock belongs to original org
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        // Should throw exception because stock belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This stock don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($stockId);
    }

    public function test_perform_with_stock_without_shop()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: null,
            brand_id: null,
            product_id: 1,
            quantity: 50,
            value: 500.0,
            description: 'Stock without shop'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($stock)
            ->andReturn(true);

        $result = $this->useCase->perform($stockId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_quantity_stock()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Empty stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($stock)
            ->andReturn(true);

        $result = $this->useCase->perform($stockId);

        $this->assertTrue($result);
    }

    public function test_perform_with_high_quantity_stock()
    {
        $stockId = 1;
        
        $stock = new Stock(
            id: $stockId,
            organization_id: $this->organization->id,
            shop_id: 1,
            brand_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 9999999.99,
            description: 'Large stock'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($stockId)
            ->andReturn($stock);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($stock)
            ->andReturn(true);

        $result = $this->useCase->perform($stockId);

        $this->assertTrue($result);
    }
}
