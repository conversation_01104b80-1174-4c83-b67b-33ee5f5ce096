<?php

namespace Tests\Unit\UseCases\Inventory\DepartmentUser;

use App\Domains\Inventory\DepartmentUser;
use App\Domains\Inventory\Department;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentUserRepository;
use App\Repositories\DepartmentRepository;
use App\UseCases\Inventory\DepartmentUser\Get;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private DepartmentUserRepository $mockDepartmentUserRepository;
    private DepartmentRepository $mockDepartmentRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockDepartmentUserRepository = Mockery::mock(DepartmentUserRepository::class);
        $this->mockDepartmentRepository = Mockery::mock(DepartmentRepository::class);
        
        $this->useCase = new Get($this->mockDepartmentUserRepository, $this->mockDepartmentRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_department_user_when_belongs_to_organization()
    {
        $departmentUserId = 1;
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $department = new Department(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($department);

        $result = $this->useCase->perform($departmentUserId);

        $this->assertInstanceOf(DepartmentUser::class, $result);
        $this->assertEquals($departmentUserId, $result->id);
        $this->assertEquals(1, $result->user_id);
        $this->assertEquals(1, $result->department_id);
    }

    public function test_perform_throws_exception_when_department_belongs_to_different_organization()
    {
        $departmentUserId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $department = new Department(
            id: 1,
            organization_id: $otherOrganization->id,
            name: 'Other Org Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($department);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department user assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_handles_department_user_not_found()
    {
        $departmentUserId = 999;

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andThrow(new Exception('Department user not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Department user not found');

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_handles_department_not_found()
    {
        $departmentUserId = 1;
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 999
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(999)
            ->andThrow(new Exception('Department not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Department not found');

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_validates_organization_ownership()
    {
        $departmentUserId = 3;
        
        // Create a department user that belongs to the user's organization
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 2,
            department_id: 2
        );

        $department = new Department(
            id: 2,
            organization_id: $this->organization->id,
            name: 'Valid Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($department);

        $result = $this->useCase->perform($departmentUserId);

        // Should not throw exception and return the department user
        $this->assertEquals($this->organization->id, $department->organization_id);
        $this->assertEquals($departmentUserId, $result->id);
    }

    public function test_perform_with_null_department_organization_id_throws_exception()
    {
        $departmentUserId = 4;
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $department = new Department(
            id: 1,
            organization_id: null,
            name: 'No Org Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($department);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department user assignment doesn't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $departmentUserId = 5;
        
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 1,
            department_id: 1
        );

        $department = new Department(
            id: 1,
            organization_id: $newOrganization->id,
            name: 'New Org Department',
            is_active: true
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($department);

        $result = $this->useCase->perform($departmentUserId);

        $this->assertEquals($newOrganization->id, $department->organization_id);
        $this->assertEquals($departmentUserId, $result->id);
    }

    public function test_perform_with_zero_id()
    {
        $departmentUserId = 0;

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andThrow(new Exception('Invalid department user ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid department user ID');

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_with_negative_id()
    {
        $departmentUserId = -1;

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andThrow(new Exception('Invalid department user ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid department user ID');

        $this->useCase->perform($departmentUserId);
    }

    public function test_perform_with_different_user_and_department_combinations()
    {
        $departmentUserId = 6;
        
        $departmentUser = new DepartmentUser(
            id: $departmentUserId,
            user_id: 10,
            department_id: 20
        );

        $department = new Department(
            id: 20,
            organization_id: $this->organization->id,
            name: 'Special Department',
            is_active: false
        );

        $this->mockDepartmentUserRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentUserId)
            ->andReturn($departmentUser);

        $this->mockDepartmentRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(20)
            ->andReturn($department);

        $result = $this->useCase->perform($departmentUserId);

        $this->assertEquals(10, $result->user_id);
        $this->assertEquals(20, $result->department_id);
        $this->assertFalse($department->is_active);
    }
}
