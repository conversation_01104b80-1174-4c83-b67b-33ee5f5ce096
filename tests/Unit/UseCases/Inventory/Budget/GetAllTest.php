<?php

namespace Tests\Unit\UseCases\Inventory\Budget;

use App\Domains\Filters\BudgetFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Budget;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BudgetRepository;
use App\UseCases\Inventory\Budget\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private BudgetRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BudgetRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_budgets()
    {
        $request = new Request([
            'client_id' => 1,
            'order' => 'value',
            'by' => 'desc',
            'limit' => 10
        ]);

        $budgets = [
            new Budget(
                id: 1,
                organization_id: $this->organization->id,
                client_id: 1,
                value: 5000.00,
                cost: 3000.00,
                name: 'Test Budget 1',
                description: 'Test budget 1 description'
            ),
            new Budget(
                id: 2,
                organization_id: $this->organization->id,
                client_id: 1,
                value: 7500.00,
                cost: 4500.00,
                name: 'Test Budget 2',
                description: 'Test budget 2 description'
            )
        ];

        $expectedResult = [
            'data' => $budgets,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BudgetFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'value' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 10;
                }),
                false // with_client
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $budget) {
            $this->assertInstanceOf(Budget::class, $budget);
            $this->assertEquals($this->organization->id, $budget->organization_id);
        }
    }

    public function test_perform_with_default_parameters()
    {
        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BudgetFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'created_at' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 30;
                }),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
    }

    public function test_perform_with_filters()
    {
        $request = new Request([
            'client_id' => 5,
            'name' => 'Alpha Budget',
            'value_min' => 1000.00,
            'value_max' => 10000.00,
            'cost_min' => 500.00,
            'cost_max' => 8000.00
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) use ($request) {
                    return $filters instanceof BudgetFilters &&
                           isset($filters->filters['client_id']) &&
                           isset($filters->filters['name']) &&
                           isset($filters->filters['value_min']) &&
                           isset($filters->filters['value_max']) &&
                           isset($filters->filters['cost_min']) &&
                           isset($filters->filters['cost_max']);
                }),
                Mockery::any(),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_custom_ordering()
    {
        $request = new Request([
            'order' => 'cost',
            'by' => 'asc',
            'limit' => 50
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'cost' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 50;
                }),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_client_relationship()
    {
        $request = new Request([
            'with_client' => true
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::any(),
                true  // with_client
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = new Request();

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_pagination()
    {
        $request = new Request([
            'limit' => 5
        ]);

        $budgets = [];
        for ($i = 1; $i <= 5; $i++) {
            $budgets[] = new Budget(
                id: $i,
                organization_id: $this->organization->id,
                client_id: $i,
                value: $i * 1000.0,
                cost: $i * 600.0,
                name: "Budget $i",
                description: "Budget $i description"
            );
        }

        $expectedResult = [
            'data' => $budgets,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy->limit === 5;
                }),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $differentOrg->id, // Should use the different organization
                Mockery::any(),
                Mockery::any(),
                false
            )
            ->andReturn($expectedResult);

        $this->useCase->perform($request);
    }

    public function test_perform_with_value_range_filter()
    {
        $request = new Request([
            'value_min' => 1000.00,
            'value_max' => 5000.00
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BudgetFilters &&
                           isset($filters->filters['value_min']) &&
                           isset($filters->filters['value_max']);
                }),
                Mockery::any(),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_cost_range_filter()
    {
        $request = new Request([
            'cost_min' => 500.00,
            'cost_max' => 3000.00
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BudgetFilters &&
                           isset($filters->filters['cost_min']) &&
                           isset($filters->filters['cost_max']);
                }),
                Mockery::any(),
                false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }
}
