<?php

namespace Tests\Unit\UseCases\Inventory\Budget;

use App\Domains\Inventory\Budget;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BudgetRepository;
use App\UseCases\Inventory\Budget\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private BudgetRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BudgetRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_budget_successfully()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 5000.00,
            cost: 3000.00,
            name: 'Test Budget',
            description: 'Test budget description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $result = $this->useCase->perform($budgetId);

        $this->assertInstanceOf(Budget::class, $result);
        $this->assertEquals($budgetId, $result->id);
        $this->assertEquals(5000.00, $result->value);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $budgetId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($budgetId);
    }

    public function test_perform_with_minimal_budget()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: null,
            value: null,
            cost: null,
            name: 'Minimal Budget',
            description: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $result = $this->useCase->perform($budgetId);

        $this->assertInstanceOf(Budget::class, $result);
        $this->assertEquals('Minimal Budget', $result->name);
        $this->assertNull($result->client_id);
        $this->assertNull($result->value);
        $this->assertNull($result->cost);
        $this->assertNull($result->description);
    }

    public function test_perform_with_complete_budget()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 5,
            value: 7500.00,
            cost: 4500.00,
            name: 'Complete Budget',
            description: 'Complete budget with all fields'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $result = $this->useCase->perform($budgetId);

        $this->assertInstanceOf(Budget::class, $result);
        $this->assertEquals($budgetId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(5, $result->client_id);
        $this->assertEquals(7500.00, $result->value);
        $this->assertEquals(4500.00, $result->cost);
        $this->assertEquals('Complete Budget', $result->name);
        $this->assertEquals('Complete budget with all fields', $result->description);
    }

    public function test_perform_with_zero_value_budget()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 0.0,
            cost: 0.0,
            name: 'Zero Budget',
            description: 'Budget with zero values'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $result = $this->useCase->perform($budgetId);

        $this->assertInstanceOf(Budget::class, $result);
        $this->assertEquals(0.0, $result->value);
        $this->assertEquals(0.0, $result->cost);
    }

    public function test_perform_with_high_value_budget()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 999999.99,
            cost: 888888.88,
            name: 'High Value Budget',
            description: 'Budget with high values'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $result = $this->useCase->perform($budgetId);

        $this->assertInstanceOf(Budget::class, $result);
        $this->assertEquals(999999.99, $result->value);
        $this->assertEquals(888888.88, $result->cost);
    }

    public function test_perform_returns_budget_with_all_properties()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 3,
            value: 12500.00,
            cost: 8750.00,
            name: 'Budget with all properties set',
            description: 'Budget with all properties set',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $result = $this->useCase->perform($budgetId);

        $this->assertInstanceOf(Budget::class, $result);
        $this->assertEquals($budgetId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(3, $result->client_id);
        $this->assertEquals(12500.00, $result->value);
        $this->assertEquals(8750.00, $result->cost);
        $this->assertEquals('Budget with all properties set', $result->name);
        $this->assertEquals('Budget with all properties set', $result->description);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_budget_ids()
    {
        // Test with different budget IDs to ensure correct ID handling
        $budgetIds = [1, 42, 999, 12345];

        foreach ($budgetIds as $budgetId) {
            $budget = new Budget(
                id: $budgetId,
                organization_id: $this->organization->id,
                client_id: 1,
                value: $budgetId * 100.0,
                cost: $budgetId * 60.0,
                name: "Budget $budgetId",
                description: "Test budget $budgetId"
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($budgetId)
                ->andReturn($budget);

            $result = $this->useCase->perform($budgetId);

            $this->assertInstanceOf(Budget::class, $result);
            $this->assertEquals($budgetId, $result->id);
            $this->assertEquals("Budget $budgetId", $result->name);
            $this->assertEquals($budgetId * 100.0, $result->value);
            $this->assertEquals($budgetId * 60.0, $result->cost);
        }
    }

    public function test_perform_with_different_value_ranges()
    {
        $values = [0.0, 100.0, 5000.0, 50000.0, 999999.99];

        foreach ($values as $index => $value) {
            $budgetId = $index + 1;
            $cost = $value * 0.6; // 60% of value
            
            $budget = new Budget(
                id: $budgetId,
                organization_id: $this->organization->id,
                client_id: 1,
                value: $value,
                cost: $cost,
                name: "Budget with value $value",
                description: "Budget with value $value"
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($budgetId)
                ->andReturn($budget);

            $result = $this->useCase->perform($budgetId);

            $this->assertInstanceOf(Budget::class, $result);
            $this->assertEquals($value, $result->value);
            $this->assertEquals($cost, $result->cost);
        }
    }

    public function test_perform_profit_calculation()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 10000.00,
            cost: 6000.00,
            name: 'Profit Budget',
            description: 'Budget for profit calculation'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $result = $this->useCase->perform($budgetId);

        $this->assertInstanceOf(Budget::class, $result);
        
        // Calculate profit (Value - Cost)
        $expectedProfit = 4000.00;
        $actualProfit = $result->value - $result->cost;
        $this->assertEquals($expectedProfit, $actualProfit);
    }
}
