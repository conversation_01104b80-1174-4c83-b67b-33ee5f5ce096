<?php

namespace Tests\Unit\UseCases\Inventory\Budget;

use App\Domains\Inventory\Budget;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BudgetRepository;
use App\UseCases\Inventory\Budget\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private BudgetRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BudgetRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_budget_successfully()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 5000.00,
            cost: 3000.00,
            name: 'Test Budget',
            description: 'Test budget description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budget)
            ->andReturn(true);

        $result = $this->useCase->perform($budgetId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_budget_belongs_to_different_organization()
    {
        $budgetId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $otherOrganization->id, // Different organization
            client_id: 1,
            value: 5000.00,
            cost: 3000.00,
            name: 'Test Budget',
            description: 'Test budget description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This budget don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($budgetId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $budgetId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($budgetId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 5000.00,
            cost: 3000.00,
            name: 'Test Budget',
            description: 'Test budget description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budget)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($budgetId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 5000.00,
            cost: 3000.00,
            name: 'Test Budget',
            description: 'Test budget description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budget)
            ->andReturn(false);

        $result = $this->useCase->perform($budgetId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $budgetId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id, // Budget belongs to original org
            client_id: 1,
            value: 5000.00,
            cost: 3000.00,
            name: 'Test Budget',
            description: 'Test budget description'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        // Should throw exception because budget belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This budget don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($budgetId);
    }

    public function test_perform_with_budget_without_client()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: null,
            value: 2500.00,
            cost: 1500.00,
            name: 'Budget without Client',
            description: 'Budget without client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budget)
            ->andReturn(true);

        $result = $this->useCase->perform($budgetId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_value_budget()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 0.0,
            cost: 0.0,
            name: 'Zero Budget',
            description: 'Budget with zero values'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budget)
            ->andReturn(true);

        $result = $this->useCase->perform($budgetId);

        $this->assertTrue($result);
    }

    public function test_perform_with_high_value_budget()
    {
        $budgetId = 1;
        
        $budget = new Budget(
            id: $budgetId,
            organization_id: $this->organization->id,
            client_id: 1,
            value: 999999.99,
            cost: 888888.88,
            name: 'High Value Budget',
            description: 'Budget with high values'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($budgetId)
            ->andReturn($budget);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($budget)
            ->andReturn(true);

        $result = $this->useCase->perform($budgetId);

        $this->assertTrue($result);
    }
}
