<?php

namespace Tests\Unit\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\CustomProductRepository;
use App\UseCases\Inventory\CustomProduct\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private CustomProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(CustomProductRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_custom_product_successfully()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $result = $this->useCase->perform($customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals($customProductId, $result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertEquals('Test custom product', $result->description);
    }

    public function test_perform_handles_repository_exception()
    {
        $customProductId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($customProductId);
    }

    public function test_perform_with_minimal_custom_product()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 1,
            value: 15.50,
            description: 'Minimal custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $result = $this->useCase->perform($customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(1, $result->quantity);
        $this->assertEquals(15.50, $result->value);
        $this->assertEquals('Minimal custom product', $result->description);
    }

    public function test_perform_with_complete_custom_product()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 2,
            budget_id: 3,
            quantity: 200,
            value: 3000.75,
            description: 'Complete custom product',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $result = $this->useCase->perform($customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals($customProductId, $result->id);
        $this->assertEquals(2, $result->project_id);
        $this->assertEquals(3, $result->budget_id);
        $this->assertEquals(200, $result->quantity);
        $this->assertEquals(3000.75, $result->value);
        $this->assertEquals('Complete custom product', $result->description);
    }

    public function test_perform_with_zero_quantity()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $result = $this->useCase->perform($customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'High quantity custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $result = $this->useCase->perform($customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(99999999.99, $result->value);
    }

    public function test_perform_with_decimal_values()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value custom product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $result = $this->useCase->perform($customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_with_long_description()
    {
        $customProductId = 1;
        $longDescription = str_repeat('Long description text. ', 20);
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 1,
            budget_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $result = $this->useCase->perform($customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals($longDescription, $result->description);
    }

    public function test_perform_returns_custom_product_with_all_properties()
    {
        $customProductId = 1;
        
        $customProduct = new CustomProduct(
            id: $customProductId,
            project_id: 5,
            budget_id: 3,
            quantity: 100,
            value: 1500.50,
            description: 'Full properties custom product',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($customProductId)
            ->andReturn($customProduct);

        $result = $this->useCase->perform($customProductId);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals($customProductId, $result->id);
        $this->assertEquals(5, $result->project_id);
        $this->assertEquals(3, $result->budget_id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertEquals('Full properties custom product', $result->description);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $index => $testCase) {
            $customProductId = $index + 1;
            
            $customProduct = new CustomProduct(
                id: $customProductId,
                project_id: 1,
                budget_id: 1,
                quantity: $testCase['quantity'],
                value: $testCase['value'],
                description: 'Test custom product'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($customProductId)
                ->andReturn($customProduct);

            $result = $this->useCase->perform($customProductId);

            $this->assertInstanceOf(CustomProduct::class, $result);
            $this->assertEquals($testCase['quantity'], $result->quantity);
            $this->assertEquals($testCase['value'], $result->value);
        }
    }

    public function test_perform_with_different_project_and_budget_ids()
    {
        $testCases = [
            ['project_id' => 1, 'budget_id' => 1],
            ['project_id' => 2, 'budget_id' => 3],
            ['project_id' => 5, 'budget_id' => 10],
        ];

        foreach ($testCases as $index => $testCase) {
            $customProductId = $index + 1;
            
            $customProduct = new CustomProduct(
                id: $customProductId,
                project_id: $testCase['project_id'],
                budget_id: $testCase['budget_id'],
                quantity: 10,
                value: 150.50,
                description: 'Test custom product'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($customProductId)
                ->andReturn($customProduct);

            $result = $this->useCase->perform($customProductId);

            $this->assertInstanceOf(CustomProduct::class, $result);
            $this->assertEquals($testCase['project_id'], $result->project_id);
            $this->assertEquals($testCase['budget_id'], $result->budget_id);
        }
    }
}
