<?php

namespace Tests\Unit\UseCases\Inventory\CustomProduct;

use App\Domains\Inventory\CustomProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\CustomProductRepository;
use App\UseCases\Inventory\CustomProduct\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private CustomProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(CustomProductRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_custom_products()
    {
        $customProducts = [
            new CustomProduct(
                id: 1,
                project_id: 1,
                budget_id: 1,
                quantity: 10,
                value: 150.50,
                description: 'First custom product'
            ),
            new CustomProduct(
                id: 2,
                project_id: 2,
                budget_id: 2,
                quantity: 5,
                value: 75.25,
                description: 'Second custom product'
            )
        ];

        $expectedResult = [
            'data' => $customProducts,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $customProduct) {
            $this->assertInstanceOf(CustomProduct::class, $customProduct);
        }
    }

    public function test_perform_with_empty_result()
    {
        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
        $this->assertEmpty($result['data']);
    }

    public function test_perform_handles_repository_exception()
    {
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform();
    }

    public function test_perform_with_pagination()
    {
        $customProducts = [];
        for ($i = 1; $i <= 5; $i++) {
            $customProducts[] = new CustomProduct(
                id: $i,
                project_id: 1,
                budget_id: 1,
                quantity: $i * 10,
                value: $i * 150.50,
                description: "Custom product {$i}"
            );
        }

        $expectedResult = [
            'data' => $customProducts,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($differentOrg->id) // Should use the different organization
            ->andReturn($expectedResult);

        $this->useCase->perform();
    }

    public function test_perform_with_various_custom_product_types()
    {
        $customProducts = [
            new CustomProduct(
                id: 1,
                project_id: 1,
                budget_id: 1,
                quantity: 0,
                value: 0.0,
                description: 'Zero quantity custom product'
            ),
            new CustomProduct(
                id: 2,
                project_id: 2,
                budget_id: 2,
                quantity: 999999,
                value: 99999999.99,
                description: 'High quantity custom product'
            ),
            new CustomProduct(
                id: 3,
                project_id: 3,
                budget_id: 3,
                quantity: 33,
                value: 123.456789,
                description: 'Decimal value custom product'
            )
        ];

        $expectedResult = [
            'data' => $customProducts,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify different custom product types
        $this->assertEquals(0, $result['data'][0]->quantity);
        $this->assertEquals(0.0, $result['data'][0]->value);
        
        $this->assertEquals(999999, $result['data'][1]->quantity);
        $this->assertEquals(99999999.99, $result['data'][1]->value);
        
        $this->assertEquals(33, $result['data'][2]->quantity);
        $this->assertEquals(123.456789, $result['data'][2]->value);
    }

    public function test_perform_with_multiple_projects()
    {
        $customProducts = [
            new CustomProduct(
                id: 1,
                project_id: 1,
                budget_id: 1,
                quantity: 10,
                value: 150.50,
                description: 'Project 1 custom product'
            ),
            new CustomProduct(
                id: 2,
                project_id: 2,
                budget_id: 2,
                quantity: 20,
                value: 300.75,
                description: 'Project 2 custom product'
            ),
            new CustomProduct(
                id: 3,
                project_id: 3,
                budget_id: 3,
                quantity: 30,
                value: 450.25,
                description: 'Project 3 custom product'
            )
        ];

        $expectedResult = [
            'data' => $customProducts,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify custom products from different projects
        $this->assertEquals(1, $result['data'][0]->project_id);
        $this->assertEquals(2, $result['data'][1]->project_id);
        $this->assertEquals(3, $result['data'][2]->project_id);
    }

    public function test_perform_with_multiple_budgets()
    {
        $customProducts = [
            new CustomProduct(
                id: 1,
                project_id: 1,
                budget_id: 1,
                quantity: 10,
                value: 150.50,
                description: 'Budget 1 custom product'
            ),
            new CustomProduct(
                id: 2,
                project_id: 2,
                budget_id: 2,
                quantity: 15,
                value: 225.75,
                description: 'Budget 2 custom product'
            ),
            new CustomProduct(
                id: 3,
                project_id: 3,
                budget_id: 3,
                quantity: 20,
                value: 300.00,
                description: 'Budget 3 custom product'
            )
        ];

        $expectedResult = [
            'data' => $customProducts,
            'count' => 3,
            'total' => 3,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(3, $result['count']);
        
        // Verify custom products with different budgets
        $this->assertEquals(1, $result['data'][0]->budget_id);
        $this->assertEquals(2, $result['data'][1]->budget_id);
        $this->assertEquals(3, $result['data'][2]->budget_id);
    }

    public function test_perform_with_long_descriptions()
    {
        $longDescription = str_repeat('Long description text. ', 20);
        
        $customProducts = [
            new CustomProduct(
                id: 1,
                project_id: 1,
                budget_id: 1,
                quantity: 1,
                value: 100.00,
                description: $longDescription
            )
        ];

        $expectedResult = [
            'data' => $customProducts,
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($longDescription, $result['data'][0]->description);
    }
}
