<?php

namespace Tests\Unit\UseCases\Inventory\Sale;

use App\Domains\Filters\SaleFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Sale;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\SaleRepository;
use App\UseCases\Inventory\Sale\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private SaleRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(SaleRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_sales()
    {
        $request = new Request([
            'user_id' => 1,
            'total_value' => 100.00,
            'order' => 'total_value',
            'by' => 'asc',
            'limit' => 10
        ]);

        $sales = [
            new Sale(
                id: 1,
                organization_id: $this->organization->id,
                user_id: 1,
                shop_id: 1,
                client_id: 1,
                total_value: 100.00
            ),
            new Sale(
                id: 2,
                organization_id: $this->organization->id,
                user_id: 1,
                shop_id: 1,
                client_id: 1,
                total_value: 150.50
            )
        ];

        $expectedResult = [
            'data' => $sales,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'total_value' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 10;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $sale) {
            $this->assertInstanceOf(Sale::class, $sale);
            $this->assertEquals($this->organization->id, $sale->organization_id);
        }
    }

    public function test_perform_with_default_parameters()
    {
        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'created_at' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 30;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
    }

    public function test_perform_with_filters()
    {
        $request = new Request([
            'user_id' => 1,
            'shop_id' => 2,
            'client_id' => 3,
            'total_value' => 100.00
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) use ($request) {
                    return $filters instanceof SaleFilters &&
                           isset($filters->filters['user_id']) &&
                           isset($filters->filters['shop_id']) &&
                           isset($filters->filters['client_id']) &&
                           isset($filters->filters['total_value']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_custom_ordering()
    {
        $request = new Request([
            'order' => 'user_id',
            'by' => 'desc',
            'limit' => 50
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'user_id' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 50;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = new Request();

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_pagination()
    {
        $request = new Request([
            'limit' => 5
        ]);

        $sales = [];
        for ($i = 1; $i <= 5; $i++) {
            $sales[] = new Sale(
                id: $i,
                organization_id: $this->organization->id,
                user_id: $this->user->id,
                shop_id: 1,
                client_id: 1,
                total_value: 100.00 * $i
            );
        }

        $expectedResult = [
            'data' => $sales,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy->limit === 5;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $differentOrg->id, // Should use the different organization
                Mockery::any(),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $this->useCase->perform($request);
    }

    public function test_perform_with_user_id_filter()
    {
        $request = new Request([
            'user_id' => 1
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters &&
                           isset($filters->filters['user_id']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_shop_id_filter()
    {
        $request = new Request([
            'shop_id' => 2
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters &&
                           isset($filters->filters['shop_id']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_client_id_filter()
    {
        $request = new Request([
            'client_id' => 3
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters &&
                           isset($filters->filters['client_id']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_total_value_filter()
    {
        $request = new Request([
            'total_value' => 150.50
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters &&
                           isset($filters->filters['total_value']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_total_value_range_filter()
    {
        $request = new Request([
            'min_total_value' => 100.00,
            'max_total_value' => 500.00
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters &&
                           isset($filters->filters['min_total_value']) &&
                           isset($filters->filters['max_total_value']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_date_range_filter()
    {
        $request = new Request([
            'start_date' => '2023-01-01',
            'end_date' => '2023-12-31'
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters &&
                           isset($filters->filters['start_date']) &&
                           isset($filters->filters['end_date']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_multiple_filters()
    {
        $request = new Request([
            'user_id' => 1,
            'shop_id' => 2,
            'client_id' => 3,
            'min_total_value' => 100.00,
            'max_total_value' => 500.00,
            'start_date' => '2023-01-01',
            'end_date' => '2023-12-31',
            'order' => 'total_value',
            'by' => 'asc',
            'limit' => 20
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof SaleFilters &&
                           isset($filters->filters['user_id']) &&
                           isset($filters->filters['shop_id']) &&
                           isset($filters->filters['client_id']) &&
                           isset($filters->filters['min_total_value']) &&
                           isset($filters->filters['max_total_value']) &&
                           isset($filters->filters['start_date']) &&
                           isset($filters->filters['end_date']);
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'total_value' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 20;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }
}
