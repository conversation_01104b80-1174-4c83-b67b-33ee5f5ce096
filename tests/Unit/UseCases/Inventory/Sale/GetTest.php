<?php

namespace Tests\Unit\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Domains\Inventory\Shop;
use App\Domains\Inventory\Client;
use App\Domains\Inventory\Item;
use App\Domains\User;
use App\Models\Organization;
use App\Models\User as UserModel;
use App\Repositories\SaleRepository;
use App\UseCases\Inventory\Sale\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private SaleRepository $mockRepository;
    private Organization $organization;
    private UserModel $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = UserModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(SaleRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_sale_successfully()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $result = $this->useCase->perform($saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($saleId, $result->id);
        $this->assertEquals(150.50, $result->total_value);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $saleId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($saleId);
    }

    public function test_perform_with_minimal_sale()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: null,
            client_id: null,
            total_value: 50.00
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $result = $this->useCase->perform($saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(50.00, $result->total_value);
        $this->assertNull($result->shop_id);
        $this->assertNull($result->client_id);
    }

    public function test_perform_with_complete_sale_and_relationships()
    {
        $saleId = 1;
        
        $user = new User(
            id: $this->user->id,
            organization_id: $this->organization->id,
            name: 'Test User',
            email: '<EMAIL>',
            password: 'password'
        );

        $shop = new Shop(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Shop',
            description: 'Test shop description'
        );

        $client = new Client(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Test Client',
            email: '<EMAIL>',
            phone: '123456789'
        );

        $items = [
            new Item(
                id: 1,
                sale_id: $saleId,
                product_id: 1,
                quantity: 2,
                unit_price: 75.25,
                total_price: 150.50
            )
        ];

        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50,
            user: $user,
            shop: $shop,
            client: $client,
            items: $items
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $result = $this->useCase->perform($saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($saleId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals($this->user->id, $result->user_id);
        $this->assertEquals(1, $result->shop_id);
        $this->assertEquals(1, $result->client_id);
        $this->assertEquals(150.50, $result->total_value);
        
        // Check relationships
        $this->assertInstanceOf(User::class, $result->user);
        $this->assertInstanceOf(Shop::class, $result->shop);
        $this->assertInstanceOf(Client::class, $result->client);
        $this->assertIsArray($result->items);
        $this->assertCount(1, $result->items);
        $this->assertInstanceOf(Item::class, $result->items[0]);
    }

    public function test_perform_with_zero_total_value()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 0.00
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $result = $this->useCase->perform($saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(0.00, $result->total_value);
    }

    public function test_perform_with_high_total_value()
    {
        $saleId = 1;
        $highValue = 999999.99;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: $highValue
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $result = $this->useCase->perform($saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($highValue, $result->total_value);
    }

    public function test_perform_with_decimal_total_value()
    {
        $saleId = 1;
        $decimalValue = 123.456;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: $decimalValue
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $result = $this->useCase->perform($saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($decimalValue, $result->total_value);
    }

    public function test_perform_returns_sale_with_all_properties()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 200.75,
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $result = $this->useCase->perform($saleId);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($saleId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals($this->user->id, $result->user_id);
        $this->assertEquals(1, $result->shop_id);
        $this->assertEquals(1, $result->client_id);
        $this->assertEquals(200.75, $result->total_value);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_sale_ids()
    {
        // Test with different sale IDs to ensure correct ID handling
        $saleIds = [1, 42, 999, 12345];

        foreach ($saleIds as $saleId) {
            $sale = new Sale(
                id: $saleId,
                organization_id: $this->organization->id,
                user_id: $this->user->id,
                shop_id: 1,
                client_id: 1,
                total_value: 100.00
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($saleId)
                ->andReturn($sale);

            $result = $this->useCase->perform($saleId);

            $this->assertInstanceOf(Sale::class, $result);
            $this->assertEquals($saleId, $result->id);
            $this->assertEquals(100.00, $result->total_value);
        }
    }

    public function test_perform_with_different_organization_ids()
    {
        $saleId = 1;
        $organizationIds = [1, 100, 999, 12345];

        foreach ($organizationIds as $orgId) {
            $sale = new Sale(
                id: $saleId,
                organization_id: $orgId,
                user_id: $this->user->id,
                shop_id: 1,
                client_id: 1,
                total_value: 100.00
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($saleId)
                ->andReturn($sale);

            $result = $this->useCase->perform($saleId);

            $this->assertInstanceOf(Sale::class, $result);
            $this->assertEquals($orgId, $result->organization_id);
            $this->assertEquals(100.00, $result->total_value);
        }
    }
}
