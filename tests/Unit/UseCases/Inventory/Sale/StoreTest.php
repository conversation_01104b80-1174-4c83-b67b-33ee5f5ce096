<?php

namespace Tests\Unit\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Factories\Inventory\SaleFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\SaleRepository;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Sales\CreatePayment;
use App\UseCases\Inventory\Sale\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private SaleRepository $mockRepository;
    private SaleFactory $mockFactory;
    private CreatePayment $mockCreatePayment;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(SaleRepository::class);
        $this->mockFactory = Mockery::mock(SaleFactory::class);
        $this->mockCreatePayment = Mockery::mock(CreatePayment::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory, $this->mockCreatePayment);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_sale_successfully()
    {
        $request = $this->createMockStoreRequest();

        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50
        );

        $storedSale = new Sale(
            id: 1,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($sale) {
                return $sale instanceof Sale &&
                       $sale->organization_id === $this->organization->id &&
                       $sale->total_value === 150.50;
            }))
            ->andReturn($storedSale);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(150.50, $result->total_value);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();

        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.00
        );

        $storedSale = new Sale(
            id: 2,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($sale) {
                return $sale->organization_id === $this->organization->id;
            }))
            ->andReturn($storedSale);

        $result = $this->useCase->perform($request);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_with_asaas_integration_enabled()
    {
        $request = $this->createMockStoreRequestWithAsaas();

        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 200.00
        );

        $storedSale = new Sale(
            id: 3,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 200.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedSale);

        $this->mockCreatePayment
            ->shouldReceive('perform')
            ->once()
            ->with($storedSale);

        // Note: The actual implementation uses DBLog, not Log facade
        // So we don't need to mock Log::info anymore

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(3, $result->id);
    }

    public function test_perform_with_asaas_integration_failure_does_not_fail_sale()
    {
        $request = $this->createMockStoreRequestWithAsaas();

        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 300.00
        );

        $storedSale = new Sale(
            id: 4,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 300.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedSale);

        $this->mockCreatePayment
            ->shouldReceive('perform')
            ->once()
            ->with($storedSale)
            ->andThrow(new AsaasException('ASAAS API error'));

        // Note: The actual implementation uses DBLog, not Log facade
        // So we don't need to mock Log::warning anymore

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(4, $result->id);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();

        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_minimal_sale_data()
    {
        $request = $this->createMockMinimalStoreRequest();

        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: null,
            client_id: null,
            total_value: 50.00
        );

        $storedSale = new Sale(
            id: 5,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: null,
            client_id: null,
            total_value: 50.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedSale);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(50.00, $result->total_value);
        $this->assertNull($result->shop_id);
        $this->assertNull($result->client_id);
    }

    public function test_perform_with_zero_total_value()
    {
        $request = $this->createMockZeroValueStoreRequest();

        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 0.00
        );

        $storedSale = new Sale(
            id: 6,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 0.00
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedSale);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(0.00, $result->total_value);
    }

    public function test_perform_with_high_total_value()
    {
        $request = $this->createMockHighValueStoreRequest();

        $highValue = 999999.99;
        $domainFromRequest = new Sale(
            id: null,
            organization_id: null,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: $highValue
        );

        $storedSale = new Sale(
            id: 7,
            organization_id: $this->organization->id,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: $highValue
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedSale);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals($highValue, $result->total_value);
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\Sale\StoreRequest {
            public $user_id = 1;
            public $shop_id = 1;
            public $client_id = 1;
            public $total_value = 150.50;
            public $enable_asaas_integration = false;

            public function input($key, $default = null)
            {
                return $key === 'enable_asaas_integration' ? $this->enable_asaas_integration : $default;
            }
        };
    }

    private function createMockStoreRequestWithAsaas()
    {
        return new class extends \App\Http\Requests\Sale\StoreRequest {
            public $user_id = 1;
            public $shop_id = 1;
            public $client_id = 1;
            public $total_value = 200.00;
            public $enable_asaas_integration = true;

            public function input($key, $default = null)
            {
                return $key === 'enable_asaas_integration' ? $this->enable_asaas_integration : $default;
            }
        };
    }

    private function createMockMinimalStoreRequest()
    {
        return new class extends \App\Http\Requests\Sale\StoreRequest {
            public $user_id = 1;
            public $shop_id = null;
            public $client_id = null;
            public $total_value = 50.00;
            public $enable_asaas_integration = false;

            public function input($key, $default = null)
            {
                return $key === 'enable_asaas_integration' ? $this->enable_asaas_integration : $default;
            }
        };
    }

    private function createMockZeroValueStoreRequest()
    {
        return new class extends \App\Http\Requests\Sale\StoreRequest {
            public $user_id = 1;
            public $shop_id = 1;
            public $client_id = 1;
            public $total_value = 0.00;
            public $enable_asaas_integration = false;

            public function input($key, $default = null)
            {
                return $key === 'enable_asaas_integration' ? $this->enable_asaas_integration : $default;
            }
        };
    }

    private function createMockHighValueStoreRequest()
    {
        return new class extends \App\Http\Requests\Sale\StoreRequest {
            public $user_id = 1;
            public $shop_id = 1;
            public $client_id = 1;
            public $total_value = 999999.99;
            public $enable_asaas_integration = false;

            public function input($key, $default = null)
            {
                return $key === 'enable_asaas_integration' ? $this->enable_asaas_integration : $default;
            }
        };
    }
}
