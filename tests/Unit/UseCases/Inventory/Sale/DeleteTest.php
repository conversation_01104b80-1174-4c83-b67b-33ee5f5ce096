<?php

namespace Tests\Unit\UseCases\Inventory\Sale;

use App\Domains\Inventory\Sale;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\SaleRepository;
use App\UseCases\Inventory\Sale\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private SaleRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(SaleRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_sale_successfully()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($sale)
            ->andReturn(true);

        $result = $this->useCase->perform($saleId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_sale_belongs_to_different_organization()
    {
        $saleId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $otherOrganization->id, // Different organization
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This sale don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($saleId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $saleId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($saleId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($sale)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($saleId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($sale)
            ->andReturn(false);

        $result = $this->useCase->perform($saleId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $saleId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id, // Sale belongs to original org
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 150.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        // Should throw exception because sale belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This sale don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($saleId);
    }

    public function test_perform_with_sale_with_zero_total_value()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: 0.00
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($sale)
            ->andReturn(true);

        $result = $this->useCase->perform($saleId);

        $this->assertTrue($result);
    }

    public function test_perform_with_sale_with_high_total_value()
    {
        $saleId = 1;
        $highValue = 999999.99;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: 1,
            client_id: 1,
            total_value: $highValue
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($sale)
            ->andReturn(true);

        $result = $this->useCase->perform($saleId);

        $this->assertTrue($result);
    }

    public function test_perform_with_sale_with_null_optional_fields()
    {
        $saleId = 1;
        
        $sale = new Sale(
            id: $saleId,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: null,
            client_id: null,
            total_value: 100.00
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($saleId)
            ->andReturn($sale);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($sale)
            ->andReturn(true);

        $result = $this->useCase->perform($saleId);

        $this->assertTrue($result);
    }

    public function test_perform_with_different_sale_ids()
    {
        $saleIds = [1, 42, 999, 12345];

        foreach ($saleIds as $saleId) {
            $sale = new Sale(
                id: $saleId,
                organization_id: $this->organization->id,
                user_id: $this->user->id,
                shop_id: 1,
                client_id: 1,
                total_value: 100.00
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($saleId)
                ->andReturn($sale);

            $this->mockRepository
                ->shouldReceive('delete')
                ->once()
                ->with($sale)
                ->andReturn(true);

            $result = $this->useCase->perform($saleId);

            $this->assertTrue($result);
        }
    }
}
