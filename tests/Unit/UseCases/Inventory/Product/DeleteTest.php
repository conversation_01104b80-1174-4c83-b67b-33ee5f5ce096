<?php

namespace Tests\Unit\UseCases\Inventory\Product;

use App\Domains\Inventory\Product;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductRepository;
use App\UseCases\Inventory\Product\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private ProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_product_successfully()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($product)
            ->andReturn(true);

        $result = $this->useCase->perform($productId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_product_belongs_to_different_organization()
    {
        $productId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $product = new Product(
            id: $productId,
            organization_id: $otherOrganization->id, // Different organization
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This product don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($productId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $productId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($productId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($product)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($productId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($product)
            ->andReturn(false);

        $result = $this->useCase->perform($productId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $productId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id, // Product belongs to original org
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        // Should throw exception because product belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This product don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($productId);
    }

    public function test_perform_with_product_without_brand()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: null,
            name: 'Product without Brand',
            barcode: null,
            description: null,
            price: null,
            unity: null,
            last_priced_at: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($product)
            ->andReturn(true);

        $result = $this->useCase->perform($productId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_price_product()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Free Product',
            barcode: '1234567890123',
            description: 'Product with zero price',
            price: 0.0,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($product)
            ->andReturn(true);

        $result = $this->useCase->perform($productId);

        $this->assertTrue($result);
    }

    public function test_perform_with_expensive_product()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Expensive Product',
            barcode: '1234567890123',
            description: 'Product with high price',
            price: 999999.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($product)
            ->andReturn(true);

        $result = $this->useCase->perform($productId);

        $this->assertTrue($result);
    }
}
