<?php

namespace Tests\Unit\UseCases\Inventory\Product;

use App\Domains\Inventory\Product;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProductRepository;
use App\UseCases\Inventory\Product\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private ProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProductRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_product_successfully()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $result = $this->useCase->perform($productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals($productId, $result->id);
        $this->assertEquals('Test Product', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $productId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($productId);
    }

    public function test_perform_with_minimal_product()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: null,
            name: 'Minimal Product',
            barcode: null,
            description: null,
            price: null,
            unity: null,
            last_priced_at: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $result = $this->useCase->perform($productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals('Minimal Product', $result->name);
        $this->assertNull($result->brand_id);
        $this->assertNull($result->barcode);
        $this->assertNull($result->price);
        $this->assertNull($result->unity);
    }

    public function test_perform_with_complete_product()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 5,
            name: 'Complete Product',
            barcode: '1234567890123',
            description: 'Complete product with all fields',
            price: 299.99,
            unity: 2,
            last_priced_at: now()->subDays(1)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $result = $this->useCase->perform($productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals($productId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(5, $result->brand_id);
        $this->assertEquals('Complete Product', $result->name);
        $this->assertEquals('1234567890123', $result->barcode);
        $this->assertEquals('Complete product with all fields', $result->description);
        $this->assertEquals(299.99, $result->price);
        $this->assertEquals(2, $result->unity);
        $this->assertNotNull($result->last_priced_at);
    }

    public function test_perform_with_zero_price_product()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Free Product',
            barcode: '1234567890123',
            description: 'Product with zero price',
            price: 0.0,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $result = $this->useCase->perform($productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals(0.0, $result->price);
    }

    public function test_perform_with_expensive_product()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 1,
            name: 'Expensive Product',
            barcode: '1234567890123',
            description: 'Product with high price',
            price: 999999.99,
            unity: 1,
            last_priced_at: now()
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $result = $this->useCase->perform($productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals(999999.99, $result->price);
    }

    public function test_perform_returns_product_with_all_properties()
    {
        $productId = 1;
        
        $product = new Product(
            id: $productId,
            organization_id: $this->organization->id,
            brand_id: 3,
            name: 'Full Property Product',
            barcode: '9876543210987',
            description: 'Product with all properties set',
            price: 199.99,
            unity: 3,
            last_priced_at: now()->subDays(3),
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($productId)
            ->andReturn($product);

        $result = $this->useCase->perform($productId);

        $this->assertInstanceOf(Product::class, $result);
        $this->assertEquals($productId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(3, $result->brand_id);
        $this->assertEquals('Full Property Product', $result->name);
        $this->assertEquals('9876543210987', $result->barcode);
        $this->assertEquals('Product with all properties set', $result->description);
        $this->assertEquals(199.99, $result->price);
        $this->assertEquals(3, $result->unity);
        $this->assertNotNull($result->last_priced_at);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_product_ids()
    {
        // Test with different product IDs to ensure correct ID handling
        $productIds = [1, 42, 999, 12345];

        foreach ($productIds as $productId) {
            $product = new Product(
                id: $productId,
                organization_id: $this->organization->id,
                brand_id: 1,
                name: "Product $productId",
                barcode: "123456789012$productId",
                description: "Test product $productId",
                price: $productId * 10.0,
                unity: 1,
                last_priced_at: now()
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($productId)
                ->andReturn($product);

            $result = $this->useCase->perform($productId);

            $this->assertInstanceOf(Product::class, $result);
            $this->assertEquals($productId, $result->id);
            $this->assertEquals("Product $productId", $result->name);
            $this->assertEquals($productId * 10.0, $result->price);
        }
    }

    public function test_perform_with_different_unity_values()
    {
        $unityValues = [1, 2, 3];

        foreach ($unityValues as $index => $unity) {
            $productId = $index + 1;
            
            $product = new Product(
                id: $productId,
                organization_id: $this->organization->id,
                brand_id: 1,
                name: "Product Unity $unity",
                barcode: '1234567890123',
                description: "Product with unity $unity",
                price: 99.99,
                unity: $unity,
                last_priced_at: now()
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($productId)
                ->andReturn($product);

            $result = $this->useCase->perform($productId);

            $this->assertInstanceOf(Product::class, $result);
            $this->assertEquals($unity, $result->unity);
        }
    }
}
