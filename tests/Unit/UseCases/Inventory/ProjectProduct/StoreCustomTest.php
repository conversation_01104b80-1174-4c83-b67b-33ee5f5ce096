<?php

namespace Tests\Unit\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Factories\Inventory\ProjectProductFactory;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectProductRepository;
use App\UseCases\Inventory\ProjectProduct\StoreCustom;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreCustomTest extends TestCase
{
    use RefreshDatabase;

    private StoreCustom $useCase;
    private ProjectProductRepository $mockRepository;
    private ProjectProductFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectProductRepository::class);
        $this->mockFactory = Mockery::mock(ProjectProductFactory::class);

        $this->useCase = new StoreCustom($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_custom_project_product_successfully()
    {
        $request = $this->createMockStoreCustomRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: null, // Custom products don't have product_id
            quantity: 10,
            value: 150.50,
            description: 'Test custom project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 1,
            project_id: 1,
            product_id: null,
            quantity: 10,
            value: 150.50,
            description: 'Test custom project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreCustomRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('storeCustom')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertNull($result->product_id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertEquals('Test custom project product', $result->description);
    }

    public function test_perform_with_different_quantities()
    {
        $request = $this->createMockStoreCustomRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: null,
            quantity: 50,
            value: 750.25,
            description: 'High quantity custom project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 2,
            project_id: 1,
            product_id: null,
            quantity: 50,
            value: 750.25,
            description: 'High quantity custom project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreCustomRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('storeCustom')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(50, $result->quantity);
        $this->assertEquals(750.25, $result->value);
    }

    public function test_perform_with_zero_quantity()
    {
        $request = $this->createMockStoreCustomRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: null,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity custom project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 3,
            project_id: 1,
            product_id: null,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity custom project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreCustomRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('storeCustom')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreCustomRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreCustomRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreCustomRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: null,
            quantity: 10,
            value: 150.50,
            description: 'Test custom project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreCustomRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('storeCustom')
            ->once()
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_decimal_values()
    {
        $request = $this->createMockStoreCustomRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: null,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value custom project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 5,
            project_id: 1,
            product_id: null,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value custom project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreCustomRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('storeCustom')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_with_long_description()
    {
        $request = $this->createMockStoreCustomRequest();
        $longDescription = str_repeat('Long description text. ', 20);
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: null,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $storedProjectProduct = new ProjectProduct(
            id: 6,
            project_id: 1,
            product_id: null,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreCustomRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('storeCustom')
            ->once()
            ->with($domainFromRequest)
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals($longDescription, $result->description);
    }

    public function test_perform_ensures_product_id_is_null()
    {
        $request = $this->createMockStoreCustomRequest();
        
        $domainFromRequest = new ProjectProduct(
            id: null,
            project_id: 1,
            product_id: null, // Should always be null for custom products
            quantity: 10,
            value: 150.50,
            description: 'Test custom project product'
        );

        $storedProjectProduct = new ProjectProduct(
            id: 1,
            project_id: 1,
            product_id: null,
            quantity: 10,
            value: 150.50,
            description: 'Test custom project product'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreCustomRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('storeCustom')
            ->once()
            ->with(Mockery::on(function ($domain) {
                // Verify that product_id is null for custom products
                return $domain->product_id === null;
            }))
            ->andReturn($storedProjectProduct);

        $result = $this->useCase->perform($request);

        $this->assertNull($result->product_id);
    }

    private function createMockStoreCustomRequest()
    {
        return new class extends \App\Http\Requests\ProjectProduct\StoreCustomRequest {
            public $project_id = 1;
            public $quantity = 10;
            public $value = 150.50;
            public $description = 'Test custom project product';
        };
    }
}
