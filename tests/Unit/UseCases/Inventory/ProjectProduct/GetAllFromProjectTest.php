<?php

namespace Tests\Unit\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectProductRepository;
use App\UseCases\Inventory\ProjectProduct\GetAllFromProject;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllFromProjectTest extends TestCase
{
    use RefreshDatabase;

    private GetAllFromProject $useCase;
    private ProjectProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectProductRepository::class);

        $this->useCase = new GetAllFromProject($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_project_products_from_specific_project()
    {
        $projectId = 1;
        $projectProducts = [
            new ProjectProduct(
                id: 1,
                project_id: $projectId,
                product_id: 1,
                quantity: 10,
                value: 150.50,
                description: 'First project product'
            ),
            new ProjectProduct(
                id: 2,
                project_id: $projectId,
                product_id: 2,
                quantity: 5,
                value: 75.25,
                description: 'Second project product'
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andReturn($projectProducts);

        $result = $this->useCase->perform($projectId);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);

        foreach ($result as $projectProduct) {
            $this->assertInstanceOf(ProjectProduct::class, $projectProduct);
            $this->assertEquals($projectId, $projectProduct->project_id);
        }
    }

    public function test_perform_with_empty_project()
    {
        $projectId = 1;

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andReturn([]);

        $result = $this->useCase->perform($projectId);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $projectId = 1;

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($projectId);
    }

    public function test_perform_with_custom_project_products()
    {
        $projectId = 1;
        $projectProducts = [
            new ProjectProduct(
                id: 1,
                project_id: $projectId,
                product_id: null, // Custom project product
                quantity: 15,
                value: 225.75,
                description: 'Custom project product'
            ),
            new ProjectProduct(
                id: 2,
                project_id: $projectId,
                product_id: null, // Another custom project product
                quantity: 25,
                value: 375.50,
                description: 'Another custom project product'
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andReturn($projectProducts);

        $result = $this->useCase->perform($projectId);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);

        foreach ($result as $projectProduct) {
            $this->assertInstanceOf(ProjectProduct::class, $projectProduct);
            $this->assertEquals($projectId, $projectProduct->project_id);
            $this->assertNull($projectProduct->product_id); // All are custom
        }
    }

    public function test_perform_with_mixed_regular_and_custom_products()
    {
        $projectId = 1;
        $projectProducts = [
            new ProjectProduct(
                id: 1,
                project_id: $projectId,
                product_id: 1, // Regular product
                quantity: 10,
                value: 150.50,
                description: 'Regular project product'
            ),
            new ProjectProduct(
                id: 2,
                project_id: $projectId,
                product_id: null, // Custom product
                quantity: 15,
                value: 225.75,
                description: 'Custom project product'
            ),
            new ProjectProduct(
                id: 3,
                project_id: $projectId,
                product_id: 2, // Another regular product
                quantity: 20,
                value: 300.00,
                description: 'Another regular project product'
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andReturn($projectProducts);

        $result = $this->useCase->perform($projectId);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);

        // Verify mixed regular and custom products
        $this->assertEquals(1, $result[0]->product_id); // Regular
        $this->assertNull($result[1]->product_id); // Custom
        $this->assertEquals(2, $result[2]->product_id); // Regular
    }

    public function test_perform_with_various_quantities_and_values()
    {
        $projectId = 1;
        $projectProducts = [
            new ProjectProduct(
                id: 1,
                project_id: $projectId,
                product_id: 1,
                quantity: 0,
                value: 0.0,
                description: 'Zero quantity project product'
            ),
            new ProjectProduct(
                id: 2,
                project_id: $projectId,
                product_id: null, // Custom
                quantity: 999999,
                value: 99999999.99,
                description: 'High quantity custom project product'
            ),
            new ProjectProduct(
                id: 3,
                project_id: $projectId,
                product_id: 3,
                quantity: 33,
                value: 123.456789,
                description: 'Decimal value project product'
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andReturn($projectProducts);

        $result = $this->useCase->perform($projectId);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);

        // Verify different quantities and values
        $this->assertEquals(0, $result[0]->quantity);
        $this->assertEquals(0.0, $result[0]->value);
        
        $this->assertEquals(999999, $result[1]->quantity);
        $this->assertEquals(99999999.99, $result[1]->value);
        
        $this->assertEquals(33, $result[2]->quantity);
        $this->assertEquals(123.456789, $result[2]->value);
    }

    public function test_perform_with_long_descriptions()
    {
        $projectId = 1;
        $longDescription = str_repeat('Long description text. ', 20);
        
        $projectProducts = [
            new ProjectProduct(
                id: 1,
                project_id: $projectId,
                product_id: 1,
                quantity: 1,
                value: 100.00,
                description: $longDescription
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andReturn($projectProducts);

        $result = $this->useCase->perform($projectId);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals($longDescription, $result[0]->description);
    }

    public function test_perform_with_different_project_ids()
    {
        $testCases = [1, 5, 10, 99];

        foreach ($testCases as $projectId) {
            $projectProducts = [
                new ProjectProduct(
                    id: 1,
                    project_id: $projectId,
                    product_id: 1,
                    quantity: 10,
                    value: 150.50,
                    description: "Project {$projectId} product"
                )
            ];

            $this->mockRepository
                ->shouldReceive('fetchFromProject')
                ->once()
                ->with($projectId)
                ->andReturn($projectProducts);

            $result = $this->useCase->perform($projectId);

            $this->assertIsArray($result);
            $this->assertCount(1, $result);
            $this->assertEquals($projectId, $result[0]->project_id);
        }
    }

    public function test_perform_with_large_number_of_products()
    {
        $projectId = 1;
        $projectProducts = [];

        // Create 50 project products
        for ($i = 1; $i <= 50; $i++) {
            $projectProducts[] = new ProjectProduct(
                id: $i,
                project_id: $projectId,
                product_id: $i % 2 === 0 ? null : $i, // Mix of regular and custom
                quantity: $i * 2,
                value: $i * 25.50,
                description: "Project product {$i}"
            );
        }

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andReturn($projectProducts);

        $result = $this->useCase->perform($projectId);

        $this->assertIsArray($result);
        $this->assertCount(50, $result);

        foreach ($result as $index => $projectProduct) {
            $this->assertInstanceOf(ProjectProduct::class, $projectProduct);
            $this->assertEquals($projectId, $projectProduct->project_id);
            $this->assertEquals(($index + 1) * 2, $projectProduct->quantity);
            $this->assertEquals(($index + 1) * 25.50, $projectProduct->value);
        }
    }

    public function test_perform_ensures_all_products_belong_to_project()
    {
        $projectId = 5;
        $projectProducts = [
            new ProjectProduct(
                id: 1,
                project_id: $projectId,
                product_id: 1,
                quantity: 10,
                value: 150.50,
                description: 'First project product'
            ),
            new ProjectProduct(
                id: 2,
                project_id: $projectId,
                product_id: null, // Custom
                quantity: 15,
                value: 225.75,
                description: 'Custom project product'
            ),
            new ProjectProduct(
                id: 3,
                project_id: $projectId,
                product_id: 3,
                quantity: 20,
                value: 300.00,
                description: 'Third project product'
            )
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromProject')
            ->once()
            ->with($projectId)
            ->andReturn($projectProducts);

        $result = $this->useCase->perform($projectId);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);

        // Ensure all products belong to the specified project
        foreach ($result as $projectProduct) {
            $this->assertEquals($projectId, $projectProduct->project_id);
        }
    }
}
