<?php

namespace Tests\Unit\UseCases\Inventory\ProjectProduct;

use App\Domains\Inventory\ProjectProduct;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectProductRepository;
use App\UseCases\Inventory\ProjectProduct\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private ProjectProductRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectProductRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_project_product_successfully()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            description: 'Test project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $result = $this->useCase->perform($projectProductId);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals($projectProductId, $result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertEquals('Test project product', $result->description);
    }

    public function test_perform_handles_repository_exception()
    {
        $projectProductId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($projectProductId);
    }

    public function test_perform_with_custom_project_product()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: null, // Custom project product
            quantity: 15,
            value: 225.75,
            description: 'Custom project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $result = $this->useCase->perform($projectProductId);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertNull($result->product_id);
        $this->assertEquals(15, $result->quantity);
        $this->assertEquals(225.75, $result->value);
        $this->assertEquals('Custom project product', $result->description);
    }

    public function test_perform_with_complete_project_product()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 2,
            product_id: 3,
            quantity: 200,
            value: 3000.75,
            description: 'Complete project product',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $result = $this->useCase->perform($projectProductId);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals($projectProductId, $result->id);
        $this->assertEquals(2, $result->project_id);
        $this->assertEquals(3, $result->product_id);
        $this->assertEquals(200, $result->quantity);
        $this->assertEquals(3000.75, $result->value);
        $this->assertEquals('Complete project product', $result->description);
    }

    public function test_perform_with_zero_quantity()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0,
            description: 'Zero quantity project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $result = $this->useCase->perform($projectProductId);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(0, $result->quantity);
        $this->assertEquals(0.0, $result->value);
    }

    public function test_perform_with_high_quantity()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99,
            description: 'High quantity project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $result = $this->useCase->perform($projectProductId);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(999999, $result->quantity);
        $this->assertEquals(99999999.99, $result->value);
    }

    public function test_perform_with_decimal_values()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 33,
            value: 123.456789,
            description: 'Decimal value project product'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $result = $this->useCase->perform($projectProductId);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals(33, $result->quantity);
        $this->assertEquals(123.456789, $result->value);
    }

    public function test_perform_with_long_description()
    {
        $projectProductId = 1;
        $longDescription = str_repeat('Long description text. ', 20);
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 1,
            product_id: 1,
            quantity: 1,
            value: 100.00,
            description: $longDescription
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $result = $this->useCase->perform($projectProductId);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals($longDescription, $result->description);
    }

    public function test_perform_returns_project_product_with_all_properties()
    {
        $projectProductId = 1;
        
        $projectProduct = new ProjectProduct(
            id: $projectProductId,
            project_id: 5,
            product_id: 3,
            quantity: 100,
            value: 1500.50,
            description: 'Full properties project product',
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectProductId)
            ->andReturn($projectProduct);

        $result = $this->useCase->perform($projectProductId);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertEquals($projectProductId, $result->id);
        $this->assertEquals(5, $result->project_id);
        $this->assertEquals(3, $result->product_id);
        $this->assertEquals(100, $result->quantity);
        $this->assertEquals(1500.50, $result->value);
        $this->assertEquals('Full properties project product', $result->description);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $index => $testCase) {
            $projectProductId = $index + 1;
            
            $projectProduct = new ProjectProduct(
                id: $projectProductId,
                project_id: 1,
                product_id: 1,
                quantity: $testCase['quantity'],
                value: $testCase['value'],
                description: 'Test project product'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($projectProductId)
                ->andReturn($projectProduct);

            $result = $this->useCase->perform($projectProductId);

            $this->assertInstanceOf(ProjectProduct::class, $result);
            $this->assertEquals($testCase['quantity'], $result->quantity);
            $this->assertEquals($testCase['value'], $result->value);
        }
    }

    public function test_perform_with_different_project_and_product_ids()
    {
        $testCases = [
            ['project_id' => 1, 'product_id' => 1],
            ['project_id' => 2, 'product_id' => 3],
            ['project_id' => 5, 'product_id' => null], // Custom
        ];

        foreach ($testCases as $index => $testCase) {
            $projectProductId = $index + 1;
            
            $projectProduct = new ProjectProduct(
                id: $projectProductId,
                project_id: $testCase['project_id'],
                product_id: $testCase['product_id'],
                quantity: 10,
                value: 150.50,
                description: 'Test project product'
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($projectProductId)
                ->andReturn($projectProduct);

            $result = $this->useCase->perform($projectProductId);

            $this->assertInstanceOf(ProjectProduct::class, $result);
            $this->assertEquals($testCase['project_id'], $result->project_id);
            $this->assertEquals($testCase['product_id'], $result->product_id);
        }
    }
}
