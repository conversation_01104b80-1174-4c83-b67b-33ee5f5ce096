<?php

namespace Tests\Unit\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Http\Requests\Client\StoreRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ClientRepository;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Services\ASAAS\UseCases\Deprecated\Clients\CreateCustomer;
use App\UseCases\Inventory\Client\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private ClientRepository $mockRepository;
    private ClientFactory $mockFactory;
    private CreateCustomer $mockCreateCustomer;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ClientRepository::class);
        $this->mockFactory = Mockery::mock(ClientFactory::class);
        $this->mockCreateCustomer = Mockery::mock(CreateCustomer::class);

        $this->useCase = new Store(
            $this->mockRepository,
            $this->mockFactory,
            $this->mockCreateCustomer
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_creates_client_successfully()
    {
        $request = $this->createMockStoreRequest();

        $domainFromRequest = new Client(
            id: null,
            organization_id: null,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $storedClient = new Client(
            id: 1,
            organization_id: $this->organization->id,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($client) {
                return $client instanceof Client &&
                       $client->organization_id === $this->organization->id;
            }))
            ->andReturn($storedClient);

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('John Doe', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_creates_client_with_asaas_integration()
    {
        $request = $this->createMockStoreRequest(true);

        $domainFromRequest = new Client(
            id: null,
            organization_id: null,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $storedClient = new Client(
            id: 1,
            organization_id: $this->organization->id,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedClient);

        $this->mockCreateCustomer
            ->shouldReceive('perform')
            ->once()
            ->with($storedClient);

        // Note: The actual implementation uses DBLog, not Log facade
        // So we don't need to mock Log::info anymore

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals(1, $result->id);
    }

    public function test_perform_handles_asaas_exception_gracefully()
    {
        $request = $this->createMockStoreRequest(true);

        $domainFromRequest = new Client(
            id: null,
            organization_id: null,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $storedClient = new Client(
            id: 1,
            organization_id: $this->organization->id,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andReturn($storedClient);

        $this->mockCreateCustomer
            ->shouldReceive('perform')
            ->once()
            ->with($storedClient)
            ->andThrow(new AsaasException('ASAAS API Error'));

        // Note: The actual implementation uses DBLog, not Log facade
        // So we don't need to mock Log::warning anymore

        // Should not throw exception, client creation should succeed
        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals(1, $result->id);
    }

    public function test_perform_rolls_back_on_repository_exception()
    {
        $request = $this->createMockStoreRequest();

        $domainFromRequest = new Client(
            id: null,
            organization_id: null,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    private function createMockStoreRequest(bool $enableAsaasIntegration = false): StoreRequest
    {
        $request = Mockery::mock(StoreRequest::class);

        $request->shouldReceive('input')
            ->with('enable_asaas_integration', false)
            ->andReturn($enableAsaasIntegration);

        return $request;
    }
}
