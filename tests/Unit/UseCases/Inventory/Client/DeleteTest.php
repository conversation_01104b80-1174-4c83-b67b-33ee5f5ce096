<?php

namespace Tests\Unit\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ClientRepository;
use App\UseCases\Inventory\Client\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private ClientRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ClientRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_client_successfully()
    {
        $clientId = 1;
        
        $client = new Client(
            id: $clientId,
            organization_id: $this->organization->id,
            name: 'Test Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andReturn($client);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($client)
            ->andReturn(true);

        $result = $this->useCase->perform($clientId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_client_belongs_to_different_organization()
    {
        $clientId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $client = new Client(
            id: $clientId,
            organization_id: $otherOrganization->id, // Different organization
            name: 'Test Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andReturn($client);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This client don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($clientId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $clientId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($clientId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $clientId = 1;
        
        $client = new Client(
            id: $clientId,
            organization_id: $this->organization->id,
            name: 'Test Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andReturn($client);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($client)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($clientId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $clientId = 1;
        
        $client = new Client(
            id: $clientId,
            organization_id: $this->organization->id,
            name: 'Test Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andReturn($client);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($client)
            ->andReturn(false);

        $result = $this->useCase->perform($clientId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $clientId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $client = new Client(
            id: $clientId,
            organization_id: $this->organization->id, // Client belongs to original org
            name: 'Test Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andReturn($client);

        // Should throw exception because client belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This client don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($clientId);
    }
}
