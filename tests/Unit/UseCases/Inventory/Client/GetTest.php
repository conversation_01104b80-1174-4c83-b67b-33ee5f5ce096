<?php

namespace Tests\Unit\UseCases\Inventory\Client;

use App\Domains\Inventory\Client;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ClientRepository;
use App\UseCases\Inventory\Client\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private ClientRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ClientRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_client_successfully()
    {
        $clientId = 1;
        
        $client = new Client(
            id: $clientId,
            organization_id: $this->organization->id,
            name: 'Test Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andReturn($client);

        $result = $this->useCase->perform($clientId);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals($clientId, $result->id);
        $this->assertEquals('Test Client', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $clientId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($clientId);
    }

    public function test_perform_with_different_client_types()
    {
        // Test individual client
        $individualClient = new Client(
            id: 1,
            organization_id: $this->organization->id,
            name: 'John Doe',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Individual client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(1)
            ->andReturn($individualClient);

        $result = $this->useCase->perform(1);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertNotNull($result->cpf);
        $this->assertNull($result->cnpj);
        $this->assertEquals('single', $result->civil_state);

        // Test company client
        $companyClient = new Client(
            id: 2,
            organization_id: $this->organization->id,
            name: 'ACME Corp',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: '12345678000195',
            service: 'Technology Services',
            address: '123 Business Ave',
            number: '456',
            neighborhood: 'Business District',
            cep: '12345-678',
            complement: 'Suite 100',
            civil_state: null,
            description: 'Company client'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with(2)
            ->andReturn($companyClient);

        $result = $this->useCase->perform(2);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertNull($result->cpf);
        $this->assertNotNull($result->cnpj);
        $this->assertNull($result->civil_state);
        $this->assertNull($result->profession);
        $this->assertNull($result->birthdate);
    }

    public function test_perform_returns_client_with_all_properties()
    {
        $clientId = 1;
        
        $client = new Client(
            id: $clientId,
            organization_id: $this->organization->id,
            name: 'Complete Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Software Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Full Stack Development',
            address: '123 Complete St',
            number: '456',
            neighborhood: 'Tech District',
            cep: '12345-678',
            complement: 'Floor 10',
            civil_state: 'married',
            description: 'Complete client with all fields'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($clientId)
            ->andReturn($client);

        $result = $this->useCase->perform($clientId);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals($clientId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Complete Client', $result->name);
        $this->assertEquals('+1234567890', $result->phone);
        $this->assertEquals('<EMAIL>', $result->email);
        $this->assertEquals('Software Engineer', $result->profession);
        $this->assertEquals('1990-01-01', $result->birthdate);
        $this->assertEquals('12345678901', $result->cpf);
        $this->assertNull($result->cnpj);
        $this->assertEquals('Full Stack Development', $result->service);
        $this->assertEquals('123 Complete St', $result->address);
        $this->assertEquals('456', $result->number);
        $this->assertEquals('Tech District', $result->neighborhood);
        $this->assertEquals('12345-678', $result->cep);
        $this->assertEquals('Floor 10', $result->complement);
        $this->assertEquals('married', $result->civil_state);
        $this->assertEquals('Complete client with all fields', $result->description);
    }
}
