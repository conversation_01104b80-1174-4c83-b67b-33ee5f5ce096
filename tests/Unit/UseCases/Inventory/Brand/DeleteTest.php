<?php

namespace Tests\Unit\UseCases\Inventory\Brand;

use App\Domains\Inventory\Brand;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BrandRepository;
use App\UseCases\Inventory\Brand\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private BrandRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BrandRepository::class);

        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_brand_successfully()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Delete Brand',
            description: 'Brand to be deleted'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($brand)
            ->andReturn(true);

        $result = $this->useCase->perform($brandId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_brand_belongs_to_different_organization()
    {
        $brandId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $otherOrganization->id, // Different organization
            name: 'Other Brand',
            description: 'Brand from other organization'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $this->mockRepository
            ->shouldNotReceive('delete');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This brand don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($brandId);
    }

    public function test_perform_handles_repository_fetch_exception()
    {
        $brandId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($brandId);
    }

    public function test_perform_handles_repository_delete_exception()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Error Brand',
            description: 'Brand that will cause delete error'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($brand)
            ->andThrow(new Exception('Database error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($brandId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Fail Brand',
            description: 'Brand that will fail to delete'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($brand)
            ->andReturn(false);

        $result = $this->useCase->perform($brandId);

        $this->assertFalse($result);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $brandId = 1;
        
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id, // Brand belongs to original org
            name: 'Auth Test Brand',
            description: 'Brand for auth test'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        // Should throw exception because brand belongs to different org than authenticated user
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This brand don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($brandId);
    }

    public function test_perform_with_brand_with_special_characters()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Brand & Co. (™)',
            description: 'Description with special chars: @#$%^&*()'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($brand)
            ->andReturn(true);

        $result = $this->useCase->perform($brandId);

        $this->assertTrue($result);
    }

    public function test_perform_with_brand_with_unicode_characters()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'Bränd Ñamé 中文',
            description: 'Descripción con caracteres especiales: ñáéíóú'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($brand)
            ->andReturn(true);

        $result = $this->useCase->perform($brandId);

        $this->assertTrue($result);
    }

    public function test_perform_with_brand_with_empty_description()
    {
        $brandId = 1;
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: 'No Description Brand',
            description: ''
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($brand)
            ->andReturn(true);

        $result = $this->useCase->perform($brandId);

        $this->assertTrue($result);
    }

    public function test_perform_with_brand_with_long_name()
    {
        $brandId = 1;
        $longName = str_repeat('A', 255);
        
        $brand = new Brand(
            id: $brandId,
            organization_id: $this->organization->id,
            name: $longName,
            description: 'Brand with long name'
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($brandId)
            ->andReturn($brand);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($brand)
            ->andReturn(true);

        $result = $this->useCase->perform($brandId);

        $this->assertTrue($result);
    }
}
