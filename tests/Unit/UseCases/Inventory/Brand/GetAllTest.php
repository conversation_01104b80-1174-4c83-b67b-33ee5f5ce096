<?php

namespace Tests\Unit\UseCases\Inventory\Brand;

use App\Domains\Filters\BrandFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Brand;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\BrandRepository;
use App\UseCases\Inventory\Brand\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private BrandRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(BrandRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_brands()
    {
        $request = new Request([
            'name' => 'Test',
            'order' => 'name',
            'by' => 'asc',
            'limit' => 10
        ]);

        $brands = [
            new Brand(
                id: 1,
                organization_id: $this->organization->id,
                name: 'Test Brand 1',
                description: 'Test brand 1 description'
            ),
            new Brand(
                id: 2,
                organization_id: $this->organization->id,
                name: 'Test Brand 2',
                description: 'Test brand 2 description'
            )
        ];

        $expectedResult = [
            'data' => $brands,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BrandFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'name' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 10;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $brand) {
            $this->assertInstanceOf(Brand::class, $brand);
            $this->assertEquals($this->organization->id, $brand->organization_id);
        }
    }

    public function test_perform_with_default_parameters()
    {
        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BrandFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'created_at' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 30;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
    }

    public function test_perform_with_filters()
    {
        $request = new Request([
            'name' => 'Alpha',
            'description' => 'Test'
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) use ($request) {
                    return $filters instanceof BrandFilters &&
                           isset($filters->filters['name']) &&
                           isset($filters->filters['description']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_custom_ordering()
    {
        $request = new Request([
            'order' => 'description',
            'by' => 'desc',
            'limit' => 50
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'description' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 50;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = new Request();

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_pagination()
    {
        $request = new Request([
            'limit' => 5
        ]);

        $brands = [];
        for ($i = 1; $i <= 5; $i++) {
            $brands[] = new Brand(
                id: $i,
                organization_id: $this->organization->id,
                name: "Brand $i",
                description: "Brand $i description"
            );
        }

        $expectedResult = [
            'data' => $brands,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy->limit === 5;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $differentOrg->id, // Should use the different organization
                Mockery::any(),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $this->useCase->perform($request);
    }

    public function test_perform_with_name_filter()
    {
        $request = new Request([
            'name' => 'Alpha'
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BrandFilters &&
                           isset($filters->filters['name']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_description_filter()
    {
        $request = new Request([
            'description' => 'Test'
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BrandFilters &&
                           isset($filters->filters['description']);
                }),
                Mockery::any()
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_multiple_filters()
    {
        $request = new Request([
            'name' => 'Alpha',
            'description' => 'Test',
            'order' => 'name',
            'by' => 'asc',
            'limit' => 20
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof BrandFilters &&
                           isset($filters->filters['name']) &&
                           isset($filters->filters['description']);
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'name' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 20;
                })
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }
}
