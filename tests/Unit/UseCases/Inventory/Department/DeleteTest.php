<?php

namespace Tests\Unit\UseCases\Inventory\Department;

use App\Domains\Inventory\Department;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentRepository;
use App\UseCases\Inventory\Department\Delete;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class DeleteTest extends TestCase
{
    use RefreshDatabase;

    private Delete $useCase;
    private DepartmentRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(DepartmentRepository::class);
        $this->useCase = new Delete($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_deletes_department_successfully()
    {
        $departmentId = 1;
        $department = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Department to Delete',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($department)
            ->andReturn(true);

        $result = $this->useCase->perform($departmentId);

        $this->assertTrue($result);
    }

    public function test_perform_throws_exception_when_department_belongs_to_different_organization()
    {
        $departmentId = 1;
        $otherOrganization = Organization::factory()->create();
        
        $department = new Department(
            id: $departmentId,
            organization_id: $otherOrganization->id,
            name: 'Other Org Department',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($departmentId);
    }

    public function test_perform_handles_fetch_exception()
    {
        $departmentId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andThrow(new Exception('Department not found'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Department not found');

        $this->useCase->perform($departmentId);
    }

    public function test_perform_handles_delete_exception()
    {
        $departmentId = 1;
        $department = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Department with Delete Error',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($department)
            ->andThrow(new Exception('Delete operation failed'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Delete operation failed');

        $this->useCase->perform($departmentId);
    }

    public function test_perform_returns_false_when_delete_fails()
    {
        $departmentId = 2;
        $department = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Department Delete Fail',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($department)
            ->andReturn(false);

        $result = $this->useCase->perform($departmentId);

        $this->assertFalse($result);
    }

    public function test_perform_validates_organization_ownership_before_delete()
    {
        $departmentId = 3;
        
        // Create a department that belongs to the user's organization
        $validDepartment = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Valid Department for Delete',
            is_active: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($validDepartment);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($validDepartment)
            ->andReturn(true);

        $result = $this->useCase->perform($departmentId);

        $this->assertTrue($result);
    }

    public function test_perform_with_null_organization_id_throws_exception()
    {
        $departmentId = 4;
        $department = new Department(
            id: $departmentId,
            organization_id: null,
            name: 'No Org Department',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage("This department don't belong to this organization.");
        $this->expectExceptionCode(403);

        $this->useCase->perform($departmentId);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $departmentId = 5;
        
        // Change the user's organization to test that it uses the authenticated user's org
        $newOrganization = Organization::factory()->create();
        $this->user->organization_id = $newOrganization->id;
        $this->user->save();

        $department = new Department(
            id: $departmentId,
            organization_id: $newOrganization->id,
            name: 'New Org Department',
            is_active: true
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($department)
            ->andReturn(true);

        $result = $this->useCase->perform($departmentId);

        $this->assertTrue($result);
    }

    public function test_perform_with_inactive_department()
    {
        $departmentId = 6;
        $department = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Inactive Department',
            is_active: false
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andReturn($department);

        $this->mockRepository
            ->shouldReceive('delete')
            ->once()
            ->with($department)
            ->andReturn(true);

        $result = $this->useCase->perform($departmentId);

        $this->assertTrue($result);
    }

    public function test_perform_with_zero_id()
    {
        $departmentId = 0;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andThrow(new Exception('Invalid department ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid department ID');

        $this->useCase->perform($departmentId);
    }

    public function test_perform_with_negative_id()
    {
        $departmentId = -1;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($departmentId)
            ->andThrow(new Exception('Invalid department ID'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid department ID');

        $this->useCase->perform($departmentId);
    }
}
