<?php

namespace Tests\Unit\UseCases\Inventory\Department;

use App\Domains\Filters\DepartmentFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Department;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentRepository;
use App\UseCases\Inventory\Department\GetAll;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private DepartmentRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(DepartmentRepository::class);
        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_departments_from_organization()
    {
        $filters = new DepartmentFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $expectedResult = [
            'data' => [
                new Department(1, $this->organization->id, 'Department 1', true),
                new Department(2, $this->organization->id, 'Department 2', false),
            ],
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $department) {
            $this->assertInstanceOf(Department::class, $department);
            $this->assertEquals($this->organization->id, $department->organization_id);
        }
    }

    public function test_perform_with_filters()
    {
        $filters = new DepartmentFilters(['name' => 'Active']);
        $orderBy = new OrderBy(['order' => 'name', 'by' => 'asc', 'limit' => 5]);

        $expectedResult = [
            'data' => [
                new Department(1, $this->organization->id, 'Active Department', true),
            ],
            'count' => 1,
            'total' => 1,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Active Department', $result['data'][0]->name);
    }

    public function test_perform_with_is_active_filter()
    {
        $filters = new DepartmentFilters(['is_active' => true]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 20]);

        $expectedResult = [
            'data' => [
                new Department(1, $this->organization->id, 'Active Dept 1', true),
                new Department(3, $this->organization->id, 'Active Dept 2', true),
            ],
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        foreach ($result['data'] as $department) {
            $this->assertTrue($department->is_active);
        }
    }

    public function test_perform_with_pagination()
    {
        $filters = new DepartmentFilters([]);
        $orderBy = new OrderBy(['order' => 'id', 'by' => 'asc', 'limit' => 2]);

        $expectedResult = [
            'data' => [
                new Department(1, $this->organization->id, 'Department 1', true),
                new Department(2, $this->organization->id, 'Department 2', true),
            ],
            'count' => 2,
            'total' => 5,
            'currentPage' => 1,
            'lastPage' => 3,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(5, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_with_empty_result()
    {
        $filters = new DepartmentFilters(['name' => 'NonExistent']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($filters, $orderBy);

        $this->assertEmpty($result['data']);
        $this->assertEquals(0, $result['count']);
        $this->assertEquals(0, $result['total']);
    }

    public function test_perform_handles_repository_exception()
    {
        $filters = new DepartmentFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, $filters, $orderBy)
            ->andThrow(new Exception('Repository error'));

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($filters, $orderBy);
    }

    public function test_perform_uses_authenticated_user_organization_id()
    {
        $filters = new DepartmentFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1,
        ];

        // Verify that the correct organization ID is passed
        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with($this->organization->id, Mockery::any(), Mockery::any())
            ->andReturn($expectedResult);

        $this->useCase->perform($filters, $orderBy);
    }

    public function test_perform_with_different_order_by_configurations()
    {
        $filters = new DepartmentFilters([]);
        
        // Test different ordering configurations
        $orderConfigs = [
            ['order' => 'name', 'by' => 'asc', 'limit' => 15],
            ['order' => 'is_active', 'by' => 'desc', 'limit' => 25],
            ['order' => 'updated_at', 'by' => 'asc', 'limit' => 50],
        ];

        foreach ($orderConfigs as $config) {
            $orderBy = new OrderBy($config);
            
            $expectedResult = [
                'data' => [],
                'count' => 0,
                'total' => 0,
                'currentPage' => 1,
                'lastPage' => 1,
            ];

            $this->mockRepository
                ->shouldReceive('fetchFromOrganization')
                ->once()
                ->with($this->organization->id, $filters, $orderBy)
                ->andReturn($expectedResult);

            $result = $this->useCase->perform($filters, $orderBy);
            $this->assertIsArray($result);
        }
    }
}
