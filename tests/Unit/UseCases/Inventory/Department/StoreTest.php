<?php

namespace Tests\Unit\UseCases\Inventory\Department;

use App\Domains\Inventory\Department;
use App\Factories\Inventory\DepartmentFactory;
use App\Http\Requests\Department\StoreRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentRepository;
use App\UseCases\Inventory\Department\Store;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class StoreTest extends TestCase
{
    use RefreshDatabase;

    private Store $useCase;
    private DepartmentRepository $mockRepository;
    private DepartmentFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(DepartmentRepository::class);
        $this->mockFactory = Mockery::mock(DepartmentFactory::class);

        $this->useCase = new Store($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_stores_department_successfully()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'Store Department',
            is_active: true
        );

        $storedDepartment = new Department(
            id: 1,
            organization_id: $this->organization->id,
            name: 'Store Department',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($department) {
                return $department instanceof Department &&
                       $department->organization_id === $this->organization->id &&
                       $department->name === 'Store Department';
            }))
            ->andReturn($storedDepartment);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertInstanceOf(Department::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Store Department', $result->name);
        $this->assertTrue($result->is_active);
    }

    public function test_perform_assigns_organization_id_from_authenticated_user()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'Organization Department',
            is_active: true
        );

        $storedDepartment = new Department(
            id: 2,
            organization_id: $this->organization->id,
            name: 'Organization Department',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($department) {
                return $department->organization_id === $this->organization->id;
            }))
            ->andReturn($storedDepartment);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $request = $this->createMockStoreRequest();

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = $this->createMockStoreRequest();
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'Exception Department',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->andThrow(new Exception('Repository error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_inactive_department()
    {
        $request = $this->createMockStoreRequest(['is_active' => false]);
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'Inactive Department',
            is_active: false
        );

        $storedDepartment = new Department(
            id: 3,
            organization_id: $this->organization->id,
            name: 'Inactive Department',
            is_active: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromStoreRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('store')
            ->once()
            ->with(Mockery::on(function ($department) {
                return $department->is_active === false;
            }))
            ->andReturn($storedDepartment);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request);

        $this->assertFalse($result->is_active);
    }

    private function createMockStoreRequest(array $data = []): StoreRequest
    {
        $defaultData = [
            'name' => 'Test Department',
            'is_active' => true,
        ];

        $requestData = array_merge($defaultData, $data);

        $request = Mockery::mock(StoreRequest::class);
        foreach ($requestData as $key => $value) {
            $request->$key = $value;
        }

        return $request;
    }
}
