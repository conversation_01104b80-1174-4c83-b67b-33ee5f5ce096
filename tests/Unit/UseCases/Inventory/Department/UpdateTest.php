<?php

namespace Tests\Unit\UseCases\Inventory\Department;

use App\Domains\Inventory\Department;
use App\Factories\Inventory\DepartmentFactory;
use App\Http\Requests\Department\UpdateRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\DepartmentRepository;
use App\UseCases\Inventory\Department\Update;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private DepartmentRepository $mockRepository;
    private DepartmentFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(DepartmentRepository::class);
        $this->mockFactory = Mockery::mock(DepartmentFactory::class);

        $this->useCase = new Update($this->mockRepository, $this->mockFactory);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_department_successfully()
    {
        $departmentId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'Updated Department',
            is_active: false
        );

        $updatedDepartment = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Updated Department',
            is_active: false
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($department) use ($departmentId) {
                    return $department instanceof Department &&
                           $department->id === $departmentId &&
                           $department->name === 'Updated Department';
                }),
                $this->organization->id
            )
            ->andReturn($updatedDepartment);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $departmentId);

        $this->assertInstanceOf(Department::class, $result);
        $this->assertEquals($departmentId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Updated Department', $result->name);
        $this->assertFalse($result->is_active);
    }

    public function test_perform_assigns_id_to_domain()
    {
        $departmentId = 5;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'ID Test Department',
            is_active: true
        );

        $updatedDepartment = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'ID Test Department',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($department) use ($departmentId) {
                    return $department->id === $departmentId;
                }),
                $this->organization->id
            )
            ->andReturn($updatedDepartment);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $departmentId);

        $this->assertEquals($departmentId, $result->id);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $departmentId = 3;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'Org Test Department',
            is_active: true
        );

        $updatedDepartment = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Org Test Department',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $this->organization->id
            )
            ->andReturn($updatedDepartment);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $departmentId);

        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_factory_exception()
    {
        $departmentId = 1;
        $request = $this->createMockUpdateRequest();

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andThrow(new Exception('Factory error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $departmentId);
    }

    public function test_perform_handles_repository_exception()
    {
        $departmentId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'Exception Department',
            is_active: true
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new Exception('Repository error'));

        DB::shouldReceive('beginTransaction')->once();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Repository error');

        $this->useCase->perform($request, $departmentId);
    }

    public function test_perform_with_partial_update_data()
    {
        $departmentId = 2;
        $request = $this->createMockUpdateRequest(['name' => 'Partial Update']);
        
        $domainFromRequest = new Department(
            id: null,
            organization_id: null,
            name: 'Partial Update',
            is_active: null
        );

        $updatedDepartment = new Department(
            id: $departmentId,
            organization_id: $this->organization->id,
            name: 'Partial Update',
            is_active: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($department) {
                    return $department->name === 'Partial Update' &&
                           $department->is_active === null;
                }),
                $this->organization->id
            )
            ->andReturn($updatedDepartment);

        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $result = $this->useCase->perform($request, $departmentId);

        $this->assertEquals('Partial Update', $result->name);
        $this->assertNull($result->is_active);
    }

    private function createMockUpdateRequest(array $data = []): UpdateRequest
    {
        $defaultData = [
            'name' => 'Updated Department',
            'is_active' => false,
        ];

        $requestData = array_merge($defaultData, $data);

        $request = Mockery::mock(UpdateRequest::class);
        foreach ($requestData as $key => $value) {
            $request->$key = $value;
        }

        return $request;
    }
}
