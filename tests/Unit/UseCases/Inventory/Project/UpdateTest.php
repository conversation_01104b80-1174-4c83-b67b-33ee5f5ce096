<?php

namespace Tests\Unit\UseCases\Inventory\Project;

use App\Domains\Inventory\Project;
use App\Factories\Inventory\ProjectFactory;
use App\Http\Requests\Project\UpdateRequest;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectRepository;
use App\UseCases\Inventory\Project\Update;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class UpdateTest extends TestCase
{
    use RefreshDatabase;

    private Update $useCase;
    private ProjectRepository $mockRepository;
    private ProjectFactory $mockFactory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectRepository::class);
        $this->mockFactory = Mockery::mock(ProjectFactory::class);

        $this->useCase = new Update(
            $this->mockRepository,
            $this->mockFactory
        );

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_updates_project_successfully()
    {
        $projectId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $updatedProject = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->with($request)
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($project) use ($projectId) {
                    return $project instanceof Project && 
                           $project->id === $projectId &&
                           $project->name === 'Updated Project';
                }),
                $this->organization->id
            )
            ->andReturn($updatedProject);

        $result = $this->useCase->perform($request, $projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals($projectId, $result->id);
        $this->assertEquals('Updated Project', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_sets_correct_project_id()
    {
        $projectId = 123;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $updatedProject = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::on(function ($project) use ($projectId) {
                    return $project->id === $projectId;
                }),
                $this->organization->id
            )
            ->andReturn($updatedProject);

        $result = $this->useCase->perform($request, $projectId);

        $this->assertEquals($projectId, $result->id);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        $projectId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $updatedProject = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $this->organization->id  // Should use authenticated user's organization
            )
            ->andReturn($updatedProject);

        $this->useCase->perform($request, $projectId);
    }

    public function test_perform_with_null_values()
    {
        $projectId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: null,
            budget_id: null,
            name: 'Updated Project',
            description: null,
            value: null,
            cost: null
        );

        $updatedProject = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: null,
            budget_id: null,
            name: 'Updated Project',
            description: null,
            value: null,
            cost: null
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedProject);

        $result = $this->useCase->perform($request, $projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals('Updated Project', $result->name);
        $this->assertNull($result->client_id);
        $this->assertNull($result->budget_id);
        $this->assertNull($result->value);
        $this->assertNull($result->cost);
    }

    public function test_perform_with_zero_values()
    {
        $projectId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 1,
            budget_id: 1,
            name: 'Zero Value Project',
            description: 'Updated with zero values',
            value: 0.0,
            cost: 0.0
        );

        $updatedProject = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Zero Value Project',
            description: 'Updated with zero values',
            value: 0.0,
            cost: 0.0
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andReturn($updatedProject);

        $result = $this->useCase->perform($request, $projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals(0.0, $result->value);
        $this->assertEquals(0.0, $result->cost);
    }

    public function test_perform_handles_repository_exception()
    {
        $projectId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request, $projectId);
    }

    public function test_perform_handles_factory_exception()
    {
        $projectId = 1;
        $request = $this->createMockUpdateRequest();

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andThrow(new \Exception('Factory error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Factory error');

        $this->useCase->perform($request, $projectId);
    }

    public function test_perform_with_different_organization_user()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $projectId = 1;
        $request = $this->createMockUpdateRequest();
        
        $domainFromRequest = new Project(
            id: null,
            organization_id: null,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $updatedProject = new Project(
            id: $projectId,
            organization_id: $differentOrg->id,
            client_id: 2,
            budget_id: 2,
            name: 'Updated Project',
            description: 'Updated project description',
            value: 15000.75,
            cost: 12000.50
        );

        $this->mockFactory
            ->shouldReceive('buildFromUpdateRequest')
            ->once()
            ->andReturn($domainFromRequest);

        $this->mockRepository
            ->shouldReceive('update')
            ->once()
            ->with(
                Mockery::any(),
                $differentOrg->id  // Should use the different organization
            )
            ->andReturn($updatedProject);

        $result = $this->useCase->perform($request, $projectId);

        $this->assertEquals($differentOrg->id, $result->organization_id);
    }

    private function createMockUpdateRequest(): UpdateRequest
    {
        return Mockery::mock(UpdateRequest::class);
    }
}
