<?php

namespace Tests\Unit\UseCases\Inventory\Project;

use App\Domains\Inventory\Project;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectRepository;
use App\UseCases\Inventory\Project\Get;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class GetTest extends TestCase
{
    use RefreshDatabase;

    private Get $useCase;
    private ProjectRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectRepository::class);

        $this->useCase = new Get($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_project_successfully()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 10000.50,
            cost: 7500.25
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $result = $this->useCase->perform($projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals($projectId, $result->id);
        $this->assertEquals('Test Project', $result->name);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_perform_handles_repository_exception()
    {
        $projectId = 999;

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andThrow(new \Illuminate\Database\Eloquent\ModelNotFoundException());

        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->useCase->perform($projectId);
    }

    public function test_perform_with_minimal_project()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: null,
            budget_id: null,
            name: 'Minimal Project',
            description: null,
            value: null,
            cost: null
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $result = $this->useCase->perform($projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals('Minimal Project', $result->name);
        $this->assertNull($result->client_id);
        $this->assertNull($result->budget_id);
        $this->assertNull($result->value);
        $this->assertNull($result->cost);
    }

    public function test_perform_with_complete_project()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 5,
            budget_id: 10,
            name: 'Complete Project',
            description: 'Complete project with all fields',
            value: 25000.99,
            cost: 18750.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $result = $this->useCase->perform($projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals($projectId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(5, $result->client_id);
        $this->assertEquals(10, $result->budget_id);
        $this->assertEquals('Complete Project', $result->name);
        $this->assertEquals('Complete project with all fields', $result->description);
        $this->assertEquals(25000.99, $result->value);
        $this->assertEquals(18750.50, $result->cost);
    }

    public function test_perform_with_zero_value_project()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'Zero Value Project',
            description: 'Project with zero values',
            value: 0.0,
            cost: 0.0
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $result = $this->useCase->perform($projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals(0.0, $result->value);
        $this->assertEquals(0.0, $result->cost);
    }

    public function test_perform_with_high_value_project()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 1,
            budget_id: 1,
            name: 'High Value Project',
            description: 'Project with high values',
            value: 1000000.99,
            cost: 750000.50
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $result = $this->useCase->perform($projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals(1000000.99, $result->value);
        $this->assertEquals(750000.50, $result->cost);
    }

    public function test_perform_returns_project_with_all_properties()
    {
        $projectId = 1;
        
        $project = new Project(
            id: $projectId,
            organization_id: $this->organization->id,
            client_id: 3,
            budget_id: 7,
            name: 'Full Property Project',
            description: 'Project with all properties set',
            value: 50000.00,
            cost: 37500.00,
            created_at: now()->subDays(5),
            updated_at: now()->subDays(2)
        );

        $this->mockRepository
            ->shouldReceive('fetchById')
            ->once()
            ->with($projectId)
            ->andReturn($project);

        $result = $this->useCase->perform($projectId);

        $this->assertInstanceOf(Project::class, $result);
        $this->assertEquals($projectId, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals(3, $result->client_id);
        $this->assertEquals(7, $result->budget_id);
        $this->assertEquals('Full Property Project', $result->name);
        $this->assertEquals('Project with all properties set', $result->description);
        $this->assertEquals(50000.00, $result->value);
        $this->assertEquals(37500.00, $result->cost);
        $this->assertNotNull($result->created_at);
        $this->assertNotNull($result->updated_at);
    }

    public function test_perform_with_different_project_ids()
    {
        // Test with different project IDs to ensure correct ID handling
        $projectIds = [1, 42, 999, 12345];

        foreach ($projectIds as $projectId) {
            $project = new Project(
                id: $projectId,
                organization_id: $this->organization->id,
                client_id: 1,
                budget_id: 1,
                name: "Project $projectId",
                description: "Test project $projectId",
                value: $projectId * 100.0,
                cost: $projectId * 75.0
            );

            $this->mockRepository
                ->shouldReceive('fetchById')
                ->once()
                ->with($projectId)
                ->andReturn($project);

            $result = $this->useCase->perform($projectId);

            $this->assertInstanceOf(Project::class, $result);
            $this->assertEquals($projectId, $result->id);
            $this->assertEquals("Project $projectId", $result->name);
            $this->assertEquals($projectId * 100.0, $result->value);
            $this->assertEquals($projectId * 75.0, $result->cost);
        }
    }
}
