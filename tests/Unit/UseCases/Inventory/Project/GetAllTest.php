<?php

namespace Tests\Unit\UseCases\Inventory\Project;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProjectFilters;
use App\Domains\Inventory\Project;
use App\Models\Organization;
use App\Models\User;
use App\Repositories\ProjectRepository;
use App\UseCases\Inventory\Project\GetAll;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use Tests\TestCase;

class GetAllTest extends TestCase
{
    use RefreshDatabase;

    private GetAll $useCase;
    private ProjectRepository $mockRepository;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->mockRepository = Mockery::mock(ProjectRepository::class);

        $this->useCase = new GetAll($this->mockRepository);

        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_returns_paginated_projects()
    {
        $request = new Request([
            'name' => 'Test',
            'order' => 'name',
            'by' => 'asc',
            'limit' => 10
        ]);

        $projects = [
            new Project(
                id: 1,
                organization_id: $this->organization->id,
                client_id: 1,
                budget_id: 1,
                name: 'Test Project 1',
                description: 'Test project 1 description',
                value: 10000.50,
                cost: 7500.25
            ),
            new Project(
                id: 2,
                organization_id: $this->organization->id,
                client_id: 2,
                budget_id: 2,
                name: 'Test Project 2',
                description: 'Test project 2 description',
                value: 15000.75,
                cost: 12000.50
            )
        ];

        $expectedResult = [
            'data' => $projects,
            'count' => 2,
            'total' => 2,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof ProjectFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'name' &&
                           $orderBy->by === 'asc' &&
                           $orderBy->limit === 10;
                }),
                false, // with_client
                false, // with_budget
                false, // with_products
                false  // with_custom_products
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);

        foreach ($result['data'] as $project) {
            $this->assertInstanceOf(Project::class, $project);
            $this->assertEquals($this->organization->id, $project->organization_id);
        }
    }

    public function test_perform_with_default_parameters()
    {
        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) {
                    return $filters instanceof ProjectFilters;
                }),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'created_at' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 30;
                }),
                false, false, false, false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertEquals(0, $result['count']);
    }

    public function test_perform_with_filters()
    {
        $request = new Request([
            'name' => 'Alpha Project',
            'client_id' => 5,
            'budget_id' => 10,
            'value_min' => 1000.00,
            'value_max' => 50000.00
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::on(function ($filters) use ($request) {
                    return $filters instanceof ProjectFilters &&
                           isset($filters->filters['name']) &&
                           isset($filters->filters['client_id']) &&
                           isset($filters->filters['budget_id']) &&
                           isset($filters->filters['value_min']) &&
                           isset($filters->filters['value_max']);
                }),
                Mockery::any(),
                false, false, false, false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_custom_ordering()
    {
        $request = new Request([
            'order' => 'value',
            'by' => 'desc',
            'limit' => 50
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy instanceof OrderBy &&
                           $orderBy->order === 'value' &&
                           $orderBy->by === 'desc' &&
                           $orderBy->limit === 50;
                }),
                false, false, false, false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_with_relationships()
    {
        $request = new Request([
            'with_client' => true,
            'with_budget' => true,
            'with_products' => true,
            'with_custom_products' => true
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::any(),
                true,  // with_client
                true,  // with_budget
                true,  // with_products
                true   // with_custom_products
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }

    public function test_perform_handles_repository_exception()
    {
        $request = new Request();

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    public function test_perform_with_pagination()
    {
        $request = new Request([
            'limit' => 5
        ]);

        $projects = [];
        for ($i = 1; $i <= 5; $i++) {
            $projects[] = new Project(
                id: $i,
                organization_id: $this->organization->id,
                client_id: $i,
                budget_id: $i,
                name: "Project $i",
                description: "Project $i description",
                value: $i * 1000.0,
                cost: $i * 750.0
            );
        }

        $expectedResult = [
            'data' => $projects,
            'count' => 5,
            'total' => 15,
            'currentPage' => 1,
            'lastPage' => 3
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::on(function ($orderBy) {
                    return $orderBy->limit === 5;
                }),
                false, false, false, false
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_perform_uses_authenticated_user_organization()
    {
        // Create a different user with different organization
        $differentOrg = Organization::factory()->create();
        $differentUser = User::factory()->create([
            'organization_id' => $differentOrg->id
        ]);

        // Switch to different user
        $this->actingAs($differentUser);

        $request = new Request();

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $differentOrg->id, // Should use the different organization
                Mockery::any(),
                Mockery::any(),
                false, false, false, false
            )
            ->andReturn($expectedResult);

        $this->useCase->perform($request);
    }

    public function test_perform_with_mixed_relationship_flags()
    {
        $request = new Request([
            'with_client' => true,
            'with_budget' => false,
            'with_products' => true,
            'with_custom_products' => false
        ]);

        $expectedResult = [
            'data' => [],
            'count' => 0,
            'total' => 0,
            'currentPage' => 1,
            'lastPage' => 1
        ];

        $this->mockRepository
            ->shouldReceive('fetchFromOrganization')
            ->once()
            ->with(
                $this->organization->id,
                Mockery::any(),
                Mockery::any(),
                true,  // with_client
                false, // with_budget
                true,  // with_products
                false  // with_custom_products
            )
            ->andReturn($expectedResult);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
    }
}
