<?php

namespace Tests\Unit\UseCases\ChatBot\Campaign;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\UseCases\ChatBot\Campaign\LaunchCampaign;
use App\UseCases\ChatBot\Campaign\GenerateMessages;
use App\Repositories\CampaignRepository;
use App\Domains\ChatBot\Campaign;
use App\Enums\CampaignStatus;
use App\Models\Organization;
use App\Models\User;
use App\Models\Template;
use App\Models\Client;

class LaunchTest extends TestCase
{
    use RefreshDatabase;

    private LaunchCampaign $useCase;
    private CampaignRepository $repository;
    private GenerateMessages $generateMessages;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        
        $this->repository = $this->createMock(CampaignRepository::class);
        $this->generateMessages = $this->createMock(GenerateMessages::class);
        $this->useCase = new LaunchCampaign($this->repository, $this->generateMessages);
    }

    public function test_can_launch_campaign_successfully()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: 'Test Campaign',
            status: CampaignStatus::DRAFT,
            is_scheduled: false,
            is_sent: false,
            is_sending: false
        );

        $launchedCampaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            name: 'Test Campaign',
            status: CampaignStatus::SENDING,
            is_scheduled: false,
            is_sent: false,
            is_sending: true
        );

        // Mock expectations
        $this->repository->expects($this->once())
                        ->method('fetchById')
                        ->with(1)
                        ->willReturn($campaign);

        $this->generateMessages->expects($this->once())
                              ->method('execute')
                              ->with(1, $this->organization->id)
                              ->willReturn(['messages_generated' => 5]);

        $this->repository->expects($this->once())
                        ->method('save')
                        ->with($this->callback(function($savedCampaign) {
                            return $savedCampaign->status === CampaignStatus::SENDING &&
                                   $savedCampaign->is_sending === true;
                        }), $this->organization->id)
                        ->willReturn($launchedCampaign);

        // Act
        $result = $this->useCase->execute(1, $this->organization->id);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals(1, $result['campaign_id']);
        $this->assertEquals('sending', $result['status']);
        $this->assertEquals(5, $result['messages_generated']);
    }

    public function test_cannot_launch_campaign_from_different_organization()
    {
        // Arrange
        $otherOrganization = Organization::factory()->create();
        $campaign = new Campaign(
            id: 1,
            organization_id: $otherOrganization->id,
            name: 'Test Campaign'
        );

        $this->repository->expects($this->once())
                        ->method('fetchById')
                        ->with(1)
                        ->willReturn($campaign);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Campaign not found or access denied');

        $this->useCase->execute(1, $this->organization->id);
    }

    public function test_cannot_launch_already_sent_campaign()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            status: CampaignStatus::COMPLETED,
            is_sent: true
        );

        $this->repository->expects($this->once())
                        ->method('fetchById')
                        ->with(1)
                        ->willReturn($campaign);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Campaign cannot be launched');

        $this->useCase->execute(1, $this->organization->id);
    }

    public function test_cannot_launch_campaign_already_sending()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            status: CampaignStatus::SENDING,
            is_sending: true
        );

        $this->repository->expects($this->once())
                        ->method('fetchById')
                        ->with(1)
                        ->willReturn($campaign);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Campaign cannot be launched');

        $this->useCase->execute(1, $this->organization->id);
    }

    public function test_cannot_launch_cancelled_campaign()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            status: CampaignStatus::CANCELLED
        );

        $this->repository->expects($this->once())
                        ->method('fetchById')
                        ->with(1)
                        ->willReturn($campaign);

        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Campaign cannot be launched');

        $this->useCase->execute(1, $this->organization->id);
    }

    public function test_handles_message_generation_failure()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            status: CampaignStatus::DRAFT
        );

        $this->repository->method('fetchById')->willReturn($campaign);
        $this->generateMessages->method('execute')
                              ->willThrowException(new \Exception('Failed to generate messages'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to generate messages');

        $this->useCase->execute(1, $this->organization->id);
    }

    public function test_updates_campaign_status_correctly()
    {
        // Arrange
        $campaign = new Campaign(
            id: 1,
            organization_id: $this->organization->id,
            status: CampaignStatus::DRAFT,
            is_sending: false
        );

        $this->repository->method('fetchById')->willReturn($campaign);
        $this->generateMessages->method('execute')->willReturn(['messages_generated' => 3]);

        $this->repository->expects($this->once())
                        ->method('save')
                        ->with($this->callback(function($savedCampaign) {
                            return $savedCampaign->status === CampaignStatus::SENDING &&
                                   $savedCampaign->is_sending === true &&
                                   $savedCampaign->sent_at !== null;
                        }), $this->organization->id);

        // Act
        $this->useCase->execute(1, $this->organization->id);
    }
}
