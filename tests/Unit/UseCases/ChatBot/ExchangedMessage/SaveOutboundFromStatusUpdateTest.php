<?php

namespace Tests\Unit\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\ExchangedMessage;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Repositories\ExchangedMessageRepository;
use App\UseCases\ChatBot\ExchangedMessage\SaveOutboundFromStatusUpdate;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaveOutboundFromStatusUpdateTest extends TestCase
{
    use RefreshDatabase;

    private SaveOutboundFromStatusUpdate $useCase;
    private ExchangedMessageFactory $exchangedMessageFactory;
    private ExchangedMessageRepository $exchangedMessageRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->exchangedMessageFactory = $this->createMock(ExchangedMessageFactory::class);
        $this->exchangedMessageRepository = $this->createMock(ExchangedMessageRepository::class);

        $this->useCase = new SaveOutboundFromStatusUpdate(
            $this->exchangedMessageFactory,
            $this->exchangedMessageRepository
        );
    }

    public function test_should_not_process_non_delivered_status()
    {
        $statusData = [
            'id' => 'wamid.123',
            'status' => 'sent',
            'timestamp' => '1234567890'
        ];

        $organization = $this->createMockOrganization();
        $phoneNumber = $this->createMockPhoneNumber();
        $message = $this->createMockMessage();

        $result = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        $this->assertFalse($result['success']);
        $this->assertEquals('Status is not delivered', $result['reason']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_should_not_process_when_exchanged_message_already_exists()
    {
        $statusData = [
            'id' => 'wamid.123',
            'status' => 'delivered',
            'timestamp' => '1234567890'
        ];

        $organization = $this->createMockOrganization();
        $phoneNumber = $this->createMockPhoneNumber();
        $message = $this->createMockMessage();
        $existingExchangedMessage = $this->createMockExchangedMessage();

        $this->exchangedMessageRepository
            ->expects($this->once())
            ->method('fetchByMessageId')
            ->with($message->id, $organization->id)
            ->willReturn($existingExchangedMessage);

        $result = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        $this->assertFalse($result['success']);
        $this->assertEquals('ExchangedMessage already exists for this message', $result['reason']);
        $this->assertEquals(0, $result['processed']);
        $this->assertEquals($existingExchangedMessage->id, $result['existing_exchanged_message_id']);
    }

    public function test_should_create_exchanged_message_for_delivered_status()
    {
        $statusData = [
            'id' => 'wamid.123',
            'status' => 'delivered',
            'timestamp' => '1234567890'
        ];

        $organization = $this->createMockOrganization();
        $phoneNumber = $this->createMockPhoneNumber();
        $message = $this->createMockMessage();
        $exchangedMessage = $this->createMockExchangedMessage();
        $savedExchangedMessage = $this->createMockExchangedMessage(123);

        $this->exchangedMessageRepository
            ->expects($this->once())
            ->method('fetchByMessageId')
            ->with($message->id, $organization->id)
            ->willReturn(null);

        $this->exchangedMessageFactory
            ->expects($this->once())
            ->method('buildFromOutboundMessage')
            ->with(
                $message,
                $phoneNumber,
                $this->isInstanceOf(Carbon::class)
            )
            ->willReturn($exchangedMessage);

        $this->exchangedMessageRepository
            ->expects($this->once())
            ->method('store')
            ->with($exchangedMessage)
            ->willReturn($savedExchangedMessage);

        $result = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        $this->assertTrue($result['success']);
        $this->assertEquals('Outbound message processed and saved successfully', $result['reason']);
        $this->assertEquals(1, $result['processed']);
        $this->assertEquals(123, $result['exchanged_message_id']);
        $this->assertEquals($message->id, $result['message_id']);
        $this->assertEquals('wamid.123', $result['wam_id']);
    }

    private function createMockOrganization(): Organization
    {
        return new Organization(
            1, 'Test Org', null, null, null, null, null, null, null, null, null,
            true, false, null, null, null, null, null, null, null
        );
    }

    private function createMockPhoneNumber(): PhoneNumber
    {
        return new PhoneNumber(
            1, 1, null, null, null, '+5511999999999', 'Test Phone', null, true,
            'phone_123', null, null, null, null, null, null, null
        );
    }

    private function createMockMessage(): Message
    {
        return new Message(
            456, 1, null, null, 789, 'Test message', null, false, false, false, false, false,
            0, null, 3, null, null, null, null, null, null, null, null, null
        );
    }

    private function createMockExchangedMessage(?int $id = null): ExchangedMessage
    {
        return new ExchangedMessage(
            $id, 1, 789, 1, null, null, 456, false, true, 'Test message', null,
            Carbon::now(), Carbon::now(), Carbon::now(), null, null, null, null
        );
    }
}
