<?php

namespace Tests\Unit\UseCases\WhatsApp;

use App\Services\Meta\WhatsApp\UseCases\Webhook\ValidateWebhookSignature;
use Tests\TestCase;

class ValidateWebhookSignatureTest extends TestCase
{
    private ValidateWebhookSignature $useCase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->useCase = new ValidateWebhookSignature();
    }

    public function test_perform_returns_true_for_valid_signature()
    {
        $secret = 'test_webhook_secret_123';
        $payload = '{"object":"whatsapp_business_account","entry":[]}';

        config(['whatsapp.webhook_secret' => $secret]);

        // Generate valid signature
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $result = $this->useCase->perform($payload, $signature);

        $this->assertTrue($result);
    }

    public function test_perform_returns_false_for_invalid_signature()
    {
        $secret = 'test_webhook_secret_123';
        $payload = '{"object":"whatsapp_business_account","entry":[]}';
        $invalidSignature = 'sha256=invalid_signature_hash';

        config(['whatsapp.webhook_secret' => $secret]);

        $result = $this->useCase->perform($payload, $invalidSignature);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_missing_signature()
    {
        $secret = 'test_webhook_secret_123';
        $payload = '{"object":"whatsapp_business_account","entry":[]}';

        config(['whatsapp.webhook_secret' => $secret]);

        $result = $this->useCase->perform($payload, null);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_empty_signature()
    {
        $secret = 'test_webhook_secret_123';
        $payload = '{"object":"whatsapp_business_account","entry":[]}';

        config(['whatsapp.webhook_secret' => $secret]);

        $result = $this->useCase->perform($payload, '');

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_missing_secret()
    {
        $payload = '{"object":"whatsapp_business_account","entry":[]}';
        $signature = 'sha256=some_signature_hash';

        config(['whatsapp.webhook_secret' => null]);

        $result = $this->useCase->perform($payload, $signature);

        $this->assertFalse($result);
    }

    public function test_perform_returns_false_for_empty_secret()
    {
        $payload = '{"object":"whatsapp_business_account","entry":[]}';
        $signature = 'sha256=some_signature_hash';

        config(['whatsapp.webhook_secret' => '']);

        $result = $this->useCase->perform($payload, $signature);

        $this->assertFalse($result);
    }

    public function test_perform_handles_different_payload_content()
    {
        $secret = 'test_webhook_secret_456';
        $payload = '{"object":"whatsapp_business_account","entry":[{"id":"123","changes":[{"field":"messages","value":{"messages":[{"id":"msg1","from":"*************","type":"text","text":{"body":"Hello"}}]}}]}]}';

        config(['whatsapp.webhook_secret' => $secret]);

        // Generate valid signature for this payload
        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $result = $this->useCase->perform($payload, $signature);

        $this->assertTrue($result);
    }

    public function test_perform_is_case_sensitive_for_signature()
    {
        $secret = 'test_webhook_secret_789';
        $payload = '{"test":"data"}';

        config(['whatsapp.webhook_secret' => $secret]);

        $validSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);
        $uppercaseSignature = strtoupper($validSignature);

        $this->assertTrue($this->useCase->perform($payload, $validSignature));
        $this->assertFalse($this->useCase->perform($payload, $uppercaseSignature));
    }

    public function test_perform_validates_signature_format()
    {
        $secret = 'test_webhook_secret_abc';
        $payload = '{"test":"data"}';

        config(['whatsapp.webhook_secret' => $secret]);

        $validHash = hash_hmac('sha256', $payload, $secret);

        // Valid format
        $validSignature = 'sha256=' . $validHash;
        $this->assertTrue($this->useCase->perform($payload, $validSignature));

        // Invalid format (missing sha256= prefix)
        $invalidSignature = $validHash;
        $this->assertFalse($this->useCase->perform($payload, $invalidSignature));

        // Invalid format (wrong prefix)
        $wrongPrefixSignature = 'sha1=' . $validHash;
        $this->assertFalse($this->useCase->perform($payload, $wrongPrefixSignature));
    }

    public function test_perform_uses_timing_safe_comparison()
    {
        $secret = 'test_webhook_secret_timing';
        $payload = '{"timing":"test"}';

        config(['whatsapp.webhook_secret' => $secret]);

        $validSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        // This test ensures hash_equals is used (timing-safe comparison)
        // We can't directly test timing, but we can verify the method works correctly
        $this->assertTrue($this->useCase->perform($payload, $validSignature));

        // Slightly different signature should fail
        $tamperedSignature = substr($validSignature, 0, -1) . 'x';
        $this->assertFalse($this->useCase->perform($payload, $tamperedSignature));
    }

    public function test_perform_handles_empty_payload()
    {
        $secret = 'test_webhook_secret_empty';
        $payload = '';

        config(['whatsapp.webhook_secret' => $secret]);

        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $result = $this->useCase->perform($payload, $signature);

        $this->assertTrue($result);
    }

    public function test_perform_handles_special_characters_in_payload()
    {
        $secret = 'test_webhook_secret_special';
        $payload = '{"message":"Hello 🌍! Special chars: áéíóú & <script>"}';

        config(['whatsapp.webhook_secret' => $secret]);

        $signature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        $result = $this->useCase->perform($payload, $signature);

        $this->assertTrue($result);
    }
}
