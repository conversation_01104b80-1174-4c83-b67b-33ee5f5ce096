<?php

namespace Tests\Unit\UseCases\WhatsApp;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ProcessWebhookMessage;
use App\UseCases\ChatBot\ExchangedMessage\SaveExchangedMessagesFromWebhook;
use Mockery;
use Tests\TestCase;

class ProcessWebhookMessageTest extends TestCase
{
    private ChatBotService $chatBotService;
    private SaveExchangedMessagesFromWebhook $saveExchangedMessagesFromWebhook;
    private ProcessWebhookMessage $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->chatBotService = Mockery::mock(ChatBotService::class);
        $this->saveExchangedMessagesFromWebhook = Mockery::mock(SaveExchangedMessagesFromWebhook::class);
        $this->useCase = new ProcessWebhookMessage($this->chatBotService, $this->saveExchangedMessagesFromWebhook);
    }

    public function test_perform_processes_incoming_messages_successfully()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 123, // Add flow_id for ChatBot activation
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true, // Ensure ChatBot is activated
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888', // Different from business number
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ],
            'contacts' => [
                [
                    'profile' => ['name' => 'John Doe'],
                    'wa_id' => '+5511888888888'
                ]
            ]
        ]);

        // Mock SaveExchangedMessages
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, null)
            ->andReturn(['success' => true, 'processed' => 1, 'total_messages' => 1]);

        $this->chatBotService
            ->shouldReceive('processWebhook')
            ->once()
            ->andReturn(['success' => true, 'message_id' => 'msg_123']);

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(1, $result['processed']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals('phone_123', $result['phone_number_id']);
        $this->assertCount(1, $result['results']);

        // Verificar informações das ExchangedMessages
        $this->assertArrayHasKey('exchanged_messages', $result);
        $this->assertEquals(1, $result['exchanged_messages']['processed']);
        $this->assertEquals(1, $result['exchanged_messages']['total_messages']);
        $this->assertTrue($result['exchanged_messages']['success']);
        $this->assertNull($result['webhook_log_id']);
    }

    public function test_perform_skips_outgoing_messages()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 123, // Add flow_id for ChatBot activation
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true, // Ensure ChatBot is activated
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '5511999999999', // Same as business number (outgoing)
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ]);

        // Mock SaveExchangedMessages - deve ser chamado mesmo para mensagens de saída
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, null)
            ->andReturn(['success' => true, 'processed' => 0, 'total_messages' => 0]); // No incoming messages

        // Should not call processWebhook for outgoing messages
        $this->chatBotService
            ->shouldNotReceive('processWebhook');

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(0, $result['processed']); // No messages processed
        $this->assertEmpty($result['results']);
    }

    public function test_perform_returns_error_when_no_messages()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ]
            // No messages array
        ]);

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('No messages found in change value', $result['error']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_perform_handles_chatbot_service_exception()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 123, // Add flow_id for ChatBot activation
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true, // Ensure ChatBot is activated
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ]);

        // Mock SaveExchangedMessages
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, null)
            ->andReturn(['success' => true, 'processed' => 1, 'total_messages' => 1]);

        $this->chatBotService
            ->shouldReceive('processWebhook')
            ->once()
            ->andThrow(new \Exception('ChatBot service error'));

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('ChatBot service error', $result['error']);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(0, $result['processed']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals('phone_123', $result['phone_number_id']);
    }

    public function test_perform_processes_multiple_messages()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 123, // Add flow_id for ChatBot activation
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true, // Ensure ChatBot is activated
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ],
                [
                    'id' => 'msg_124',
                    'from' => '+5511777777777',
                    'timestamp' => '1234567891',
                    'type' => 'text',
                    'text' => ['body' => 'Hi']
                ]
            ]
        ]);

        // Mock SaveExchangedMessages
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, null)
            ->andReturn(['success' => true, 'processed' => 2, 'total_messages' => 2]);

        $this->chatBotService
            ->shouldReceive('processWebhook')
            ->twice()
            ->andReturn(['success' => true]);

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('message', $result['type']);
        $this->assertEquals(2, $result['processed']);
        $this->assertCount(2, $result['results']);
    }

    public function test_perform_returns_error_when_chatbot_not_activated()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        // Phone number with ChatBot disabled
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: false, // ChatBot disabled
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ]);

        // Mock SaveExchangedMessages - deve ser chamado mesmo com ChatBot desabilitado
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, null)
            ->andReturn(['success' => true, 'processed' => 1, 'total_messages' => 1]);

        // ChatBot service should not be called
        $this->chatBotService->shouldNotReceive('processWebhook');

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('ChatBot processing disabled for this phone number', $result['error']);
        $this->assertEquals('Phone number does not meet ChatBot activation criteria', $result['reason']);
        $this->assertEquals(0, $result['processed']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals('phone_123', $result['phone_number_id']);
        $this->assertArrayHasKey('phone_number_checks', $result);
        $this->assertTrue($result['phone_number_checks']['is_active']);
        $this->assertFalse($result['phone_number_checks']['is_chatbot_activated']);
        $this->assertTrue($result['phone_number_checks']['has_flow']);
    }

    public function test_perform_returns_error_when_phone_number_inactive()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        // Inactive phone number
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: false, // Phone number inactive
            is_chatbot_activated: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ]);

        // Mock SaveExchangedMessages - deve ser chamado mesmo com ChatBot desabilitado
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, null)
            ->andReturn(['success' => true, 'processed' => 1, 'total_messages' => 1]);

        // ChatBot service should not be called
        $this->chatBotService->shouldNotReceive('processWebhook');

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('ChatBot processing disabled for this phone number', $result['error']);
        $this->assertEquals(0, $result['processed']);
        $this->assertFalse($result['phone_number_checks']['is_active']);
        $this->assertTrue($result['phone_number_checks']['is_chatbot_activated']);
        $this->assertTrue($result['phone_number_checks']['has_flow']);
    }

    public function test_perform_returns_error_when_no_flow_configured()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        // Phone number without flow
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null, // No flow configured
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ]);

        // Mock SaveExchangedMessages - deve ser chamado mesmo com ChatBot desabilitado
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, null)
            ->andReturn(['success' => true, 'processed' => 1, 'total_messages' => 1]);

        // ChatBot service should not be called
        $this->chatBotService->shouldNotReceive('processWebhook');

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('ChatBot processing disabled for this phone number', $result['error']);
        $this->assertEquals(0, $result['processed']);
        $this->assertTrue($result['phone_number_checks']['is_active']);
        $this->assertTrue($result['phone_number_checks']['is_chatbot_activated']);
        $this->assertFalse($result['phone_number_checks']['has_flow']);
    }

    public function test_perform_passes_webhook_log_id_correctly()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ]);

        $webhookLogId = 456;

        // Mock SaveExchangedMessages with webhookLogId
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, $webhookLogId)
            ->andReturn(['success' => true, 'processed' => 1, 'total_messages' => 1]);

        $this->chatBotService
            ->shouldReceive('processWebhook')
            ->once()
            ->andReturn(['success' => true, 'message_id' => 'msg_123']);

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber, $webhookLogId);

        $this->assertTrue($result['success']);
        $this->assertEquals($webhookLogId, $result['webhook_log_id']);
    }

    public function test_perform_saves_exchanged_messages_even_when_chatbot_disabled()
    {
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        // PhoneNumber with ChatBot disabled (no flow_id)
        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null, // No flow = ChatBot disabled
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true,
            whatsapp_phone_number_id: 'phone_123'
        );

        $changeValue = new ChangeValue([
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'messages' => [
                [
                    'id' => 'msg_123',
                    'from' => '+5511888888888',
                    'timestamp' => '1234567890',
                    'type' => 'text',
                    'text' => ['body' => 'Hello']
                ]
            ]
        ]);

        $webhookLogId = 789;

        // Mock SaveExchangedMessages - deve ser chamado mesmo com ChatBot desabilitado
        $this->saveExchangedMessagesFromWebhook
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue, $organization, $phoneNumber, $webhookLogId)
            ->andReturn(['success' => true, 'processed' => 1, 'total_messages' => 1]);

        // ChatBot service should NOT be called because ChatBot is disabled
        $this->chatBotService->shouldNotReceive('processWebhook');

        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber, $webhookLogId);

        // Verificar que ExchangedMessages foram processadas mesmo com ChatBot desabilitado
        $this->assertFalse($result['success']); // ChatBot processing failed
        $this->assertEquals('ChatBot processing disabled for this phone number', $result['error']);
        $this->assertEquals(0, $result['processed']); // No ChatBot processing
        $this->assertEquals($webhookLogId, $result['webhook_log_id']);

        // Mas ExchangedMessages devem ter sido registradas
        // Nota: Como o SaveExchangedMessages é chamado ANTES da verificação do ChatBot,
        // as mensagens são sempre registradas independente do status do ChatBot
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
