<?php

namespace Tests\Unit\UseCases\WhatsApp;

use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\Enums\MessageStatus;
use App\Repositories\MessageRepository;
use App\Services\Meta\WhatsApp\Domains\WhatsAppMessage;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ProcessWebhookMessageStatusUpdate;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppMessage\FetchByWAMID;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppWebhookEntry\ProcessWebhookEntry;
use Mockery;
use Tests\TestCase;

class ProcessWebhookMessageStatusUpdateTest extends TestCase
{
    private ProcessWebhookEntry $processWebhookEntry;
    private FetchByWAMID $fetchByWAMID;
    private WhatsAppMessageRepository $whatsAppMessageRepository;
    private MessageRepository $messageRepository;
    private ProcessWebhookMessageStatusUpdate $useCase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->processWebhookEntry = Mockery::mock(ProcessWebhookEntry::class);
        $this->fetchByWAMID = Mockery::mock(FetchByWAMID::class);
        $this->whatsAppMessageRepository = Mockery::mock(WhatsAppMessageRepository::class);
        $this->messageRepository = Mockery::mock(MessageRepository::class);

        $this->useCase = new ProcessWebhookMessageStatusUpdate(
            $this->processWebhookEntry,
            $this->fetchByWAMID,
            $this->whatsAppMessageRepository,
            $this->messageRepository
        );
    }

    public function test_perform_processes_delivered_status_updates_successfully()
    {
        // Arrange
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123',
            whatsapp_business_id: 'business_456'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.123456789',
                    'status' => 'delivered',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+5511888888888'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Mock Message domain
        $message = Mockery::mock(Message::class);
        $message->shouldReceive('updateMessageStatus')
            ->once()
            ->with('delivered')
            ->andReturnNull();

        // Mock WhatsApp Message domain
        $whatsAppMessage = Mockery::mock(WhatsAppMessage::class);
        $whatsAppMessage->message = $message;
        $whatsAppMessage->shouldReceive('updateWhatsAppMessageStatus')
            ->once()
            ->with('delivered')
            ->andReturnNull();

        // Mock FetchByWAMID
        $this->fetchByWAMID
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue)
            ->andReturn($whatsAppMessage);

        // Mock repositories
        $this->messageRepository
            ->shouldReceive('update')
            ->once()
            ->with($message, 1)
            ->andReturn($message);

        $this->whatsAppMessageRepository
            ->shouldReceive('update')
            ->once()
            ->with($whatsAppMessage)
            ->andReturn($whatsAppMessage);

        // Mock ProcessWebhookEntry
        $this->processWebhookEntry
            ->shouldReceive('perform')
            ->once()
            ->andReturn([
                'success' => true,
                'processed_entries' => 1,
                'entries' => ['entry1']
            ]);

        // Act
        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('status', $result['type']);
        $this->assertEquals(1, $result['processed']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals('phone_123', $result['phone_number_id']);
        $this->assertTrue($result['message_updated']);
        $this->assertTrue($result['whatsapp_message_updated']);
        $this->assertEquals('wamid.123456789', $result['wam_id']);
    }

    public function test_perform_processes_read_status_updates_successfully()
    {
        // Arrange
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123',
            whatsapp_business_id: 'business_456'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.123456789',
                    'status' => 'read',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+5511888888888'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Mock Message domain
        $message = Mockery::mock(Message::class);
        $message->shouldReceive('updateMessageStatus')
            ->once()
            ->with('read')
            ->andReturnNull();

        // Mock WhatsApp Message domain
        $whatsAppMessage = Mockery::mock(WhatsAppMessage::class);
        $whatsAppMessage->message = $message;
        $whatsAppMessage->shouldReceive('updateWhatsAppMessageStatus')
            ->once()
            ->with('read')
            ->andReturnNull();

        // Mock FetchByWAMID
        $this->fetchByWAMID
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue)
            ->andReturn($whatsAppMessage);

        // Mock repositories
        $this->messageRepository
            ->shouldReceive('update')
            ->once()
            ->with($message, 1)
            ->andReturn($message);

        $this->whatsAppMessageRepository
            ->shouldReceive('update')
            ->once()
            ->with($whatsAppMessage)
            ->andReturn($whatsAppMessage);

        // Mock ProcessWebhookEntry
        $this->processWebhookEntry
            ->shouldReceive('perform')
            ->once()
            ->andReturn([
                'success' => true,
                'processed_entries' => 1,
                'entries' => ['entry1']
            ]);

        // Act
        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('status', $result['type']);
        $this->assertEquals(1, $result['processed']);
        $this->assertTrue($result['message_updated']);
        $this->assertTrue($result['whatsapp_message_updated']);
        $this->assertEquals('wamid.123456789', $result['wam_id']);
    }

    public function test_perform_processes_sent_status_updates_successfully()
    {
        // Arrange
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123',
            whatsapp_business_id: 'business_456'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.123456789',
                    'status' => 'sent',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+5511888888888'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Mock Message domain
        $message = Mockery::mock(Message::class);
        $message->shouldReceive('updateMessageStatus')
            ->once()
            ->with('sent')
            ->andReturnNull();

        // Mock WhatsApp Message domain
        $whatsAppMessage = Mockery::mock(WhatsAppMessage::class);
        $whatsAppMessage->message = $message;
        $whatsAppMessage->shouldReceive('updateWhatsAppMessageStatus')
            ->once()
            ->with('sent')
            ->andReturnNull();

        // Mock FetchByWAMID
        $this->fetchByWAMID
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue)
            ->andReturn($whatsAppMessage);

        // Mock repositories
        $this->messageRepository
            ->shouldReceive('update')
            ->once()
            ->with($message, 1)
            ->andReturn($message);

        $this->whatsAppMessageRepository
            ->shouldReceive('update')
            ->once()
            ->with($whatsAppMessage)
            ->andReturn($whatsAppMessage);

        // Mock ProcessWebhookEntry
        $this->processWebhookEntry
            ->shouldReceive('perform')
            ->once()
            ->andReturn([
                'success' => true,
                'processed_entries' => 1,
                'entries' => ['entry1']
            ]);

        // Act
        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('status', $result['type']);
        $this->assertEquals(1, $result['processed']);
        $this->assertTrue($result['message_updated']);
        $this->assertTrue($result['whatsapp_message_updated']);
        $this->assertEquals('wamid.123456789', $result['wam_id']);
    }

    public function test_perform_handles_no_statuses_error()
    {
        // Arrange
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123',
            whatsapp_business_id: 'business_456'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => []
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Act
        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        // Assert
        $this->assertFalse($result['success']);
        $this->assertEquals('No statuses found in change value', $result['error']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_perform_continues_when_whatsapp_message_not_found()
    {
        // Arrange
        $organization = new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $phoneNumber = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            whatsapp_phone_number_id: 'phone_123',
            whatsapp_business_id: 'business_456'
        );

        $changeValueData = [
            'metadata' => [
                'phone_number_id' => 'phone_123'
            ],
            'statuses' => [
                [
                    'id' => 'wamid.nonexistent',
                    'status' => 'delivered',
                    'timestamp' => '1234567890',
                    'recipient_id' => '+5511888888888'
                ]
            ]
        ];

        $changeValue = new ChangeValue($changeValueData);

        // Mock FetchByWAMID returning null
        $this->fetchByWAMID
            ->shouldReceive('perform')
            ->once()
            ->with($changeValue)
            ->andReturn(null);

        // Mock ProcessWebhookEntry
        $this->processWebhookEntry
            ->shouldReceive('perform')
            ->once()
            ->andReturn([
                'success' => true,
                'processed_entries' => 1,
                'entries' => ['entry1']
            ]);

        // Act
        $result = $this->useCase->perform($changeValue, $organization, $phoneNumber);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('status', $result['type']);
        $this->assertEquals(1, $result['processed']);
        $this->assertFalse($result['message_updated']);
        $this->assertFalse($result['whatsapp_message_updated']);
        $this->assertEquals('wamid.nonexistent', $result['wam_id']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
