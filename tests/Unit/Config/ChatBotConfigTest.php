<?php

namespace Tests\Unit\Config;

use Tests\TestCase;

class ChatBotConfigTest extends TestCase
{
    public function test_chatbot_config_file_exists()
    {
        $configPath = config_path('chatbot.php');
        $this->assertFileExists($configPath);
    }

    public function test_chatbot_config_has_required_sections()
    {
        $config = config('chatbot');
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('timeouts', $config);
        $this->assertArrayHasKey('cleanup', $config);
        $this->assertArrayHasKey('commands', $config);
        $this->assertArrayHasKey('validation', $config);
        $this->assertArrayHasKey('error_handling', $config);
        $this->assertArrayHasKey('flow_validation', $config);
        $this->assertArrayHasKey('logging', $config);
    }

    public function test_timeout_configuration_has_required_values()
    {
        $timeouts = config('chatbot.timeouts');
        
        $this->assertIsArray($timeouts);
        $this->assertArrayHasKey('input_step', $timeouts);
        $this->assertArrayHasKey('interactive_step', $timeouts);
        $this->assertArrayHasKey('conversation', $timeouts);
        $this->assertArrayHasKey('inactive_after', $timeouts);
        
        // Check default values
        $this->assertEquals(300, $timeouts['input_step']);
        $this->assertEquals(600, $timeouts['interactive_step']);
        $this->assertEquals(3600, $timeouts['conversation']);
        $this->assertEquals(86400, $timeouts['inactive_after']);
    }

    public function test_cleanup_configuration_has_required_values()
    {
        $cleanup = config('chatbot.cleanup');
        
        $this->assertIsArray($cleanup);
        $this->assertArrayHasKey('run_cleanup_every', $cleanup);
        $this->assertArrayHasKey('enabled', $cleanup);
        $this->assertArrayHasKey('soft_delete', $cleanup);
        
        // Check default values
        $this->assertEquals(3600, $cleanup['run_cleanup_every']);
        $this->assertTrue($cleanup['enabled']);
        $this->assertTrue($cleanup['soft_delete']);
    }

    public function test_commands_configuration_has_required_values()
    {
        $commands = config('chatbot.commands');
        
        $this->assertIsArray($commands);
        $this->assertArrayHasKey('max_execution_time', $commands);
        $this->assertArrayHasKey('retry_on_failure', $commands);
        $this->assertArrayHasKey('max_retries', $commands);
        $this->assertArrayHasKey('retry_delay', $commands);
        
        // Check default values
        $this->assertEquals(30, $commands['max_execution_time']);
        $this->assertTrue($commands['retry_on_failure']);
        $this->assertEquals(3, $commands['max_retries']);
        $this->assertEquals(5, $commands['retry_delay']);
    }

    public function test_validation_configuration_has_required_values()
    {
        $validation = config('chatbot.validation');
        
        $this->assertIsArray($validation);
        $this->assertArrayHasKey('max_attempts', $validation);
        $this->assertArrayHasKey('default_error_message', $validation);
        $this->assertArrayHasKey('rules', $validation);
        $this->assertArrayHasKey('messages', $validation);
        
        // Check default values
        $this->assertEquals(3, $validation['max_attempts']);
        $this->assertEquals('Por favor, digite um valor válido.', $validation['default_error_message']);
        
        // Check validation rules
        $rules = $validation['rules'];
        $this->assertArrayHasKey('email', $rules);
        $this->assertArrayHasKey('phone', $rules);
        $this->assertArrayHasKey('cpf', $rules);
        $this->assertArrayHasKey('cnpj', $rules);
        $this->assertArrayHasKey('number', $rules);
        $this->assertArrayHasKey('date', $rules);
        
        // Check validation messages
        $messages = $validation['messages'];
        $this->assertArrayHasKey('email.email', $messages);
        $this->assertArrayHasKey('phone.regex', $messages);
        $this->assertArrayHasKey('cpf.cpf', $messages);
        $this->assertArrayHasKey('cnpj.cnpj', $messages);
        $this->assertArrayHasKey('number.numeric', $messages);
        $this->assertArrayHasKey('date.date_format', $messages);
    }

    public function test_error_handling_configuration_has_required_values()
    {
        $errorHandling = config('chatbot.error_handling');
        
        $this->assertIsArray($errorHandling);
        $this->assertArrayHasKey('default_strategy', $errorHandling);
        $this->assertArrayHasKey('strategies', $errorHandling);
        $this->assertArrayHasKey('log_errors', $errorHandling);
        $this->assertArrayHasKey('notify_on_errors', $errorHandling);
        
        // Check default values
        $this->assertEquals('retry', $errorHandling['default_strategy']);
        $this->assertTrue($errorHandling['log_errors']);
        $this->assertFalse($errorHandling['notify_on_errors']);
        
        // Check strategies
        $strategies = $errorHandling['strategies'];
        $this->assertArrayHasKey('retry', $strategies);
        $this->assertArrayHasKey('fallback', $strategies);
        $this->assertArrayHasKey('escalate', $strategies);
        $this->assertArrayHasKey('reset', $strategies);
        
        // Check retry strategy
        $retryStrategy = $strategies['retry'];
        $this->assertArrayHasKey('max_attempts', $retryStrategy);
        $this->assertArrayHasKey('delay', $retryStrategy);
        $this->assertEquals(3, $retryStrategy['max_attempts']);
        $this->assertEquals(5, $retryStrategy['delay']);
        
        // Check fallback strategy
        $fallbackStrategy = $strategies['fallback'];
        $this->assertArrayHasKey('fallback_step', $fallbackStrategy);
        $this->assertArrayHasKey('fallback_message', $fallbackStrategy);
        
        // Check escalate strategy
        $escalateStrategy = $strategies['escalate'];
        $this->assertArrayHasKey('escalation_message', $escalateStrategy);
        $this->assertArrayHasKey('notify_team', $escalateStrategy);
        
        // Check reset strategy
        $resetStrategy = $strategies['reset'];
        $this->assertArrayHasKey('reset_message', $resetStrategy);
        $this->assertArrayHasKey('preserve_client_data', $resetStrategy);
    }

    public function test_flow_validation_configuration_has_required_values()
    {
        $flowValidation = config('chatbot.flow_validation');
        
        $this->assertIsArray($flowValidation);
        $this->assertArrayHasKey('enabled', $flowValidation);
        $this->assertArrayHasKey('rules', $flowValidation);
        $this->assertArrayHasKey('cache_results', $flowValidation);
        $this->assertArrayHasKey('cache_ttl', $flowValidation);
        
        // Check default values
        $this->assertTrue($flowValidation['enabled']);
        $this->assertTrue($flowValidation['cache_results']);
        $this->assertEquals(3600, $flowValidation['cache_ttl']);
        
        // Check validation rules
        $rules = $flowValidation['rules'];
        $this->assertArrayHasKey('require_initial_step', $rules);
        $this->assertArrayHasKey('require_ending_step', $rules);
        $this->assertArrayHasKey('check_orphaned_steps', $rules);
        $this->assertArrayHasKey('check_infinite_loops', $rules);
        $this->assertArrayHasKey('validate_conditional_targets', $rules);
        
        $this->assertTrue($rules['require_initial_step']);
        $this->assertTrue($rules['require_ending_step']);
        $this->assertTrue($rules['check_orphaned_steps']);
        $this->assertTrue($rules['check_infinite_loops']);
        $this->assertTrue($rules['validate_conditional_targets']);
    }

    public function test_logging_configuration_has_required_values()
    {
        $logging = config('chatbot.logging');
        
        $this->assertIsArray($logging);
        $this->assertArrayHasKey('level', $logging);
        $this->assertArrayHasKey('channel', $logging);
        $this->assertArrayHasKey('log_interactions', $logging);
        $this->assertArrayHasKey('log_step_transitions', $logging);
        $this->assertArrayHasKey('log_command_executions', $logging);
        
        // Check default values
        $this->assertEquals('info', $logging['level']);
        $this->assertEquals('daily', $logging['channel']);
        $this->assertTrue($logging['log_interactions']);
        $this->assertTrue($logging['log_step_transitions']);
        $this->assertTrue($logging['log_command_executions']);
    }

    public function test_config_values_can_be_overridden_by_environment()
    {
        // Test that environment variables can override config values
        $originalValue = config('chatbot.timeouts.conversation');
        
        // Temporarily set environment variable
        putenv('CHATBOT_CONVERSATION_TIMEOUT=7200');
        
        // Reload config
        $this->app['config']->set('chatbot.timeouts.conversation', env('CHATBOT_CONVERSATION_TIMEOUT', 3600));
        
        $newValue = config('chatbot.timeouts.conversation');
        $this->assertEquals(7200, $newValue);
        $this->assertNotEquals($originalValue, $newValue);
        
        // Clean up
        putenv('CHATBOT_CONVERSATION_TIMEOUT');
    }

    public function test_validation_rules_are_properly_formatted()
    {
        $rules = config('chatbot.validation.rules');
        
        foreach ($rules as $inputType => $rule) {
            $this->assertIsString($rule, "Validation rule for {$inputType} should be a string");
            $this->assertNotEmpty($rule, "Validation rule for {$inputType} should not be empty");
        }
    }

    public function test_validation_messages_are_properly_formatted()
    {
        $messages = config('chatbot.validation.messages');
        
        foreach ($messages as $key => $message) {
            $this->assertIsString($message, "Validation message for {$key} should be a string");
            $this->assertNotEmpty($message, "Validation message for {$key} should not be empty");
        }
    }

    public function test_error_handling_strategies_are_complete()
    {
        $strategies = config('chatbot.error_handling.strategies');
        
        $requiredStrategies = ['retry', 'fallback', 'escalate', 'reset'];
        
        foreach ($requiredStrategies as $strategy) {
            $this->assertArrayHasKey($strategy, $strategies, "Strategy {$strategy} should be defined");
            $this->assertIsArray($strategies[$strategy], "Strategy {$strategy} should be an array");
        }
    }

    public function test_flow_validation_rules_are_boolean()
    {
        $rules = config('chatbot.flow_validation.rules');
        
        foreach ($rules as $ruleName => $ruleValue) {
            $this->assertIsBool($ruleValue, "Flow validation rule {$ruleName} should be boolean");
        }
    }

    public function test_timeout_values_are_numeric()
    {
        $timeouts = config('chatbot.timeouts');
        
        foreach ($timeouts as $timeoutName => $timeoutValue) {
            $this->assertIsNumeric($timeoutValue, "Timeout {$timeoutName} should be numeric");
            $this->assertGreaterThan(0, $timeoutValue, "Timeout {$timeoutName} should be greater than 0");
        }
    }
}
