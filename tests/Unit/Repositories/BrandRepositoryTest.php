<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\BrandFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Brand;
use App\Factories\Inventory\BrandFactory;
use App\Models\Brand as BrandModel;
use App\Models\Organization;
use App\Repositories\BrandRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BrandRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private BrandRepository $repository;
    private BrandFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(BrandRepository::class);
        $this->factory = app(BrandFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test brands
        BrandModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new BrandFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $brand) {
            $this->assertInstanceOf(Brand::class, $brand);
        }
    }

    public function test_fetch_all_with_filters()
    {
        // Create brands with different names
        BrandModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Brand'
        ]);
        BrandModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Brand'
        ]);

        $filters = new BrandFilters(['name' => 'Alpha']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha Brand', $result['data'][0]->name);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more brands than the limit
        BrandModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new BrandFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 5]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_brands()
    {
        $otherOrganization = Organization::factory()->create();

        // Create brands for different organizations
        BrandModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        BrandModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new BrandFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $brand) {
            $this->assertEquals($this->organization->id, $brand->organization_id);
        }
    }

    public function test_fetch_by_id_returns_brand()
    {
        $model = BrandModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $brand = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Brand::class, $brand);
        $this->assertEquals($model->id, $brand->id);
        $this->assertEquals($model->name, $brand->name);
        $this->assertEquals($model->organization_id, $brand->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_brand()
    {
        $domain = new Brand(
            id: null,
            organization_id: $this->organization->id,
            name: 'Store Test Brand',
            description: 'Test brand for store'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals('Store Test Brand', $result->name);

        $this->assertDatabaseHas('brands', [
            'id' => $result->id,
            'name' => 'Store Test Brand',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_brand()
    {
        $model = BrandModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Brand',
            'description' => 'Old description'
        ]);

        $domain = new Brand(
            id: $model->id,
            organization_id: $this->organization->id,
            name: 'New Brand',
            description: 'New description'
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Brand::class, $result);
        $this->assertEquals('New Brand', $result->name);

        $this->assertDatabaseHas('brands', [
            'id' => $model->id,
            'name' => 'New Brand',
            'description' => 'New description'
        ]);
    }

    public function test_delete_removes_brand()
    {
        $model = BrandModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('brands', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        BrandModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create brands for different organization
        $otherOrg = Organization::factory()->create();
        BrandModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new BrandFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_repository_handles_soft_deleted_brands()
    {
        $model = BrandModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the brand
        $model->delete();

        // Should not find soft deleted brand
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $brand1 = BrandModel::factory()->create(['organization_id' => $org1->id]);
        $brand2 = BrandModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new BrandFilters([]);

        // Count should only include brands from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_repository_handles_empty_description()
    {
        BrandModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => ''
        ]);

        $filters = new BrandFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('', $result['data'][0]->description);
    }

    public function test_repository_handles_null_description()
    {
        BrandModel::factory()->create([
            'organization_id' => $this->organization->id,
            'description' => null
        ]);

        $filters = new BrandFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('', $result['data'][0]->description); // Factory converts null to empty string
    }

    public function test_repository_handles_special_characters()
    {
        $specialName = 'Brand & Co. (™)';
        $specialDescription = 'Description with special chars: @#$%^&*()';
        
        BrandModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => $specialName,
            'description' => $specialDescription
        ]);

        $filters = new BrandFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($specialName, $result['data'][0]->name);
        $this->assertEquals($specialDescription, $result['data'][0]->description);
    }

    public function test_repository_handles_unicode_characters()
    {
        $unicodeName = 'Bränd Ñamé 中文';
        $unicodeDescription = 'Descripción con caracteres especiales: ñáéíóú';
        
        BrandModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => $unicodeName,
            'description' => $unicodeDescription
        ]);

        $filters = new BrandFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($unicodeName, $result['data'][0]->name);
        $this->assertEquals($unicodeDescription, $result['data'][0]->description);
    }
}
