<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\GroupFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Group;
use App\Factories\Inventory\GroupFactory;
use App\Models\Group as GroupModel;
use App\Models\Organization;
use App\Repositories\GroupRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GroupRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private GroupRepository $repository;
    private GroupFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(GroupRepository::class);
        $this->factory = app(GroupFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test groups
        GroupModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $group) {
            $this->assertInstanceOf(Group::class, $group);
        }
    }

    public function test_fetch_all_with_filters()
    {
        // Create groups with different names
        GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Group'
        ]);
        GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Group'
        ]);

        $filters = new GroupFilters(['name' => 'Alpha']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha Group', $result['data'][0]->name);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more groups than the limit
        GroupModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 5]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_groups()
    {
        $otherOrganization = Organization::factory()->create();

        // Create groups for different organizations
        GroupModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        GroupModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $group) {
            $this->assertEquals($this->organization->id, $group->organization_id);
        }
    }

    public function test_fetch_by_id_returns_group()
    {
        $model = GroupModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $group = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Group::class, $group);
        $this->assertEquals($model->id, $group->id);
        $this->assertEquals($model->name, $group->name);
        $this->assertEquals($model->organization_id, $group->organization_id);
        $this->assertEquals($model->description, $group->description);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_group()
    {
        $domain = new Group(
            id: null,
            organization_id: $this->organization->id,
            name: 'Store Test Group',
            description: 'Store test group description'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals('Store Test Group', $result->name);
        $this->assertEquals('Store test group description', $result->description);

        $this->assertDatabaseHas('groups', [
            'id' => $result->id,
            'name' => 'Store Test Group',
            'organization_id' => $this->organization->id,
            'description' => 'Store test group description'
        ]);
    }

    public function test_store_creates_group_with_null_description()
    {
        $domain = new Group(
            id: null,
            organization_id: $this->organization->id,
            name: 'Group Without Description',
            description: null
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals('Group Without Description', $result->name);
        $this->assertNull($result->description);

        $this->assertDatabaseHas('groups', [
            'id' => $result->id,
            'name' => 'Group Without Description',
            'description' => null
        ]);
    }

    public function test_update_modifies_existing_group()
    {
        $model = GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Group',
            'description' => 'Old description'
        ]);

        $domain = new Group(
            id: $model->id,
            organization_id: $this->organization->id,
            name: 'New Group',
            description: 'New description'
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals('New Group', $result->name);
        $this->assertEquals('New description', $result->description);

        $this->assertDatabaseHas('groups', [
            'id' => $model->id,
            'name' => 'New Group',
            'description' => 'New description'
        ]);
    }

    public function test_update_with_null_description()
    {
        $model = GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Group With Description',
            'description' => 'Original description'
        ]);

        $domain = new Group(
            id: $model->id,
            organization_id: $this->organization->id,
            name: 'Updated Group',
            description: null
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Group::class, $result);
        $this->assertEquals('Updated Group', $result->name);
        $this->assertNull($result->description);

        $this->assertDatabaseHas('groups', [
            'id' => $model->id,
            'name' => 'Updated Group',
            'description' => null
        ]);
    }

    public function test_delete_removes_group()
    {
        $model = GroupModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('groups', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        GroupModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create groups for different organization
        $otherOrg = Organization::factory()->create();
        GroupModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new GroupFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_count_with_filters()
    {
        GroupModel::factory()->count(2)->create([
            'organization_id' => $this->organization->id,
            'name' => 'Filtered Group'
        ]);
        GroupModel::factory()->count(1)->create([
            'organization_id' => $this->organization->id,
            'name' => 'Other Group'
        ]);

        $filters = new GroupFilters(['name' => 'Filtered']);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(2, $count);
    }

    public function test_sum_returns_correct_value()
    {
        // Create groups with specific IDs for sum testing
        $group1 = GroupModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $group2 = GroupModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new GroupFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'id');

        $this->assertEquals($group1->id + $group2->id, $sum);
    }

    public function test_repository_handles_soft_deleted_groups()
    {
        $model = GroupModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the group
        $model->delete();

        // Should not find soft deleted group
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $group1 = GroupModel::factory()->create(['organization_id' => $org1->id]);
        $group2 = GroupModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new GroupFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result1 = $this->repository->fetchFromOrganization($org1->id, $filters, $orderBy);
        $result2 = $this->repository->fetchFromOrganization($org2->id, $filters, $orderBy);

        $this->assertEquals(1, $result1['count']);
        $this->assertEquals(1, $result2['count']);
        $this->assertEquals($group1->id, $result1['data'][0]->id);
        $this->assertEquals($group2->id, $result2['data'][0]->id);
    }

    public function test_update_only_affects_organization_groups()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $group1 = GroupModel::factory()->create([
            'organization_id' => $org1->id,
            'name' => 'Original Name'
        ]);
        $group2 = GroupModel::factory()->create([
            'organization_id' => $org2->id,
            'name' => 'Original Name'
        ]);

        $domain = new Group(
            id: $group1->id,
            organization_id: $org1->id,
            name: 'Updated Name',
            description: 'Updated description'
        );

        $this->repository->update($domain, $org1->id);

        // Check that only the group from org1 was updated
        $this->assertDatabaseHas('groups', [
            'id' => $group1->id,
            'name' => 'Updated Name'
        ]);
        $this->assertDatabaseHas('groups', [
            'id' => $group2->id,
            'name' => 'Original Name'
        ]);
    }

    public function test_store_sets_id_on_domain_object()
    {
        $domain = new Group(
            id: null,
            organization_id: $this->organization->id,
            name: 'Test Group',
            description: 'Test description'
        );

        $this->assertNull($domain->id);

        $result = $this->repository->store($domain);

        $this->assertNotNull($result->id);
        $this->assertEquals($domain->id, $result->id); // Should be the same object
    }

    public function test_fetch_all_with_description_filter()
    {
        GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Group 1',
            'description' => 'Special description'
        ]);
        GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Group 2',
            'description' => 'Normal description'
        ]);

        $filters = new GroupFilters(['description' => 'Special']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Group 1', $result['data'][0]->name);
        $this->assertStringContainsString('Special', $result['data'][0]->description);
    }

    public function test_fetch_all_with_multiple_filters()
    {
        GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Group',
            'description' => 'Test description'
        ]);
        GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Beta Group',
            'description' => 'Test description'
        ]);
        GroupModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Alpha Group',
            'description' => 'Other description'
        ]);

        $filters = new GroupFilters(['name' => 'Alpha', 'description' => 'Test']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('Alpha Group', $result['data'][0]->name);
        $this->assertEquals('Test description', $result['data'][0]->description);
    }
}
