<?php

namespace Tests\Unit\Repositories;

use App\Domains\Inventory\Item;
use App\Factories\Inventory\ItemFactory;
use App\Models\Item as ItemModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Sale;
use App\Repositories\ItemRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ItemRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ItemRepository $repository;
    private ItemFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ItemRepository::class);
        $this->factory = app(ItemFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test items
        ItemModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $result = $this->repository->fetchAll();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $item) {
            $this->assertInstanceOf(Item::class, $item);
        }
    }

    public function test_fetch_from_organization_returns_organization_specific_items()
    {
        $otherOrganization = Organization::factory()->create();

        // Create items for different organizations
        ItemModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        ItemModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $item) {
            $this->assertEquals($this->organization->id, $item->organization_id);
        }
    }

    public function test_fetch_from_sale_returns_sale_specific_items()
    {
        $sale1 = Sale::factory()->create(['organization_id' => $this->organization->id]);
        $sale2 = Sale::factory()->create(['organization_id' => $this->organization->id]);

        // Create items for different sales
        ItemModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $sale1->id
        ]);
        ItemModel::factory()->count(2)->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $sale2->id
        ]);

        $result = $this->repository->fetchFromSale($sale1->id);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);

        foreach ($result as $item) {
            $this->assertInstanceOf(Item::class, $item);
            $this->assertEquals($sale1->id, $item->sale_id);
        }
    }

    public function test_fetch_by_id_returns_item()
    {
        $model = ItemModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $item = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Item::class, $item);
        $this->assertEquals($model->id, $item->id);
        $this->assertEquals($model->quantity, $item->quantity);
        $this->assertEquals($model->value, $item->value);
        $this->assertEquals($model->organization_id, $item->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_item()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $sale = Sale::factory()->create(['organization_id' => $this->organization->id]);

        $domain = new Item(
            id: null,
            organization_id: $this->organization->id,
            sale_id: $sale->id,
            product_id: $product->id,
            quantity: 10,
            value: 150.50
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Item::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);

        $this->assertDatabaseHas('items', [
            'id' => $result->id,
            'quantity' => 10,
            'value' => 150.50,
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_item()
    {
        $model = ItemModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 5,
            'value' => 75.25
        ]);

        $domain = new Item(
            id: $model->id,
            organization_id: $this->organization->id,
            sale_id: $model->sale_id,
            product_id: $model->product_id,
            quantity: 15,
            value: 225.75
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Item::class, $result);
        $this->assertEquals(15, $result->quantity);
        $this->assertEquals(225.75, $result->value);

        $this->assertDatabaseHas('items', [
            'id' => $model->id,
            'quantity' => 15,
            'value' => 225.75
        ]);
    }

    public function test_delete_removes_item()
    {
        $model = ItemModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('items', [
            'id' => $model->id
        ]);
    }

    public function test_repository_handles_soft_deleted_items()
    {
        $model = ItemModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the item
        $model->delete();

        // Should not find soft deleted item
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $item1 = ItemModel::factory()->create(['organization_id' => $org1->id]);
        $item2 = ItemModel::factory()->create(['organization_id' => $org2->id]);

        // Fetch should only include items from specific organization
        $result1 = $this->repository->fetchFromOrganization($org1->id);
        $result2 = $this->repository->fetchFromOrganization($org2->id);

        $this->assertEquals(1, $result1['count']);
        $this->assertEquals(1, $result2['count']);
    }

    public function test_repository_handles_zero_quantity()
    {
        ItemModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 0
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(0, $result['data'][0]->quantity);
    }

    public function test_repository_handles_high_quantity()
    {
        ItemModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 999999
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(999999, $result['data'][0]->quantity);
    }

    public function test_repository_handles_decimal_values()
    {
        ItemModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 123.456789
        ]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(123.456789, $result['data'][0]->value);
    }

    public function test_repository_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            ItemModel::factory()->create([
                'organization_id' => $this->organization->id,
                'quantity' => $testCase['quantity'],
                'value' => $testCase['value']
            ]);
        }

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(4, $result['count']);

        foreach ($result['data'] as $index => $item) {
            $this->assertEquals($testCases[$index]['quantity'], $item->quantity);
            $this->assertEquals($testCases[$index]['value'], $item->value);
        }
    }

    public function test_fetch_from_sale_with_empty_sale()
    {
        $sale = Sale::factory()->create(['organization_id' => $this->organization->id]);

        $result = $this->repository->fetchFromSale($sale->id);

        $this->assertIsArray($result);
        $this->assertCount(0, $result);
    }

    public function test_fetch_from_sale_with_multiple_items()
    {
        $sale = Sale::factory()->create(['organization_id' => $this->organization->id]);

        ItemModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id,
            'sale_id' => $sale->id
        ]);

        $result = $this->repository->fetchFromSale($sale->id);

        $this->assertIsArray($result);
        $this->assertCount(5, $result);

        foreach ($result as $item) {
            $this->assertInstanceOf(Item::class, $item);
            $this->assertEquals($sale->id, $item->sale_id);
        }
    }
}
