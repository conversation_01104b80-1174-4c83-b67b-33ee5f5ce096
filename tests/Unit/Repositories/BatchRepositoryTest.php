<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\BatchFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Batch;
use App\Factories\Inventory\BatchFactory;
use App\Models\Batch as BatchModel;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Shop;
use App\Repositories\BatchRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BatchRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private BatchRepository $repository;
    private BatchFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(BatchRepository::class);
        $this->factory = app(BatchFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test batches
        BatchModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $batch) {
            $this->assertInstanceOf(Batch::class, $batch);
        }
    }

    public function test_fetch_all_with_filters()
    {
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        // Create batches with different products
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product1->id,
            'batch_number' => 'ALPHA-001'
        ]);
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product2->id,
            'batch_number' => 'BETA-001'
        ]);

        $filters = new BatchFilters(['product_id' => $product1->id]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($product1->id, $result['data'][0]->product_id);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more batches than the limit
        BatchModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 5]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_batches()
    {
        $otherOrganization = Organization::factory()->create();

        // Create batches for different organizations
        BatchModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        BatchModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $batch) {
            $this->assertEquals($this->organization->id, $batch->organization_id);
        }
    }

    public function test_fetch_by_id_returns_batch()
    {
        $model = BatchModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $batch = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Batch::class, $batch);
        $this->assertEquals($model->id, $batch->id);
        $this->assertEquals($model->batch_number, $batch->batch_number);
        $this->assertEquals($model->organization_id, $batch->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_batch()
    {
        $domain = new Batch(
            id: null,
            organization_id: $this->organization->id,
            shop_id: null,
            product_id: 1,
            batch_number: 'STORE-001',
            name: 'Store Test Batch',
            description: 'Test batch for store',
            quantity: 100,
            produced_at: now()->subDays(10),
            expired_at: now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals('STORE-001', $result->batch_number);

        $this->assertDatabaseHas('batches', [
            'id' => $result->id,
            'batch_number' => 'STORE-001',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_batch()
    {
        $model = BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'batch_number' => 'OLD-001',
            'quantity' => 50
        ]);

        $domain = new Batch(
            id: $model->id,
            organization_id: $this->organization->id,
            shop_id: $model->shop_id,
            product_id: $model->product_id,
            batch_number: 'NEW-001',
            name: 'Updated Batch',
            description: 'Updated description',
            quantity: 150,
            produced_at: $model->produced_at ? \Carbon\Carbon::parse($model->produced_at) : null,
            expired_at: $model->expired_at ? \Carbon\Carbon::parse($model->expired_at) : null,
            processed_at: \Carbon\Carbon::now(),
            is_processed_at_stock: true
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Batch::class, $result);
        $this->assertEquals('NEW-001', $result->batch_number);

        $this->assertDatabaseHas('batches', [
            'id' => $model->id,
            'batch_number' => 'NEW-001',
            'quantity' => 150,
            'is_processed_at_stock' => true
        ]);
    }

    public function test_delete_removes_batch()
    {
        $model = BatchModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('batches', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        BatchModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create batches for different organization
        $otherOrg = Organization::factory()->create();
        BatchModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new BatchFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 100
        ]);
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 200
        ]);
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 300
        ]);

        $filters = new BatchFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'quantity');

        $this->assertEquals(600, $sum);
    }

    public function test_repository_handles_soft_deleted_batches()
    {
        $model = BatchModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the batch
        $model->delete();

        // Should not find soft deleted batch
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $batch1 = BatchModel::factory()->create(['organization_id' => $org1->id]);
        $batch2 = BatchModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new BatchFilters([]);

        // Count should only include batches from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_fetch_all_with_product_relationship()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);

        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product->id
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, true);

        $this->assertEquals(1, $result['count']);
        $batch = $result['data'][0];
        $this->assertNotNull($batch->product);
        $this->assertEquals($product->name, $batch->product->name);
    }

    public function test_fetch_all_without_product_relationship()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);

        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product->id
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, false);

        $this->assertEquals(1, $result['count']);
        $batch = $result['data'][0];
        $this->assertNull($batch->product);
    }

    public function test_fetch_all_with_shop_relationship()
    {
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);

        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop->id
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, true, true);

        $this->assertEquals(1, $result['count']);
        $batch = $result['data'][0];
        $this->assertNotNull($batch->shop);
        $this->assertEquals($shop->name, $batch->shop->name);
    }

    public function test_fetch_all_without_shop_relationship()
    {
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);

        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop->id
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy, true, false);

        $this->assertEquals(1, $result['count']);
        $batch = $result['data'][0];
        $this->assertNull($batch->shop);
    }

    public function test_sum_with_filters()
    {
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product1->id,
            'quantity' => 100
        ]);
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product2->id,
            'quantity' => 200
        ]);

        $filters = new BatchFilters(['product_id' => $product1->id]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'quantity');

        $this->assertEquals(100, $sum);
    }

    public function test_repository_handles_different_quantity_ranges()
    {
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 0
        ]);
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 999999
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'quantity', 'by' => 'asc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(0, $result['data'][0]->quantity);
        $this->assertEquals(999999, $result['data'][1]->quantity);
    }

    public function test_repository_handles_null_shop()
    {
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => null
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->shop_id);
    }

    public function test_repository_handles_processed_and_unprocessed_batches()
    {
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'is_processed_at_stock' => true,
            'processed_at' => now()
        ]);
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'is_processed_at_stock' => false,
            'processed_at' => null
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);

        $processedBatch = collect($result['data'])->firstWhere('is_processed_at_stock', true);
        $unprocessedBatch = collect($result['data'])->firstWhere('is_processed_at_stock', false);

        $this->assertNotNull($processedBatch);
        $this->assertNotNull($unprocessedBatch);
        $this->assertTrue($processedBatch->is_processed_at_stock);
        $this->assertFalse($unprocessedBatch->is_processed_at_stock);
    }

    public function test_repository_handles_expired_batches()
    {
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'expired_at' => now()->subDays(10) // Expired
        ]);
        BatchModel::factory()->create([
            'organization_id' => $this->organization->id,
            'expired_at' => now()->addDays(10) // Not expired
        ]);

        $filters = new BatchFilters([]);
        $orderBy = new OrderBy(['order' => 'expired_at', 'by' => 'asc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);

        // First batch should be the expired one (earlier date)
        $this->assertLessThan(now(), $result['data'][0]->expired_at);
        $this->assertGreaterThan(now(), $result['data'][1]->expired_at);
    }
}
