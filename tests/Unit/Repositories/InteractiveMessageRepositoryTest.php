<?php

namespace Tests\Unit\Repositories;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Repositories\InteractiveMessageRepository;
use App\Factories\ChatBot\InteractiveMessageFactory;
use App\Domains\ChatBot\InteractiveMessage;
use App\Domains\ChatBot\Button;
use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\ListRow;
use App\Enums\ChatBot\InteractiveType;
use App\Models\InteractiveMessage as InteractiveMessageModel;
use App\Models\Organization;
use App\Models\Button as ButtonModel;
use App\Models\ListSection as ListSectionModel;
use App\Models\ListRow as ListRowModel;
use App\Domains\Filters\InteractiveMessageFilters;
use App\Domains\Filters\OrderBy;

class InteractiveMessageRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private InteractiveMessageRepository $repository;
    private InteractiveMessageFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->factory = $this->createMock(InteractiveMessageFactory::class);
        $this->repository = new InteractiveMessageRepository($this->factory);
    }

    public function test_store_creates_new_interactive_message()
    {
        // Arrange
        $interactiveMessage = new InteractiveMessage(
            organization_id: $this->organization->id,
            header: 'Test Header',
            body: 'Test Body',
            footer: 'Test Footer',
            type: InteractiveType::BUTTON
        );

        // Act
        $result = $this->repository->store($interactiveMessage);

        // Assert
        $this->assertInstanceOf(InteractiveMessage::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals('Test Header', $result->header);
        $this->assertEquals('Test Body', $result->body);
        $this->assertEquals('Test Footer', $result->footer);
        $this->assertEquals(InteractiveType::BUTTON, $result->type);

        // Verify database record
        $this->assertDatabaseHas('interactive_messages', [
            'id' => $result->id,
            'organization_id' => $this->organization->id,
            'header' => 'Test Header',
            'body' => 'Test Body',
            'footer' => 'Test Footer',
            'type' => 'button'
        ]);
    }

    public function test_store_button_message_with_buttons()
    {
        // Arrange
        $buttons = [
            new Button(null, $this->organization->id, 'Yes', 'reply', null, null, 'yes_callback', '{"action": "yes"}'),
            new Button(null, $this->organization->id, 'No', 'reply', null, null, 'no_callback', '{"action": "no"}'),
        ];

        $interactiveMessage = new InteractiveMessage(
            organization_id: $this->organization->id,
            body: 'Do you agree?',
            type: InteractiveType::BUTTON,
            buttons: $buttons
        );

        // Act
        $result = $this->repository->store($interactiveMessage);

        // Assert
        $this->assertNotNull($result->id);
        $this->assertCount(2, $result->buttons);

        // Verify buttons were created and linked
        $this->assertDatabaseHas('buttons', ['text' => 'Yes']);
        $this->assertDatabaseHas('buttons', ['text' => 'No']);
        $this->assertDatabaseHas('interactive_message_button', [
            'interactive_message_id' => $result->id
        ]);
    }

    public function test_store_list_message_with_sections_and_rows()
    {
        // Arrange
        $rows = [
            new ListRow(null, null, 'option_1', 'Option 1', 'First option'),
            new ListRow(null, null, 'option_2', 'Option 2', 'Second option'),
        ];

        $sections = [
            new ListSection(null, null, 'Main Options', null, null, $rows),
        ];

        $interactiveMessage = new InteractiveMessage(
            organization_id: $this->organization->id,
            body: 'Choose an option',
            type: InteractiveType::LIST,
            button_text: 'View Options',
            sections: $sections
        );

        // Act
        $result = $this->repository->store($interactiveMessage);

        // Assert
        $this->assertNotNull($result->id);
        $this->assertCount(1, $result->sections);
        $this->assertCount(2, $result->sections[0]->rows);

        // Verify database records
        $this->assertDatabaseHas('list_sections', [
            'interactive_message_id' => $result->id,
            'title' => 'Main Options'
        ]);
        $this->assertDatabaseHas('list_rows', ['title' => 'Option 1']);
        $this->assertDatabaseHas('list_rows', ['title' => 'Option 2']);
    }

    public function test_fetch_by_id()
    {
        // Arrange
        $model = InteractiveMessageModel::factory()->create([
            'organization_id' => $this->organization->id,
            'header' => 'Test Header',
            'body' => 'Test Body',
            'type' => 'button'
        ]);

        $expectedDomain = new InteractiveMessage(
            id: $model->id,
            organization_id: $model->organization_id,
            header: $model->header,
            body: $model->body,
            type: InteractiveType::BUTTON
        );

        $this->factory->expects($this->once())
            ->method('buildFromModel')
            ->with($this->anything(), true)
            ->willReturn($expectedDomain);

        // Act
        $result = $this->repository->fetchById($model->id);

        // Assert
        $this->assertInstanceOf(InteractiveMessage::class, $result);
        $this->assertEquals($model->id, $result->id);
    }

    public function test_fetch_by_id_from_organization()
    {
        // Arrange
        $model = InteractiveMessageModel::factory()->create([
            'organization_id' => $this->organization->id,
            'body' => 'Test Body',
            'type' => 'button'
        ]);

        $expectedDomain = new InteractiveMessage(
            id: $model->id,
            organization_id: $model->organization_id,
            body: $model->body,
            type: InteractiveType::BUTTON
        );

        $this->factory->expects($this->once())
            ->method('buildFromModel')
            ->with($this->anything(), true)
            ->willReturn($expectedDomain);

        // Act
        $result = $this->repository->fetchByIdFromOrganization($model->id, $this->organization->id);

        // Assert
        $this->assertInstanceOf(InteractiveMessage::class, $result);
        $this->assertEquals($model->id, $result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
    }

    public function test_update_interactive_message()
    {
        // Arrange
        $model = InteractiveMessageModel::factory()->create([
            'organization_id' => $this->organization->id,
            'header' => 'Original Header',
            'body' => 'Original Body',
            'type' => 'button'
        ]);

        $interactiveMessage = new InteractiveMessage(
            id: $model->id,
            organization_id: $this->organization->id,
            header: 'Updated Header',
            body: 'Updated Body',
            type: InteractiveType::BUTTON
        );

        // Act
        $result = $this->repository->update($interactiveMessage, $this->organization->id);

        // Assert
        $this->assertInstanceOf(InteractiveMessage::class, $result);
        $this->assertEquals('Updated Header', $result->header);
        $this->assertEquals('Updated Body', $result->body);

        // Verify database was updated
        $this->assertDatabaseHas('interactive_messages', [
            'id' => $model->id,
            'header' => 'Updated Header',
            'body' => 'Updated Body'
        ]);
    }

    public function test_delete_interactive_message()
    {
        // Arrange
        $model = InteractiveMessageModel::factory()->create([
            'organization_id' => $this->organization->id,
            'body' => 'Test Body',
            'type' => 'button'
        ]);

        $interactiveMessage = new InteractiveMessage(
            id: $model->id,
            organization_id: $this->organization->id,
            body: 'Test Body',
            type: InteractiveType::BUTTON
        );

        // Act
        $result = $this->repository->delete($interactiveMessage);

        // Assert
        $this->assertTrue($result);
        $this->assertDatabaseMissing('interactive_messages', ['id' => $model->id]);
    }

    public function test_fetch_by_type()
    {
        // Arrange
        $buttonMessage = InteractiveMessageModel::factory()->create([
            'organization_id' => $this->organization->id,
            'body' => 'Button Message',
            'type' => 'button'
        ]);

        $listMessage = InteractiveMessageModel::factory()->create([
            'organization_id' => $this->organization->id,
            'body' => 'List Message',
            'type' => 'list'
        ]);

        $expectedDomains = [
            new InteractiveMessage(id: $buttonMessage->id, type: InteractiveType::BUTTON)
        ];

        $this->factory->expects($this->once())
            ->method('buildFromModel')
            ->willReturn($expectedDomains[0]);

        // Act
        $result = $this->repository->fetchByType('button', $this->organization->id, 10);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals(InteractiveType::BUTTON, $result[0]->type);
    }

    public function test_count()
    {
        // Arrange
        InteractiveMessageModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'type' => 'button'
        ]);

        $filters = new InteractiveMessageFilters([]);

        // Act
        $count = $this->repository->count($this->organization->id, $filters);

        // Assert
        $this->assertEquals(3, $count);
    }

    public function test_save_calls_store_for_new_message()
    {
        // Arrange
        $interactiveMessage = new InteractiveMessage(
            organization_id: $this->organization->id,
            body: 'Test Body',
            type: InteractiveType::BUTTON
        );

        // Act
        $result = $this->repository->save($interactiveMessage, $this->organization->id);

        // Assert
        $this->assertInstanceOf(InteractiveMessage::class, $result);
        $this->assertNotNull($result->id);
    }

    public function test_save_calls_update_for_existing_message()
    {
        // Arrange
        $model = InteractiveMessageModel::factory()->create([
            'organization_id' => $this->organization->id,
            'body' => 'Original Body',
            'type' => 'button'
        ]);

        $interactiveMessage = new InteractiveMessage(
            id: $model->id,
            organization_id: $this->organization->id,
            body: 'Updated Body',
            type: InteractiveType::BUTTON
        );

        // Act
        $result = $this->repository->save($interactiveMessage, $this->organization->id);

        // Assert
        $this->assertInstanceOf(InteractiveMessage::class, $result);
        $this->assertEquals($model->id, $result->id);
        $this->assertEquals('Updated Body', $result->body);
    }
}
