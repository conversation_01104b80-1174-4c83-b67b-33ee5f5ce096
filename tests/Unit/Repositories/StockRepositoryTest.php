<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\StockFilters;
use App\Domains\Inventory\Stock;
use App\Factories\Inventory\StockFactory;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Shop;
use App\Models\Stock as StockModel;
use App\Repositories\StockRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StockRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private StockRepository $repository;
    private StockFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(StockRepository::class);
        $this->factory = app(StockFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test stocks
        StockModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new StockFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $stock) {
            $this->assertInstanceOf(Stock::class, $stock);
        }
    }

    public function test_fetch_all_with_filters()
    {
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        // Create stocks with different products
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product1->id,
            'quantity' => 100
        ]);
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product2->id,
            'quantity' => 50
        ]);

        $filters = new StockFilters(['product_id' => $product1->id]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($product1->id, $result['data'][0]->product_id);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more stocks than the limit
        StockModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new StockFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 5);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_stocks()
    {
        $otherOrganization = Organization::factory()->create();

        // Create stocks for different organizations
        StockModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        StockModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id
        ]);

        $filters = new StockFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $stock) {
            $this->assertEquals($this->organization->id, $stock->organization_id);
        }
    }

    public function test_fetch_by_id_returns_stock()
    {
        $model = StockModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $stock = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Stock::class, $stock);
        $this->assertEquals($model->id, $stock->id);
        $this->assertEquals($model->quantity, $stock->quantity);
        $this->assertEquals($model->organization_id, $stock->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_stock()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);

        $domain = new Stock(
            id: null,
            organization_id: $this->organization->id,
            shop_id: null,
            brand_id: null,
            product_id: $product->id,
            quantity: 100,
            value: 1000.0,
            description: 'Test stock'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($domain->quantity, $result->quantity);
        $this->assertEquals($domain->organization_id, $result->organization_id);

        // Verify it was actually saved to database
        $this->assertDatabaseHas('stocks', [
            'id' => $result->id,
            'quantity' => 100,
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_stock()
    {
        $model = StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 50
        ]);

        $domain = new Stock(
            id: $model->id,
            organization_id: $this->organization->id,
            shop_id: $model->shop_id,
            brand_id: $model->brand_id,
            product_id: $model->product_id,
            quantity: 150,
            value: 1500.0,
            description: 'Updated stock'
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals($model->id, $result->id);
        $this->assertEquals(150, $result->quantity);

        // Verify it was actually updated in database
        $this->assertDatabaseHas('stocks', [
            'id' => $model->id,
            'quantity' => 150,
            'description' => 'Updated stock'
        ]);
    }

    public function test_delete_removes_stock()
    {
        $model = StockModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('stocks', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        StockModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create stocks for different organization
        $otherOrg = Organization::factory()->create();
        StockModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new StockFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 100.00
        ]);
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 200.00
        ]);
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 300.00
        ]);

        $filters = new StockFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'value');

        $this->assertEquals(600.00, $sum);
    }

    public function test_find_by_product_and_organization()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);
        $stock = StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product->id
        ]);

        $result = $this->repository->findByProductAndOrganization($product->id, $this->organization->id);

        $this->assertInstanceOf(Stock::class, $result);
        $this->assertEquals($stock->id, $result->id);
        $this->assertEquals($product->id, $result->product_id);
    }

    public function test_find_by_product_and_organization_returns_null_when_not_found()
    {
        $result = $this->repository->findByProductAndOrganization(999, $this->organization->id);

        $this->assertNull($result);
    }

    public function test_save_creates_or_updates_stock()
    {
        $product = Product::factory()->create(['organization_id' => $this->organization->id]);

        // Test create
        $newDomain = new Stock(
            id: null,
            organization_id: $this->organization->id,
            shop_id: null,
            brand_id: null,
            product_id: $product->id,
            quantity: 75,
            value: 750.0,
            description: 'New stock'
        );

        $savedNew = $this->repository->save($newDomain);

        $this->assertInstanceOf(Stock::class, $savedNew);
        $this->assertNotNull($savedNew->id);
        $this->assertEquals(75, $savedNew->quantity);

        // Test update
        $updateDomain = new Stock(
            id: $savedNew->id,
            organization_id: $this->organization->id,
            shop_id: null,
            brand_id: null,
            product_id: $product->id,
            quantity: 125,
            value: 1250.0,
            description: 'Updated stock'
        );

        $savedUpdated = $this->repository->save($updateDomain);

        $this->assertInstanceOf(Stock::class, $savedUpdated);
        $this->assertEquals($savedNew->id, $savedUpdated->id);
        $this->assertEquals(125, $savedUpdated->quantity);
    }

    public function test_repository_handles_soft_deleted_stocks()
    {
        $model = StockModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the stock
        $model->delete();

        // Should not find soft deleted stock
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $stock1 = StockModel::factory()->create(['organization_id' => $org1->id]);
        $stock2 = StockModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new StockFilters([]);

        // Count should only include stocks from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_fetch_all_with_shop_relationship()
    {
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);

        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop->id
        ]);

        $filters = new StockFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy, true);

        $this->assertEquals(1, $result['count']);
        $stock = $result['data'][0];
        $this->assertNotNull($stock->shop);
        $this->assertEquals($shop->name, $stock->shop->name);
    }

    public function test_fetch_all_without_shop_relationship()
    {
        $shop = Shop::factory()->create(['organization_id' => $this->organization->id]);

        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => $shop->id
        ]);

        $filters = new StockFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy, false);

        $this->assertEquals(1, $result['count']);
        $stock = $result['data'][0];
        $this->assertNull($stock->shop);
    }

    public function test_sum_with_filters()
    {
        $product1 = Product::factory()->create(['organization_id' => $this->organization->id]);
        $product2 = Product::factory()->create(['organization_id' => $this->organization->id]);

        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product1->id,
            'value' => 100.00
        ]);
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'product_id' => $product2->id,
            'value' => 200.00
        ]);

        $filters = new StockFilters(['product_id' => $product1->id]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'value');

        $this->assertEquals(100.00, $sum);
    }

    public function test_find_by_product_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $product1 = Product::factory()->create(['organization_id' => $org1->id]);
        $product2 = Product::factory()->create(['organization_id' => $org2->id]);

        StockModel::factory()->create([
            'organization_id' => $org1->id,
            'product_id' => $product1->id
        ]);
        StockModel::factory()->create([
            'organization_id' => $org2->id,
            'product_id' => $product2->id
        ]);

        $result1 = $this->repository->findByProductAndOrganization($product1->id, $org1->id);
        $result2 = $this->repository->findByProductAndOrganization($product2->id, $org2->id);

        $this->assertInstanceOf(Stock::class, $result1);
        $this->assertInstanceOf(Stock::class, $result2);
        $this->assertEquals($org1->id, $result1->organization_id);
        $this->assertEquals($org2->id, $result2->organization_id);
        $this->assertNotEquals($result1->id, $result2->id);
    }

    public function test_repository_handles_different_quantity_ranges()
    {
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 0
        ]);
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'quantity' => 999999
        ]);

        $filters = new StockFilters([]);
        $orderBy = new OrderBy('quantity', 'asc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(0, $result['data'][0]->quantity);
        $this->assertEquals(999999, $result['data'][1]->quantity);
    }

    public function test_repository_handles_different_value_ranges()
    {
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 0.0
        ]);
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'value' => 999999.99
        ]);

        $filters = new StockFilters([]);
        $orderBy = new OrderBy('value', 'asc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(0.0, $result['data'][0]->value);
        $this->assertEquals(999999.99, $result['data'][1]->value);
    }

    public function test_repository_handles_null_shop_and_brand()
    {
        StockModel::factory()->create([
            'organization_id' => $this->organization->id,
            'shop_id' => null,
            'brand_id' => null
        ]);

        $filters = new StockFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->shop_id);
        $this->assertNull($result['data'][0]->brand_id);
    }
}
