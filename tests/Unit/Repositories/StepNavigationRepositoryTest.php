<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\StepNavigationFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\ChatBot\StepNavigation;
use App\Factories\ChatBot\StepNavigationFactory;
use App\Models\StepNavigation as StepNavigationModel;
use App\Models\Organization;
use App\Models\Step;
use App\Repositories\StepNavigationRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class StepNavigationRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private StepNavigationRepository $repository;
    private StepNavigationFactory $factory;
    private Organization $organization;
    private Step $step;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(StepNavigationRepository::class);
        $this->factory = app(StepNavigationFactory::class);
        $this->organization = Organization::factory()->create();
        $this->step = Step::factory()->create([
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test step navigations
        StepNavigationModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id
        ]);

        $filters = new StepNavigationFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);

        foreach ($result['data'] as $stepNavigation) {
            $this->assertInstanceOf(StepNavigation::class, $stepNavigation);
        }
    }

    public function test_fetch_from_organization_filters_by_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherStep = Step::factory()->create(['organization_id' => $otherOrganization->id]);

        // Create step navigations for our organization
        StepNavigationModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id
        ]);

        // Create step navigations for other organization
        StepNavigationModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id,
            'step_id' => $otherStep->id
        ]);

        $filters = new StepNavigationFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertCount(3, $result['data']);

        foreach ($result['data'] as $stepNavigation) {
            $this->assertEquals($this->organization->id, $stepNavigation->organization_id);
        }
    }

    public function test_fetch_by_step_id_returns_ordered_rules()
    {
        // Create step navigations with different priorities
        StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'priority' => 2,
            'condition_type' => 'button_click',
            'is_active' => true
        ]);

        StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'priority' => 0,
            'condition_type' => 'text_match',
            'is_active' => true
        ]);

        StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'priority' => 1,
            'condition_type' => 'regex',
            'is_active' => true
        ]);

        $result = $this->repository->fetchByStepId($this->step->id);

        $this->assertCount(3, $result);
        $this->assertEquals(0, $result[0]->priority); // Highest priority first
        $this->assertEquals(1, $result[1]->priority);
        $this->assertEquals(2, $result[2]->priority);
    }

    public function test_fetch_by_step_id_filters_inactive_rules()
    {
        // Create active rule
        StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'is_active' => true
        ]);

        // Create inactive rule
        StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'is_active' => false
        ]);

        $activeOnly = $this->repository->fetchByStepId($this->step->id, true);
        $allRules = $this->repository->fetchByStepId($this->step->id, false);

        $this->assertCount(1, $activeOnly);
        $this->assertCount(2, $allRules);
    }

    public function test_store_creates_new_step_navigation()
    {
        $domain = StepNavigation::createButtonClickRule(
            $this->organization->id,
            $this->step->id,
            'test_button',
            'next_step',
            0
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(StepNavigation::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($this->organization->id, $result->organization_id);
        $this->assertEquals($this->step->id, $result->step_id);

        $this->assertDatabaseHas('step_navigations', [
            'id' => $result->id,
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'condition_type' => 'button_click',
            'target_step_identifier' => 'next_step'
        ]);
    }

    public function test_update_modifies_existing_step_navigation()
    {
        $model = StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'condition_type' => 'button_click',
            'target_step_identifier' => 'old_step'
        ]);

        $domain = $this->factory->buildFromModel($model);
        $domain->target_step_identifier = 'new_step';
        $domain->priority = 5;

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertEquals('new_step', $result->target_step_identifier);
        $this->assertEquals(5, $result->priority);

        $this->assertDatabaseHas('step_navigations', [
            'id' => $model->id,
            'target_step_identifier' => 'new_step',
            'priority' => 5
        ]);
    }

    public function test_delete_removes_step_navigation()
    {
        $model = StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id
        ]);

        $result = $this->repository->delete($model->id, $this->organization->id);

        $this->assertTrue($result);
        $this->assertSoftDeleted('step_navigations', ['id' => $model->id]);
    }

    public function test_find_by_id_returns_domain()
    {
        $model = StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id
        ]);

        $result = $this->repository->findById($model->id, $this->organization->id);

        $this->assertInstanceOf(StepNavigation::class, $result);
        $this->assertEquals($model->id, $result->id);
    }

    public function test_find_matching_rule_returns_first_match()
    {
        // Create rules with different priorities
        StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'condition_type' => 'text_match',
            'condition_data' => ['text' => 'yes'],
            'priority' => 1,
            'is_active' => true
        ]);

        StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'condition_type' => 'default',
            'condition_data' => null,
            'priority' => 999,
            'is_active' => true
        ]);

        $result = $this->repository->findMatchingRule($this->step->id, 'yes');

        $this->assertInstanceOf(StepNavigation::class, $result);
        $this->assertEquals('text_match', $result->condition_type);
    }

    public function test_replace_for_step_removes_old_and_adds_new()
    {
        // Create existing rules
        StepNavigationModel::factory()->count(2)->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id
        ]);

        $newRules = [
            StepNavigation::createButtonClickRule(
                $this->organization->id,
                $this->step->id,
                'btn1',
                'step1',
                0
            ),
            StepNavigation::createTextMatchRule(
                $this->organization->id,
                $this->step->id,
                'yes',
                'step2',
                1
            )
        ];

        $result = $this->repository->replaceForStep($this->step->id, $this->organization->id, $newRules);

        $this->assertCount(2, $result);

        // Check that old rules are deleted and new ones exist
        $currentRules = $this->repository->fetchByStepId($this->step->id);
        $this->assertCount(2, $currentRules);
    }

    public function test_toggle_active_changes_status()
    {
        $model = StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'is_active' => true
        ]);

        $result = $this->repository->toggleActive($model->id, $this->organization->id, false);

        $this->assertTrue($result);
        $this->assertDatabaseHas('step_navigations', [
            'id' => $model->id,
            'is_active' => false
        ]);
    }

    public function test_update_priority_changes_priority()
    {
        $model = StepNavigationModel::factory()->create([
            'organization_id' => $this->organization->id,
            'step_id' => $this->step->id,
            'priority' => 0
        ]);

        $result = $this->repository->updatePriority($model->id, $this->organization->id, 10);

        $this->assertTrue($result);
        $this->assertDatabaseHas('step_navigations', [
            'id' => $model->id,
            'priority' => 10
        ]);
    }
}
