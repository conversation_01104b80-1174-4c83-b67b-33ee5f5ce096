<?php

namespace Tests\Unit\Repositories;

use App\Domains\Inventory\ProductHistory;
use App\Factories\Inventory\ProductHistoryFactory;
use App\Models\ProductHistory as ProductHistoryModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use App\Repositories\ProductHistoryRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductHistoryRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ProductHistoryRepository $repository;
    private ProductHistoryFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ProductHistoryRepository::class);
        $this->factory = app(ProductHistoryFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test product histories
        ProductHistoryModel::factory()->count(5)->create();

        $result = $this->repository->fetchAll();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $productHistory) {
            $this->assertInstanceOf(ProductHistory::class, $productHistory);
        }
    }

    public function test_fetch_from_organization_returns_organization_specific_histories()
    {
        $otherOrganization = Organization::factory()->create();

        // Create products for different organizations
        $brand1 = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $brand2 = Brand::factory()->create(['organization_id' => $otherOrganization->id]);
        
        $product1 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand1->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $otherOrganization->id,
            'brand_id' => $brand2->id
        ]);

        // Create product histories for different organizations via products
        ProductHistoryModel::factory()->count(3)->create(['product_id' => $product1->id]);
        ProductHistoryModel::factory()->count(2)->create(['product_id' => $product2->id]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $productHistory) {
            $this->assertInstanceOf(ProductHistory::class, $productHistory);
        }
    }

    public function test_fetch_by_id_returns_product_history()
    {
        $model = ProductHistoryModel::factory()->create();

        $productHistory = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(ProductHistory::class, $productHistory);
        $this->assertEquals($model->id, $productHistory->id);
        $this->assertEquals($model->field, $productHistory->field);
        $this->assertEquals($model->alias, $productHistory->alias);
        $this->assertEquals($model->old, $productHistory->old);
        $this->assertEquals($model->new, $productHistory->new);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_product_history()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);

        $domain = new ProductHistory(
            id: null,
            user_id: $user->id,
            product_id: $product->id,
            field: 'price',
            alias: 'Preço',
            old: '10.50',
            new: '15.75'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(ProductHistory::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals('price', $result->field);
        $this->assertEquals('Preço', $result->alias);
        $this->assertEquals('10.50', $result->old);
        $this->assertEquals('15.75', $result->new);

        $this->assertDatabaseHas('products_histories', [
            'id' => $result->id,
            'field' => 'price',
            'alias' => 'Preço',
            'old' => '10.50',
            'new' => '15.75'
        ]);
    }

    public function test_delete_removes_product_history()
    {
        $model = ProductHistoryModel::factory()->create();

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        $this->assertDatabaseMissing('products_histories', [
            'id' => $model->id
        ]);
    }

    public function test_repository_handles_different_fields()
    {
        $fields = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Old Name', 'new' => 'New Name'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '10.50', 'new' => '15.75'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Old Description', 'new' => 'New Description'],
            ['field' => 'barcode', 'alias' => 'Código de Barras', 'old' => '1234567890', 'new' => '0987654321'],
            ['field' => 'unity', 'alias' => 'Unidade', 'old' => '1', 'new' => '2'],
        ];

        foreach ($fields as $fieldData) {
            ProductHistoryModel::factory()->create($fieldData);
        }

        $result = $this->repository->fetchAll();

        $this->assertEquals(5, $result['count']);

        foreach ($result['data'] as $index => $productHistory) {
            $this->assertEquals($fields[$index]['field'], $productHistory->field);
            $this->assertEquals($fields[$index]['alias'], $productHistory->alias);
            $this->assertEquals($fields[$index]['old'], $productHistory->old);
            $this->assertEquals($fields[$index]['new'], $productHistory->new);
        }
    }

    public function test_repository_handles_price_changes()
    {
        $priceChanges = [
            ['old' => '0.00', 'new' => '10.50'],
            ['old' => '10.50', 'new' => '15.75'],
            ['old' => '15.75', 'new' => '20.00'],
            ['old' => '20.00', 'new' => '0.00'],
            ['old' => '100.99', 'new' => '999.99'],
        ];

        foreach ($priceChanges as $priceChange) {
            ProductHistoryModel::factory()->create([
                'field' => 'price',
                'alias' => 'Preço',
                'old' => $priceChange['old'],
                'new' => $priceChange['new']
            ]);
        }

        $result = $this->repository->fetchAll();

        $this->assertEquals(5, $result['count']);

        foreach ($result['data'] as $index => $productHistory) {
            $this->assertEquals($priceChanges[$index]['old'], $productHistory->old);
            $this->assertEquals($priceChanges[$index]['new'], $productHistory->new);
        }
    }

    public function test_repository_handles_long_values()
    {
        $longOldValue = str_repeat('This is a very long old value. ', 10);
        $longNewValue = str_repeat('This is a very long new value. ', 10);
        
        ProductHistoryModel::factory()->create([
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => $longOldValue,
            'new' => $longNewValue
        ]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($longOldValue, $result['data'][0]->old);
        $this->assertEquals($longNewValue, $result['data'][0]->new);
    }

    public function test_repository_handles_null_values()
    {
        ProductHistoryModel::factory()->create([
            'field' => 'description',
            'alias' => 'Descrição',
            'old' => '',
            'new' => ''
        ]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('', $result['data'][0]->old);
        $this->assertEquals('', $result['data'][0]->new);
    }

    public function test_fetch_from_organization_with_multiple_products()
    {
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $product1 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);

        // Create product histories via different products
        ProductHistoryModel::factory()->count(2)->create(['product_id' => $product1->id]);
        ProductHistoryModel::factory()->count(3)->create(['product_id' => $product2->id]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $brand1 = Brand::factory()->create(['organization_id' => $org1->id]);
        $brand2 = Brand::factory()->create(['organization_id' => $org2->id]);

        $product1 = Product::factory()->create([
            'organization_id' => $org1->id,
            'brand_id' => $brand1->id
        ]);
        $product2 = Product::factory()->create([
            'organization_id' => $org2->id,
            'brand_id' => $brand2->id
        ]);

        $productHistory1 = ProductHistoryModel::factory()->create(['product_id' => $product1->id]);
        $productHistory2 = ProductHistoryModel::factory()->create(['product_id' => $product2->id]);

        // Fetch should only include product histories from specific organization
        $result1 = $this->repository->fetchFromOrganization($org1->id);
        $result2 = $this->repository->fetchFromOrganization($org2->id);

        $this->assertEquals(1, $result1['count']);
        $this->assertEquals(1, $result2['count']);
    }

    public function test_repository_with_different_users()
    {
        $user1 = User::factory()->create(['organization_id' => $this->organization->id]);
        $user2 = User::factory()->create(['organization_id' => $this->organization->id]);

        ProductHistoryModel::factory()->create(['user_id' => $user1->id]);
        ProductHistoryModel::factory()->create(['user_id' => $user2->id]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(2, $result['count']);

        $userIds = collect($result['data'])->pluck('user_id')->toArray();
        $this->assertContains($user1->id, $userIds);
        $this->assertContains($user2->id, $userIds);
    }

    public function test_store_with_different_field_types()
    {
        $user = User::factory()->create(['organization_id' => $this->organization->id]);
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);

        $fields = [
            ['field' => 'name', 'alias' => 'Nome', 'old' => 'Old Name', 'new' => 'New Name'],
            ['field' => 'price', 'alias' => 'Preço', 'old' => '10.50', 'new' => '15.75'],
            ['field' => 'description', 'alias' => 'Descrição', 'old' => 'Old Description', 'new' => 'New Description'],
        ];

        foreach ($fields as $fieldData) {
            $domain = new ProductHistory(
                id: null,
                user_id: $user->id,
                product_id: $product->id,
                field: $fieldData['field'],
                alias: $fieldData['alias'],
                old: $fieldData['old'],
                new: $fieldData['new']
            );

            $result = $this->repository->store($domain);

            $this->assertInstanceOf(ProductHistory::class, $result);
            $this->assertEquals($fieldData['field'], $result->field);
            $this->assertEquals($fieldData['alias'], $result->alias);
            $this->assertEquals($fieldData['old'], $result->old);
            $this->assertEquals($fieldData['new'], $result->new);

            $this->assertDatabaseHas('products_histories', [
                'field' => $fieldData['field'],
                'alias' => $fieldData['alias'],
                'old' => $fieldData['old'],
                'new' => $fieldData['new']
            ]);
        }
    }
}
