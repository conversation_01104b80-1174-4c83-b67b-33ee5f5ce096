<?php

namespace Tests\Unit\Repositories;

use App\Domains\Inventory\CustomProduct;
use App\Factories\Inventory\CustomProductFactory;
use App\Models\CustomProduct as CustomProductModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Project;
use App\Models\Budget;
use App\Models\Client;
use App\Repositories\CustomProductRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CustomProductRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private CustomProductRepository $repository;
    private CustomProductFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(CustomProductRepository::class);
        $this->factory = app(CustomProductFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test custom products
        CustomProductModel::factory()->count(5)->create();

        $result = $this->repository->fetchAll();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $customProduct) {
            $this->assertInstanceOf(CustomProduct::class, $customProduct);
        }
    }

    public function test_fetch_from_organization_returns_organization_specific_custom_products()
    {
        $otherOrganization = Organization::factory()->create();

        // Create projects for different organizations
        $project1 = Project::factory()->create(['organization_id' => $this->organization->id]);
        $project2 = Project::factory()->create(['organization_id' => $otherOrganization->id]);

        // Create custom products for different organizations via projects
        CustomProductModel::factory()->count(3)->create(['project_id' => $project1->id]);
        CustomProductModel::factory()->count(2)->create(['project_id' => $project2->id]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $customProduct) {
            $this->assertInstanceOf(CustomProduct::class, $customProduct);
        }
    }

    public function test_fetch_from_organization_via_budget()
    {
        $otherOrganization = Organization::factory()->create();

        // Create budgets for different organizations
        $budget1 = Budget::factory()->create(['organization_id' => $this->organization->id]);
        $budget2 = Budget::factory()->create(['organization_id' => $otherOrganization->id]);

        // Create custom products for different organizations via budgets
        CustomProductModel::factory()->count(2)->create(['budget_id' => $budget1->id]);
        CustomProductModel::factory()->count(3)->create(['budget_id' => $budget2->id]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(2, $result['count']);
        $this->assertEquals(2, $result['total']);
    }

    public function test_fetch_from_custom_returns_project_specific_products()
    {
        $project1 = Project::factory()->create(['organization_id' => $this->organization->id]);
        $project2 = Project::factory()->create(['organization_id' => $this->organization->id]);

        // Create custom products for different projects
        CustomProductModel::factory()->count(3)->create(['project_id' => $project1->id]);
        CustomProductModel::factory()->count(2)->create(['project_id' => $project2->id]);

        $result = $this->repository->fetchFromCustom($project1->id);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);

        foreach ($result as $customProduct) {
            $this->assertInstanceOf(CustomProduct::class, $customProduct);
            $this->assertEquals($project1->id, $customProduct->project_id);
        }
    }

    public function test_fetch_by_id_returns_custom_product()
    {
        $model = CustomProductModel::factory()->create();

        $customProduct = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(CustomProduct::class, $customProduct);
        $this->assertEquals($model->id, $customProduct->id);
        $this->assertEquals($model->quantity, $customProduct->quantity);
        $this->assertEquals($model->value, $customProduct->value);
        $this->assertEquals($model->description, $customProduct->description);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_custom_product()
    {
        $project = Project::factory()->create(['organization_id' => $this->organization->id]);
        $budget = Budget::factory()->create(['organization_id' => $this->organization->id]);

        $domain = new CustomProduct(
            id: null,
            project_id: $project->id,
            budget_id: $budget->id,
            quantity: 10,
            value: 150.50,
            description: 'Test custom product'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertEquals('Test custom product', $result->description);

        $this->assertDatabaseHas('custom_products', [
            'id' => $result->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Test custom product'
        ]);
    }

    public function test_update_modifies_existing_custom_product()
    {
        $model = CustomProductModel::factory()->create([
            'quantity' => 5,
            'value' => 75.25,
            'description' => 'Original description'
        ]);

        $domain = new CustomProduct(
            id: $model->id,
            project_id: $model->project_id,
            budget_id: $model->budget_id,
            quantity: 15,
            value: 225.75,
            description: 'Updated description'
        );

        $result = $this->repository->update($domain);

        $this->assertInstanceOf(CustomProduct::class, $result);
        $this->assertEquals(15, $result->quantity);
        $this->assertEquals(225.75, $result->value);
        $this->assertEquals('Updated description', $result->description);

        $this->assertDatabaseHas('custom_products', [
            'id' => $model->id,
            'quantity' => 15,
            'value' => 225.75,
            'description' => 'Updated description'
        ]);
    }

    public function test_delete_removes_custom_product()
    {
        $model = CustomProductModel::factory()->create();

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        $this->assertDatabaseMissing('custom_products', [
            'id' => $model->id
        ]);
    }

    public function test_repository_handles_zero_quantity()
    {
        CustomProductModel::factory()->create(['quantity' => 0]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(0, $result['data'][0]->quantity);
    }

    public function test_repository_handles_high_quantity()
    {
        CustomProductModel::factory()->create(['quantity' => 999999]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(999999, $result['data'][0]->quantity);
    }

    public function test_repository_handles_decimal_values()
    {
        CustomProductModel::factory()->create(['value' => 123.456789]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(123.456789, $result['data'][0]->value);
    }

    public function test_repository_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            CustomProductModel::factory()->create($testCase);
        }

        $result = $this->repository->fetchAll();

        $this->assertEquals(4, $result['count']);

        foreach ($result['data'] as $index => $customProduct) {
            $this->assertEquals($testCases[$index]['quantity'], $customProduct->quantity);
            $this->assertEquals($testCases[$index]['value'], $customProduct->value);
        }
    }

    public function test_fetch_from_custom_with_empty_project()
    {
        $project = Project::factory()->create(['organization_id' => $this->organization->id]);

        $result = $this->repository->fetchFromCustom($project->id);

        $this->assertIsArray($result);
        $this->assertCount(0, $result);
    }

    public function test_fetch_from_custom_with_multiple_products()
    {
        $project = Project::factory()->create(['organization_id' => $this->organization->id]);

        CustomProductModel::factory()->count(5)->create(['project_id' => $project->id]);

        $result = $this->repository->fetchFromCustom($project->id);

        $this->assertIsArray($result);
        $this->assertCount(5, $result);

        foreach ($result as $customProduct) {
            $this->assertInstanceOf(CustomProduct::class, $customProduct);
            $this->assertEquals($project->id, $customProduct->project_id);
        }
    }

    public function test_repository_with_long_descriptions()
    {
        $longDescription = str_repeat('Long description text. ', 20);
        CustomProductModel::factory()->create(['description' => $longDescription]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($longDescription, $result['data'][0]->description);
    }

    public function test_fetch_from_organization_with_mixed_project_and_budget_products()
    {
        $project = Project::factory()->create(['organization_id' => $this->organization->id]);
        $budget = Budget::factory()->create(['organization_id' => $this->organization->id]);

        // Create custom products via project
        CustomProductModel::factory()->count(2)->create(['project_id' => $project->id]);
        
        // Create custom products via budget
        CustomProductModel::factory()->count(3)->create(['budget_id' => $budget->id]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);
    }
}
