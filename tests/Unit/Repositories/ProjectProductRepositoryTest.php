<?php

namespace Tests\Unit\Repositories;

use App\Domains\Inventory\ProjectProduct;
use App\Factories\Inventory\ProjectProductFactory;
use App\Models\ProjectProduct as ProjectProductModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Project;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Client;
use App\Repositories\ProjectProductRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProjectProductRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ProjectProductRepository $repository;
    private ProjectProductFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ProjectProductRepository::class);
        $this->factory = app(ProjectProductFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test project products
        ProjectProductModel::factory()->count(5)->create();

        $result = $this->repository->fetchAll();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $projectProduct) {
            $this->assertInstanceOf(ProjectProduct::class, $projectProduct);
        }
    }

    public function test_fetch_from_organization_returns_organization_specific_project_products()
    {
        $otherOrganization = Organization::factory()->create();

        // Create projects for different organizations
        $project1 = Project::factory()->create(['organization_id' => $this->organization->id]);
        $project2 = Project::factory()->create(['organization_id' => $otherOrganization->id]);

        // Create project products for different organizations via projects
        ProjectProductModel::factory()->count(3)->create(['project_id' => $project1->id]);
        ProjectProductModel::factory()->count(2)->create(['project_id' => $project2->id]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $projectProduct) {
            $this->assertInstanceOf(ProjectProduct::class, $projectProduct);
        }
    }

    public function test_fetch_from_project_returns_project_specific_products()
    {
        $project1 = Project::factory()->create(['organization_id' => $this->organization->id]);
        $project2 = Project::factory()->create(['organization_id' => $this->organization->id]);

        // Create project products for different projects
        ProjectProductModel::factory()->count(3)->create(['project_id' => $project1->id]);
        ProjectProductModel::factory()->count(2)->create(['project_id' => $project2->id]);

        $result = $this->repository->fetchFromProject($project1->id);

        $this->assertIsArray($result);
        $this->assertCount(3, $result);

        foreach ($result as $projectProduct) {
            $this->assertInstanceOf(ProjectProduct::class, $projectProduct);
            $this->assertEquals($project1->id, $projectProduct->project_id);
        }
    }

    public function test_fetch_by_id_returns_project_product()
    {
        $model = ProjectProductModel::factory()->create();

        $projectProduct = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(ProjectProduct::class, $projectProduct);
        $this->assertEquals($model->id, $projectProduct->id);
        $this->assertEquals($model->quantity, $projectProduct->quantity);
        $this->assertEquals($model->value, $projectProduct->value);
        $this->assertEquals($model->description, $projectProduct->description);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_project_product()
    {
        $project = Project::factory()->create(['organization_id' => $this->organization->id]);
        $brand = Brand::factory()->create(['organization_id' => $this->organization->id]);
        $product = Product::factory()->create([
            'organization_id' => $this->organization->id,
            'brand_id' => $brand->id
        ]);

        $domain = new ProjectProduct(
            id: null,
            project_id: $project->id,
            product_id: $product->id,
            quantity: 10,
            value: 150.50,
            description: 'Test project product'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(10, $result->quantity);
        $this->assertEquals(150.50, $result->value);
        $this->assertEquals('Test project product', $result->description);

        $this->assertDatabaseHas('project_products', [
            'id' => $result->id,
            'quantity' => 10,
            'value' => 150.50,
            'description' => 'Test project product'
        ]);
    }

    public function test_delete_removes_project_product()
    {
        $model = ProjectProductModel::factory()->create();

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        $this->assertDatabaseMissing('project_products', [
            'id' => $model->id
        ]);
    }

    public function test_repository_handles_zero_quantity()
    {
        ProjectProductModel::factory()->create(['quantity' => 0]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(0, $result['data'][0]->quantity);
    }

    public function test_repository_handles_high_quantity()
    {
        ProjectProductModel::factory()->create(['quantity' => 999999]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(999999, $result['data'][0]->quantity);
    }

    public function test_repository_handles_decimal_values()
    {
        ProjectProductModel::factory()->create(['value' => 123.456789]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(123.456789, $result['data'][0]->value);
    }

    public function test_repository_with_various_quantities_and_values()
    {
        $testCases = [
            ['quantity' => 1, 'value' => 10.50],
            ['quantity' => 5, 'value' => 52.75],
            ['quantity' => 100, 'value' => 1050.00],
            ['quantity' => 999, 'value' => 9999.99],
        ];

        foreach ($testCases as $testCase) {
            ProjectProductModel::factory()->create($testCase);
        }

        $result = $this->repository->fetchAll();

        $this->assertEquals(4, $result['count']);

        foreach ($result['data'] as $index => $projectProduct) {
            $this->assertEquals($testCases[$index]['quantity'], $projectProduct->quantity);
            $this->assertEquals($testCases[$index]['value'], $projectProduct->value);
        }
    }

    public function test_fetch_from_project_with_empty_project()
    {
        $project = Project::factory()->create(['organization_id' => $this->organization->id]);

        $result = $this->repository->fetchFromProject($project->id);

        $this->assertIsArray($result);
        $this->assertCount(0, $result);
    }

    public function test_fetch_from_project_with_multiple_products()
    {
        $project = Project::factory()->create(['organization_id' => $this->organization->id]);

        ProjectProductModel::factory()->count(5)->create(['project_id' => $project->id]);

        $result = $this->repository->fetchFromProject($project->id);

        $this->assertIsArray($result);
        $this->assertCount(5, $result);

        foreach ($result as $projectProduct) {
            $this->assertInstanceOf(ProjectProduct::class, $projectProduct);
            $this->assertEquals($project->id, $projectProduct->project_id);
        }
    }

    public function test_repository_with_long_descriptions()
    {
        $longDescription = str_repeat('Long description text. ', 20);
        ProjectProductModel::factory()->create(['description' => $longDescription]);

        $result = $this->repository->fetchAll();

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($longDescription, $result['data'][0]->description);
    }

    public function test_fetch_from_organization_with_multiple_projects()
    {
        $project1 = Project::factory()->create(['organization_id' => $this->organization->id]);
        $project2 = Project::factory()->create(['organization_id' => $this->organization->id]);

        // Create project products via different projects
        ProjectProductModel::factory()->count(2)->create(['project_id' => $project1->id]);
        ProjectProductModel::factory()->count(3)->create(['project_id' => $project2->id]);

        $result = $this->repository->fetchFromOrganization($this->organization->id);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $project1 = Project::factory()->create(['organization_id' => $org1->id]);
        $project2 = Project::factory()->create(['organization_id' => $org2->id]);

        $projectProduct1 = ProjectProductModel::factory()->create(['project_id' => $project1->id]);
        $projectProduct2 = ProjectProductModel::factory()->create(['project_id' => $project2->id]);

        // Fetch should only include project products from specific organization
        $result1 = $this->repository->fetchFromOrganization($org1->id);
        $result2 = $this->repository->fetchFromOrganization($org2->id);

        $this->assertEquals(1, $result1['count']);
        $this->assertEquals(1, $result2['count']);
    }

    public function test_store_custom_creates_new_project_product()
    {
        $project = Project::factory()->create(['organization_id' => $this->organization->id]);

        $domain = new ProjectProduct(
            id: null,
            project_id: $project->id,
            product_id: null, // Custom products don't have product_id
            quantity: 15,
            value: 225.75,
            description: 'Custom project product'
        );

        $result = $this->repository->storeCustom($domain);

        $this->assertInstanceOf(ProjectProduct::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(15, $result->quantity);
        $this->assertEquals(225.75, $result->value);
        $this->assertEquals('Custom project product', $result->description);
        $this->assertNull($result->product_id);

        $this->assertDatabaseHas('project_products', [
            'id' => $result->id,
            'project_id' => $project->id,
            'product_id' => null,
            'quantity' => 15,
            'value' => 225.75,
            'description' => 'Custom project product'
        ]);
    }
}
