<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\ClientFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Client;
use App\Factories\Inventory\ClientFactory;
use App\Models\Client as ClientModel;
use App\Models\Organization;
use App\Repositories\ClientRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClientRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ClientRepository $repository;
    private ClientFactory $factory;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ClientRepository::class);
        $this->factory = app(ClientFactory::class);
        $this->organization = Organization::factory()->create();
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test clients
        ClientModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ClientFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $client) {
            $this->assertInstanceOf(Client::class, $client);
        }
    }

    public function test_fetch_all_with_filters()
    {
        // Create clients with different names
        ClientModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'John Doe'
        ]);
        ClientModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Jane Smith'
        ]);

        $filters = new ClientFilters(['name' => 'John']);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals('John Doe', $result['data'][0]->name);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more clients than the limit
        ClientModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ClientFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 5]);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_by_id_returns_client()
    {
        $model = ClientModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $client = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Client::class, $client);
        $this->assertEquals($model->id, $client->id);
        $this->assertEquals($model->name, $client->name);
        $this->assertEquals($model->organization_id, $client->organization_id);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_client()
    {
        $domain = new Client(
            id: null,
            organization_id: $this->organization->id,
            name: 'Test Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: 'Engineer',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Software Development',
            address: '123 Main St',
            number: '123',
            neighborhood: 'Downtown',
            cep: '12345-678',
            complement: 'Apt 1',
            civil_state: 'single',
            description: 'Test client'
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals($domain->name, $result->name);
        $this->assertEquals($domain->organization_id, $result->organization_id);

        // Verify it was actually saved to database
        $this->assertDatabaseHas('clients', [
            'id' => $result->id,
            'name' => 'Test Client',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_update_modifies_existing_client()
    {
        $model = ClientModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $domain = new Client(
            id: $model->id,
            organization_id: $this->organization->id,
            name: 'Updated Name',
            phone: '+0987654321',
            email: '<EMAIL>',
            profession: 'Designer',
            birthdate: '1985-05-15',
            cpf: '98765432109',
            cnpj: null,
            service: 'Graphic Design',
            address: '456 Oak Ave',
            number: '456',
            neighborhood: 'Uptown',
            cep: '98765-432',
            complement: 'Suite 2',
            civil_state: 'married',
            description: 'Updated client'
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals($model->id, $result->id);
        $this->assertEquals('Updated Name', $result->name);

        // Verify it was actually updated in database
        $this->assertDatabaseHas('clients', [
            'id' => $model->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>'
        ]);
    }

    public function test_delete_removes_client()
    {
        $model = ClientModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('clients', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        ClientModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id
        ]);
        
        // Create clients for different organization
        $otherOrg = Organization::factory()->create();
        ClientModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id
        ]);

        $filters = new ClientFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        // This test assumes there's a numeric field to sum
        // Since Client doesn't have obvious numeric fields, we'll skip this
        $this->markTestSkipped('Client model does not have obvious numeric fields for sum operation');
    }

    public function test_find_by_phone_and_organization()
    {
        $model = ClientModel::factory()->create([
            'organization_id' => $this->organization->id,
            'phone' => '+1234567890'
        ]);

        $client = $this->repository->findByPhoneAndOrganization('+1234567890', $this->organization->id);

        $this->assertInstanceOf(Client::class, $client);
        $this->assertEquals($model->id, $client->id);
        $this->assertEquals('+1234567890', $client->phone);
    }

    public function test_find_by_phone_and_organization_returns_null_when_not_found()
    {
        $client = $this->repository->findByPhoneAndOrganization('+9999999999', $this->organization->id);

        $this->assertNull($client);
    }

    public function test_save_creates_new_client_when_id_is_null()
    {
        $domain = new Client(
            id: null,
            organization_id: $this->organization->id,
            name: 'New Client',
            phone: '+1234567890',
            email: '<EMAIL>',
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );

        $result = $this->repository->save($domain);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals('New Client', $result->name);
    }

    public function test_save_updates_existing_client_when_id_exists()
    {
        $model = ClientModel::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Original Name'
        ]);

        $domain = new Client(
            id: $model->id,
            organization_id: $this->organization->id,
            name: 'Updated Name',
            phone: $model->phone,
            email: $model->email,
            profession: $model->profession,
            birthdate: $model->birthdate,
            cpf: $model->cpf,
            cnpj: $model->cnpj,
            service: $model->service,
            address: $model->address,
            number: $model->number,
            neighborhood: $model->neighborhood,
            cep: $model->cep,
            complement: $model->complement,
            civil_state: $model->civil_state,
            description: $model->description
        );

        $result = $this->repository->save($domain);

        $this->assertInstanceOf(Client::class, $result);
        $this->assertEquals($model->id, $result->id);
        $this->assertEquals('Updated Name', $result->name);
    }

    public function test_fetch_by_campaign_returns_clients()
    {
        // Create campaign and clients
        $campaign = \App\Models\Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $client1 = ClientModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $client2 = ClientModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $client3 = ClientModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Attach clients to campaign
        $campaign->clients()->attach([$client1->id, $client2->id]);

        $filters = new ClientFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchByCampaign($campaign->id, $filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertCount(2, $result['data']);

        foreach ($result['data'] as $client) {
            $this->assertInstanceOf(Client::class, $client);
            $this->assertContains($client->id, [$client1->id, $client2->id]);
        }
    }

    public function test_fetch_by_campaign_with_empty_campaign()
    {
        $campaign = \App\Models\Campaign::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $filters = new ClientFilters([]);
        $orderBy = new OrderBy(['order' => 'created_at', 'by' => 'desc', 'limit' => 10]);

        $result = $this->repository->fetchByCampaign($campaign->id, $filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertEmpty($result['data']);
        $this->assertEquals(0, $result['count']);
    }

    public function test_repository_handles_soft_deleted_clients()
    {
        $model = ClientModel::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        // Soft delete the client
        $model->delete();

        // Should not find soft deleted client
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $client1 = ClientModel::factory()->create(['organization_id' => $org1->id]);
        $client2 = ClientModel::factory()->create(['organization_id' => $org2->id]);

        $filters = new ClientFilters([]);

        // Count should only include clients from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }
}
