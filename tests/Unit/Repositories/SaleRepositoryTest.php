<?php

namespace Tests\Unit\Repositories;

use App\Domains\Filters\SaleFilters;
use App\Domains\Filters\OrderBy;
use App\Domains\Inventory\Sale;
use App\Factories\Inventory\SaleFactory;
use App\Models\Sale as SaleModel;
use App\Models\Organization;
use App\Models\User;
use App\Models\Shop;
use App\Models\Client;
use App\Repositories\SaleRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaleRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private SaleRepository $repository;
    private SaleFactory $factory;
    private Organization $organization;
    private User $user;
    private Shop $shop;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(SaleRepository::class);
        $this->factory = app(SaleFactory::class);
        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->shop = Shop::factory()->create(['organization_id' => $this->organization->id]);
        $this->client = Client::factory()->create(['organization_id' => $this->organization->id]);
    }

    public function test_fetch_all_returns_paginated_results()
    {
        // Create test sales
        SaleModel::factory()->count(5)->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id
        ]);

        $filters = new SaleFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('currentPage', $result);
        $this->assertArrayHasKey('lastPage', $result);

        $this->assertCount(5, $result['data']);
        $this->assertEquals(5, $result['count']);
        $this->assertEquals(5, $result['total']);

        foreach ($result['data'] as $sale) {
            $this->assertInstanceOf(Sale::class, $sale);
        }
    }

    public function test_fetch_all_with_filters()
    {
        // Create sales with different total values
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 100.00
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 200.00
        ]);

        $filters = new SaleFilters(['total_value' => 100.00]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(100.00, $result['data'][0]->total_value);
    }

    public function test_fetch_all_with_pagination()
    {
        // Create more sales than the limit
        SaleModel::factory()->count(15)->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);

        $filters = new SaleFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 5);

        $result = $this->repository->fetchAll($filters, $orderBy);

        $this->assertEquals(5, $result['count']);
        $this->assertEquals(15, $result['total']);
        $this->assertEquals(1, $result['currentPage']);
        $this->assertEquals(3, $result['lastPage']);
    }

    public function test_fetch_from_organization_returns_organization_specific_sales()
    {
        $otherOrganization = Organization::factory()->create();
        $otherUser = User::factory()->create(['organization_id' => $otherOrganization->id]);

        // Create sales for different organizations
        SaleModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);
        SaleModel::factory()->count(2)->create([
            'organization_id' => $otherOrganization->id,
            'user_id' => $otherUser->id
        ]);

        $filters = new SaleFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(3, $result['count']);
        $this->assertEquals(3, $result['total']);

        foreach ($result['data'] as $sale) {
            $this->assertEquals($this->organization->id, $sale->organization_id);
        }
    }

    public function test_fetch_by_id_returns_sale_with_relationships()
    {
        $model = SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id
        ]);

        // Create items for the sale
        \App\Models\Item::factory()->count(2)->create(['sale_id' => $model->id]);

        $sale = $this->repository->fetchById($model->id);

        $this->assertInstanceOf(Sale::class, $sale);
        $this->assertEquals($model->id, $sale->id);
        $this->assertEquals($model->total_value, $sale->total_value);
        $this->assertEquals($model->organization_id, $sale->organization_id);
        
        // Should load relationships
        $this->assertNotNull($sale->user);
        $this->assertNotNull($sale->shop);
        $this->assertNotNull($sale->client);
        $this->assertNotNull($sale->items);
        $this->assertIsArray($sale->items);
        $this->assertCount(2, $sale->items);
    }

    public function test_fetch_by_id_throws_exception_when_not_found()
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        
        $this->repository->fetchById(999);
    }

    public function test_store_creates_new_sale()
    {
        $domain = new Sale(
            id: null,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: $this->shop->id,
            client_id: $this->client->id,
            total_value: 150.75
        );

        $result = $this->repository->store($domain);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertNotNull($result->id);
        $this->assertEquals(150.75, $result->total_value);

        $this->assertDatabaseHas('sales', [
            'id' => $result->id,
            'total_value' => 150.75,
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);
    }

    public function test_update_modifies_existing_sale()
    {
        $model = SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 100.00
        ]);

        $domain = new Sale(
            id: $model->id,
            organization_id: $this->organization->id,
            user_id: $this->user->id,
            shop_id: $this->shop->id,
            client_id: $this->client->id,
            total_value: 200.50
        );

        $result = $this->repository->update($domain, $this->organization->id);

        $this->assertInstanceOf(Sale::class, $result);
        $this->assertEquals(200.50, $result->total_value);

        $this->assertDatabaseHas('sales', [
            'id' => $model->id,
            'total_value' => 200.50,
            'shop_id' => $this->shop->id,
            'client_id' => $this->client->id
        ]);
    }

    public function test_delete_removes_sale()
    {
        $model = SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);

        $domain = $this->factory->buildFromModel($model);
        $result = $this->repository->delete($domain);

        $this->assertTrue($result);

        // Verify it was soft deleted
        $this->assertSoftDeleted('sales', [
            'id' => $model->id
        ]);
    }

    public function test_count_returns_correct_number()
    {
        SaleModel::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);
        
        // Create sales for different organization
        $otherOrg = Organization::factory()->create();
        $otherUser = User::factory()->create(['organization_id' => $otherOrg->id]);
        SaleModel::factory()->count(2)->create([
            'organization_id' => $otherOrg->id,
            'user_id' => $otherUser->id
        ]);

        $filters = new SaleFilters([]);
        $count = $this->repository->count($this->organization->id, $filters);

        $this->assertEquals(3, $count);
    }

    public function test_sum_returns_correct_total()
    {
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 100.00
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 150.50
        ]);

        $filters = new SaleFilters([]);
        $sum = $this->repository->sum($this->organization->id, $filters, 'total_value');

        $this->assertEquals(250.50, $sum);
    }

    public function test_repository_handles_soft_deleted_sales()
    {
        $model = SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);

        // Soft delete the sale
        $model->delete();

        // Should not find soft deleted sale
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);
        $this->repository->fetchById($model->id);
    }

    public function test_repository_with_different_organizations()
    {
        $org1 = Organization::factory()->create();
        $org2 = Organization::factory()->create();

        $user1 = User::factory()->create(['organization_id' => $org1->id]);
        $user2 = User::factory()->create(['organization_id' => $org2->id]);

        $sale1 = SaleModel::factory()->create(['organization_id' => $org1->id, 'user_id' => $user1->id]);
        $sale2 = SaleModel::factory()->create(['organization_id' => $org2->id, 'user_id' => $user2->id]);

        $filters = new SaleFilters([]);

        // Count should only include sales from specific organization
        $count1 = $this->repository->count($org1->id, $filters);
        $count2 = $this->repository->count($org2->id, $filters);

        $this->assertEquals(1, $count1);
        $this->assertEquals(1, $count2);
    }

    public function test_repository_handles_zero_total_value()
    {
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 0.00
        ]);

        $filters = new SaleFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(0.00, $result['data'][0]->total_value);
    }

    public function test_repository_handles_high_total_value()
    {
        $highValue = 999999.99;
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => $highValue
        ]);

        $filters = new SaleFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($highValue, $result['data'][0]->total_value);
    }

    public function test_repository_handles_decimal_total_value()
    {
        $decimalValue = 123.456;
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => $decimalValue
        ]);

        $filters = new SaleFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($decimalValue, $result['data'][0]->total_value);
    }

    public function test_repository_handles_null_optional_fields()
    {
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => null,
            'client_id' => null
        ]);

        $filters = new SaleFilters([]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertNull($result['data'][0]->shop_id);
        $this->assertNull($result['data'][0]->client_id);
    }

    public function test_repository_with_user_filtering()
    {
        $user2 = User::factory()->create(['organization_id' => $this->organization->id]);

        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $user2->id
        ]);

        $filters = new SaleFilters(['user_id' => $this->user->id]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($this->user->id, $result['data'][0]->user_id);
    }

    public function test_repository_with_shop_filtering()
    {
        $shop2 = Shop::factory()->create(['organization_id' => $this->organization->id]);

        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $this->shop->id
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'shop_id' => $shop2->id
        ]);

        $filters = new SaleFilters(['shop_id' => $this->shop->id]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($this->shop->id, $result['data'][0]->shop_id);
    }

    public function test_repository_with_client_filtering()
    {
        $client2 = Client::factory()->create(['organization_id' => $this->organization->id]);

        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'client_id' => $this->client->id
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'client_id' => $client2->id
        ]);

        $filters = new SaleFilters(['client_id' => $this->client->id]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals($this->client->id, $result['data'][0]->client_id);
    }

    public function test_repository_with_total_value_range_filtering()
    {
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 50.00
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 150.00
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'total_value' => 250.00
        ]);

        $filters = new SaleFilters(['min_total_value' => 100.00, 'max_total_value' => 200.00]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(1, $result['count']);
        $this->assertEquals(150.00, $result['data'][0]->total_value);
    }

    public function test_repository_with_date_range_filtering()
    {
        $yesterday = now()->subDay();
        $today = now();
        $tomorrow = now()->addDay();

        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'created_at' => $yesterday
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'created_at' => $today
        ]);
        SaleModel::factory()->create([
            'organization_id' => $this->organization->id,
            'user_id' => $this->user->id,
            'created_at' => $tomorrow
        ]);

        $filters = new SaleFilters([
            'start_date' => $today->format('Y-m-d'),
            'end_date' => $tomorrow->format('Y-m-d')
        ]);
        $orderBy = new OrderBy('created_at', 'desc', 10);

        $result = $this->repository->fetchFromOrganization($this->organization->id, $filters, $orderBy);

        $this->assertEquals(2, $result['count']);
    }
}
