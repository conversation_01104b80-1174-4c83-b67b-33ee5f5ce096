<?php

namespace Tests\Unit\Factories;

use App\Factories\OrganizationFactory;
use App\Domains\Organization;
use App\Models\Organization as OrganizationModel;
use Illuminate\Foundation\Testing\RefreshDatabase;

class OrganizationFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $organization = OrganizationModel::factory()->create([
            'name' => 'Test Organization',
            'description' => 'Test Description',
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($organization);

        $this->assertInstanceOf(Organization::class, $domain);
        $this->assertEquals($organization->id, $domain->id);
        $this->assertEquals($organization->name, $domain->name);
        $this->assertEquals($organization->description, $domain->description);
        $this->assertEquals($organization->is_active, $domain->is_active);
        $this->assertEquals($organization->is_suspended, $domain->is_suspended);
        $this->assertEquals($organization->created_at, $domain->created_at);
        $this->assertEquals($organization->updated_at, $domain->updated_at);
    }

    public function test_build_from_store_array()
    {
        $data = [
            'name' => 'New Organization',
            'description' => 'New Description'
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreArray($data);

        $this->assertInstanceOf(Organization::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals('New Organization', $domain->name);
        $this->assertEquals('New Description', $domain->description);
        $this->assertTrue($domain->is_active);
        $this->assertFalse($domain->is_suspended);
    }

    public function test_build_from_store_array_with_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreArray(null);

        $this->assertNull($domain);
    }

    public function test_build_from_store_array_with_empty_array()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreArray([]);

        $this->assertInstanceOf(Organization::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals('', $domain->name);
        $this->assertEquals('', $domain->description);
        $this->assertTrue($domain->is_active);
        $this->assertFalse($domain->is_suspended);
    }

    public function test_build_from_model_with_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    protected function createFactoryInstance()
    {
        return new OrganizationFactory();
    }

    protected function getDomainClass(): string
    {
        return Organization::class;
    }

    protected function createModelInstance()
    {
        return OrganizationModel::factory()->create();
    }
}
