<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\ShopFactory;
use App\Domains\Inventory\Shop;
use App\Models\Shop as ShopModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ShopFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->is_active, $domain->is_active);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createMockStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Store Shop', $domain->name);
        $this->assertEquals('Store shop description', $domain->description);
        $this->assertTrue($domain->is_active);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new class extends \App\Http\Requests\Shop\StoreRequest {
            public $organization_id = null;
            public $name = null;
            public $description = null;
            public $is_active = null;
        };

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->is_active);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createMockUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertEquals('Updated Shop', $domain->name);
        $this->assertEquals('Updated shop description', $domain->description);
        $this->assertFalse($domain->is_active);
    }

    public function test_build_from_update_request_with_null_values()
    {
        $request = new class extends \App\Http\Requests\Shop\UpdateRequest {
            public $name = null;
            public $description = null;
            public $is_active = null;
        };

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->is_active);
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(ShopFactory::class);

        $this->assertInstanceOf(ShopFactory::class, $factory);
    }

    public function test_factory_can_handle_different_shop_types()
    {
        $mainStore = ShopModel::factory()->mainStore()->create();
        $warehouse = ShopModel::factory()->warehouse()->create();
        $onlineStore = ShopModel::factory()->online()->create();
        $inactiveShop = ShopModel::factory()->inactive()->create();

        $factory = $this->createFactoryInstance();

        $mainDomain = $factory->buildFromModel($mainStore);
        $warehouseDomain = $factory->buildFromModel($warehouse);
        $onlineDomain = $factory->buildFromModel($onlineStore);
        $inactiveDomain = $factory->buildFromModel($inactiveShop);

        $this->assertInstanceOf(Shop::class, $mainDomain);
        $this->assertEquals('Main Store', $mainDomain->name);
        $this->assertTrue($mainDomain->is_active);

        $this->assertInstanceOf(Shop::class, $warehouseDomain);
        $this->assertStringContainsString('Warehouse', $warehouseDomain->name);
        $this->assertTrue($warehouseDomain->is_active);

        $this->assertInstanceOf(Shop::class, $onlineDomain);
        $this->assertEquals('Online Store', $onlineDomain->name);
        $this->assertTrue($onlineDomain->is_active);

        $this->assertInstanceOf(Shop::class, $inactiveDomain);
        $this->assertFalse($inactiveDomain->is_active);
    }

    protected function createFactoryInstance()
    {
        return new ShopFactory();
    }

    protected function getDomainClass(): string
    {
        return Shop::class;
    }

    protected function createModelInstance()
    {
        return ShopModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        return new class {
            public $organization_id = 1;
            public $name = 'Test Shop';
            public $description = 'Test Description';
            public $is_active = true;
        };
    }

    public function test_build_from_model_with_empty_description()
    {
        $model = ShopModel::factory()->create(['description' => '']);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals('', $domain->description);
    }

    public function test_build_from_model_with_null_description()
    {
        $model = ShopModel::factory()->create(['description' => null]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertNull($domain->description);
    }

    public function test_build_from_model_with_long_name()
    {
        $longName = str_repeat('A', 255);
        $model = ShopModel::factory()->create(['name' => $longName]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals($longName, $domain->name);
    }

    public function test_build_from_model_with_special_characters()
    {
        $specialName = 'Shop & Co. (™)';
        $specialDescription = 'Description with special chars: @#$%^&*()';
        $model = ShopModel::factory()->create([
            'name' => $specialName,
            'description' => $specialDescription
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals($specialName, $domain->name);
        $this->assertEquals($specialDescription, $domain->description);
    }

    public function test_build_from_model_with_unicode_characters()
    {
        $unicodeName = 'Shöp Ñamé 中文';
        $unicodeDescription = 'Descripción con caracteres especiales: ñáéíóú';
        $model = ShopModel::factory()->create([
            'name' => $unicodeName,
            'description' => $unicodeDescription
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Shop::class, $domain);
        $this->assertEquals($unicodeName, $domain->name);
        $this->assertEquals($unicodeDescription, $domain->description);
    }

    public function test_factory_handles_different_organization_ids()
    {
        $org1Shop = ShopModel::factory()->create(['organization_id' => 1]);
        $org2Shop = ShopModel::factory()->create(['organization_id' => 999]);

        $factory = $this->createFactoryInstance();

        $org1Domain = $factory->buildFromModel($org1Shop);
        $org2Domain = $factory->buildFromModel($org2Shop);

        $this->assertEquals(1, $org1Domain->organization_id);
        $this->assertEquals(999, $org2Domain->organization_id);
    }

    public function test_factory_handles_active_and_inactive_shops()
    {
        $activeShop = ShopModel::factory()->create(['is_active' => true]);
        $inactiveShop = ShopModel::factory()->create(['is_active' => false]);

        $factory = $this->createFactoryInstance();

        $activeDomain = $factory->buildFromModel($activeShop);
        $inactiveDomain = $factory->buildFromModel($inactiveShop);

        $this->assertTrue($activeDomain->is_active);
        $this->assertFalse($inactiveDomain->is_active);
    }

    protected function createUpdateRequest()
    {
        return new class {
            public $name = 'Updated Shop';
            public $description = 'Updated Description';
            public $is_active = false;
        };
    }

    private function createMockStoreRequest()
    {
        return new class extends \App\Http\Requests\Shop\StoreRequest {
            public $organization_id = 1;
            public $name = 'Store Shop';
            public $description = 'Store shop description';
            public $is_active = true;
        };
    }

    private function createMockUpdateRequest()
    {
        return new class extends \App\Http\Requests\Shop\UpdateRequest {
            public $name = 'Updated Shop';
            public $description = 'Updated shop description';
            public $is_active = false;
        };
    }
}
