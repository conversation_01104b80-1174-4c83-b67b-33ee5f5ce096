<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\SaleFactory;
use App\Domains\Inventory\Sale;
use App\Models\Sale as SaleModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SaleFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->shop_id, $domain->shop_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->total_value, $domain->total_value);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_with_relationships()
    {
        $model = $this->createModelInstanceWithRelationships();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, true, true, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNotNull($domain->user);
        $this->assertNotNull($domain->shop);
        $this->assertNotNull($domain->client);
        $this->assertNotNull($domain->items);
        $this->assertIsArray($domain->items);
    }

    public function test_build_from_model_without_relationships()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false, false, false, false);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->user);
        $this->assertNull($domain->shop);
        $this->assertNull($domain->client);
        $this->assertNull($domain->items);
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertEquals(1, $domain->shop_id);
        $this->assertEquals(1, $domain->client_id);
        $this->assertEquals(150.50, $domain->total_value);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\Sale\StoreRequest();
        $request->merge([
            'organization_id' => null,
            'user_id' => null,
            'shop_id' => null,
            'client_id' => null,
            'total_value' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->user_id);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->total_value);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertEquals(2, $domain->user_id);
        $this->assertEquals(2, $domain->shop_id);
        $this->assertEquals(2, $domain->client_id);
        $this->assertEquals(200.75, $domain->total_value);
    }

    public function test_build_from_update_request_with_null_values()
    {
        $request = new \App\Http\Requests\Sale\UpdateRequest();
        $request->merge([
            'user_id' => null,
            'shop_id' => null,
            'client_id' => null,
            'total_value' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->user_id);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->total_value);
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve dependencies
        return app()->make(SaleFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Sale::class;
    }

    protected function createModelInstance()
    {
        return SaleModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Sale\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'user_id' => 1,
            'shop_id' => 1,
            'client_id' => 1,
            'total_value' => 150.50,
        ]);
        return $request;
    }

    public function test_build_from_model_with_zero_total_value()
    {
        $model = SaleModel::factory()->create(['total_value' => 0.00]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals(0.00, $domain->total_value);
    }

    public function test_build_from_model_with_high_total_value()
    {
        $highValue = 999999.99;
        $model = SaleModel::factory()->create(['total_value' => $highValue]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($highValue, $domain->total_value);
    }

    public function test_build_from_model_with_decimal_total_value()
    {
        $decimalValue = 123.456;
        $model = SaleModel::factory()->create(['total_value' => $decimalValue]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertEquals($decimalValue, $domain->total_value);
    }

    public function test_build_from_model_with_null_optional_fields()
    {
        $model = SaleModel::factory()->create([
            'shop_id' => null,
            'client_id' => null
        ]);
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Sale::class, $domain);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->client_id);
    }

    public function test_factory_handles_different_organization_ids()
    {
        $org1Sale = SaleModel::factory()->create(['organization_id' => 1]);
        $org2Sale = SaleModel::factory()->create(['organization_id' => 999]);

        $factory = $this->createFactoryInstance();

        $org1Domain = $factory->buildFromModel($org1Sale);
        $org2Domain = $factory->buildFromModel($org2Sale);

        $this->assertEquals(1, $org1Domain->organization_id);
        $this->assertEquals(999, $org2Domain->organization_id);
    }

    public function test_build_from_model_array()
    {
        $models = SaleModel::factory()->count(3)->create();
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray($models->toArray());

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Sale::class, $domain);
        }
    }

    public function test_build_from_empty_model_array()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray([]);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Sale\UpdateRequest();
        $request->merge([
            'user_id' => 2,
            'shop_id' => 2,
            'client_id' => 2,
            'total_value' => 200.75,
        ]);
        return $request;
    }

    private function createModelInstanceWithRelationships()
    {
        $user = \App\Models\User::factory()->create();
        $shop = \App\Models\Shop::factory()->create();
        $client = \App\Models\Client::factory()->create();

        $sale = SaleModel::factory()->create([
            'user_id' => $user->id,
            'shop_id' => $shop->id,
            'client_id' => $client->id,
        ]);

        // Create items for the sale
        \App\Models\Item::factory()->count(2)->create(['sale_id' => $sale->id]);

        // Load relationships
        return $sale->load(['user', 'shop', 'client', 'items']);
    }
}
