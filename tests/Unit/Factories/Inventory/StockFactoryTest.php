<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\StockFactory;
use App\Domains\Inventory\Stock;
use App\Models\Stock as StockModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StockFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->shop_id, $domain->shop_id);
        $this->assertEquals($model->brand_id, $domain->brand_id);
        $this->assertEquals($model->product_id, $domain->product_id);
        $this->assertEquals($model->quantity, $domain->quantity);
        $this->assertEquals($model->value, $domain->value);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_with_shop_relationship()
    {
        $shop = \App\Models\Shop::factory()->create();
        $model = StockModel::factory()->create(['shop_id' => $shop->id]);
        $model->load('shop');

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true);

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertNotNull($domain->shop);
        $this->assertEquals($shop->name, $domain->shop->name);
        $this->assertEquals($shop->id, $domain->shop_id);
    }

    public function test_build_from_model_without_shop_relationship()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false);

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertNull($domain->shop);
    }

    public function test_build_from_model_with_product_relationship()
    {
        $product = \App\Models\Product::factory()->create();
        $model = StockModel::factory()->create(['product_id' => $product->id]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Stock::class, $domain);
        // Product should be loaded automatically
        $this->assertNotNull($domain->product);
        $this->assertEquals($product->name, $domain->product->name);
        $this->assertEquals($product->id, $domain->product_id);
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createMockStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->shop_id);
        $this->assertEquals(1, $domain->brand_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals(100, $domain->quantity);
        $this->assertEquals(1500.50, $domain->value);
        $this->assertEquals('Test stock item', $domain->description);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new class {
            public $organization_id = 1;
            public $shop_id = null;
            public $brand_id = null;
            public $product_id = 1;
            public $quantity = 50;
            public $value = null;
            public $description = null;
        };

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->brand_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals(50, $domain->quantity);
        $this->assertNull($domain->value);
        $this->assertNull($domain->description);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createMockUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->brand_id);
        $this->assertNull($domain->product_id);
        $this->assertEquals(200, $domain->quantity);
        $this->assertEquals(2000.75, $domain->value);
        $this->assertEquals('Updated stock item', $domain->description);
    }

    public function test_build_from_update_request_with_null_values()
    {
        $request = new class {
            public $quantity = null;
            public $value = null;
            public $description = null;
        };

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->brand_id);
        $this->assertNull($domain->product_id);
        $this->assertNull($domain->quantity);
        $this->assertNull($domain->value);
        $this->assertNull($domain->description);
    }

    protected function createFactoryInstance()
    {
        // Use app()->make() to resolve dependencies
        return app()->make(StockFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Stock::class;
    }

    protected function createModelInstance()
    {
        return StockModel::factory()->create();
    }

    public function test_build_from_model_array_with_empty_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray(collect([]));

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_from_model_array_with_mixed_stocks()
    {
        $models = collect([
            StockModel::factory()->create(['quantity' => 100, 'value' => 1000.0]),
            StockModel::factory()->create(['quantity' => 50, 'value' => 500.0]),
            StockModel::factory()->create(['quantity' => 0, 'value' => 0.0]),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Stock::class, $domain);
        }

        // Verify different quantities are preserved
        $this->assertEquals(100, $domains[0]->quantity);
        $this->assertEquals(50, $domains[1]->quantity);
        $this->assertEquals(0, $domains[2]->quantity);
    }

    public function test_factory_handles_different_quantity_ranges()
    {
        // Zero quantity stock
        $zeroStock = StockModel::factory()->create(['quantity' => 0, 'value' => 0.0]);
        // High quantity stock
        $highStock = StockModel::factory()->create(['quantity' => 999999, 'value' => 9999999.99]);
        // Medium quantity stock
        $mediumStock = StockModel::factory()->create(['quantity' => 500, 'value' => 5000.0]);

        $factory = $this->createFactoryInstance();

        $zeroDomain = $factory->buildFromModel($zeroStock);
        $highDomain = $factory->buildFromModel($highStock);
        $mediumDomain = $factory->buildFromModel($mediumStock);

        $this->assertEquals(0, $zeroDomain->quantity);
        $this->assertEquals(999999, $highDomain->quantity);
        $this->assertEquals(500, $mediumDomain->quantity);
    }

    public function test_factory_handles_different_value_ranges()
    {
        $zeroValue = StockModel::factory()->create(['value' => 0.0]);
        $highValue = StockModel::factory()->create(['value' => 999999.99]);
        $decimalValue = StockModel::factory()->create(['value' => 123.45]);

        $factory = $this->createFactoryInstance();

        $zeroDomain = $factory->buildFromModel($zeroValue);
        $highDomain = $factory->buildFromModel($highValue);
        $decimalDomain = $factory->buildFromModel($decimalValue);

        $this->assertEquals(0.0, $zeroDomain->value);
        $this->assertEquals(999999.99, $highDomain->value);
        $this->assertEquals(123.45, $decimalDomain->value);
    }

    public function test_factory_handles_null_relationships()
    {
        $nullShop = StockModel::factory()->create(['shop_id' => null]);
        $nullBrand = StockModel::factory()->create(['brand_id' => null]);

        $factory = $this->createFactoryInstance();

        $nullShopDomain = $factory->buildFromModel($nullShop);
        $nullBrandDomain = $factory->buildFromModel($nullBrand);

        $this->assertNull($nullShopDomain->shop_id);
        $this->assertNull($nullBrandDomain->brand_id);
    }

    public function test_build_from_entry_request()
    {
        $request = new class {
            public $organization_id = 1;
            public $shop_id = 2;
            public $brand_id = 3;
            public $product_id = 4;
            public $quantity = 75;
            public $value = 750.0;
            public $description = 'Entry stock';
        };

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromEntryRequest($request);

        $this->assertInstanceOf(Stock::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(2, $domain->shop_id);
        $this->assertEquals(3, $domain->brand_id);
        $this->assertEquals(4, $domain->product_id);
        $this->assertEquals(75, $domain->quantity);
        $this->assertEquals(750.0, $domain->value);
        $this->assertEquals('Entry stock', $domain->description);
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\Stock\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'shop_id' => 1,
            'brand_id' => 1,
            'product_id' => 1,
            'quantity' => 100,
            'value' => 1500.50,
            'description' => 'Test stock item',
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\Stock\UpdateRequest();
        $request->merge([
            'quantity' => 200,
            'value' => 2000.75,
            'description' => 'Updated stock item',
        ]);
        return $request;
    }

    private function createMockStoreRequest()
    {
        return new class {
            public $organization_id = 1;
            public $shop_id = 1;
            public $brand_id = 1;
            public $product_id = 1;
            public $quantity = 100;
            public $value = 1500.50;
            public $description = 'Test stock item';
        };
    }

    private function createMockUpdateRequest()
    {
        return new class {
            public $quantity = 200;
            public $value = 2000.75;
            public $description = 'Updated stock item';
        };
    }
}
