<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\ProductFactory;
use App\Factories\Inventory\BrandFactory;
use App\Domains\Inventory\Product;
use App\Models\Product as ProductModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->brand_id, $domain->brand_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->barcode, $domain->barcode);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->price, $domain->price);
        $this->assertEquals($model->unity, $domain->unity);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_array()
    {
        $models = collect([
            $this->createModelInstance(),
            $this->createModelInstance(),
            $this->createModelInstance(),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Product::class, $domain);
        }
    }

    public function test_build_from_null_model_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_app_make_factory()
    {
        $factory = app()->make(ProductFactory::class);

        $this->assertInstanceOf(ProductFactory::class, $factory);
    }

    public function test_factory_can_handle_different_product_types()
    {
        $expensiveProduct = ProductModel::factory()->expensive()->create();
        $cheapProduct = ProductModel::factory()->cheap()->create();
        $productWithoutBrand = ProductModel::factory()->withoutBrand()->create();
        $recentlyPricedProduct = ProductModel::factory()->recentlyPriced()->create();

        $factory = $this->createFactoryInstance();

        $expensiveDomain = $factory->buildFromModel($expensiveProduct);
        $cheapDomain = $factory->buildFromModel($cheapProduct);
        $noBrandDomain = $factory->buildFromModel($productWithoutBrand);
        $recentDomain = $factory->buildFromModel($recentlyPricedProduct);

        $this->assertInstanceOf(Product::class, $expensiveDomain);
        $this->assertGreaterThanOrEqual(1000, $expensiveDomain->price);

        $this->assertInstanceOf(Product::class, $cheapDomain);
        $this->assertLessThanOrEqual(50, $cheapDomain->price);

        $this->assertInstanceOf(Product::class, $noBrandDomain);
        $this->assertNull($noBrandDomain->brand_id);

        $this->assertInstanceOf(Product::class, $recentDomain);
        $this->assertNotNull($recentDomain->last_priced_at);
    }

    public function test_build_from_model_with_brand_relationship()
    {
        $brand = \App\Models\Brand::factory()->create();
        $model = ProductModel::factory()->create(['brand_id' => $brand->id]);
        $model->load('brand');

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true);

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertNotNull($domain->brand);
        $this->assertEquals($brand->name, $domain->brand->name);
        $this->assertEquals($brand->id, $domain->brand_id);
    }

    public function test_build_from_model_without_brand_relationship()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, false);

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertNull($domain->brand);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createMockStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->brand_id);
        $this->assertEquals('Test Product', $domain->name);
        $this->assertEquals('1234567890123', $domain->barcode);
        $this->assertEquals('Test product description', $domain->description);
        $this->assertEquals(99.99, $domain->price);
        $this->assertEquals(1, $domain->unity);
        $this->assertInstanceOf(\Carbon\Carbon::class, $domain->last_priced_at);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\Product\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'brand_id' => null,
            'name' => 'Minimal Product',
            'barcode' => null,
            'description' => null,
            'price' => null,
            'unity' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->brand_id);
        $this->assertEquals('Minimal Product', $domain->name);
        $this->assertNull($domain->barcode);
        $this->assertNull($domain->description);
        $this->assertNull($domain->price);
        $this->assertNull($domain->unity);
        $this->assertInstanceOf(\Carbon\Carbon::class, $domain->last_priced_at);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createMockUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertEquals(2, $domain->brand_id);
        $this->assertEquals('Updated Product', $domain->name);
        $this->assertEquals('9876543210987', $domain->barcode);
        $this->assertEquals('Updated product description', $domain->description);
        $this->assertEquals(149.99, $domain->price);
        $this->assertEquals(2, $domain->unity);
        $this->assertNull($domain->last_priced_at); // Should not be set in update
    }

    public function test_build_from_update_request_with_null_values()
    {
        $request = new \App\Http\Requests\Product\UpdateRequest();
        $request->merge([
            'brand_id' => null,
            'name' => 'Updated Product',
            'barcode' => null,
            'description' => null,
            'price' => null,
            'unity' => null,
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Product::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->brand_id);
        $this->assertEquals('Updated Product', $domain->name);
        $this->assertNull($domain->barcode);
        $this->assertNull($domain->description);
        $this->assertNull($domain->price);
        $this->assertNull($domain->unity);
        $this->assertNull($domain->last_priced_at);
    }

    protected function createFactoryInstance()
    {
        return new ProductFactory(new BrandFactory());
    }

    protected function getDomainClass(): string
    {
        return Product::class;
    }

    protected function createModelInstance()
    {
        return ProductModel::factory()->create();
    }

    public function test_factory_handles_different_price_ranges()
    {
        // Zero price product
        $zeroProduct = ProductModel::factory()->create(['price' => 0.0]);
        // High price product
        $highProduct = ProductModel::factory()->create(['price' => 999999.99]);
        // Decimal price product
        $decimalProduct = ProductModel::factory()->create(['price' => 123.45]);

        $factory = $this->createFactoryInstance();

        $zeroDomain = $factory->buildFromModel($zeroProduct);
        $highDomain = $factory->buildFromModel($highProduct);
        $decimalDomain = $factory->buildFromModel($decimalProduct);

        $this->assertEquals(0.0, $zeroDomain->price);
        $this->assertEquals(999999.99, $highDomain->price);
        $this->assertEquals(123.45, $decimalDomain->price);
    }

    public function test_factory_handles_different_unity_values()
    {
        $unity1Product = ProductModel::factory()->create(['unity' => 1]);
        $unity2Product = ProductModel::factory()->create(['unity' => 2]);
        $unity3Product = ProductModel::factory()->create(['unity' => 3]);

        $factory = $this->createFactoryInstance();

        $unity1Domain = $factory->buildFromModel($unity1Product);
        $unity2Domain = $factory->buildFromModel($unity2Product);
        $unity3Domain = $factory->buildFromModel($unity3Product);

        $this->assertEquals(1, $unity1Domain->unity);
        $this->assertEquals(2, $unity2Domain->unity);
        $this->assertEquals(3, $unity3Domain->unity);
    }

    public function test_factory_handles_barcode_variations()
    {
        $shortBarcode = ProductModel::factory()->create(['barcode' => '123456']);
        $longBarcode = ProductModel::factory()->create(['barcode' => '1234567890123456789']);
        $nullBarcode = ProductModel::factory()->create(['barcode' => null]);

        $factory = $this->createFactoryInstance();

        $shortDomain = $factory->buildFromModel($shortBarcode);
        $longDomain = $factory->buildFromModel($longBarcode);
        $nullDomain = $factory->buildFromModel($nullBarcode);

        $this->assertEquals('123456', $shortDomain->barcode);
        $this->assertEquals('1234567890123456789', $longDomain->barcode);
        $this->assertNull($nullDomain->barcode);
    }

    public function test_build_from_model_array_with_empty_collection()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray(collect([]));

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    public function test_build_from_model_array_with_mixed_products()
    {
        $models = collect([
            ProductModel::factory()->expensive()->create(),
            ProductModel::factory()->cheap()->create(),
            ProductModel::factory()->withoutBrand()->create(),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModelArray($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(Product::class, $domain);
        }

        // Verify different types are preserved
        $this->assertGreaterThanOrEqual(1000, $domains[0]->price); // expensive
        $this->assertLessThanOrEqual(50, $domains[1]->price); // cheap
        $this->assertNull($domains[2]->brand_id); // without brand
    }

    private function createMockStoreRequest()
    {
        $request = new \App\Http\Requests\Product\StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'brand_id' => 1,
            'name' => 'Test Product',
            'barcode' => '1234567890123',
            'description' => 'Test product description',
            'price' => 99.99,
            'unity' => 1,
        ]);
        return $request;
    }

    private function createMockUpdateRequest()
    {
        $request = new \App\Http\Requests\Product\UpdateRequest();
        $request->merge([
            'brand_id' => 2,
            'name' => 'Updated Product',
            'barcode' => '9876543210987',
            'description' => 'Updated product description',
            'price' => 149.99,
            'unity' => 2,
        ]);
        return $request;
    }
}
