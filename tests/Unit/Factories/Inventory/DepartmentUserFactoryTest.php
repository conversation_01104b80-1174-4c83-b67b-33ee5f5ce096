<?php

namespace Tests\Unit\Factories\Inventory;

use App\Factories\Inventory\DepartmentUserFactory;
use App\Domains\Inventory\DepartmentUser;
use App\Models\DepartmentUser as DepartmentUserModel;
use App\Models\Department as DepartmentModel;
use App\Models\User as UserModel;
use App\Models\Organization;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class DepartmentUserFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->department_id, $domain->department_id);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_build_from_model_with_relationships()
    {
        $organization = Organization::factory()->create();
        $department = DepartmentModel::factory()->create([
            'organization_id' => $organization->id,
            'name' => 'Test Department'
        ]);
        $user = UserModel::factory()->create([
            'organization_id' => $organization->id,
            'first_name' => 'Test User'
        ]);

        $model = DepartmentUserModel::factory()->create([
            'department_id' => $department->id,
            'user_id' => $user->id
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($department->id, $domain->department_id);
        $this->assertEquals($user->id, $domain->user_id);

        // Note: The factory loads relationships, but they might be null if not eager loaded
        // This tests the factory's ability to handle relationship loading
    }

    public function test_build_from_store_request()
    {
        $factory = $this->createFactoryInstance();
        $request = $this->createStoreRequest();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertEquals(2, $domain->department_id);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->user);
        $this->assertNull($domain->department);
    }

    public function test_build_from_store_request_with_null_values()
    {
        $request = new \App\Http\Requests\DepartmentUser\StoreRequest();
        $request->merge([]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->user_id);
        $this->assertNull($domain->department_id);
    }

    public function test_build_from_update_request()
    {
        $factory = $this->createFactoryInstance();
        $request = $this->createUpdateRequest();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(3, $domain->user_id);
        $this->assertEquals(4, $domain->department_id);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->user);
        $this->assertNull($domain->department);
    }

    public function test_build_from_update_request_with_partial_data()
    {
        $request = new \App\Http\Requests\DepartmentUser\UpdateRequest();
        $request->merge([
            'user_id' => 5,
            // department_id not provided
        ]);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals(5, $domain->user_id);
        $this->assertNull($domain->department_id);
    }

    public function test_build_from_model_handles_missing_relationships()
    {
        $model = $this->createModelInstance();

        // Mock the model to return null for relationships
        $model = \Mockery::mock($model);
        $model->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $model->shouldReceive('getAttribute')->with('user_id')->andReturn(1);
        $model->shouldReceive('getAttribute')->with('department_id')->andReturn(1);
        $model->shouldReceive('getAttribute')->with('created_at')->andReturn(Carbon::now());
        $model->shouldReceive('getAttribute')->with('updated_at')->andReturn(Carbon::now());
        $model->shouldReceive('user')->andReturnSelf();
        $model->shouldReceive('first')->andReturn(null);
        $model->shouldReceive('department')->andReturnSelf();

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertEquals(1, $domain->department_id);
    }

    protected function createFactoryInstance()
    {
        return app()->make(DepartmentUserFactory::class);
    }

    protected function getDomainClass(): string
    {
        return DepartmentUser::class;
    }

    protected function createModelInstance()
    {
        return DepartmentUserModel::factory()->create();
    }

    protected function createStoreRequest()
    {
        $request = new \App\Http\Requests\DepartmentUser\StoreRequest();
        $request->merge([
            'user_id' => 1,
            'department_id' => 2,
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new \App\Http\Requests\DepartmentUser\UpdateRequest();
        $request->merge([
            'user_id' => 3,
            'department_id' => 4,
        ]);
        return $request;
    }

    protected function tearDown(): void
    {
        \Mockery::close();
        parent::tearDown();
    }
}
