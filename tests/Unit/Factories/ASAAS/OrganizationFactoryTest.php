<?php

namespace Tests\Unit\Factories\ASAAS;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Factories\ASAAS\OrganizationFactory;
use App\Domains\ASAAS\Organization;
use App\Models\Organization as OrganizationModel;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Enums\AsaasEnvironment;
use App\Enums\SubscriptionStatus;
use Carbon\Carbon;

class OrganizationFactoryTest extends TestCase
{
    use RefreshDatabase;

    private OrganizationFactory $factory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = new OrganizationFactory();
    }

    public function test_build_from_store_array()
    {
        $request = [
            'name' => 'Test Organization',
            'description' => 'Test Description',
            'default_flow_id' => 5,
        ];

        $organization = $this->factory->buildFromStoreArray($request);

        $this->assertInstanceOf(Organization::class, $organization);
        $this->assertNull($organization->id);
        $this->assertEquals('Test Organization', $organization->name);
        $this->assertEquals('Test Description', $organization->description);
        $this->assertTrue($organization->is_active);
        $this->assertFalse($organization->is_suspended);
        $this->assertEquals(5, $organization->default_flow_id);
    }

    public function test_build_from_store_array_null()
    {
        $organization = $this->factory->buildFromStoreArray(null);
        $this->assertNull($organization);
    }

    public function test_build_from_model_without_asaas()
    {
        $model = OrganizationModel::factory()->create([
            'name' => 'Test Organization',
            'description' => 'Test Description',
            'is_active' => true,
            'is_suspended' => false,
        ]);

        $organization = $this->factory->buildFromModel($model);

        $this->assertInstanceOf(Organization::class, $organization);
        $this->assertEquals($model->id, $organization->id);
        $this->assertEquals('Test Organization', $organization->name);
        $this->assertEquals('Test Description', $organization->description);
        $this->assertTrue($organization->is_active);
        $this->assertFalse($organization->is_suspended);
        $this->assertFalse($organization->hasAsaasIntegration());
    }

    public function test_build_from_model_with_asaas()
    {
        $model = OrganizationModel::factory()->create([
            'name' => 'Test Organization',
            'description' => 'Test Description',
        ]);

        $asaasModel = AsaasOrganization::factory()->create([
            'organization_id' => $model->id,
            'asaas_account_id' => 'acc_123456',
            'asaas_api_key' => 'api_key_123',
            'asaas_environment' => AsaasEnvironment::SANDBOX,
            'subscription_status' => SubscriptionStatus::ACTIVE,
            'subscription_value' => 99.90,
            'is_courtesy' => false,
        ]);

        // Load the relationship
        $model->load('asaas');

        $organization = $this->factory->buildFromModel($model);

        $this->assertInstanceOf(Organization::class, $organization);
        $this->assertEquals($model->id, $organization->id);
        $this->assertEquals('Test Organization', $organization->name);
        $this->assertTrue($organization->hasAsaasIntegration());
        $this->assertEquals('acc_123456', $organization->asaas_account_id);
        $this->assertEquals('api_key_123', $organization->asaas_api_key);
        $this->assertEquals(AsaasEnvironment::SANDBOX, $organization->asaas_environment);
        $this->assertEquals(SubscriptionStatus::ACTIVE, $organization->subscription_status);
        $this->assertEquals(99.90, $organization->subscription_value);
        $this->assertFalse($organization->is_courtesy);
    }

    public function test_build_from_model_null()
    {
        $organization = $this->factory->buildFromModel(null);
        $this->assertNull($organization);
    }

    public function test_build_from_asaas_model()
    {
        $model = OrganizationModel::factory()->create([
            'name' => 'Test Organization',
            'description' => 'Test Description',
        ]);

        $asaasModel = AsaasOrganization::factory()->create([
            'organization_id' => $model->id,
            'asaas_account_id' => 'acc_123456',
            'asaas_api_key' => 'api_key_123',
            'asaas_environment' => AsaasEnvironment::PRODUCTION,
            'subscription_status' => SubscriptionStatus::ACTIVE,
            'subscription_value' => 199.90,
            'is_courtesy' => true,
            'courtesy_reason' => 'Test courtesy',
        ]);

        // Load the relationship
        $asaasModel->load('organization');

        $organization = $this->factory->buildFromAsaasModel($asaasModel);

        $this->assertInstanceOf(Organization::class, $organization);
        $this->assertEquals($model->id, $organization->id);
        $this->assertEquals('Test Organization', $organization->name);
        $this->assertTrue($organization->hasAsaasIntegration());
        $this->assertEquals('acc_123456', $organization->asaas_account_id);
        $this->assertEquals('api_key_123', $organization->asaas_api_key);
        $this->assertEquals(AsaasEnvironment::PRODUCTION, $organization->asaas_environment);
        $this->assertEquals(SubscriptionStatus::ACTIVE, $organization->subscription_status);
        $this->assertEquals(199.90, $organization->subscription_value);
        $this->assertTrue($organization->is_courtesy);
        $this->assertEquals('Test courtesy', $organization->courtesy_reason);
    }

    public function test_build_from_asaas_model_null()
    {
        $organization = $this->factory->buildFromAsaasModel(null);
        $this->assertNull($organization);
    }

    public function test_build_from_models_collection()
    {
        $model1 = OrganizationModel::factory()->create(['name' => 'Org 1']);
        $model2 = OrganizationModel::factory()->create(['name' => 'Org 2']);
        
        $collection = collect([$model1, $model2]);

        $organizations = $this->factory->buildFromModels($collection);

        $this->assertIsArray($organizations);
        $this->assertCount(2, $organizations);
        $this->assertInstanceOf(Organization::class, $organizations[0]);
        $this->assertInstanceOf(Organization::class, $organizations[1]);
        $this->assertEquals('Org 1', $organizations[0]->name);
        $this->assertEquals('Org 2', $organizations[1]->name);
    }

    public function test_build_from_models_empty()
    {
        $organizations = $this->factory->buildFromModels(null);
        $this->assertEquals([], $organizations);
    }

    public function test_build_from_model_without_asaas_method()
    {
        $model = OrganizationModel::factory()->create([
            'name' => 'Test Organization',
            'description' => 'Test Description',
        ]);

        $organization = $this->factory->buildFromModelWithoutAsaas($model);

        $this->assertInstanceOf(Organization::class, $organization);
        $this->assertEquals($model->id, $organization->id);
        $this->assertEquals('Test Organization', $organization->name);
        $this->assertFalse($organization->hasAsaasIntegration());
        $this->assertNull($organization->asaas_account_id);
        $this->assertNull($organization->asaas_api_key);
    }
}
