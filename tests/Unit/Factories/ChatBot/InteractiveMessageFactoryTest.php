<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\InteractiveMessageFactory;
use App\Factories\ChatBot\ButtonFactory;
use App\Factories\ChatBot\ListSectionFactory;
use App\Factories\ChatBot\ListRowFactory;
use App\Domains\ChatBot\InteractiveMessage;
use App\Domains\ChatBot\Button;
use App\Domains\ChatBot\ListSection;
use App\Domains\ChatBot\ListRow;
use App\Enums\ChatBot\InteractiveType;
use App\Models\InteractiveMessage as InteractiveMessageModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class InteractiveMessageFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory()
    {
        $factory = app()->make(InteractiveMessageFactory::class);

        $this->assertInstanceOf(InteractiveMessageFactory::class, $factory);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf(InteractiveMessage::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->header, $domain->header);
        $this->assertEquals($model->body, $domain->body);
        $this->assertEquals($model->footer, $domain->footer);
        $this->assertEquals(InteractiveType::from($model->type), $domain->type);
        $this->assertEquals($model->button_text, $domain->button_text);
        $this->assertEquals($model->created_at, $domain->created_at);
        $this->assertEquals($model->updated_at, $domain->updated_at);
    }

    public function test_build_from_model_with_null_returns_null()
    {
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel(null);

        $this->assertNull($domain);
    }

    public function test_build_from_models_collection()
    {
        $models = collect([
            $this->createModelInstance(),
            $this->createModelInstance(),
            $this->createModelInstance(),
        ]);

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildFromModels($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $domain) {
            $this->assertInstanceOf(InteractiveMessage::class, $domain);
        }
    }

    public function test_build_from_array_button_type()
    {
        $data = [
            'id' => 1,
            'organization_id' => 1,
            'header' => 'Test Header',
            'body' => 'Test Body',
            'footer' => 'Test Footer',
            'type' => 'button',
            'buttons' => [
                [
                    'id' => 1,
                    'text' => 'Yes',
                    'type' => 'reply',
                    'json' => '{"test": true}'
                ],
                [
                    'id' => 2,
                    'text' => 'No',
                    'type' => 'reply',
                    'json' => '{"test": false}'
                ]
            ]
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data, 1);

        $this->assertInstanceOf(InteractiveMessage::class, $domain);
        $this->assertEquals(InteractiveType::BUTTON, $domain->type);
        $this->assertCount(2, $domain->buttons);
        $this->assertEquals('Yes', $domain->buttons[0]->text);
        $this->assertEquals('No', $domain->buttons[1]->text);
    }

    public function test_build_from_array_list_type()
    {
        $data = [
            'id' => 1,
            'organization_id' => 1,
            'body' => 'Choose an option',
            'type' => 'list',
            'button_text' => 'View Options',
            'sections' => [
                [
                    'title' => 'Options',
                    'rows' => [
                        [
                            'row_id' => 'option_1',
                            'title' => 'Option 1',
                            'description' => 'First option'
                        ],
                        [
                            'row_id' => 'option_2',
                            'title' => 'Option 2',
                            'description' => 'Second option'
                        ]
                    ]
                ]
            ]
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data, 1);

        $this->assertInstanceOf(InteractiveMessage::class, $domain);
        $this->assertEquals(InteractiveType::LIST, $domain->type);
        $this->assertEquals('View Options', $domain->button_text);
        $this->assertCount(1, $domain->sections);
        $this->assertEquals('Options', $domain->sections[0]->title);
        $this->assertCount(2, $domain->sections[0]->rows);
    }

    public function test_build_button_message()
    {
        $buttons = [
            [
                'text' => 'Confirm',
                'type' => 'reply',
                'json' => '{"action": "confirm"}'
            ],
            [
                'text' => 'Cancel',
                'type' => 'reply',
                'json' => '{"action": "cancel"}'
            ]
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildButtonMessage(
            'Do you want to proceed?',
            $buttons,
            1,
            'Confirmation',
            'Choose wisely'
        );

        $this->assertInstanceOf(InteractiveMessage::class, $domain);
        $this->assertEquals(InteractiveType::BUTTON, $domain->type);
        $this->assertEquals('Do you want to proceed?', $domain->body);
        $this->assertEquals('Confirmation', $domain->header);
        $this->assertEquals('Choose wisely', $domain->footer);
        $this->assertCount(2, $domain->buttons);
    }

    public function test_build_button_message_fails_with_too_many_buttons()
    {
        $buttons = [
            ['text' => 'Button 1', 'type' => 'reply'],
            ['text' => 'Button 2', 'type' => 'reply'],
            ['text' => 'Button 3', 'type' => 'reply'],
            ['text' => 'Button 4', 'type' => 'reply'],
        ];

        $factory = $this->createFactoryInstance();

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Button messages support maximum 3 buttons');

        $factory->buildButtonMessage('Test body', $buttons, 1);
    }

    public function test_build_list_message()
    {
        $sections = [
            [
                'title' => 'Main Options',
                'rows' => [
                    [
                        'title' => 'Option 1',
                        'description' => 'First option'
                    ],
                    [
                        'title' => 'Option 2',
                        'description' => 'Second option'
                    ]
                ]
            ]
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildListMessage(
            'Choose an option',
            $sections,
            1,
            'Selection',
            'Pick one',
            'View All'
        );

        $this->assertInstanceOf(InteractiveMessage::class, $domain);
        $this->assertEquals(InteractiveType::LIST, $domain->type);
        $this->assertEquals('Choose an option', $domain->body);
        $this->assertEquals('Selection', $domain->header);
        $this->assertEquals('Pick one', $domain->footer);
        $this->assertEquals('View All', $domain->button_text);
        $this->assertCount(1, $domain->sections);
    }

    public function test_build_simple_button_message()
    {
        $buttonTexts = ['Yes', 'No', 'Maybe'];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildSimpleButtonMessage(
            'Do you agree?',
            $buttonTexts,
            1,
            'Question',
            'Please respond'
        );

        $this->assertInstanceOf(InteractiveMessage::class, $domain);
        $this->assertEquals(InteractiveType::BUTTON, $domain->type);
        $this->assertEquals('Do you agree?', $domain->body);
        $this->assertEquals('Question', $domain->header);
        $this->assertEquals('Please respond', $domain->footer);
        $this->assertCount(3, $domain->buttons);
        $this->assertEquals('Yes', $domain->buttons[0]->text);
        $this->assertEquals('No', $domain->buttons[1]->text);
        $this->assertEquals('Maybe', $domain->buttons[2]->text);
    }

    public function test_build_simple_list_message()
    {
        $options = [
            'Option 1',
            'Option 2',
            ['title' => 'Option 3', 'description' => 'Third option with description']
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildSimpleListMessage(
            'Choose an option',
            $options,
            1,
            'Selection',
            'Pick one',
            'View Options',
            'Available Options'
        );

        $this->assertInstanceOf(InteractiveMessage::class, $domain);
        $this->assertEquals(InteractiveType::LIST, $domain->type);
        $this->assertEquals('Choose an option', $domain->body);
        $this->assertEquals('Selection', $domain->header);
        $this->assertEquals('Pick one', $domain->footer);
        $this->assertEquals('View Options', $domain->button_text);
        $this->assertCount(1, $domain->sections);
        $this->assertEquals('Available Options', $domain->sections[0]->title);
        $this->assertCount(3, $domain->sections[0]->rows);
    }

    protected function createFactoryInstance()
    {
        return new InteractiveMessageFactory();
    }

    protected function getDomainClass(): string
    {
        return InteractiveMessage::class;
    }

    protected function createModelInstance()
    {
        return new InteractiveMessageModel([
            'id' => 1,
            'organization_id' => 1,
            'header' => 'Test Header',
            'body' => 'Test Body',
            'footer' => 'Test Footer',
            'type' => 'button',
            'button_text' => 'View Options',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }
}
