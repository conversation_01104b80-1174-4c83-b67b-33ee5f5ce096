<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\CampaignFactory;
use App\Factories\ChatBot\TemplateFactory;
use App\Factories\ChatBot\MessageFactory;
use App\Factories\Inventory\ClientFactory;
use App\Factories\ChatBot\PhoneNumberFactory;
use App\Domains\ChatBot\Campaign;
use App\Models\Campaign as CampaignModel;
use App\Models\Template as TemplateModel;
use App\Models\PhoneNumber as PhoneNumberModel;
use App\Http\Requests\Campaign\StoreRequest;
use App\Http\Requests\Campaign\UpdateRequest;
use App\Enums\CampaignStatus;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class CampaignFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        // Note: CampaignFactory has circular dependencies, so we test manual creation
        $templateFactory = new TemplateFactory();
        $messageFactory = new MessageFactory(new ClientFactory(), $templateFactory);
        $clientFactory = new ClientFactory();
        $phoneNumberFactory = new PhoneNumberFactory();

        $factory = new CampaignFactory($templateFactory, $messageFactory, $clientFactory, $phoneNumberFactory);

        $this->assertInstanceOf(CampaignFactory::class, $factory);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->template_id, $domain->template_id);
        $this->assertEquals($model->phone_number_id, $domain->phone_number_id);
        $this->assertEquals($model->name, $domain->name);
        $this->assertEquals($model->description, $domain->description);
        $this->assertEquals($model->is_scheduled, $domain->is_scheduled);
        $this->assertEquals($model->is_sent, $domain->is_sent);
        $this->assertEquals($model->is_sending, $domain->is_sending);
        $this->assertEquals($model->is_direct_message, $domain->is_direct_message);
        $this->assertEquals($model->message_count, $domain->message_count);
        $this->assertEquals($model->status, $domain->status);
    }

    public function test_build_from_store_request()
    {
        $request = $this->createStoreRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromStoreRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertEquals($request->organization_id, $domain->organization_id);
        $this->assertEquals($request->user_id, $domain->user_id);
        $this->assertEquals($request->template_id, $domain->template_id);
        $this->assertEquals($request->phone_number_id, $domain->phone_number_id);
        $this->assertEquals($request->name, $domain->name);
        $this->assertEquals($request->description, $domain->description);
        $this->assertEquals($request->is_scheduled, $domain->is_scheduled);
        $this->assertEquals($request->is_sent, $domain->is_sent);
        $this->assertEquals($request->is_sending, $domain->is_sending);
        $this->assertEquals($request->is_direct_message, $domain->is_direct_message);
        $this->assertEquals($request->message_count, $domain->message_count);
    }

    public function test_build_from_update_request()
    {
        $request = $this->createUpdateRequest();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->organization_id);
        $this->assertNull($domain->user_id);
        $this->assertEquals($request->template_id, $domain->template_id);
        $this->assertEquals($request->phone_number_id, $domain->phone_number_id);
        $this->assertEquals($request->name, $domain->name);
        $this->assertEquals($request->description, $domain->description);
    }

    protected function createFactoryInstance()
    {
        $templateFactory = new TemplateFactory();
        $messageFactory = new MessageFactory(new ClientFactory(), $templateFactory);
        $clientFactory = new ClientFactory();
        $phoneNumberFactory = new PhoneNumberFactory();

        return new CampaignFactory($templateFactory, $messageFactory, $clientFactory, $phoneNumberFactory);
    }

    protected function getDomainClass(): string
    {
        return Campaign::class;
    }

    protected function createModelInstance(array $attributes = [])
    {
        return CampaignModel::factory()->make(array_merge([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'template_id' => 1,
            'phone_number_id' => 1,
            'name' => 'Test Campaign',
            'description' => 'Test Description',
            'is_scheduled' => false,
            'is_sent' => false,
            'is_sending' => false,
            'is_direct_message' => false,
            'message_count' => 0,
            'status' => CampaignStatus::DRAFT,
            'sent_at' => null,
            'scheduled_at' => null,
            'cancelled_at' => null,
            'failed_at' => null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ], $attributes));
    }

    protected function createStoreRequest()
    {
        $request = new StoreRequest();
        $request->merge([
            'organization_id' => 1,
            'user_id' => 1,
            'template_id' => 1,
            'phone_number_id' => 1,
            'name' => 'Test Campaign',
            'description' => 'Test Description',
            'is_scheduled' => false,
            'is_sent' => false,
            'is_sending' => false,
            'is_direct_message' => false,
            'message_count' => 0,
            'sent_at' => null,
            'scheduled_at' => null,
        ]);
        return $request;
    }

    protected function createUpdateRequest()
    {
        $request = new UpdateRequest();
        $request->merge([
            'template_id' => 2,
            'phone_number_id' => 2,
            'name' => 'Updated Campaign',
            'description' => 'Updated Description',
            'is_scheduled' => true,
            'is_sent' => false,
            'is_sending' => false,
            'is_direct_message' => false,
            'message_count' => 5,
            'sent_at' => null,
            'scheduled_at' => Carbon::now()->addDay(),
        ]);
        return $request;
    }
}
