<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Factories\ChatBot\CampaignTagAssignmentFactory;
use App\Domains\ChatBot\CampaignTagAssignment;
use App\Models\CampaignTagAssignment as CampaignTagAssignmentModel;
use App\Models\Campaign as CampaignModel;
use App\Models\Tag as TagModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class CampaignTagAssignmentFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(CampaignTagAssignmentFactory::class);

        $this->assertInstanceOf(CampaignTagAssignmentFactory::class, $factory);
    }

    public function test_app_make_resolves_factory_multiple_times()
    {
        $factory1 = app()->make(CampaignTagAssignmentFactory::class);
        $factory2 = app()->make(CampaignTagAssignmentFactory::class);

        $this->assertInstanceOf(CampaignTagAssignmentFactory::class, $factory1);
        $this->assertInstanceOf(CampaignTagAssignmentFactory::class, $factory2);
        $this->assertNotSame($factory1, $factory2);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->campaign_id, $domain->campaign_id);
        $this->assertEquals($model->tag_id, $domain->tag_id);
        $this->assertEquals($model->assigned_at, $domain->assigned_at);
        $this->assertNull($domain->campaign);
        $this->assertNull($domain->tag);
    }

    public function test_build_from_model_with_relations()
    {
        $campaign = CampaignModel::factory()->make(['id' => 1]);
        $tag = TagModel::factory()->make(['id' => 2]);

        $model = $this->createModelInstance([
            'campaign_id' => $campaign->id,
            'tag_id' => $tag->id
        ]);

        $model->setRelation('campaign', $campaign);
        $model->setRelation('tag', $tag);

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model, true, true);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->campaign_id, $domain->campaign_id);
        $this->assertEquals($model->tag_id, $domain->tag_id);
        // Relations should be loaded properly now that circular dependency is resolved
        $this->assertNotNull($domain->campaign);
        $this->assertInstanceOf(\App\Domains\ChatBot\Campaign::class, $domain->campaign);
        $this->assertNotNull($domain->tag);
        $this->assertInstanceOf(\App\Domains\ChatBot\Tag::class, $domain->tag);
    }

    public function test_build_from_array()
    {
        $data = [
            'id' => 1,
            'campaign_id' => 2,
            'tag_id' => 3,
            'assigned_at' => '2023-01-01 12:00:00',
        ];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($data['id'], $domain->id);
        $this->assertEquals($data['campaign_id'], $domain->campaign_id);
        $this->assertEquals($data['tag_id'], $domain->tag_id);
        $this->assertInstanceOf(Carbon::class, $domain->assigned_at);
    }

    public function test_build_from_array_with_nulls()
    {
        $data = [];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromArray($data);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->campaign_id);
        $this->assertNull($domain->tag_id);
        // Domain constructor sets default value to now() when assigned_at is null
        $this->assertInstanceOf(Carbon::class, $domain->assigned_at);
    }

    public function test_build_collection()
    {
        $models = [
            $this->createModelInstance(['id' => 1]),
            $this->createModelInstance(['id' => 2]),
            $this->createModelInstance(['id' => 3]),
        ];

        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection($models);

        $this->assertIsArray($domains);
        $this->assertCount(3, $domains);

        foreach ($domains as $index => $domain) {
            $this->assertInstanceOf($this->getDomainClass(), $domain);
            $this->assertEquals($models[$index]->id, $domain->id);
        }
    }

    public function test_build_collection_empty()
    {
        $factory = $this->createFactoryInstance();
        $domains = $factory->buildCollection([]);

        $this->assertIsArray($domains);
        $this->assertEmpty($domains);
    }

    protected function createFactoryInstance()
    {
        return new CampaignTagAssignmentFactory();
    }

    protected function getDomainClass(): string
    {
        return CampaignTagAssignment::class;
    }

    protected function createModelInstance(array $attributes = [])
    {
        return CampaignTagAssignmentModel::factory()->make(array_merge([
            'id' => 1,
            'campaign_id' => 1,
            'tag_id' => 1,
            'assigned_at' => Carbon::now(),
        ], $attributes));
    }
}
