<?php

namespace Tests\Unit\Domains\WhatsApp;

use Tests\TestCase;
use App\Domains\WhatsApp\ChangeValueMessage;

class ChangeValueMessageTest extends TestCase
{
    public function test_constructor_initializes_properties_correctly()
    {
        $messageData = [
            'id' => 'msg_123',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text',
            'text' => ['body' => 'Hello World'],
            'context' => ['id' => 'reply_to_msg']
        ];

        $message = new ChangeValueMessage($messageData);

        $this->assertEquals('msg_123', $message->id);
        $this->assertEquals('+5511999999999', $message->from);
        $this->assertEquals('1234567890', $message->timestamp);
        $this->assertEquals('text', $message->type);
        $this->assertEquals(['body' => 'Hello World'], $message->text);
        $this->assertEquals(['id' => 'reply_to_msg'], $message->context);
    }

    public function test_constructor_handles_missing_properties()
    {
        $messageData = [
            'id' => 'msg_456',
            'from' => '+5511888888888',
            'timestamp' => '1234567891',
            'type' => 'image'
        ];

        $message = new ChangeValueMessage($messageData);

        $this->assertEquals('msg_456', $message->id);
        $this->assertEquals([], $message->text);
        $this->assertEquals([], $message->button);
        $this->assertEquals([], $message->context);
    }

    public function test_is_valid_returns_true_for_complete_message()
    {
        $messageData = [
            'id' => 'msg_789',
            'from' => '+5511777777777',
            'timestamp' => '1234567892',
            'type' => 'text'
        ];

        $message = new ChangeValueMessage($messageData);

        $this->assertTrue($message->isValid());
    }

    public function test_is_valid_returns_false_for_incomplete_message()
    {
        $messageData = [
            'id' => 'msg_incomplete',
            'from' => '+5511666666666'
            // Missing timestamp and type
        ];

        $message = new ChangeValueMessage($messageData);

        $this->assertFalse($message->isValid());
    }

    public function test_get_content_returns_correct_content_by_type()
    {
        $textMessage = new ChangeValueMessage([
            'id' => 'text_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text',
            'text' => ['body' => 'Text content']
        ]);

        $buttonMessage = new ChangeValueMessage([
            'id' => 'button_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'button',
            'button' => ['payload' => 'button_payload']
        ]);

        $this->assertEquals(['body' => 'Text content'], $textMessage->getContent());
        $this->assertEquals(['payload' => 'button_payload'], $buttonMessage->getContent());
    }

    public function test_get_text_body_returns_text_for_text_messages()
    {
        $textMessage = new ChangeValueMessage([
            'id' => 'text_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text',
            'text' => ['body' => 'Hello from text message']
        ]);

        $buttonMessage = new ChangeValueMessage([
            'id' => 'button_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'button'
        ]);

        $this->assertEquals('Hello from text message', $textMessage->getTextBody());
        $this->assertNull($buttonMessage->getTextBody());
    }

    public function test_is_reply_detects_reply_messages()
    {
        $replyMessage = new ChangeValueMessage([
            'id' => 'reply_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text',
            'context' => ['id' => 'original_msg_123']
        ]);

        $normalMessage = new ChangeValueMessage([
            'id' => 'normal_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text'
        ]);

        $this->assertTrue($replyMessage->isReply());
        $this->assertFalse($normalMessage->isReply());
    }

    public function test_get_replied_message_id_returns_correct_id()
    {
        $replyMessage = new ChangeValueMessage([
            'id' => 'reply_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text',
            'context' => ['id' => 'original_msg_456']
        ]);

        $normalMessage = new ChangeValueMessage([
            'id' => 'normal_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text'
        ]);

        $this->assertEquals('original_msg_456', $replyMessage->getRepliedMessageId());
        $this->assertNull($normalMessage->getRepliedMessageId());
    }

    public function test_is_interactive_detects_interactive_messages()
    {
        $buttonMessage = new ChangeValueMessage([
            'id' => 'button_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'button'
        ]);

        $interactiveMessage = new ChangeValueMessage([
            'id' => 'interactive_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'interactive'
        ]);

        $textMessage = new ChangeValueMessage([
            'id' => 'text_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text'
        ]);

        $this->assertTrue($buttonMessage->isInteractive());
        $this->assertTrue($interactiveMessage->isInteractive());
        $this->assertFalse($textMessage->isInteractive());
    }

    public function test_get_button_payload_returns_payload_for_button_messages()
    {
        $buttonMessage = new ChangeValueMessage([
            'id' => 'button_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'button',
            'button' => ['payload' => 'button_action_123']
        ]);

        $textMessage = new ChangeValueMessage([
            'id' => 'text_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text'
        ]);

        $this->assertEquals('button_action_123', $buttonMessage->getButtonPayload());
        $this->assertNull($textMessage->getButtonPayload());
    }

    public function test_get_interactive_selection_returns_selection_for_interactive_messages()
    {
        $interactiveButtonMessage = new ChangeValueMessage([
            'id' => 'interactive_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'interactive',
            'interactive' => [
                'button_reply' => [
                    'id' => 'btn_1',
                    'title' => 'Option 1'
                ]
            ]
        ]);

        $interactiveListMessage = new ChangeValueMessage([
            'id' => 'list_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'interactive',
            'interactive' => [
                'list_reply' => [
                    'id' => 'list_1',
                    'title' => 'List Option 1'
                ]
            ]
        ]);

        $textMessage = new ChangeValueMessage([
            'id' => 'text_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text'
        ]);

        $this->assertEquals(['id' => 'btn_1', 'title' => 'Option 1'], $interactiveButtonMessage->getInteractiveSelection());
        $this->assertEquals(['id' => 'list_1', 'title' => 'List Option 1'], $interactiveListMessage->getInteractiveSelection());
        $this->assertNull($textMessage->getInteractiveSelection());
    }

    public function test_has_media_detects_media_messages()
    {
        $imageMessage = new ChangeValueMessage([
            'id' => 'image_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'image'
        ]);

        $audioMessage = new ChangeValueMessage([
            'id' => 'audio_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'audio'
        ]);

        $textMessage = new ChangeValueMessage([
            'id' => 'text_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text'
        ]);

        $this->assertTrue($imageMessage->hasMedia());
        $this->assertTrue($audioMessage->hasMedia());
        $this->assertFalse($textMessage->hasMedia());
    }

    public function test_get_media_info_returns_media_content()
    {
        $imageMessage = new ChangeValueMessage([
            'id' => 'image_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'image',
            'image' => [
                'id' => 'image_123',
                'mime_type' => 'image/jpeg'
            ]
        ]);

        $textMessage = new ChangeValueMessage([
            'id' => 'text_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text'
        ]);

        $this->assertEquals(['id' => 'image_123', 'mime_type' => 'image/jpeg'], $imageMessage->getMediaInfo());
        $this->assertNull($textMessage->getMediaInfo());
    }

    public function test_get_normalized_from_removes_formatting()
    {
        $message = new ChangeValueMessage([
            'id' => 'msg_123',
            'from' => '+55 11 99999-9999',
            'timestamp' => '1234567890',
            'type' => 'text'
        ]);

        $this->assertEquals('5511999999999', $message->getNormalizedFrom());
    }

    public function test_to_array_returns_all_properties()
    {
        $messageData = [
            'id' => 'msg_complete',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text',
            'text' => ['body' => 'Complete message'],
            'context' => ['id' => 'reply_to']
        ];

        $message = new ChangeValueMessage($messageData);
        $result = $message->toArray();

        $this->assertEquals('msg_complete', $result['id']);
        $this->assertEquals('+5511999999999', $result['from']);
        $this->assertEquals('1234567890', $result['timestamp']);
        $this->assertEquals('text', $result['type']);
        $this->assertEquals(['body' => 'Complete message'], $result['text']);
        $this->assertEquals(['id' => 'reply_to'], $result['context']);
    }

    public function test_to_webhook_array_returns_formatted_structure()
    {
        $messageData = [
            'id' => 'webhook_msg',
            'from' => '+5511999999999',
            'timestamp' => '1234567890',
            'type' => 'text',
            'text' => ['body' => 'Webhook message']
        ];

        $message = new ChangeValueMessage($messageData);
        $result = $message->toWebhookArray();

        $this->assertArrayHasKey('message', $result);
        $this->assertEquals('webhook_msg', $result['message']['id']);
        $this->assertEquals('+5511999999999', $result['message']['from']);
        $this->assertEquals('text', $result['message']['type']);
        $this->assertEquals(['body' => 'Webhook message'], $result['message']['text']);
    }
}
