<?php

namespace Tests\Unit\Domains\ASAAS;

use Tests\TestCase;
use App\Services\ASAAS\Domains\AsaasOrganization;
use App\Domains\Organization as OrganizationDomain;
use App\Enums\AsaasEnvironment;
use App\Enums\SubscriptionStatus;
use Carbon\Carbon;

class OrganizationTest extends TestCase
{
    public function test_organization_domain_can_be_created()
    {
        $organizationDomain = new OrganizationDomain(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false
        );

        $asaasOrganization = new AsaasOrganization(
            id: 1,
            organization_id: 1,
            organization: $organizationDomain
        );

        $this->assertEquals(1, $asaasOrganization->id);
        $this->assertEquals(1, $asaasOrganization->organization_id);
        $this->assertEquals('Test Organization', $asaasOrganization->organization->name);
        $this->assertEquals('Test Description', $asaasOrganization->organization->description);
        $this->assertTrue($asaasOrganization->organization->is_active);
        $this->assertFalse($asaasOrganization->organization->is_suspended);
    }
}
