<?php

namespace Tests\Unit\Domains\ASAAS;

use Tests\TestCase;
use App\Services\ASAAS\Domains\AsaasSale;
use App\Domains\Inventory\Sale as SaleDomain;
use App\Enums\PaymentStatus;
use Carbon\Carbon;

class SaleTest extends TestCase
{
    public function test_sale_domain_can_be_created()
    {
        $saleDomain = new SaleDomain(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
        );

        $asaasSale = new AsaasSale(
            id: 1,
            organization_id: 1,
            client_id: 1,
            sale_id: 1,
            sale: $saleDomain
        );

        $this->assertEquals(1, $asaasSale->id);
        $this->assertEquals(1, $asaasSale->organization_id);
        $this->assertEquals(1, $asaasSale->client_id);
        $this->assertEquals(1, $asaasSale->sale_id);
        $this->assertEquals(100.50, $asaasSale->sale->total_value);
    }

    public function test_sale_without_asaas_payment()
    {
        $saleDomain = new SaleDomain(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50,
        );

        $asaasSale = new AsaasSale(
            id: 1,
            organization_id: 1,
            client_id: 1,
            sale_id: 1,
            sale: $saleDomain
        );

        $this->assertFalse($asaasSale->hasAsaasPayment());
    }

    public function test_sale_with_asaas_payment()
    {
        $saleDomain = new SaleDomain(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 100.50
        );

        $asaasSale = new AsaasSale(
            id: 1,
            organization_id: 1,
            client_id: 1,
            sale_id: 1,
            asaas_payment_id: 'pay_123456',
            payment_status: PaymentStatus::PENDING,
            sale: $saleDomain
        );

        $this->assertTrue($asaasSale->hasAsaasPayment());
    }
}
