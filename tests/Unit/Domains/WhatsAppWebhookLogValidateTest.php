<?php

namespace Tests\Unit\Domains;

use Tests\TestCase;
use App\Domains\WhatsAppWebhookLog;
use Carbon\Carbon;

class WhatsAppWebhookLogValidateTest extends TestCase
{
    public function test_validate_updates_fields_correctly()
    {
        $log = new WhatsAppWebhookLog(
            id: 1,
            organization_id: 123,
            phone_number_id: '569357716260641',
            event_type: 'message',
            webhook_payload: ['test' => 'data'],
            processing_status: 'pending'
        );

        $updateData = [
            'processing_status' => 'success',
            'error_message' => null,
        ];

        $log->validate($updateData);

        $this->assertEquals('success', $log->processing_status);
        $this->assertNull($log->error_message);
        $this->assertInstanceOf(Carbon::class, $log->processed_at);
        $this->assertInstanceOf(Carbon::class, $log->updated_at);
    }

    public function test_validate_sets_processed_at_for_success_status()
    {
        $log = new WhatsAppWebhookLog(
            processing_status: 'pending',
            processed_at: null
        );

        $log->validate(['processing_status' => 'success']);

        $this->assertEquals('success', $log->processing_status);
        $this->assertNotNull($log->processed_at);
    }

    public function test_validate_sets_processed_at_for_failed_status()
    {
        $log = new WhatsAppWebhookLog(
            processing_status: 'pending',
            processed_at: null
        );

        $log->validate(['processing_status' => 'failed']);

        $this->assertEquals('failed', $log->processing_status);
        $this->assertNotNull($log->processed_at);
    }

    public function test_validate_does_not_set_processed_at_for_pending_status()
    {
        $log = new WhatsAppWebhookLog(
            processing_status: 'success',
            processed_at: now()
        );

        $originalProcessedAt = $log->processed_at;
        $log->validate(['processing_status' => 'pending']);

        $this->assertEquals('pending', $log->processing_status);
        $this->assertEquals($originalProcessedAt, $log->processed_at);
    }

    public function test_validate_throws_exception_for_invalid_data()
    {
        $log = new WhatsAppWebhookLog();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid event type');

        $log->validate(['event_type' => 'invalid']);
    }

    public function test_validate_throws_exception_for_empty_phone_number()
    {
        $log = new WhatsAppWebhookLog();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Phone number ID cannot be empty');

        $log->validate(['phone_number_id' => '']);
    }

    public function test_validate_throws_exception_for_empty_webhook_payload()
    {
        $log = new WhatsAppWebhookLog();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Webhook payload must be a non-empty array');

        $log->validate(['webhook_payload' => []]);
    }

    public function test_validate_updates_multiple_fields()
    {
        $log = new WhatsAppWebhookLog(
            organization_id: 123,
            phone_number_id: '111111111',
            event_type: 'message',
            webhook_payload: ['old' => 'data'],
            processing_status: 'pending'
        );

        $updateData = [
            'organization_id' => 456,
            'phone_number_id' => '222222222',
            'event_type' => 'status',
            'webhook_payload' => ['new' => 'data'],
            'processing_status' => 'success',
            'error_message' => 'No error',
        ];

        $log->validate($updateData);

        $this->assertEquals(456, $log->organization_id);
        $this->assertEquals('222222222', $log->phone_number_id);
        $this->assertEquals('status', $log->event_type);
        $this->assertEquals(['new' => 'data'], $log->webhook_payload);
        $this->assertEquals('success', $log->processing_status);
        $this->assertEquals('No error', $log->error_message);
        $this->assertNotNull($log->processed_at);
        $this->assertNotNull($log->updated_at);
    }

    public function test_validate_only_updates_provided_fields()
    {
        $log = new WhatsAppWebhookLog(
            organization_id: 123,
            phone_number_id: '111111111',
            event_type: 'message',
            webhook_payload: ['original' => 'data'],
            processing_status: 'pending'
        );

        $updateData = [
            'processing_status' => 'success',
        ];

        $log->validate($updateData);

        // Only processing_status should change
        $this->assertEquals(123, $log->organization_id);
        $this->assertEquals('111111111', $log->phone_number_id);
        $this->assertEquals('message', $log->event_type);
        $this->assertEquals(['original' => 'data'], $log->webhook_payload);
        $this->assertEquals('success', $log->processing_status);
    }
}
