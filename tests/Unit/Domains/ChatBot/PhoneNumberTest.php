<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\PhoneNumber;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class PhoneNumberTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(PhoneNumber::class, $domain);
        // Add specific assertions for PhoneNumber properties
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayHasKey('is_chatbot_activated', $array);
        $this->assertTrue($array['is_chatbot_activated']);
    }

    public function test_is_chatbot_activated_default_value()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description'
        );

        $this->assertTrue($domain->is_chatbot_activated);
    }

    public function test_is_chatbot_activated_can_be_set_to_false()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: false
        );

        $this->assertFalse($domain->is_chatbot_activated);
    }

    /**
     * Test shouldProcessChatBot method - ChatBot Ativo scenario
     * is_active=true, is_chatbot_activated=true, flow_id=123 → Should return TRUE
     */
    public function test_should_process_chatbot_returns_true_when_all_conditions_met()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true
        );

        $this->assertTrue($domain->shouldProcessChatBot());
    }

    /**
     * Test shouldProcessChatBot method - Número Manual scenario
     * is_active=true, is_chatbot_activated=false, flow_id=qualquer → Should return FALSE
     */
    public function test_should_process_chatbot_returns_false_when_chatbot_not_activated()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: false
        );

        $this->assertFalse($domain->shouldProcessChatBot());
    }

    /**
     * Test shouldProcessChatBot method - Temporariamente Desabilitado scenario
     * is_active=true, is_chatbot_activated=false, flow_id=123 → Should return FALSE
     */
    public function test_should_process_chatbot_returns_false_when_temporarily_disabled()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: false
        );

        $this->assertFalse($domain->shouldProcessChatBot());
    }

    /**
     * Test shouldProcessChatBot method - Número Inativo scenario
     * is_active=false, qualquer configuração → Should return FALSE
     */
    public function test_should_process_chatbot_returns_false_when_phone_number_inactive()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: false,
            is_chatbot_activated: true
        );

        $this->assertFalse($domain->shouldProcessChatBot());
    }

    /**
     * Test shouldProcessChatBot method - Sem Flow scenario
     * flow_id=null, qualquer configuração → Should return FALSE
     */
    public function test_should_process_chatbot_returns_false_when_no_flow_configured()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true
        );

        $this->assertFalse($domain->shouldProcessChatBot());
    }

    /**
     * Test shouldProcessChatBot method - Edge case: All conditions false
     * is_active=false, is_chatbot_activated=false, flow_id=null → Should return FALSE
     */
    public function test_should_process_chatbot_returns_false_when_all_conditions_false()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: false,
            is_chatbot_activated: false
        );

        $this->assertFalse($domain->shouldProcessChatBot());
    }

    /**
     * Test shouldProcessChatBot method - Edge case: Multiple failing conditions
     * is_active=false, is_chatbot_activated=false, flow_id=123 → Should return FALSE
     */
    public function test_should_process_chatbot_returns_false_when_multiple_conditions_fail()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: false,
            is_chatbot_activated: false
        );

        $this->assertFalse($domain->shouldProcessChatBot());
    }

    /**
     * Test shouldProcessChatBot method - Edge case: Only flow_id missing
     * is_active=true, is_chatbot_activated=true, flow_id=null → Should return FALSE
     */
    public function test_should_process_chatbot_returns_false_when_only_flow_missing()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: null,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true
        );

        $this->assertFalse($domain->shouldProcessChatBot());
    }

    /**
     * Test shouldProcessChatBot method - Edge case: Only is_active missing
     * is_active=false, is_chatbot_activated=true, flow_id=123 → Should return FALSE
     */
    public function test_should_process_chatbot_returns_false_when_only_active_missing()
    {
        $domain = new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 123,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: false,
            is_chatbot_activated: true
        );

        $this->assertFalse($domain->shouldProcessChatBot());
    }

    public function test_to_store_array_includes_is_chatbot_activated()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toStoreArray();

        $this->assertArrayHasKey('is_chatbot_activated', $array);
        $this->assertTrue($array['is_chatbot_activated']);
    }

    public function test_to_update_array_includes_is_chatbot_activated()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toUpdateArray();

        $this->assertArrayHasKey('is_chatbot_activated', $array);
        $this->assertTrue($array['is_chatbot_activated']);
    }

    protected function createDomainInstance()
    {
        return new PhoneNumber(
            id: 1,
            organization_id: 1,
            user_id: 1,
            client_id: 1,
            flow_id: 1,
            phone_number: '+5511999999999',
            name: 'Test Phone',
            description: 'Test Description',
            is_active: true,
            is_chatbot_activated: true,
            whatsapp_phone_number_id: 'test_phone_id',
            whatsapp_business_id: 'test_business_id',
            whatsapp_access_token: 'test_token'
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        // TODO: Define expected array keys for PhoneNumber
        return [
            'id',
            // Add other expected keys
        ];
    }
}
