<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Button;
use Tests\TestCase;

class ButtonValidationLimitsTest extends TestCase
{
    public function test_reply_button_title_length_limit()
    {
        // Valid title (20 characters)
        $validButton = new Button(
            1, 1, '12345678901234567890', 'reply', null, null, null, null
        );
        $this->assertTrue($validButton->validateTitleLength());

        // Invalid title (21 characters)
        $invalidButton = new Button(
            1, 1, '123456789012345678901', 'reply', null, null, null, null
        );
        $this->assertFalse($invalidButton->validateTitleLength());
    }

    public function test_url_button_title_length_limit()
    {
        // Valid title (20 characters)
        $validButton = new Button(
            1, 1, '12345678901234567890', 'url', null, 'https://example.com', null, null
        );
        $this->assertTrue($validButton->validateTitleLength());

        // Invalid title (21 characters)
        $invalidButton = new Button(
            1, 1, '123456789012345678901', 'url', null, 'https://example.com', null, null
        );
        $this->assertFalse($invalidButton->validateTitleLength());
    }

    public function test_phone_number_button_title_length_limit()
    {
        // Valid title (20 characters)
        $validButton = new Button(
            1, 1, '12345678901234567890', 'phone_number', null, '+1234567890', null, null
        );
        $this->assertTrue($validButton->validateTitleLength());

        // Invalid title (21 characters)
        $invalidButton = new Button(
            1, 1, '123456789012345678901', 'phone_number', null, '+1234567890', null, null
        );
        $this->assertFalse($invalidButton->validateTitleLength());
    }

    public function test_copy_code_button_title_length_limit()
    {
        // Valid title (25 characters)
        $validButton = new Button(
            1, 1, '1234567890123456789012345', 'copy_code', null, 'PROMO123', null, null
        );
        $this->assertTrue($validButton->validateTitleLength());

        // Invalid title (26 characters)
        $invalidButton = new Button(
            1, 1, '12345678901234567890123456', 'copy_code', null, 'PROMO123', null, null
        );
        $this->assertFalse($invalidButton->validateTitleLength());
    }

    public function test_flow_button_title_length_limit()
    {
        // Valid title (25 characters)
        $validButton = new Button(
            1, 1, '1234567890123456789012345', 'flow', null, 'flow_token', null, null
        );
        $this->assertTrue($validButton->validateTitleLength());

        // Invalid title (26 characters)
        $invalidButton = new Button(
            1, 1, '12345678901234567890123456', 'flow', null, 'flow_token', null, null
        );
        $this->assertFalse($invalidButton->validateTitleLength());
    }

    public function test_url_button_requires_https()
    {
        $httpButton = new Button(
            1, 1, 'Visit', 'url', null, 'http://example.com', null, null
        );
        $this->assertFalse($httpButton->validateForWhatsApp());

        $httpsButton = new Button(
            1, 1, 'Visit', 'url', null, 'https://example.com', null, null
        );
        $this->assertTrue($httpsButton->validateForWhatsApp());
    }

    public function test_url_button_requires_valid_url()
    {
        $invalidUrlButton = new Button(
            1, 1, 'Visit', 'url', null, 'not-a-url', null, null
        );
        $this->assertFalse($invalidUrlButton->validateForWhatsApp());

        $validUrlButton = new Button(
            1, 1, 'Visit', 'url', null, 'https://example.com', null, null
        );
        $this->assertTrue($validUrlButton->validateForWhatsApp());
    }

    public function test_url_button_requires_url_data()
    {
        $noUrlButton = new Button(
            1, 1, 'Visit', 'url', null, null, null, null
        );
        $this->assertFalse($noUrlButton->validateForWhatsApp());

        $emptyUrlButton = new Button(
            1, 1, 'Visit', 'url', null, '', null, null
        );
        $this->assertFalse($emptyUrlButton->validateForWhatsApp());
    }

    public function test_phone_number_button_requires_international_format()
    {
        $invalidFormats = [
            '************',
            '(*************',
            '1234567890',
            '+123',
            '+12345678901234567890' // Too long
        ];

        foreach ($invalidFormats as $invalidFormat) {
            $button = new Button(
                1, 1, 'Call', 'phone_number', null, $invalidFormat, null, null
            );
            $this->assertFalse($button->validateForWhatsApp(), "Format should be invalid: {$invalidFormat}");
        }

        $validFormats = [
            '+1234567890',
            '+12345678901',
            '+123456789012345' // Maximum length
        ];

        foreach ($validFormats as $validFormat) {
            $button = new Button(
                1, 1, 'Call', 'phone_number', null, $validFormat, null, null
            );
            $this->assertTrue($button->validateForWhatsApp(), "Format should be valid: {$validFormat}");
        }
    }

    public function test_phone_number_button_requires_phone_data()
    {
        $noPhoneButton = new Button(
            1, 1, 'Call', 'phone_number', null, null, null, null
        );
        $this->assertFalse($noPhoneButton->validateForWhatsApp());

        $emptyPhoneButton = new Button(
            1, 1, 'Call', 'phone_number', null, '', null, null
        );
        $this->assertFalse($emptyPhoneButton->validateForWhatsApp());
    }

    public function test_copy_code_button_requires_code_data()
    {
        $noCodeButton = new Button(
            1, 1, 'Copy', 'copy_code', null, null, null, null
        );
        $this->assertFalse($noCodeButton->validateForWhatsApp());

        $emptyCodeButton = new Button(
            1, 1, 'Copy', 'copy_code', null, '', null, null
        );
        $this->assertFalse($emptyCodeButton->validateForWhatsApp());

        $validCodeButton = new Button(
            1, 1, 'Copy', 'copy_code', null, 'PROMO123', null, null
        );
        $this->assertTrue($validCodeButton->validateForWhatsApp());
    }

    public function test_flow_button_requires_flow_token()
    {
        $noTokenButton = new Button(
            1, 1, 'Start', 'flow', null, null, null, null
        );
        $this->assertFalse($noTokenButton->validateForWhatsApp());

        $emptyTokenButton = new Button(
            1, 1, 'Start', 'flow', null, '', null, null
        );
        $this->assertFalse($emptyTokenButton->validateForWhatsApp());

        $validTokenButton = new Button(
            1, 1, 'Start', 'flow', null, 'flow_token_123', null, null
        );
        $this->assertTrue($validTokenButton->validateForWhatsApp());
    }

    public function test_reply_button_no_additional_validation()
    {
        $replyButton = new Button(
            1, 1, 'Yes', 'reply', null, null, null, null
        );
        $this->assertTrue($replyButton->validateForWhatsApp());
    }

    public function test_invalid_button_type_validation()
    {
        $invalidButton = new Button(
            1, 1, 'Invalid', 'invalid_type', null, null, null, null
        );
        $this->assertFalse($invalidButton->validateForWhatsApp());
    }

    public function test_empty_title_validation()
    {
        $emptyTitleButton = new Button(
            1, 1, '', 'reply', null, null, null, null
        );
        $this->assertTrue($emptyTitleButton->validateTitleLength()); // Empty title is allowed

        $nullTitleButton = new Button(
            1, 1, null, 'reply', null, null, null, null
        );
        $this->assertTrue($nullTitleButton->validateTitleLength()); // Null title is allowed
    }
}
