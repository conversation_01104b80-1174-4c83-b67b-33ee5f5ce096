<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\Step;
use App\Enums\ChatBot\FlowStatus;
use App\Enums\StepType;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class FlowTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();

        $steps = [
            new Step(
                id: 1,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 1',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 1,
                next_step: null,
                earlier_step: null,
                is_initial_step: true,
                is_ending_step: false,
                configuration: ['text' => 'Hello'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: $createdAt,
                updated_at: $updatedAt
            ),
            new Step(
                id: 2,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 2',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 2,
                next_step: null,
                earlier_step: null,
                is_initial_step: false,
                is_ending_step: false,
                configuration: ['text' => 'World'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: $createdAt,
                updated_at: $updatedAt
            ),
        ];

        $flow = new Flow(
            1,
            1,
            'Test Flow',
            'Test Description',
            2,
            '{"flow": "data"}',
            true,
            60,
            'Thank you for using our service!',
            '1.0',
            FlowStatus::ACTIVE,
            ['client' => ['name', 'email']],
            $createdAt,
            $updatedAt,
            $steps
        );

        $this->assertEquals(1, $flow->id);
        $this->assertEquals(1, $flow->organization_id);
        $this->assertEquals('Test Flow', $flow->name);
        $this->assertEquals('Test Description', $flow->description);
        $this->assertEquals(2, $flow->steps_count);
        $this->assertEquals('{"flow": "data"}', $flow->json);
        $this->assertTrue($flow->is_default_flow);
        $this->assertEquals(60, $flow->inactivity_minutes);
        $this->assertEquals('Thank you for using our service!', $flow->ending_conversation_message);
        $this->assertEquals('1.0', $flow->version);
        $this->assertEquals(FlowStatus::ACTIVE, $flow->status);
        $this->assertEquals(['client' => ['name', 'email']], $flow->variables);
        $this->assertEquals($createdAt, $flow->created_at);
        $this->assertEquals($updatedAt, $flow->updated_at);
        $this->assertIsArray($flow->steps);
        $this->assertCount(2, $flow->steps);
        $this->assertInstanceOf(Step::class, $flow->steps[0]);
    }

    public function test_to_array_method()
    {
        $flow = $this->createDomainInstance();
        $array = $flow->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
        $this->assertEquals('Test Flow', $array['name']);
        $this->assertEquals('Test Description', $array['description']);
        $this->assertEquals(2, $array['steps_count']);
        $this->assertEquals('{"flow": "data"}', $array['json']);
        $this->assertTrue($array['is_default_flow']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        $this->assertIsArray($array['steps']);
    }

    public function test_to_store_array_excludes_timestamps_and_id()
    {
        $flow = $this->createDomainInstance();
        $storeArray = $flow->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);
        $this->assertArrayNotHasKey('steps', $storeArray);
        $this->assertArrayHasKey('organization_id', $storeArray);
        $this->assertArrayHasKey('name', $storeArray);
        $this->assertArrayHasKey('description', $storeArray);
        $this->assertArrayHasKey('steps_count', $storeArray);
        $this->assertArrayHasKey('json', $storeArray);
        $this->assertArrayHasKey('is_default_flow', $storeArray);
        $this->assertArrayHasKey('inactivity_minutes', $storeArray);
        $this->assertArrayHasKey('ending_conversation_message', $storeArray);
        $this->assertArrayHasKey('version', $storeArray);
        $this->assertArrayHasKey('status', $storeArray);
        $this->assertArrayHasKey('variables', $storeArray);
    }

    public function test_flow_with_null_values()
    {
        $flow = new Flow(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );

        $this->assertNull($flow->id);
        $this->assertNull($flow->organization_id);
        $this->assertNull($flow->name);
        $this->assertNull($flow->description);
        $this->assertNull($flow->steps_count);
        $this->assertNull($flow->json);
        $this->assertNull($flow->is_default_flow);
        $this->assertNull($flow->inactivity_minutes);
        $this->assertNull($flow->ending_conversation_message);
        $this->assertNull($flow->version);
        $this->assertNull($flow->status);
        $this->assertNull($flow->variables);
        $this->assertNull($flow->created_at);
        $this->assertNull($flow->updated_at);
        $this->assertNull($flow->steps);
    }

    public function test_flow_with_empty_steps()
    {
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();

        $flow = new Flow(
            1,
            1,
            'Empty Flow',
            'Flow with no steps',
            0,
            '{}',
            false,
            60,
            'Thank you!',
            '1.0',
            FlowStatus::ACTIVE,
            null,
            $createdAt,
            $updatedAt,
            []
        );

        $this->assertEquals('Empty Flow', $flow->name);
        $this->assertEquals(0, $flow->steps_count);
        $this->assertIsArray($flow->steps);
        $this->assertEmpty($flow->steps);
    }

    protected function createDomainInstance()
    {
        $createdAt = Carbon::now();
        $updatedAt = Carbon::now();

        $steps = [
            new Step(
                id: 1,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 1',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 1,
                next_step: null,
                earlier_step: null,
                is_initial_step: true,
                is_ending_step: false,
                configuration: ['text' => 'Hello'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: $createdAt,
                updated_at: $updatedAt
            ),
            new Step(
                id: 2,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 2',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 2,
                next_step: null,
                earlier_step: null,
                is_initial_step: false,
                is_ending_step: false,
                configuration: ['text' => 'World'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: $createdAt,
                updated_at: $updatedAt
            ),
        ];

        return new Flow(
            1,
            1,
            'Test Flow',
            'Test Description',
            2,
            '{"flow": "data"}',
            true,
            60,
            'Thank you for using our service!',
            '1.0',
            FlowStatus::ACTIVE,
            ['client' => ['name', 'email']],
            $createdAt,
            $updatedAt,
            $steps
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'name',
            'description',
            'steps_count',
            'json',
            'is_default_flow',
            'inactivity_minutes',
            'ending_conversation_message',
            'version',
            'status',
            'variables',
            'created_at',
            'updated_at',
            'steps'
        ];
    }

    public function test_get_timeout_minutes_returns_inactivity_minutes()
    {
        $flow = $this->createDomainInstance();
        $this->assertEquals(60, $flow->getTimeoutMinutes());
    }

    public function test_get_timeout_minutes_returns_default_when_null()
    {
        $flow = new Flow(1, 1, 'Test', 'Test', 1, '{}', false, null);
        $this->assertEquals(60, $flow->getTimeoutMinutes());
    }

    public function test_get_ending_message_returns_ending_conversation_message()
    {
        $flow = $this->createDomainInstance();
        $this->assertEquals('Thank you for using our service!', $flow->getEndingMessage());
    }

    public function test_get_ending_message_returns_null_when_not_set()
    {
        $flow = new Flow(1, 1, 'Test', 'Test', 1, '{}', false);
        $this->assertNull($flow->getEndingMessage());
    }

    public function test_validate_flow_integrity_passes_for_valid_flow()
    {
        $flow = $this->createDomainInstance();
        $errors = $flow->validateFlowIntegrity();
        $this->assertEmpty($errors);
    }

    public function test_validate_flow_integrity_fails_for_missing_name()
    {
        $flow = new Flow(1, 1, null, 'Test', 1, '{}', false);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Flow name is required', $errors);
    }

    public function test_validate_flow_integrity_fails_for_missing_organization_id()
    {
        $flow = new Flow(1, null, 'Test', 'Test', 1, '{}', false);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Organization ID is required', $errors);
    }

    public function test_validate_flow_integrity_fails_for_invalid_inactivity_minutes()
    {
        $flow = new Flow(1, 1, 'Test', 'Test', 1, '{}', false, 0);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Inactivity minutes must be at least 1', $errors);
    }

    public function test_validate_flow_integrity_fails_for_missing_version()
    {
        $flow = new Flow(1, 1, 'Test', 'Test', 1, '{}', false, 60, null, null);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Flow version is required', $errors);
    }

    public function test_validate_flow_integrity_fails_for_missing_status()
    {
        $flow = new Flow(1, 1, 'Test', 'Test', 1, '{}', false, 60, null, '1.0', null);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Flow status is required', $errors);
    }

    public function test_validate_flow_integrity_fails_for_multiple_initial_steps()
    {
        $steps = [
            new Step(
                id: 1,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 1',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 1,
                next_step: null,
                earlier_step: null,
                is_initial_step: true,
                is_ending_step: false,
                configuration: ['text' => 'Hello'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: Carbon::now(),
                updated_at: Carbon::now()
            ),
            new Step(
                id: 2,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 2',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 2,
                next_step: null,
                earlier_step: null,
                is_initial_step: true,
                is_ending_step: false,
                configuration: ['text' => 'World'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: Carbon::now(),
                updated_at: Carbon::now()
            ),
        ];

        $flow = new Flow(1, 1, 'Test', 'Test', 2, '{}', false, 60, null, '1.0', FlowStatus::ACTIVE, null, null, null, $steps);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Flow can only have one initial step', $errors);
    }

    public function test_validate_flow_integrity_fails_for_duplicate_step_positions()
    {
        $steps = [
            new Step(
                id: 1,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 1',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 1,
                next_step: null,
                earlier_step: null,
                is_initial_step: true,
                is_ending_step: false,
                configuration: ['text' => 'Hello'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: Carbon::now(),
                updated_at: Carbon::now()
            ),
            new Step(
                id: 2,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 2',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 1,
                next_step: null,
                earlier_step: null,
                is_initial_step: false,
                is_ending_step: false,
                configuration: ['text' => 'World'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: Carbon::now(),
                updated_at: Carbon::now()
            ),
        ];

        $flow = new Flow(1, 1, 'Test', 'Test', 2, '{}', false, 60, null, '1.0', FlowStatus::ACTIVE, null, null, null, $steps);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Duplicate step position: 1', $errors);
    }

    public function test_validate_flow_integrity_fails_for_missing_initial_step()
    {
        $steps = [
            new Step(
                id: 1,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 1',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 1,
                next_step: null,
                earlier_step: null,
                is_initial_step: false,
                is_ending_step: false,
                configuration: ['text' => 'Hello'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: Carbon::now(),
                updated_at: Carbon::now()
            ),
            new Step(
                id: 2,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 2',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 2,
                next_step: null,
                earlier_step: null,
                is_initial_step: false,
                is_ending_step: false,
                configuration: ['text' => 'World'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: Carbon::now(),
                updated_at: Carbon::now()
            ),
        ];

        $flow = new Flow(1, 1, 'Test', 'Test', 2, '{}', false, 60, null, '1.0', FlowStatus::ACTIVE, null, null, null, $steps);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Flow must have an initial step', $errors);
    }

    public function test_validate_flow_integrity_fails_for_steps_count_mismatch()
    {
        $steps = [
            new Step(
                id: 1,
                organization_id: 1,
                flow_id: 1,
                step: 'Step 1',
                type: 'text',
                step_type: StepType::MESSAGE,
                position: 1,
                next_step: null,
                earlier_step: null,
                is_initial_step: true,
                is_ending_step: false,
                configuration: ['text' => 'Hello'],
                navigation_rules: null,
                timeout_seconds: null,
                created_at: Carbon::now(),
                updated_at: Carbon::now()
            ),
        ];

        $flow = new Flow(1, 1, 'Test', 'Test', 3, '{}', false, 60, null, '1.0', FlowStatus::ACTIVE, null, null, null, $steps);
        $errors = $flow->validateFlowIntegrity();
        $this->assertContains('Steps count does not match actual number of steps', $errors);
    }

    public function test_validate_flow_integrity_passes_for_valid_variables()
    {
        $flow = new Flow(1, 1, 'Test', 'Test', 1, '{}', false, 60, null, '1.0', FlowStatus::ACTIVE, ['valid' => 'array']);
        $errors = $flow->validateFlowIntegrity();
        $this->assertEmpty($errors);
    }
}
