<?php

namespace Tests\Unit\Domains\ChatBot;

use App\Domains\ChatBot\Button;
use Tests\TestCase;

class ButtonCollectionValidationTest extends TestCase
{
    public function test_validate_empty_button_collection()
    {
        $result = Button::validateButtonCollection([]);
        $this->assertTrue($result);
    }

    public function test_validate_single_reply_button()
    {
        $buttons = [
            new Button(1, 1, 'Yes', 'reply', null, null, null, null)
        ];

        $result = Button::validateButtonCollection($buttons);
        $this->assertTrue($result);
    }

    public function test_validate_three_reply_buttons_allowed()
    {
        $buttons = [
            new Button(1, 1, 'Yes', 'reply', null, null, null, null),
            new Button(2, 1, 'No', 'reply', null, null, null, null),
            new Button(3, 1, 'Maybe', 'reply', null, null, null, null)
        ];

        $result = Button::validateButtonCollection($buttons);
        $this->assertTrue($result);
    }

    public function test_validate_four_reply_buttons_not_allowed()
    {
        $buttons = [
            new Button(1, 1, 'Yes', 'reply', null, null, null, null),
            new Button(2, 1, 'No', 'reply', null, null, null, null),
            new Button(3, 1, 'Maybe', 'reply', null, null, null, null),
            new Button(4, 1, 'Later', 'reply', null, null, null, null)
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Maximum of 3 reply buttons allowed per message');
        
        Button::validateButtonCollection($buttons);
    }

    public function test_validate_single_url_button()
    {
        $buttons = [
            new Button(1, 1, 'Visit Site', 'url', null, 'https://example.com', null, null)
        ];

        $result = Button::validateButtonCollection($buttons);
        $this->assertTrue($result);
    }

    public function test_validate_multiple_url_buttons_not_allowed()
    {
        $buttons = [
            new Button(1, 1, 'Site 1', 'url', null, 'https://example1.com', null, null),
            new Button(2, 1, 'Site 2', 'url', null, 'https://example2.com', null, null)
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Too many url buttons. Maximum allowed: 1');
        
        Button::validateButtonCollection($buttons);
    }

    public function test_validate_mixed_button_types()
    {
        $buttons = [
            new Button(1, 1, 'Yes', 'reply', null, null, null, null),
            new Button(2, 1, 'No', 'reply', null, null, null, null),
            new Button(3, 1, 'Visit Site', 'url', null, 'https://example.com', null, null)
        ];

        $result = Button::validateButtonCollection($buttons);
        $this->assertTrue($result);
    }

    public function test_validate_invalid_button_in_collection()
    {
        $buttons = [
            new Button(1, 1, 'Valid', 'reply', null, null, null, null),
            new Button(2, 1, 'Invalid URL', 'url', null, 'invalid-url', null, null)
        ];

        $this->expectException(\Exception::class);
        
        Button::validateButtonCollection($buttons);
    }

    public function test_validate_non_button_instance_in_collection()
    {
        $buttons = [
            new Button(1, 1, 'Valid', 'reply', null, null, null, null),
            'not a button'
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('All items must be Button instances');
        
        Button::validateButtonCollection($buttons);
    }

    public function test_validate_copy_code_button()
    {
        $buttons = [
            new Button(1, 1, 'Copy Code', 'copy_code', null, 'PROMO123', null, null)
        ];

        $result = Button::validateButtonCollection($buttons);
        $this->assertTrue($result);
    }

    public function test_validate_flow_button()
    {
        $buttons = [
            new Button(1, 1, 'Start Flow', 'flow', null, 'flow_token_123', null, null)
        ];

        $result = Button::validateButtonCollection($buttons);
        $this->assertTrue($result);
    }

    public function test_validate_multiple_copy_code_buttons_not_allowed()
    {
        $buttons = [
            new Button(1, 1, 'Code 1', 'copy_code', null, 'PROMO123', null, null),
            new Button(2, 1, 'Code 2', 'copy_code', null, 'PROMO456', null, null)
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Too many copy_code buttons. Maximum allowed: 1');
        
        Button::validateButtonCollection($buttons);
    }

    public function test_validate_multiple_flow_buttons_not_allowed()
    {
        $buttons = [
            new Button(1, 1, 'Flow 1', 'flow', null, 'token1', null, null),
            new Button(2, 1, 'Flow 2', 'flow', null, 'token2', null, null)
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Too many flow buttons. Maximum allowed: 1');
        
        Button::validateButtonCollection($buttons);
    }
}
