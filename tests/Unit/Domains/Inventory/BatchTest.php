<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Batch;
use App\Domains\Inventory\Product;
use App\Domains\Inventory\Shop;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class BatchTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Batch::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->shop_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals('BATCH-001', $domain->batch_number);
        $this->assertEquals('Test Batch', $domain->name);
        $this->assertEquals('Test batch description', $domain->description);
        $this->assertEquals(100, $domain->quantity);
        $this->assertInstanceOf(Carbon::class, $domain->produced_at);
        $this->assertInstanceOf(Carbon::class, $domain->expired_at);
        $this->assertNull($domain->processed_at);
        $this->assertFalse($domain->is_processed_at_stock);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
        $this->assertNull($domain->product);
        $this->assertNull($domain->shop);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Batch(
            id: null,
            organization_id: 1,
            shop_id: null,
            product_id: 1,
            batch_number: 'MIN-001',
            name: 'Minimal Batch',
            description: null,
            quantity: 50,
            produced_at: null,
            expired_at: null,
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->assertInstanceOf(Batch::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->shop_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals('MIN-001', $domain->batch_number);
        $this->assertEquals('Minimal Batch', $domain->name);
        $this->assertNull($domain->description);
        $this->assertEquals(50, $domain->quantity);
        $this->assertNull($domain->produced_at);
        $this->assertNull($domain->expired_at);
        $this->assertNull($domain->processed_at);
        $this->assertFalse($domain->is_processed_at_stock);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->product);
        $this->assertNull($domain->shop);
    }

    public function test_domain_instantiation_with_relationships()
    {
        $product = $this->createMockProduct();
        $shop = $this->createMockShop();

        $domain = new Batch(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            product_id: 1,
            batch_number: 'REL-001',
            name: 'Batch with Relationships',
            description: 'Batch with all relationships',
            quantity: 200,
            produced_at: Carbon::now()->subDays(30),
            expired_at: Carbon::now()->addDays(30),
            processed_at: Carbon::now(),
            is_processed_at_stock: true,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            product: $product,
            shop: $shop
        );

        $this->assertInstanceOf(Batch::class, $domain);
        $this->assertInstanceOf(Product::class, $domain->product);
        $this->assertInstanceOf(Shop::class, $domain->shop);
        $this->assertEquals('Test Product', $domain->product->name);
        $this->assertEquals('Test Shop', $domain->shop->name);
        $this->assertEquals(1, $domain->shop_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertTrue($domain->is_processed_at_stock);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals(1, $array['shop_id']);
        $this->assertEquals(1, $array['product_id']);
        $this->assertEquals('BATCH-001', $array['batch_number']);
        $this->assertEquals('Test Batch', $array['name']);
        $this->assertEquals('Test batch description', $array['description']);
        $this->assertEquals(100, $array['quantity']);
        $this->assertArrayHasKey('produced_at', $array);
        $this->assertArrayHasKey('expired_at', $array);
        $this->assertArrayHasKey('processed_at', $array);
        $this->assertFalse($array['is_processed_at_stock']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
        $this->assertNull($array['product']);
        $this->assertNull($array['shop']);
    }

    public function test_to_array_with_relationships()
    {
        $product = $this->createMockProduct();
        $shop = $this->createMockShop();

        $domain = new Batch(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            product_id: 1,
            batch_number: 'REL-001',
            name: 'Batch with Relationships',
            description: 'Batch with all relationships',
            quantity: 200,
            produced_at: Carbon::now()->subDays(30),
            expired_at: Carbon::now()->addDays(30),
            processed_at: Carbon::now(),
            is_processed_at_stock: true,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            product: $product,
            shop: $shop
        );

        $array = $domain->toArray();

        $this->assertIsArray($array['product']);
        $this->assertIsArray($array['shop']);
        $this->assertEquals('Test Product', $array['product']['name']);
        $this->assertEquals('Test Shop', $array['shop']['name']);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Batch(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            product_id: 1,
            batch_number: 'NULL-001',
            name: 'Null Dates Batch',
            description: 'Batch with null dates',
            quantity: 75,
            produced_at: null,
            expired_at: null,
            processed_at: null,
            is_processed_at_stock: false,
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertNull($array['produced_at']);
        $this->assertNull($array['expired_at']);
        $this->assertNull($array['processed_at']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'organization_id', 'shop_id', 'product_id', 'batch_number', 'name',
            'description', 'quantity', 'produced_at', 'expired_at', 'processed_at',
            'is_processed_at_stock'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals(1, $storeArray['shop_id']);
        $this->assertEquals(1, $storeArray['product_id']);
        $this->assertEquals('BATCH-001', $storeArray['batch_number']);
        $this->assertEquals('Test Batch', $storeArray['name']);
        $this->assertEquals('Test batch description', $storeArray['description']);
        $this->assertEquals(100, $storeArray['quantity']);
        $this->assertFalse($storeArray['is_processed_at_stock']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('shop_id', $updateArray);
        $this->assertArrayNotHasKey('product_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = [
            'batch_number', 'name', 'description', 'quantity', 'produced_at',
            'expired_at', 'processed_at', 'is_processed_at_stock'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals('BATCH-001', $updateArray['batch_number']);
        $this->assertEquals('Test Batch', $updateArray['name']);
        $this->assertEquals('Test batch description', $updateArray['description']);
        $this->assertEquals(100, $updateArray['quantity']);
        $this->assertFalse($updateArray['is_processed_at_stock']);
    }

    public function test_process_at_stock_method()
    {
        $domain = new Batch(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            product_id: 1,
            batch_number: 'PROC-001',
            name: 'Process Batch',
            description: 'Batch to be processed',
            quantity: 150,
            produced_at: Carbon::now()->subDays(10),
            expired_at: Carbon::now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->assertNull($domain->processed_at);
        $this->assertFalse($domain->is_processed_at_stock);

        $domain->processAtStock();

        $this->assertInstanceOf(Carbon::class, $domain->processed_at);
        $this->assertTrue($domain->is_processed_at_stock);
        $this->assertEqualsWithDelta(Carbon::now()->timestamp, $domain->processed_at->timestamp, 2);
    }

    public function test_batch_with_zero_quantity()
    {
        $domain = new Batch(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            product_id: 1,
            batch_number: 'ZERO-001',
            name: 'Zero Batch',
            description: 'Batch with zero quantity',
            quantity: 0,
            produced_at: Carbon::now(),
            expired_at: Carbon::now()->addDays(30),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->assertInstanceOf(Batch::class, $domain);
        $this->assertEquals(0, $domain->quantity);

        $array = $domain->toArray();
        $this->assertEquals(0, $array['quantity']);
    }

    public function test_batch_with_high_quantity()
    {
        $domain = new Batch(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            product_id: 1,
            batch_number: 'HIGH-001',
            name: 'High Quantity Batch',
            description: 'Batch with high quantity',
            quantity: 999999,
            produced_at: Carbon::now(),
            expired_at: Carbon::now()->addDays(30),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->assertInstanceOf(Batch::class, $domain);
        $this->assertEquals(999999, $domain->quantity);

        $array = $domain->toArray();
        $this->assertEquals(999999, $array['quantity']);
    }

    public function test_batch_expiration_dates()
    {
        $producedAt = Carbon::now()->subDays(30);
        $expiredAt = Carbon::now()->addDays(30);

        $domain = new Batch(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            product_id: 1,
            batch_number: 'EXP-001',
            name: 'Expiration Batch',
            description: 'Batch with expiration dates',
            quantity: 100,
            produced_at: $producedAt,
            expired_at: $expiredAt,
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->assertEquals($producedAt, $domain->produced_at);
        $this->assertEquals($expiredAt, $domain->expired_at);

        $array = $domain->toArray();
        $this->assertEquals($producedAt->format('d/m/Y'), $array['produced_at']);
        $this->assertEquals($expiredAt->format('d/m/Y'), $array['expired_at']);
    }

    public function test_batch_without_shop()
    {
        $domain = new Batch(
            id: 1,
            organization_id: 1,
            shop_id: null,
            product_id: 1,
            batch_number: 'NO-SHOP-001',
            name: 'No Shop Batch',
            description: 'Batch without shop',
            quantity: 75,
            produced_at: Carbon::now(),
            expired_at: Carbon::now()->addDays(30),
            processed_at: null,
            is_processed_at_stock: false
        );

        $this->assertInstanceOf(Batch::class, $domain);
        $this->assertNull($domain->shop_id);
        $this->assertNull($domain->shop);

        $array = $domain->toArray();
        $this->assertNull($array['shop_id']);
        $this->assertNull($array['shop']);
    }

    protected function createDomainInstance()
    {
        return new Batch(
            id: 1,
            organization_id: 1,
            shop_id: 1,
            product_id: 1,
            batch_number: 'BATCH-001',
            name: 'Test Batch',
            description: 'Test batch description',
            quantity: 100,
            produced_at: Carbon::now()->subDays(10),
            expired_at: Carbon::now()->addDays(20),
            processed_at: null,
            is_processed_at_stock: false,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'shop_id',
            'product_id',
            'batch_number',
            'name',
            'description',
            'quantity',
            'produced_at',
            'expired_at',
            'processed_at',
            'is_processed_at_stock',
            'created_at',
            'updated_at',
            'product',
            'shop'
        ];
    }

    private function createMockProduct(): Product
    {
        return new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '1234567890123',
            description: 'Test product description',
            price: 99.99,
            unity: 1,
            last_priced_at: Carbon::now(),
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createMockShop(): Shop
    {
        return new Shop(
            id: 1,
            organization_id: 1,
            name: 'Test Shop',
            description: 'Test shop description',
            is_active: true,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }
}
