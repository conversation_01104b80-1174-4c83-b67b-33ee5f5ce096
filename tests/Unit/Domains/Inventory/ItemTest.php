<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Item;
use App\Domains\Inventory\Sale;
use App\Domains\Inventory\Product;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class ItemTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Item::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->sale_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals(10, $domain->quantity);
        $this->assertEquals(150.50, $domain->value);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Item(
            id: null,
            organization_id: 1,
            sale_id: 1,
            product_id: 1,
            quantity: 5,
            value: 75.25
        );

        $this->assertInstanceOf(Item::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->sale_id);
        $this->assertEquals(1, $domain->product_id);
        $this->assertEquals(5, $domain->quantity);
        $this->assertEquals(75.25, $domain->value);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->sale);
        $this->assertNull($domain->product);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals(1, $array['sale_id']);
        $this->assertEquals(1, $array['product_id']);
        $this->assertEquals(10, $array['quantity']);
        $this->assertEquals(150.50, $array['value']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Item(
            id: 1,
            organization_id: 1,
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'organization_id', 'sale_id', 'product_id', 'quantity', 'value'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals(1, $storeArray['sale_id']);
        $this->assertEquals(1, $storeArray['product_id']);
        $this->assertEquals(10, $storeArray['quantity']);
        $this->assertEquals(150.50, $storeArray['value']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = [
            'sale_id', 'product_id', 'quantity', 'value'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals(1, $updateArray['sale_id']);
        $this->assertEquals(1, $updateArray['product_id']);
        $this->assertEquals(10, $updateArray['quantity']);
        $this->assertEquals(150.50, $updateArray['value']);
    }

    public function test_item_with_zero_quantity()
    {
        $domain = new Item(
            id: 1,
            organization_id: 1,
            sale_id: 1,
            product_id: 1,
            quantity: 0,
            value: 0.0
        );

        $this->assertInstanceOf(Item::class, $domain);
        $this->assertEquals(0, $domain->quantity);
        $this->assertEquals(0.0, $domain->value);

        $array = $domain->toArray();
        $this->assertEquals(0, $array['quantity']);
        $this->assertEquals(0.0, $array['value']);
    }

    public function test_item_with_high_quantity()
    {
        $domain = new Item(
            id: 1,
            organization_id: 1,
            sale_id: 1,
            product_id: 1,
            quantity: 999999,
            value: 99999999.99
        );

        $this->assertInstanceOf(Item::class, $domain);
        $this->assertEquals(999999, $domain->quantity);
        $this->assertEquals(99999999.99, $domain->value);

        $array = $domain->toArray();
        $this->assertEquals(999999, $array['quantity']);
        $this->assertEquals(99999999.99, $array['value']);
    }

    public function test_item_with_decimal_values()
    {
        $domain = new Item(
            id: 1,
            organization_id: 1,
            sale_id: 1,
            product_id: 1,
            quantity: 33,
            value: 123.456789
        );

        $this->assertInstanceOf(Item::class, $domain);
        $this->assertEquals(33, $domain->quantity);
        $this->assertEquals(123.456789, $domain->value);

        $array = $domain->toArray();
        $this->assertEquals(33, $array['quantity']);
        $this->assertEquals(123.456789, $array['value']);
    }

    public function test_to_array_with_relationships()
    {
        $sale = new Sale(
            id: 1,
            organization_id: 1,
            user_id: 1,
            shop_id: 1,
            client_id: 1,
            total_value: 500.00
        );

        $product = new Product(
            id: 1,
            organization_id: 1,
            brand_id: 1,
            name: 'Test Product',
            barcode: '123456789',
            description: 'Test product description',
            price: 15.50,
            unity: 1,
            last_priced_at: Carbon::now()
        );

        $domain = new Item(
            id: 1,
            organization_id: 1,
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            sale: $sale,
            product: $product
        );

        $array = $domain->toArray();

        $this->assertIsArray($array['sale']);
        $this->assertIsArray($array['product']);
        $this->assertEquals(500.00, $array['sale']['total_value']);
        $this->assertEquals('Test Product', $array['product']['name']);
    }

    public function test_to_array_with_null_relationships()
    {
        $domain = new Item(
            id: 1,
            organization_id: 1,
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            sale: null,
            product: null
        );

        $array = $domain->toArray();

        $this->assertNull($array['sale']);
        $this->assertNull($array['product']);
    }

    public function test_item_with_different_quantities()
    {
        $quantities = [1, 5, 10, 50, 100, 500, 1000];

        foreach ($quantities as $quantity) {
            $domain = new Item(
                id: 1,
                organization_id: 1,
                sale_id: 1,
                product_id: 1,
                quantity: $quantity,
                value: $quantity * 15.50
            );

            $this->assertEquals($quantity, $domain->quantity);
            $this->assertEquals($quantity * 15.50, $domain->value);

            $array = $domain->toArray();
            $this->assertEquals($quantity, $array['quantity']);
            $this->assertEquals($quantity * 15.50, $array['value']);
        }
    }

    public function test_item_with_different_values()
    {
        $values = [0.01, 1.50, 10.99, 100.00, 999.99, 9999.99];

        foreach ($values as $value) {
            $domain = new Item(
                id: 1,
                organization_id: 1,
                sale_id: 1,
                product_id: 1,
                quantity: 1,
                value: $value
            );

            $this->assertEquals($value, $domain->value);

            $array = $domain->toArray();
            $this->assertEquals($value, $array['value']);
        }
    }

    protected function createDomainInstance()
    {
        return new Item(
            id: 1,
            organization_id: 1,
            sale_id: 1,
            product_id: 1,
            quantity: 10,
            value: 150.50,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'sale_id',
            'product_id',
            'quantity',
            'value',
            'created_at',
            'updated_at',
            'sale',
            'product'
        ];
    }
}
