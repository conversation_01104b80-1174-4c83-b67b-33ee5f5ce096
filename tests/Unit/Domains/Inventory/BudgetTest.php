<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\Budget;
use App\Domains\Inventory\BudgetProduct;
use App\Domains\Inventory\Client;
use App\Domains\Inventory\CustomProduct;
use App\Domains\Inventory\Project;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class BudgetTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals(1, $domain->client_id);
        $this->assertEquals(5000.00, $domain->value);
        $this->assertEquals(3000.00, $domain->cost);
        $this->assertEquals('Test Budget', $domain->name);
        $this->assertEquals('Test budget description', $domain->description);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
        $this->assertNull($domain->client);
        $this->assertNull($domain->products);
        $this->assertNull($domain->customProducts);
        $this->assertNull($domain->projects);
    }

    public function test_domain_instantiation_with_minimal_data()
    {
        $domain = new Budget(
            id: null,
            organization_id: 1,
            client_id: null,
            value: null,
            cost: null,
            name: 'Minimal Budget',
            description: null
        );

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->value);
        $this->assertNull($domain->cost);
        $this->assertEquals('Minimal Budget', $domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->client);
        $this->assertNull($domain->products);
        $this->assertNull($domain->customProducts);
        $this->assertNull($domain->projects);
    }

    public function test_domain_instantiation_with_relationships()
    {
        $client = $this->createMockClient();
        $products = [$this->createMockBudgetProduct()];
        $customProducts = [$this->createMockCustomProduct()];
        $projects = [$this->createMockProject()];

        $domain = new Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            value: 7500.00,
            cost: 4500.00,
            name: 'Budget with Relationships',
            description: 'Budget with all relationships',
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            client: $client,
            products: $products,
            customProducts: $customProducts,
            projects: $projects
        );

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertInstanceOf(Client::class, $domain->client);
        $this->assertIsArray($domain->products);
        $this->assertIsArray($domain->customProducts);
        $this->assertIsArray($domain->projects);
        $this->assertEquals('Test Client', $domain->client->name);
        $this->assertCount(1, $domain->products);
        $this->assertCount(1, $domain->customProducts);
        $this->assertCount(1, $domain->projects);
        $this->assertEquals(1, $domain->client_id);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['organization_id']);
        $this->assertEquals(1, $array['client_id']);
        $this->assertEquals(5000.00, $array['value']);
        $this->assertEquals(3000.00, $array['cost']);
        $this->assertEquals('Test Budget', $array['name']);
        $this->assertEquals('Test budget description', $array['description']);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
        $this->assertNull($array['client']);
        $this->assertNull($array['products']);
        $this->assertNull($array['customProducts']);
        $this->assertNull($array['projects']);
    }

    public function test_to_array_with_relationships()
    {
        $client = $this->createMockClient();
        $products = [$this->createMockBudgetProduct()];
        $customProducts = [$this->createMockCustomProduct()];
        $projects = [$this->createMockProject()];

        $domain = new Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            value: 7500.00,
            cost: 4500.00,
            name: 'Budget with Relationships',
            description: 'Budget with all relationships',
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            client: $client,
            products: $products,
            customProducts: $customProducts,
            projects: $projects
        );

        $array = $domain->toArray();

        $this->assertIsArray($array['client']);
        $this->assertIsArray($array['products']);
        $this->assertIsArray($array['customProducts']);
        $this->assertIsArray($array['projects']);
        $this->assertEquals('Test Client', $array['client']['name']);
        $this->assertCount(1, $array['products']);
        $this->assertCount(1, $array['customProducts']);
        $this->assertCount(1, $array['projects']);
    }

    public function test_to_array_with_null_dates()
    {
        $domain = new Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            value: 5000.00,
            cost: 3000.00,
            name: 'Test Budget',
            description: 'Test budget description',
            created_at: null,
            updated_at: null
        );

        $array = $domain->toArray();

        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);

        // Should contain all other fields
        $expectedKeys = [
            'organization_id', 'client_id', 'value', 'cost', 'name', 'description'
        ];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $storeArray);
        }

        $this->assertEquals(1, $storeArray['organization_id']);
        $this->assertEquals(1, $storeArray['client_id']);
        $this->assertEquals(5000.00, $storeArray['value']);
        $this->assertEquals(3000.00, $storeArray['cost']);
        $this->assertEquals('Test Budget', $storeArray['name']);
        $this->assertEquals('Test budget description', $storeArray['description']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('organization_id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);

        // Should contain updatable fields
        $expectedKeys = ['client_id', 'value', 'cost', 'name', 'description'];

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $updateArray);
        }

        $this->assertEquals(1, $updateArray['client_id']);
        $this->assertEquals(5000.00, $updateArray['value']);
        $this->assertEquals(3000.00, $updateArray['cost']);
        $this->assertEquals('Test Budget', $updateArray['name']);
        $this->assertEquals('Test budget description', $updateArray['description']);
    }

    public function test_budget_with_zero_values()
    {
        $domain = new Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            value: 0.0,
            cost: 0.0,
            name: 'Zero Budget',
            description: 'Budget with zero values'
        );

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertEquals(0.0, $domain->value);
        $this->assertEquals(0.0, $domain->cost);

        $array = $domain->toArray();
        $this->assertEquals(0.0, $array['value']);
        $this->assertEquals(0.0, $array['cost']);
    }

    public function test_budget_with_high_values()
    {
        $domain = new Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            value: 999999.99,
            cost: 888888.88,
            name: 'High Value Budget',
            description: 'Budget with high values'
        );

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertEquals(999999.99, $domain->value);
        $this->assertEquals(888888.88, $domain->cost);

        $array = $domain->toArray();
        $this->assertEquals(999999.99, $array['value']);
        $this->assertEquals(888888.88, $array['cost']);
    }

    public function test_budget_profit_calculation()
    {
        $domain = new Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            value: 10000.00,
            cost: 6000.00,
            name: 'Profit Budget',
            description: 'Budget for profit calculation'
        );

        // Profit = Value - Cost
        $expectedProfit = 4000.00;
        $this->assertEquals($expectedProfit, $domain->value - $domain->cost);
    }

    public function test_budget_with_null_client()
    {
        $domain = new Budget(
            id: 1,
            organization_id: 1,
            client_id: null,
            value: 5000.00,
            cost: 3000.00,
            name: 'No Client Budget',
            description: 'Budget without client'
        );

        $this->assertInstanceOf(Budget::class, $domain);
        $this->assertNull($domain->client_id);
        $this->assertNull($domain->client);

        $array = $domain->toArray();
        $this->assertNull($array['client_id']);
        $this->assertNull($array['client']);
    }

    protected function createDomainInstance()
    {
        return new Budget(
            id: 1,
            organization_id: 1,
            client_id: 1,
            value: 5000.00,
            cost: 3000.00,
            name: 'Test Budget',
            description: 'Test budget description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'client_id',
            'value',
            'cost',
            'name',
            'description',
            'created_at',
            'updated_at',
            'client',
            'products',
            'customProducts',
            'projects'
        ];
    }

    private function createMockClient(): Client
    {
        return new Client(
            id: 1,
            organization_id: 1,
            name: 'Test Client',
            phone: '123456789',
            email: '<EMAIL>',
            profession: 'Test Profession',
            birthdate: '1990-01-01',
            cpf: '12345678901',
            cnpj: null,
            service: 'Test Service',
            address: 'Test Address',
            number: '123',
            neighborhood: 'Test Neighborhood',
            cep: '12345678',
            complement: null,
            civil_state: 'Single',
            description: 'Test client description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createMockBudgetProduct(): BudgetProduct
    {
        return new BudgetProduct(
            id: 1,
            budget_id: 1,
            product_id: 1,
            quantity: 10,
            value: 1000.00,
            description: 'Test budget product description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createMockCustomProduct(): CustomProduct
    {
        return new CustomProduct(
            id: 1,
            project_id: 1,
            budget_id: 1,
            quantity: 5,
            value: 150.00,
            description: 'Custom product description',
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createMockProject(): Project
    {
        return new Project(
            id: 1,
            organization_id: 1,
            client_id: 1,
            budget_id: 1,
            name: 'Test Project',
            description: 'Test project description',
            value: 2000.00,
            cost: 1200.00,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }
}
