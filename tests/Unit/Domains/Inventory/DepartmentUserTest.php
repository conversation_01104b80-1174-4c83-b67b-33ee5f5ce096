<?php

namespace Tests\Unit\Domains\Inventory;

use App\Domains\Inventory\DepartmentUser;
use App\Domains\Inventory\Department;
use App\Domains\User;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class DepartmentUserTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(DepartmentUser::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->user_id);
        $this->assertEquals(1, $domain->department_id);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
        $this->assertInstanceOf(User::class, $domain->user);
        $this->assertInstanceOf(Department::class, $domain->department);
    }

    public function test_domain_instantiation_with_null_values()
    {
        $domain = new DepartmentUser(null, null, null, null, null, null, null);

        $this->assertInstanceOf(DepartmentUser::class, $domain);
        $this->assertNull($domain->id);
        $this->assertNull($domain->user_id);
        $this->assertNull($domain->department_id);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
        $this->assertNull($domain->user);
        $this->assertNull($domain->department);
    }

    public function test_domain_instantiation_without_relationships()
    {
        $domain = new DepartmentUser(1, 2, 3, Carbon::now(), Carbon::now(), null, null);

        $this->assertInstanceOf(DepartmentUser::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(2, $domain->user_id);
        $this->assertEquals(3, $domain->department_id);
        $this->assertNull($domain->user);
        $this->assertNull($domain->department);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);

        // Test specific values
        $this->assertEquals(1, $array['id']);
        $this->assertEquals(1, $array['user_id']);
        $this->assertEquals(1, $array['department_id']);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        $this->assertIsArray($array['user']);
        $this->assertIsArray($array['department']);
    }

    public function test_to_array_method_with_null_dates()
    {
        $domain = new DepartmentUser(1, 1, 1, null, null, null, null);
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertIsString($array['created_at']);
        $this->assertIsString($array['updated_at']);
        $this->assertNull($array['user']);
        $this->assertNull($array['department']);
        // Should use Carbon::now() when dates are null
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['created_at']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $array['updated_at']);
    }

    public function test_to_array_method_with_relationships()
    {
        $user = $this->createMockUser();
        $department = $this->createMockDepartment();

        $domain = new DepartmentUser(1, 1, 1, Carbon::now(), Carbon::now(), $user, $department);
        $array = $domain->toArray();

        $this->assertIsArray($array['user']);
        $this->assertIsArray($array['department']);
        $this->assertEquals('Test User', $array['user']['first_name']);
        $this->assertEquals('Test Department', $array['department']['name']);
    }

    public function test_to_store_array_method()
    {
        $domain = $this->createDomainInstance();
        $storeArray = $domain->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);
        $this->assertArrayNotHasKey('user', $storeArray);
        $this->assertArrayNotHasKey('department', $storeArray);

        $this->assertArrayHasKey('user_id', $storeArray);
        $this->assertArrayHasKey('department_id', $storeArray);

        $this->assertEquals(1, $storeArray['user_id']);
        $this->assertEquals(1, $storeArray['department_id']);
    }

    public function test_to_update_array_method()
    {
        $domain = $this->createDomainInstance();
        $updateArray = $domain->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);
        $this->assertArrayNotHasKey('user', $updateArray);
        $this->assertArrayNotHasKey('department', $updateArray);

        $this->assertArrayHasKey('user_id', $updateArray);
        $this->assertArrayHasKey('department_id', $updateArray);

        $this->assertEquals(1, $updateArray['user_id']);
        $this->assertEquals(1, $updateArray['department_id']);
    }

    protected function createDomainInstance()
    {
        return new DepartmentUser(
            1,
            1,
            1,
            Carbon::now(),
            Carbon::now(),
            $this->createMockUser(),
            $this->createMockDepartment()
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'user_id',
            'department_id',
            'created_at',
            'updated_at',
            'user',
            'department',
        ];
    }

    private function createMockUser(): User
    {
        return new User(
            1,
            1,
            1,
            'Test User',
            'User',
            'testuser',
            '<EMAIL>',
            'password',
            null,
            null
        );
    }

    private function createMockDepartment(): Department
    {
        return new Department(
            1,
            1,
            'Test Department',
            true,
            Carbon::now(),
            Carbon::now()
        );
    }
}
