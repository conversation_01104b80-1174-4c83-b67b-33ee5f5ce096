<?php

namespace Tests\Unit\Enums;

use App\Enums\EngagementEventType;
use Tests\TestCase;

class EngagementEventTypeTest extends TestCase
{
    public function test_engagement_event_labels()
    {
        $this->assertEquals('Delivered', EngagementEventType::DELIVERED->label());
        $this->assertEquals('Read', EngagementEventType::READ->label());
        $this->assertEquals('Replied', EngagementEventType::REPLIED->label());
        $this->assertEquals('Clicked Button', EngagementEventType::CLICKED_BUTTON->label());
        $this->assertEquals('Clicked URL', EngagementEventType::CLICKED_URL->label());
        $this->assertEquals('Forwarded', EngagementEventType::FORWARDED->label());
        $this->assertEquals('Failed', EngagementEventType::FAILED->label());
    }

    public function test_engagement_event_colors()
    {
        $this->assertEquals('success', EngagementEventType::DELIVERED->color());
        $this->assertEquals('info', EngagementEventType::READ->color());
        $this->assertEquals('primary', EngagementEventType::REPLIED->color());
        $this->assertEquals('warning', EngagementEventType::CLICKED_BUTTON->color());
        $this->assertEquals('warning', EngagementEventType::CLICKED_URL->color());
        $this->assertEquals('secondary', EngagementEventType::FORWARDED->color());
        $this->assertEquals('danger', EngagementEventType::FAILED->color());
    }

    public function test_is_positive_engagement()
    {
        $this->assertTrue(EngagementEventType::DELIVERED->isPositiveEngagement());
        $this->assertTrue(EngagementEventType::READ->isPositiveEngagement());
        $this->assertTrue(EngagementEventType::REPLIED->isPositiveEngagement());
        $this->assertTrue(EngagementEventType::CLICKED_BUTTON->isPositiveEngagement());
        $this->assertTrue(EngagementEventType::CLICKED_URL->isPositiveEngagement());
        $this->assertTrue(EngagementEventType::FORWARDED->isPositiveEngagement());
        $this->assertFalse(EngagementEventType::FAILED->isPositiveEngagement());
    }

    public function test_engagement_weights()
    {
        $this->assertEquals(1, EngagementEventType::DELIVERED->getEngagementWeight());
        $this->assertEquals(2, EngagementEventType::READ->getEngagementWeight());
        $this->assertEquals(5, EngagementEventType::REPLIED->getEngagementWeight());
        $this->assertEquals(4, EngagementEventType::CLICKED_BUTTON->getEngagementWeight());
        $this->assertEquals(3, EngagementEventType::CLICKED_URL->getEngagementWeight());
        $this->assertEquals(3, EngagementEventType::FORWARDED->getEngagementWeight());
        $this->assertEquals(0, EngagementEventType::FAILED->getEngagementWeight());
    }

    public function test_engagement_weight_ordering()
    {
        // Test that replied has highest weight
        $this->assertGreaterThan(
            EngagementEventType::CLICKED_BUTTON->getEngagementWeight(),
            EngagementEventType::REPLIED->getEngagementWeight()
        );

        // Test that clicked button has higher weight than clicked URL
        $this->assertGreaterThan(
            EngagementEventType::CLICKED_URL->getEngagementWeight(),
            EngagementEventType::CLICKED_BUTTON->getEngagementWeight()
        );

        // Test that read has higher weight than delivered
        $this->assertGreaterThan(
            EngagementEventType::DELIVERED->getEngagementWeight(),
            EngagementEventType::READ->getEngagementWeight()
        );

        // Test that failed has lowest weight
        $this->assertEquals(0, EngagementEventType::FAILED->getEngagementWeight());
    }

    public function test_engagement_descriptions()
    {
        $this->assertStringContainsString('delivered', EngagementEventType::DELIVERED->description());
        $this->assertStringContainsString('read', EngagementEventType::READ->description());
        $this->assertStringContainsString('replied', EngagementEventType::REPLIED->description());
        $this->assertStringContainsString('button', EngagementEventType::CLICKED_BUTTON->description());
        $this->assertStringContainsString('URL', EngagementEventType::CLICKED_URL->description());
        $this->assertStringContainsString('forwarded', EngagementEventType::FORWARDED->description());
        $this->assertStringContainsString('failed', EngagementEventType::FAILED->description());
    }
}
