<?php

namespace Tests\Unit\Enums;

use App\Enums\CampaignStatus;
use Tests\TestCase;

class CampaignStatusTest extends TestCase
{
    public function test_campaign_status_labels()
    {
        $this->assertEquals('Draft', CampaignStatus::DRAFT->label());
        $this->assertEquals('Scheduled', CampaignStatus::SCHEDULED->label());
        $this->assertEquals('Sending', CampaignStatus::SENDING->label());
        $this->assertEquals('Completed', CampaignStatus::COMPLETED->label());
        $this->assertEquals('Failed', CampaignStatus::FAILED->label());
        $this->assertEquals('Cancelled', CampaignStatus::CANCELLED->label());
    }

    public function test_campaign_status_colors()
    {
        $this->assertEquals('secondary', CampaignStatus::DRAFT->color());
        $this->assertEquals('info', CampaignStatus::SCHEDULED->color());
        $this->assertEquals('warning', CampaignStatus::SENDING->color());
        $this->assertEquals('success', CampaignStatus::COMPLETED->color());
        $this->assertEquals('danger', CampaignStatus::FAILED->color());
        $this->assertEquals('dark', CampaignStatus::CANCELLED->color());
    }

    public function test_can_edit_permissions()
    {
        $this->assertTrue(CampaignStatus::DRAFT->canEdit());
        $this->assertTrue(CampaignStatus::SCHEDULED->canEdit());
        $this->assertFalse(CampaignStatus::SENDING->canEdit());
        $this->assertFalse(CampaignStatus::COMPLETED->canEdit());
        $this->assertFalse(CampaignStatus::FAILED->canEdit());
        $this->assertFalse(CampaignStatus::CANCELLED->canEdit());
    }

    public function test_can_cancel_permissions()
    {
        $this->assertTrue(CampaignStatus::DRAFT->canCancel());
        $this->assertTrue(CampaignStatus::SCHEDULED->canCancel());
        $this->assertFalse(CampaignStatus::SENDING->canCancel());
        $this->assertFalse(CampaignStatus::COMPLETED->canCancel());
        $this->assertFalse(CampaignStatus::FAILED->canCancel());
        $this->assertFalse(CampaignStatus::CANCELLED->canCancel());
    }

    public function test_can_launch_permissions()
    {
        $this->assertTrue(CampaignStatus::DRAFT->canLaunch());
        $this->assertTrue(CampaignStatus::SCHEDULED->canLaunch());
        $this->assertFalse(CampaignStatus::SENDING->canLaunch());
        $this->assertFalse(CampaignStatus::COMPLETED->canLaunch());
        $this->assertFalse(CampaignStatus::FAILED->canLaunch());
        $this->assertFalse(CampaignStatus::CANCELLED->canLaunch());
    }

    public function test_is_in_progress()
    {
        $this->assertFalse(CampaignStatus::DRAFT->isInProgress());
        $this->assertFalse(CampaignStatus::SCHEDULED->isInProgress());
        $this->assertTrue(CampaignStatus::SENDING->isInProgress());
        $this->assertFalse(CampaignStatus::COMPLETED->isInProgress());
        $this->assertFalse(CampaignStatus::FAILED->isInProgress());
        $this->assertFalse(CampaignStatus::CANCELLED->isInProgress());
    }

    public function test_is_finished()
    {
        $this->assertFalse(CampaignStatus::DRAFT->isFinished());
        $this->assertFalse(CampaignStatus::SCHEDULED->isFinished());
        $this->assertFalse(CampaignStatus::SENDING->isFinished());
        $this->assertTrue(CampaignStatus::COMPLETED->isFinished());
        $this->assertTrue(CampaignStatus::FAILED->isFinished());
        $this->assertTrue(CampaignStatus::CANCELLED->isFinished());
    }

    public function test_from_booleans()
    {
        // Test COMPLETED (sent=true, sending=false, has_failed=false)
        $this->assertEquals(
            CampaignStatus::COMPLETED,
            CampaignStatus::fromBooleans(true, false, false, false, false)
        );

        // Test FAILED (sent=true, sending=false, has_failed=true)
        $this->assertEquals(
            CampaignStatus::FAILED,
            CampaignStatus::fromBooleans(true, false, false, true, false)
        );

        // Test SENDING (sending=true)
        $this->assertEquals(
            CampaignStatus::SENDING,
            CampaignStatus::fromBooleans(false, true, false, false, false)
        );

        // Test SCHEDULED (scheduled=true)
        $this->assertEquals(
            CampaignStatus::SCHEDULED,
            CampaignStatus::fromBooleans(false, false, true, false, false)
        );

        // Test CANCELLED (cancelled=true)
        $this->assertEquals(
            CampaignStatus::CANCELLED,
            CampaignStatus::fromBooleans(false, false, false, false, true)
        );

        // Test DRAFT (all false)
        $this->assertEquals(
            CampaignStatus::DRAFT,
            CampaignStatus::fromBooleans(false, false, false, false, false)
        );
    }

    public function test_to_booleans()
    {
        $this->assertEquals(
            ['is_sent' => true, 'is_sending' => false, 'is_scheduled' => false],
            CampaignStatus::COMPLETED->toBooleans()
        );

        $this->assertEquals(
            ['is_sent' => false, 'is_sending' => true, 'is_scheduled' => false],
            CampaignStatus::SENDING->toBooleans()
        );

        $this->assertEquals(
            ['is_sent' => false, 'is_sending' => false, 'is_scheduled' => true],
            CampaignStatus::SCHEDULED->toBooleans()
        );

        $this->assertEquals(
            ['is_sent' => false, 'is_sending' => false, 'is_scheduled' => false],
            CampaignStatus::DRAFT->toBooleans()
        );
    }
}
