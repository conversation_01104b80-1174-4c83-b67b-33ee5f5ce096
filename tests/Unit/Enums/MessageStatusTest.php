<?php

namespace Tests\Unit\Enums;

use App\Enums\MessageStatus;
use Tests\TestCase;

class MessageStatusTest extends TestCase
{
    public function test_message_status_enum_has_all_expected_cases()
    {
        // Assert
        $this->assertEquals(1, MessageStatus::is_draft->value);
        $this->assertEquals(2, MessageStatus::is_sending->value);
        $this->assertEquals(3, MessageStatus::is_failed->value);
        $this->assertEquals(4, MessageStatus::is_sent->value);
        $this->assertEquals(5, MessageStatus::is_delivered->value);
        $this->assertEquals(6, MessageStatus::is_read->value);
    }

    public function test_message_status_enum_cases_count()
    {
        // Assert
        $cases = MessageStatus::cases();
        $this->assertCount(6, $cases);
    }

    public function test_message_status_enum_names()
    {
        // Assert
        $this->assertEquals('is_draft', MessageStatus::is_draft->name);
        $this->assertEquals('is_sending', MessageStatus::is_sending->name);
        $this->assertEquals('is_failed', MessageStatus::is_failed->name);
        $this->assertEquals('is_sent', MessageStatus::is_sent->name);
        $this->assertEquals('is_delivered', MessageStatus::is_delivered->name);
        $this->assertEquals('is_read', MessageStatus::is_read->name);
    }

    public function test_message_status_enum_try_from()
    {
        // Assert
        $this->assertEquals(MessageStatus::is_draft, MessageStatus::tryFrom(1));
        $this->assertEquals(MessageStatus::is_sending, MessageStatus::tryFrom(2));
        $this->assertEquals(MessageStatus::is_failed, MessageStatus::tryFrom(3));
        $this->assertEquals(MessageStatus::is_sent, MessageStatus::tryFrom(4));
        $this->assertEquals(MessageStatus::is_delivered, MessageStatus::tryFrom(5));
        $this->assertEquals(MessageStatus::is_read, MessageStatus::tryFrom(6));
        $this->assertNull(MessageStatus::tryFrom(999));
    }

    public function test_message_status_progression_logic()
    {
        // Test logical progression: draft -> sending -> sent -> delivered -> read
        // Or: draft -> sending -> failed
        
        // Draft is the initial state
        $this->assertEquals(1, MessageStatus::is_draft->value);
        
        // Sending comes after draft
        $this->assertEquals(2, MessageStatus::is_sending->value);
        
        // Failed can happen during sending
        $this->assertEquals(3, MessageStatus::is_failed->value);
        
        // Sent comes after sending (successful)
        $this->assertEquals(4, MessageStatus::is_sent->value);
        
        // Delivered comes after sent
        $this->assertEquals(5, MessageStatus::is_delivered->value);
        
        // Read is the final successful state
        $this->assertEquals(6, MessageStatus::is_read->value);
        
        // Verify progression order
        $this->assertLessThan(MessageStatus::is_sending->value, MessageStatus::is_draft->value);
        $this->assertLessThan(MessageStatus::is_sent->value, MessageStatus::is_sending->value);
        $this->assertLessThan(MessageStatus::is_delivered->value, MessageStatus::is_sent->value);
        $this->assertLessThan(MessageStatus::is_read->value, MessageStatus::is_delivered->value);
    }

    public function test_message_status_can_be_used_in_match_expressions()
    {
        // Test that the enum works properly in match expressions
        $getStatusDescription = function(MessageStatus $status): string {
            return match($status) {
                MessageStatus::is_draft => 'Draft',
                MessageStatus::is_sending => 'Sending',
                MessageStatus::is_failed => 'Failed',
                MessageStatus::is_sent => 'Sent',
                MessageStatus::is_delivered => 'Delivered',
                MessageStatus::is_read => 'Read',
            };
        };

        $this->assertEquals('Draft', $getStatusDescription(MessageStatus::is_draft));
        $this->assertEquals('Sending', $getStatusDescription(MessageStatus::is_sending));
        $this->assertEquals('Failed', $getStatusDescription(MessageStatus::is_failed));
        $this->assertEquals('Sent', $getStatusDescription(MessageStatus::is_sent));
        $this->assertEquals('Delivered', $getStatusDescription(MessageStatus::is_delivered));
        $this->assertEquals('Read', $getStatusDescription(MessageStatus::is_read));
    }
}
