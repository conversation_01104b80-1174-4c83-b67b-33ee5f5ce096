<?php

namespace Tests\Unit\Enums\ChatBot;

use App\Enums\ChatBot\FlowStatus;
use PHPUnit\Framework\TestCase;

class FlowStatusTest extends TestCase
{
    public function test_enum_values()
    {
        $this->assertEquals('draft', FlowStatus::DRAFT->value);
        $this->assertEquals('active', FlowStatus::ACTIVE->value);
        $this->assertEquals('archived', FlowStatus::ARCHIVED->value);
    }

    public function test_get_all()
    {
        $expected = [
            'draft',
            'active',
            'archived'
        ];

        $this->assertEquals($expected, FlowStatus::getAll());
    }

    public function test_get_description()
    {
        $this->assertEquals('Fluxo em rascunho, sendo criado/editado e não publicado', FlowStatus::DRAFT->getDescription());
        $this->assertEquals('Fluxo ativo e disponível para uso em conversas', FlowStatus::ACTIVE->getDescription());
        $this->assertEquals('Fluxo arquivado, não mais ativo mas preservado para histórico', FlowStatus::ARCHIVED->getDescription());
    }

    public function test_get_display_name()
    {
        $this->assertEquals('Rascunho', FlowStatus::DRAFT->getDisplayName());
        $this->assertEquals('Ativo', FlowStatus::ACTIVE->getDisplayName());
        $this->assertEquals('Arquivado', FlowStatus::ARCHIVED->getDisplayName());
    }

    public function test_is_valid()
    {
        $this->assertTrue(FlowStatus::isValid('draft'));
        $this->assertTrue(FlowStatus::isValid('active'));
        $this->assertTrue(FlowStatus::isValid('archived'));

        $this->assertFalse(FlowStatus::isValid('invalid'));
        $this->assertFalse(FlowStatus::isValid(''));
        $this->assertFalse(FlowStatus::isValid('DRAFT'));
    }

    public function test_from_string()
    {
        $this->assertEquals(FlowStatus::DRAFT, FlowStatus::fromString('draft'));
        $this->assertEquals(FlowStatus::ACTIVE, FlowStatus::fromString('active'));
        $this->assertEquals(FlowStatus::ARCHIVED, FlowStatus::fromString('archived'));

        $this->assertNull(FlowStatus::fromString('invalid'));
        $this->assertNull(FlowStatus::fromString(''));
        $this->assertNull(FlowStatus::fromString('DRAFT'));
    }

    public function test_can_be_used()
    {
        $this->assertTrue(FlowStatus::ACTIVE->canBeUsed());
        $this->assertFalse(FlowStatus::DRAFT->canBeUsed());
        $this->assertFalse(FlowStatus::ARCHIVED->canBeUsed());
    }

    public function test_can_be_edited()
    {
        $this->assertTrue(FlowStatus::DRAFT->canBeEdited());
        $this->assertFalse(FlowStatus::ACTIVE->canBeEdited());
        $this->assertFalse(FlowStatus::ARCHIVED->canBeEdited());
    }

    public function test_can_be_published()
    {
        $this->assertTrue(FlowStatus::DRAFT->canBePublished());
        $this->assertFalse(FlowStatus::ACTIVE->canBePublished());
        $this->assertFalse(FlowStatus::ARCHIVED->canBePublished());
    }

    public function test_can_be_archived()
    {
        $this->assertTrue(FlowStatus::ACTIVE->canBeArchived());
        $this->assertFalse(FlowStatus::DRAFT->canBeArchived());
        $this->assertFalse(FlowStatus::ARCHIVED->canBeArchived());
    }

    public function test_can_be_restored()
    {
        $this->assertTrue(FlowStatus::ARCHIVED->canBeRestored());
        $this->assertFalse(FlowStatus::DRAFT->canBeRestored());
        $this->assertFalse(FlowStatus::ACTIVE->canBeRestored());
    }

    public function test_can_be_deleted()
    {
        $this->assertTrue(FlowStatus::DRAFT->canBeDeleted());
        $this->assertTrue(FlowStatus::ARCHIVED->canBeDeleted());
        $this->assertFalse(FlowStatus::ACTIVE->canBeDeleted());
    }

    public function test_get_allowed_transitions()
    {
        $draftTransitions = FlowStatus::DRAFT->getAllowedTransitions();
        $this->assertContains(FlowStatus::ACTIVE, $draftTransitions);
        $this->assertCount(1, $draftTransitions);

        $activeTransitions = FlowStatus::ACTIVE->getAllowedTransitions();
        $this->assertContains(FlowStatus::ARCHIVED, $activeTransitions);
        $this->assertCount(1, $activeTransitions);

        $archivedTransitions = FlowStatus::ARCHIVED->getAllowedTransitions();
        $this->assertContains(FlowStatus::ACTIVE, $archivedTransitions);
        $this->assertCount(1, $archivedTransitions);
    }

    public function test_can_transition_to()
    {
        // Draft can transition to Active
        $this->assertTrue(FlowStatus::DRAFT->canTransitionTo(FlowStatus::ACTIVE));
        $this->assertFalse(FlowStatus::DRAFT->canTransitionTo(FlowStatus::ARCHIVED));
        $this->assertFalse(FlowStatus::DRAFT->canTransitionTo(FlowStatus::DRAFT));

        // Active can transition to Archived
        $this->assertTrue(FlowStatus::ACTIVE->canTransitionTo(FlowStatus::ARCHIVED));
        $this->assertFalse(FlowStatus::ACTIVE->canTransitionTo(FlowStatus::DRAFT));
        $this->assertFalse(FlowStatus::ACTIVE->canTransitionTo(FlowStatus::ACTIVE));

        // Archived can transition to Active
        $this->assertTrue(FlowStatus::ARCHIVED->canTransitionTo(FlowStatus::ACTIVE));
        $this->assertFalse(FlowStatus::ARCHIVED->canTransitionTo(FlowStatus::DRAFT));
        $this->assertFalse(FlowStatus::ARCHIVED->canTransitionTo(FlowStatus::ARCHIVED));
    }

    public function test_get_next_status()
    {
        $this->assertEquals(FlowStatus::ACTIVE, FlowStatus::DRAFT->getNextStatus());
        $this->assertEquals(FlowStatus::ARCHIVED, FlowStatus::ACTIVE->getNextStatus());
        $this->assertNull(FlowStatus::ARCHIVED->getNextStatus());
    }

    public function test_get_previous_status()
    {
        $this->assertNull(FlowStatus::DRAFT->getPreviousStatus());
        $this->assertEquals(FlowStatus::DRAFT, FlowStatus::ACTIVE->getPreviousStatus());
        $this->assertEquals(FlowStatus::ACTIVE, FlowStatus::ARCHIVED->getPreviousStatus());
    }

    public function test_get_css_class()
    {
        $this->assertEquals('status-draft', FlowStatus::DRAFT->getCssClass());
        $this->assertEquals('status-active', FlowStatus::ACTIVE->getCssClass());
        $this->assertEquals('status-archived', FlowStatus::ARCHIVED->getCssClass());
    }

    public function test_get_color()
    {
        $this->assertEquals('#6c757d', FlowStatus::DRAFT->getColor());
        $this->assertEquals('#28a745', FlowStatus::ACTIVE->getColor());
        $this->assertEquals('#ffc107', FlowStatus::ARCHIVED->getColor());
    }

    public function test_get_icon()
    {
        $this->assertEquals('edit', FlowStatus::DRAFT->getIcon());
        $this->assertEquals('play-circle', FlowStatus::ACTIVE->getIcon());
        $this->assertEquals('archive', FlowStatus::ARCHIVED->getIcon());
    }

    public function test_get_usable_statuses()
    {
        $usableStatuses = FlowStatus::getUsableStatuses();

        $this->assertContains(FlowStatus::ACTIVE, $usableStatuses);
        $this->assertNotContains(FlowStatus::DRAFT, $usableStatuses);
        $this->assertNotContains(FlowStatus::ARCHIVED, $usableStatuses);
        $this->assertCount(1, $usableStatuses);
    }

    public function test_get_editable_statuses()
    {
        $editableStatuses = FlowStatus::getEditableStatuses();

        $this->assertContains(FlowStatus::DRAFT, $editableStatuses);
        $this->assertNotContains(FlowStatus::ACTIVE, $editableStatuses);
        $this->assertNotContains(FlowStatus::ARCHIVED, $editableStatuses);
        $this->assertCount(1, $editableStatuses);
    }

    public function test_get_deletable_statuses()
    {
        $deletableStatuses = FlowStatus::getDeletableStatuses();

        $this->assertContains(FlowStatus::DRAFT, $deletableStatuses);
        $this->assertContains(FlowStatus::ARCHIVED, $deletableStatuses);
        $this->assertNotContains(FlowStatus::ACTIVE, $deletableStatuses);
        $this->assertCount(2, $deletableStatuses);
    }

    public function test_app_make_works()
    {
        // Test that we can use the enum class directly
        $this->assertInstanceOf(FlowStatus::class, FlowStatus::DRAFT);

        // Test that enum values work correctly
        $this->assertEquals('draft', FlowStatus::DRAFT->value);
        $this->assertTrue(FlowStatus::isValid('draft'));
    }

    public function test_all_cases_covered()
    {
        $allCases = FlowStatus::cases();
        $this->assertCount(3, $allCases);

        $expectedCases = [
            FlowStatus::DRAFT,
            FlowStatus::ACTIVE,
            FlowStatus::ARCHIVED
        ];

        foreach ($expectedCases as $expectedCase) {
            $this->assertContains($expectedCase, $allCases);
        }
    }

    public function test_flow_lifecycle()
    {
        // Test complete flow lifecycle
        $draft = FlowStatus::DRAFT;

        // Draft should be editable but not usable
        $this->assertTrue($draft->canBeEdited());
        $this->assertFalse($draft->canBeUsed());
        $this->assertTrue($draft->canBePublished());
        $this->assertTrue($draft->canBeDeleted());

        // Transition to active
        $active = $draft->getNextStatus();
        $this->assertEquals(FlowStatus::ACTIVE, $active);

        // Active should be usable but not editable
        $this->assertFalse($active->canBeEdited());
        $this->assertTrue($active->canBeUsed());
        $this->assertTrue($active->canBeArchived());
        $this->assertFalse($active->canBeDeleted());

        // Transition to archived
        $archived = $active->getNextStatus();
        $this->assertEquals(FlowStatus::ARCHIVED, $archived);

        // Archived should not be usable or editable but can be restored
        $this->assertFalse($archived->canBeEdited());
        $this->assertFalse($archived->canBeUsed());
        $this->assertTrue($archived->canBeRestored());
        $this->assertTrue($archived->canBeDeleted());
    }
}
