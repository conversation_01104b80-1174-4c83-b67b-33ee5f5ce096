<?php

/**
 * Test Runner for Domain and Factory Tests
 * 
 * This script helps run all domain and factory tests with SQLite memory database
 * Usage: php tests/run-domain-factory-tests.php [--filter=pattern] [--verbose]
 */

require_once __DIR__ . '/../vendor/autoload.php';

class TestRunner
{
    private array $testSuites = [
        'Unit/Domains' => [
            'UserTest',
            'OrganizationTest',
            'ChatBot/TemplateTest',
        ],
        'Unit/Factories' => [
            'UserFactoryTest',
            'OrganizationFactoryTest',
            'ChatBot/TemplateFactoryTest',
        ]
    ];

    private bool $verbose = false;
    private ?string $filter = null;

    public function __construct(array $args = [])
    {
        $this->parseArguments($args);
    }

    private function parseArguments(array $args): void
    {
        foreach ($args as $arg) {
            if (str_starts_with($arg, '--filter=')) {
                $this->filter = substr($arg, 9);
            } elseif ($arg === '--verbose' || $arg === '-v') {
                $this->verbose = true;
            }
        }
    }

    public function run(): int
    {
        $this->printHeader();
        
        $totalTests = 0;
        $passedTests = 0;
        $failedTests = 0;

        foreach ($this->testSuites as $suite => $tests) {
            $this->printSuiteHeader($suite);
            
            foreach ($tests as $test) {
                if ($this->filter && !str_contains($test, $this->filter)) {
                    continue;
                }

                $result = $this->runTest($suite, $test);
                $totalTests++;
                
                if ($result['success']) {
                    $passedTests++;
                    $this->printTestResult($test, 'PASS', $result['output']);
                } else {
                    $failedTests++;
                    $this->printTestResult($test, 'FAIL', $result['output']);
                }
            }
        }

        $this->printSummary($totalTests, $passedTests, $failedTests);
        
        return $failedTests > 0 ? 1 : 0;
    }

    private function runTest(string $suite, string $test): array
    {
        $testPath = "tests/{$suite}/{$test}.php";
        $testClass = str_replace('/', '\\', "Tests\\{$suite}\\{$test}");
        
        $command = sprintf(
            'cd %s && php vendor/bin/phpunit --testdox --colors=never %s 2>&1',
            escapeshellarg(dirname(__DIR__)),
            escapeshellarg($testPath)
        );

        $output = shell_exec($command);
        $success = strpos($output, 'FAILURES!') === false && strpos($output, 'ERRORS!') === false;

        return [
            'success' => $success,
            'output' => $output ?: 'No output'
        ];
    }

    private function printHeader(): void
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════════════════════╗\n";
        echo "║                          Domain & Factory Test Runner                       ║\n";
        echo "║                              SQLite Memory Database                          ║\n";
        echo "╚══════════════════════════════════════════════════════════════════════════════╝\n";
        echo "\n";
    }

    private function printSuiteHeader(string $suite): void
    {
        echo "┌─ Running {$suite} Tests ─────────────────────────────────────────────────────\n";
    }

    private function printTestResult(string $test, string $status, string $output): void
    {
        $statusColor = $status === 'PASS' ? "\033[32m" : "\033[31m";
        $resetColor = "\033[0m";
        
        echo sprintf("│ %-60s [%s%s%s]\n", $test, $statusColor, $status, $resetColor);
        
        if ($this->verbose || $status === 'FAIL') {
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (!empty(trim($line))) {
                    echo "│   " . $line . "\n";
                }
            }
            echo "│\n";
        }
    }

    private function printSummary(int $total, int $passed, int $failed): void
    {
        echo "└─────────────────────────────────────────────────────────────────────────────\n";
        echo "\n";
        echo "Summary:\n";
        echo "  Total Tests: {$total}\n";
        echo "  Passed: \033[32m{$passed}\033[0m\n";
        echo "  Failed: \033[31m{$failed}\033[0m\n";
        echo "\n";
        
        if ($failed === 0) {
            echo "\033[32m✓ All tests passed!\033[0m\n";
        } else {
            echo "\033[31m✗ Some tests failed. Check output above for details.\033[0m\n";
        }
        echo "\n";
    }
}

// Run the tests
$runner = new TestRunner(array_slice($argv, 1));
exit($runner->run());
