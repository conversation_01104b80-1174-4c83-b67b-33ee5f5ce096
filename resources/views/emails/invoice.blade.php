@extends('emails.layout')

@section('title', 'Fatura #' . $invoice->id . ' - ' . config('app.name'))

@section('header', 'Nova Fatura')

@section('content')
    <h2>Fatura #{{ $invoice->id }}</h2>
    
    <p><PERSON><PERSON><PERSON>, <strong>{{ $client->name }}</strong>!</p>
    
    <p>Uma nova fatura foi gerada para sua conta. Confira os detalhes abaixo:</p>

    <div style="background: #f8f9fa; padding: 20px; border-radius: 4px; margin: 20px 0;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span><strong>Número da Fatura:</strong></span>
            <span>#{{ $invoice->id }}</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span><strong>Data de Vencimento:</strong></span>
            <span>{{ $due_date->format('d/m/Y') }}</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span><strong>Valor Total:</strong></span>
            <span style="font-size: 18px; color: #667eea;"><strong>R$ {{ number_format($total_amount, 2, ',', '.') }}</strong></span>
        </div>
        <div style="display: flex; justify-content: space-between;">
            <span><strong>Organização:</strong></span>
            <span>{{ $organization->name }}</span>
        </div>
    </div>

    @if(count($items) > 0)
    <h3>Itens da Fatura</h3>
    <table class="table">
        <thead>
            <tr>
                <th>Descrição</th>
                <th>Quantidade</th>
                <th>Valor Unitário</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($items as $item)
            <tr>
                <td>{{ $item['description'] }}</td>
                <td>{{ $item['quantity'] }}</td>
                <td>R$ {{ number_format($item['unit_price'], 2, ',', '.') }}</td>
                <td>R$ {{ number_format($item['total'], 2, ',', '.') }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <div class="alert">
        <p><strong>⏰ Vencimento em {{ $due_date->diffInDays(now()) }} dias</strong></p>
        <p>Para evitar juros e multas, realize o pagamento até a data de vencimento.</p>
    </div>

    <div class="text-center" style="margin: 30px 0;">
        <a href="{{ $payment_link }}" class="button">
            Pagar Agora
        </a>
    </div>

    <div style="margin-top: 30px;">
        <h3>Formas de Pagamento</h3>
        <ul>
            <li>💳 Cartão de Crédito ou Débito</li>
            <li>🏦 Transferência Bancária</li>
            <li>📱 PIX (Instantâneo)</li>
            <li>📄 Boleto Bancário</li>
        </ul>
    </div>

    <p>Se você tiver alguma dúvida sobre esta fatura ou precisar de esclarecimentos, entre em contato conosco. Estamos aqui para ajudar!</p>
@endsection

@section('footer')
    <p>Esta fatura foi gerada automaticamente pelo sistema {{ config('app.name') }}.</p>
    <p>Se você está tendo problemas para acessar o link de pagamento, copie e cole a URL abaixo em seu navegador:</p>
    <p style="word-break: break-all; color: #667eea;">{{ $payment_link }}</p>
@endsection
