<?php

namespace Database\Factories;

use App\Models\Budget;
use App\Models\CustomProduct;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomProduct>
 */
class CustomProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'budget_id' => Budget::factory(),
            'project_id' => Project::factory(),
            'quantity' => fake()->numberBetween(1, 100),
            'value' => fake()->randomFloat(2, 10, 1000),
            'description' => fake()->sentence(),
        ];
    }

    /**
     * Indicate that the custom product has high quantity.
     */
    public function highQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(100, 1000),
                'value' => fake()->randomFloat(2, 1000, 10000),
                'description' => 'High quantity custom product - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the custom product has low quantity.
     */
    public function lowQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(1, 5),
                'value' => fake()->randomFloat(2, 10, 100),
                'description' => 'Low quantity custom product - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the custom product is expensive.
     */
    public function expensive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 1000, 10000),
                'description' => 'Premium custom product - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the custom product is for a specific project.
     */
    public function forProject($projectId = null): static
    {
        return $this->state(function (array $attributes) use ($projectId) {
            return [
                'project_id' => $projectId ?? Project::factory(),
            ];
        });
    }

    /**
     * Indicate that the custom product is for a specific budget.
     */
    public function forBudget($budgetId = null): static
    {
        return $this->state(function (array $attributes) use ($budgetId) {
            return [
                'budget_id' => $budgetId ?? Budget::factory(),
            ];
        });
    }
}
