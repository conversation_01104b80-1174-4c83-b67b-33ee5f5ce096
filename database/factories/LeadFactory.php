<?php

namespace Database\Factories;

use App\Models\Lead;
use App\Models\Organization;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Lead>
 */
class LeadFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Lead::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'client_id' => Client::factory(),
            'source' => $this->faker->randomElement(['website', 'social_media', 'referral', 'chatbot_whatsapp', 'manual']),
            'status' => $this->faker->randomElement(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high', 'urgent']),
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(),
            'notes' => $this->faker->optional()->paragraph(),
            'estimated_value' => $this->faker->optional()->randomFloat(2, 100, 50000),
            'service_type' => $this->faker->optional()->randomElement(['consulting', 'development', 'design', 'marketing', 'support']),
            'budget_range' => $this->faker->optional()->randomElement(['1000-5000', '5000-10000', '10000-25000', '25000-50000', '50000+']),
            'timeline' => $this->faker->optional()->randomElement(['immediate', '1-2 weeks', '1 month', '2-3 months', '3-6 months', '6+ months']),
            'company' => $this->faker->optional()->company(),
            'custom_fields' => [
                'lead_score' => $this->faker->numberBetween(1, 100),
                'interest_level' => $this->faker->randomElement(['low', 'medium', 'high']),
            ],
            'created_via' => $this->faker->randomElement(['manual', 'system', 'chatbot', 'import']),
            'contacted_at' => $this->faker->optional()->dateTimeBetween('-30 days', 'now'),
            'qualified_at' => $this->faker->optional()->dateTimeBetween('-20 days', 'now'),
            'closed_at' => $this->faker->optional()->dateTimeBetween('-10 days', 'now'),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'deleted_at' => null,
        ];
    }

    /**
     * Indicate that the lead is new.
     */
    public function newLead(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'new',
                'contacted_at' => null,
                'qualified_at' => null,
                'closed_at' => null,
            ];
        });
    }

    /**
     * Indicate that the lead is qualified.
     */
    public function qualified(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'qualified',
                'contacted_at' => $this->faker->dateTimeBetween('-15 days', '-10 days'),
                'qualified_at' => $this->faker->dateTimeBetween('-10 days', '-5 days'),
                'closed_at' => null,
            ];
        });
    }

    /**
     * Indicate that the lead is closed won.
     */
    public function closedWon(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'closed_won',
                'contacted_at' => $this->faker->dateTimeBetween('-30 days', '-20 days'),
                'qualified_at' => $this->faker->dateTimeBetween('-20 days', '-15 days'),
                'closed_at' => $this->faker->dateTimeBetween('-10 days', 'now'),
                'estimated_value' => $this->faker->randomFloat(2, 1000, 25000),
            ];
        });
    }

    /**
     * Indicate that the lead came from chatbot.
     */
    public function fromChatbot(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'source' => 'chatbot_whatsapp',
                'created_via' => 'chatbot',
                'custom_fields' => [
                    'chatbot_conversation_data' => [
                        'service_interest' => 'web development',
                        'budget' => '5000-10000',
                        'timeline' => '2-4 months',
                    ],
                    'created_via_chatbot' => true,
                ],
            ];
        });
    }
}
