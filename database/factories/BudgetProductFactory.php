<?php

namespace Database\Factories;

use App\Models\Budget;
use App\Models\BudgetProduct;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BudgetProduct>
 */
class BudgetProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = BudgetProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'budget_id' => Budget::factory(),
            'product_id' => Product::factory(),
            'quantity' => fake()->numberBetween(1, 100),
            'value' => fake()->randomFloat(2, 10, 1000),
            'description' => fake()->sentence(),
        ];
    }

    /**
     * Indicate that the budget product has high quantity.
     */
    public function highQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(100, 1000),
                'value' => fake()->randomFloat(2, 100, 5000),
            ];
        });
    }

    /**
     * Indicate that the budget product has low quantity.
     */
    public function lowQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(1, 10),
                'value' => fake()->randomFloat(2, 5, 100),
            ];
        });
    }

    /**
     * Indicate that the budget product is expensive.
     */
    public function expensive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 1000, 10000),
                'description' => 'Premium ' . fake()->sentence(),
            ];
        });
    }
}
