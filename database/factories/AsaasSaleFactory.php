<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Services\ASAAS\Models\AsaasSale;
use App\Models\Sale;
use App\Models\Client;
use App\Models\Organization;
use App\Enums\PaymentStatus;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Services\ASAAS\Models\AsaasSale>
 */
class AsaasSaleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AsaasSale::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $value = $this->faker->randomFloat(2, 10, 5000);
        $discountValue = $this->faker->optional(0.3)->randomFloat(2, 0, $value * 0.2);
        $interestValue = $this->faker->optional(0.2)->randomFloat(2, 0, $value * 0.1);
        $netValue = $value - ($discountValue ?? 0) + ($interestValue ?? 0);
        $status = $this->faker->randomElement([
            'PENDING', 'PENDING_CONFIRMATION', 'CONFIRMED', 'RECEIVED',
            'OVERDUE', 'REFUNDED', 'RECEIVED_IN_CASH'
        ]);
        $billingType = $this->faker->randomElement(['BOLETO', 'CREDIT_CARD', 'PIX']);

        return [
            'sale_id' => Sale::factory(),
            'organization_id' => Organization::factory(),
            'client_id' => Client::factory(),
            'asaas_payment_id' => 'pay_' . $this->faker->uuid(),
            'asaas_customer_id' => 'cus_' . $this->faker->uuid(),
            'value' => $value,
            'net_value' => $netValue,
            'original_value' => $value,
            'interest_value' => $interestValue,
            'discount_value' => $discountValue,
            'description' => $this->faker->sentence(),
            'billing_type' => $billingType,
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'payment_date' => $status === 'RECEIVED' ? $this->faker->dateTimeBetween('-30 days', 'now') : null,
            'original_due_date' => null,
            'client_payment_date' => null,
            'status' => $status,
            'invoice_url' => $this->faker->optional(0.7)->url(),
            'bank_slip_url' => $this->faker->optional(0.5)->url(),
            'pix_qr_code_id' => $this->faker->optional(0.3)->regexify('[A-Z0-9]{32}'),
            'external_reference' => 'sale_' . $this->faker->numberBetween(1000, 9999),
            'installment_count' => $this->faker->optional(0.3)->numberBetween(2, 12),
            'installment_value' => null,
            'installment_number' => null,
            'asaas_synced_at' => $this->faker->optional(0.8)->dateTimeBetween('-7 days', 'now'),
            'asaas_sync_errors' => null,
            'asaas_webhook_data' => null,
        ];
    }

    /**
     * Indicate that the payment is paid
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'RECEIVED',
            'payment_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'asaas_synced_at' => now(),
        ]);
    }

    /**
     * Indicate that the payment is pending
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'PENDING',
            'payment_date' => null,
            'due_date' => $this->faker->dateTimeBetween('+1 day', '+30 days'),
        ]);
    }

    /**
     * Indicate that the payment is overdue
     */
    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'OVERDUE',
            'payment_date' => null,
            'due_date' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
        ]);
    }

    /**
     * Indicate that the payment is confirmed
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => PaymentStatus::CONFIRMED,
            'payment_date' => $this->faker->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Indicate that the payment is refunded
     */
    public function refunded(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => PaymentStatus::REFUNDED,
            'payment_date' => $this->faker->dateTimeBetween('-30 days', '-7 days'),
        ]);
    }

    /**
     * Indicate that the payment is via boleto
     */
    public function boleto(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'BOLETO',
            'bank_slip_url' => $this->faker->url(),
            'due_date' => $this->faker->dateTimeBetween('+1 day', '+7 days'),
        ]);
    }

    /**
     * Indicate that the payment is via credit card
     */
    public function creditCard(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'CREDIT_CARD',
            'bank_slip_url' => null,
            'pix_qr_code' => null,
        ]);
    }

    /**
     * Indicate that the payment is via PIX
     */
    public function pix(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'PIX',
            'pix_qr_code' => $this->faker->regexify('[A-Z0-9]{32}'),
            'bank_slip_url' => null,
            'due_date' => $this->faker->dateTimeBetween('now', '+1 day'),
        ]);
    }

    /**
     * Indicate that the payment is installment
     */
    public function installment(int $count = null, int $number = null): static
    {
        $installmentCount = $count ?? $this->faker->numberBetween(2, 12);
        $installmentNumber = $number ?? $this->faker->numberBetween(1, $installmentCount);

        return $this->state(fn (array $attributes) => [
            'installment_count' => $installmentCount,
            'installment_number' => $installmentNumber,
            'installment_value' => $attributes['original_value'] / $installmentCount,
            'asaas_installment_id' => 'ins_' . $this->faker->uuid(),
        ]);
    }

    /**
     * Indicate that the payment has sync errors
     */
    public function withSyncErrors(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_sync_errors' => [
                'error' => $this->faker->randomElement([
                    'Payment not found in ASAAS',
                    'Invalid payment status',
                    'API timeout',
                    'Authentication failed',
                    'Rate limit exceeded'
                ]),
                'timestamp' => now()->toISOString(),
                'attempt_count' => $this->faker->numberBetween(1, 5),
                'last_response' => [
                    'status' => $this->faker->randomElement([404, 422, 429, 500, 503]),
                    'message' => $this->faker->sentence(),
                ]
            ],
            'asaas_synced_at' => null,
        ]);
    }

    /**
     * Indicate that the payment needs sync
     */
    public function needsSync(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_synced_at' => $this->faker->dateTimeBetween('-30 days', '-2 hours'),
        ]);
    }

    /**
     * Indicate that the payment was recently synced
     */
    public function recentlySynced(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_synced_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'asaas_sync_errors' => null,
        ]);
    }

    /**
     * Indicate that the payment has webhook data
     */
    public function withWebhookData(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_webhook_data' => [
                'event' => $this->faker->randomElement([
                    'PAYMENT_CREATED',
                    'PAYMENT_UPDATED',
                    'PAYMENT_CONFIRMED',
                    'PAYMENT_RECEIVED',
                    'PAYMENT_OVERDUE',
                    'PAYMENT_REFUNDED'
                ]),
                'payment' => [
                    'id' => $attributes['asaas_payment_id'],
                    'status' => $attributes['payment_status']->value,
                    'value' => $attributes['original_value'],
                    'netValue' => $attributes['net_value'],
                    'paymentDate' => $attributes['payment_date']?->format('Y-m-d'),
                ],
                'timestamp' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Indicate that the payment belongs to a specific sale
     */
    public function forSale(Sale $sale): static
    {
        return $this->state(fn (array $attributes) => [
            'sale_id' => $sale->id,
            'organization_id' => $sale->organization_id,
            'client_id' => $sale->client_id,
            'original_value' => $sale->total_value,
            'net_value' => $sale->total_value,
        ]);
    }

    /**
     * Indicate that the payment belongs to a specific organization
     */
    public function forOrganization(Organization $organization): static
    {
        return $this->state(fn (array $attributes) => [
            'organization_id' => $organization->id,
        ]);
    }

    /**
     * Create with specific value
     */
    public function withValue(float $value): static
    {
        return $this->state(fn (array $attributes) => [
            'original_value' => $value,
            'net_value' => $value,
            'installment_value' => isset($attributes['installment_count'])
                ? $value / $attributes['installment_count']
                : null,
        ]);
    }

    /**
     * Create with discount
     */
    public function withDiscount(float $discount): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_value' => $discount,
            'net_value' => $attributes['original_value'] - $discount + ($attributes['interest_value'] ?? 0),
        ]);
    }

    /**
     * Create with interest
     */
    public function withInterest(float $interest): static
    {
        return $this->state(fn (array $attributes) => [
            'interest_value' => $interest,
            'net_value' => $attributes['original_value'] - ($attributes['discount_value'] ?? 0) + $interest,
        ]);
    }
}
