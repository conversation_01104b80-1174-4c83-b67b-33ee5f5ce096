<?php

namespace Database\Factories;

use App\Models\ExchangedMessage;
use App\Models\Organization;
use App\Models\Client;
use App\Models\PhoneNumber;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\WhatsAppWebhookLog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ExchangedMessage>
 */
class ExchangedMessageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ExchangedMessage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isInbound = $this->faker->boolean();
        
        return [
            'organization_id' => Organization::factory(),
            'client_id' => Client::factory(),
            'phone_number_id' => null,
            'conversation_id' => null,
            'webhook_log_id' => null,
            'message_id' => null,
            'inbound' => $isInbound,
            'outbound' => !$isInbound,
            'message' => $this->faker->sentence(),
            'json' => [
                'type' => $isInbound ? 'received' : 'sent',
                'timestamp' => now()->timestamp,
                'data' => $this->faker->words(3, true)
            ],
            'sent_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Indicate that the message is inbound.
     */
    public function inbound(): static
    {
        return $this->state(fn (array $attributes) => [
            'inbound' => true,
            'outbound' => false,
        ]);
    }

    /**
     * Indicate that the message is outbound.
     */
    public function outbound(): static
    {
        return $this->state(fn (array $attributes) => [
            'inbound' => false,
            'outbound' => true,
        ]);
    }

    /**
     * Indicate that the message has a phone number.
     */
    public function withPhoneNumber(): static
    {
        return $this->state(fn (array $attributes) => [
            'phone_number_id' => PhoneNumber::factory(),
        ]);
    }

    /**
     * Indicate that the message has a conversation.
     */
    public function withConversation(): static
    {
        return $this->state(fn (array $attributes) => [
            'conversation_id' => Conversation::factory(),
        ]);
    }

    /**
     * Indicate that the message has a webhook log.
     */
    public function withWebhookLog(): static
    {
        return $this->state(fn (array $attributes) => [
            'webhook_log_id' => WhatsAppWebhookLog::factory(),
        ]);
    }

    /**
     * Indicate that the message has a message.
     */
    public function withMessage(): static
    {
        return $this->state(fn (array $attributes) => [
            'message_id' => Message::factory(),
        ]);
    }
}
