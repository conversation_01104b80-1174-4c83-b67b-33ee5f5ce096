<?php

namespace Database\Factories;

use App\Models\Budget;
use App\Models\Client;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Budget>
 */
class BudgetFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Budget::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'client_id' => Client::factory(),
            'value' => fake()->randomFloat(2, 100, 10000),
            'cost' => fake()->randomFloat(2, 50, 5000),
            'name' => fake()->words(3, true),
            'description' => fake()->paragraph(),
        ];
    }

    /**
     * Indicate that the budget is high value.
     */
    public function highValue(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 10000, 100000),
                'cost' => fake()->randomFloat(2, 5000, 50000),
                'name' => 'High Value ' . fake()->words(2, true),
            ];
        });
    }

    /**
     * Indicate that the budget is low value.
     */
    public function lowValue(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 10, 500),
                'cost' => fake()->randomFloat(2, 5, 250),
                'name' => 'Low Value ' . fake()->words(2, true),
            ];
        });
    }

    /**
     * Indicate that the budget is approved.
     */
    public function approved(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Approved ' . fake()->words(2, true),
                'description' => 'This budget has been approved for execution.',
            ];
        });
    }

    /**
     * Indicate that the budget is pending.
     */
    public function pending(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Pending ' . fake()->words(2, true),
                'description' => 'This budget is pending approval.',
            ];
        });
    }
}
