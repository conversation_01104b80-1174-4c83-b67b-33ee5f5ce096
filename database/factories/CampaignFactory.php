<?php

namespace Database\Factories;

use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\Template;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Campaign>
 */
class CampaignFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'user_id' => User::factory(),
            'template_id' => Template::factory(),
            'phone_number_id' => PhoneNumber::factory(),
            'name' => fake()->words(3, true),
            'description' => fake()->sentence(),
            'is_scheduled' => false,
            'is_sent' => false,
            'is_sending' => false,
            'is_direct_message' => false,
            'message_count' => 0,
            'sent_at' => null,
            'scheduled_at' => null,
        ];
    }

    /**
     * Indicate that the campaign is scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_scheduled' => true,
            'scheduled_at' => fake()->dateTimeBetween('now', '+1 month'),
        ]);
    }

    /**
     * Indicate that the campaign is sent.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_sent' => true,
            'sent_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'message_count' => fake()->numberBetween(1, 100),
        ]);
    }

    /**
     * Indicate that the campaign is currently sending.
     */
    public function sending(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_sending' => true,
            'sent_at' => fake()->dateTimeBetween('-1 hour', 'now'),
            'message_count' => fake()->numberBetween(1, 100),
        ]);
    }
}
