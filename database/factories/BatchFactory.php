<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Batch>
 */
class BatchFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $producedAt = fake()->dateTimeBetween('-6 months', '-1 month');
        $expiredAt = fake()->dateTimeBetween('+1 month', '+2 years');
        
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'shop_id' => \App\Models\Shop::factory(),
            'product_id' => \App\Models\Product::factory(),
            'batch_number' => fake()->unique()->bothify('BATCH-####-???'),
            'name' => fake()->words(2, true),
            'description' => fake()->sentence(),
            'quantity' => fake()->numberBetween(10, 1000),
            'produced_at' => $producedAt,
            'expired_at' => $expiredAt,
            'processed_at' => fake()->optional()->dateTimeBetween($producedAt, 'now'),
            'is_processed_at_stock' => fake()->boolean(),
        ];
    }

    /**
     * Create an expired batch.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expired_at' => fake()->dateTimeBetween('-1 year', '-1 day'),
        ]);
    }

    /**
     * Create a batch expiring soon.
     */
    public function expiringSoon(): static
    {
        return $this->state(fn (array $attributes) => [
            'expired_at' => fake()->dateTimeBetween('now', '+1 month'),
        ]);
    }

    /**
     * Create a processed batch.
     */
    public function processed(): static
    {
        return $this->state(fn (array $attributes) => [
            'processed_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'is_processed_at_stock' => true,
        ]);
    }

    /**
     * Create an unprocessed batch.
     */
    public function unprocessed(): static
    {
        return $this->state(fn (array $attributes) => [
            'processed_at' => null,
            'is_processed_at_stock' => false,
        ]);
    }

    /**
     * Create a large batch.
     */
    public function large(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity' => fake()->numberBetween(1000, 10000),
        ]);
    }

    /**
     * Create a small batch.
     */
    public function small(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity' => fake()->numberBetween(1, 50),
        ]);
    }
}
