<?php

namespace Database\Factories;

use App\Models\CampaignAnalytics;
use App\Models\Campaign;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CampaignAnalytics>
 */
class CampaignAnalyticsFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CampaignAnalytics::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $totalMessages = $this->faker->numberBetween(50, 1000);
        $sentCount = $this->faker->numberBetween(0, $totalMessages);
        $deliveredCount = $this->faker->numberBetween(0, $sentCount);
        $failedCount = $totalMessages - $sentCount;
        $readCount = $this->faker->numberBetween(0, $deliveredCount);
        $responseCount = $this->faker->numberBetween(0, $readCount);

        return [
            'campaign_id' => Campaign::factory(),
            'total_messages' => $totalMessages,
            'sent_count' => $sentCount,
            'delivered_count' => $deliveredCount,
            'failed_count' => $failedCount,
            'read_count' => $readCount,
            'response_count' => $responseCount,
            'avg_delivery_time' => $this->faker->randomFloat(2, 1, 300),
            'delivery_rate' => $sentCount > 0 ? round($deliveredCount / $sentCount, 4) : 0,
            'read_rate' => $deliveredCount > 0 ? round($readCount / $deliveredCount, 4) : 0,
            'response_rate' => $readCount > 0 ? round($responseCount / $readCount, 4) : 0,
            'failure_rate' => $totalMessages > 0 ? round($failedCount / $totalMessages, 4) : 0,
            'calculated_at' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Indicate that the analytics are for a successful campaign.
     */
    public function successful(): static
    {
        return $this->state(function (array $attributes) {
            $totalMessages = $attributes['total_messages'];
            $sentCount = $totalMessages;
            $deliveredCount = $this->faker->numberBetween(
                (int)($totalMessages * 0.9), 
                $totalMessages
            );
            $readCount = $this->faker->numberBetween(
                (int)($deliveredCount * 0.7), 
                $deliveredCount
            );
            $responseCount = $this->faker->numberBetween(
                (int)($readCount * 0.1), 
                (int)($readCount * 0.3)
            );

            return [
                'sent_count' => $sentCount,
                'delivered_count' => $deliveredCount,
                'failed_count' => 0,
                'read_count' => $readCount,
                'response_count' => $responseCount,
                'delivery_rate' => round($deliveredCount / $sentCount, 4),
                'read_rate' => round($readCount / $deliveredCount, 4),
                'response_rate' => round($responseCount / $readCount, 4),
                'failure_rate' => 0,
            ];
        });
    }

    /**
     * Indicate that the analytics are for a failed campaign.
     */
    public function failed(): static
    {
        return $this->state(function (array $attributes) {
            $totalMessages = $attributes['total_messages'];
            $sentCount = $this->faker->numberBetween(0, (int)($totalMessages * 0.5));
            $deliveredCount = $this->faker->numberBetween(0, $sentCount);
            $failedCount = $totalMessages - $sentCount;

            return [
                'sent_count' => $sentCount,
                'delivered_count' => $deliveredCount,
                'failed_count' => $failedCount,
                'read_count' => $this->faker->numberBetween(0, $deliveredCount),
                'response_count' => 0,
                'delivery_rate' => $sentCount > 0 ? round($deliveredCount / $sentCount, 4) : 0,
                'read_rate' => $deliveredCount > 0 ? round($this->faker->numberBetween(0, $deliveredCount) / $deliveredCount, 4) : 0,
                'response_rate' => 0,
                'failure_rate' => round($failedCount / $totalMessages, 4),
            ];
        });
    }
}
