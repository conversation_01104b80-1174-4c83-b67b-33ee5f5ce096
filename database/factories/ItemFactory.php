<?php

namespace Database\Factories;

use App\Models\Item;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Sale;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Item>
 */
class ItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Item::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'sale_id' => Sale::factory(),
            'product_id' => Product::factory(),
            'quantity' => fake()->numberBetween(1, 100),
            'value' => fake()->randomFloat(2, 10, 1000),
        ];
    }

    /**
     * Indicate that the item has high quantity.
     */
    public function highQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(100, 1000),
                'value' => fake()->randomFloat(2, 1000, 10000),
            ];
        });
    }

    /**
     * Indicate that the item has low quantity.
     */
    public function lowQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(1, 5),
                'value' => fake()->randomFloat(2, 5, 50),
            ];
        });
    }

    /**
     * Indicate that the item is expensive.
     */
    public function expensive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 1000, 10000),
                'quantity' => fake()->numberBetween(1, 10),
            ];
        });
    }

    /**
     * Indicate that the item is cheap.
     */
    public function cheap(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 1, 50),
                'quantity' => fake()->numberBetween(1, 20),
            ];
        });
    }
}
