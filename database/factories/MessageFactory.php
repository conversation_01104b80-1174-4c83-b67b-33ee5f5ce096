<?php

namespace Database\Factories;

use App\Models\Campaign;
use App\Models\Client;
use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\Template;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Message>
 */
class MessageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'campaign_id' => Campaign::factory(),
            'template_id' => Template::factory(),
            'client_id' => Client::factory(),
            'message' => json_encode([
                'type' => 'template',
                'template' => [
                    'name' => fake()->slug(2),
                    'language' => ['code' => 'en_US'],
                    'components' => []
                ]
            ]),
            'status' => fake()->randomElement([1, 2, 3, 4]), // MessageStatus enum values
            'is_sent' => false,
            'is_fail' => false,
            'is_read' => false,
            'is_direct_message' => false,
            'sent_at' => null,
            'scheduled_at' => null,
        ];
    }

    /**
     * Indicate that the message is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 1, // MessageStatus::is_draft
            'is_sent' => false,
            'sent_at' => null,
        ]);
    }

    /**
     * Indicate that the message is ready to send.
     */
    public function readyToSend(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 2, // MessageStatus::is_sending
            'is_sent' => false,
            'sent_at' => null,
        ]);
    }

    /**
     * Indicate that the message is sent.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 4, // MessageStatus::is_sent
            'is_sent' => true,
            'sent_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'external_id' => fake()->uuid(),
        ]);
    }

    /**
     * Indicate that the message failed to send.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 3, // MessageStatus::is_failed
            'is_sent' => false,
            'is_fail' => true,
            'sent_at' => null,
        ]);
    }

    /**
     * Indicate that the message is scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 2, // MessageStatus::is_sending
            'is_sent' => false,
            'scheduled_at' => fake()->dateTimeBetween('now', '+1 month'),
        ]);
    }
}
