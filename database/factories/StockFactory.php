<?php

namespace Database\Factories;

use App\Models\Brand;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Shop;
use App\Models\Stock;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Stock>
 */
class StockFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Stock::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'shop_id' => Shop::factory(),
            'brand_id' => Brand::factory(),
            'product_id' => Product::factory(),
            'quantity' => fake()->numberBetween(1, 1000),
            'value' => fake()->randomFloat(2, 10, 10000),
            'description' => fake()->sentence(),
        ];
    }

    /**
     * Indicate that the stock has high quantity.
     */
    public function highQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(1000, 10000),
                'value' => fake()->randomFloat(2, 10000, 100000),
                'description' => 'High quantity stock - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the stock has low quantity.
     */
    public function lowQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(1, 10),
                'value' => fake()->randomFloat(2, 10, 100),
                'description' => 'Low quantity stock - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the stock is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => 0,
                'value' => 0,
                'description' => 'Out of stock - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the stock is expensive.
     */
    public function expensive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 10000, 100000),
                'description' => 'Premium stock - ' . fake()->sentence(),
            ];
        });
    }
}
