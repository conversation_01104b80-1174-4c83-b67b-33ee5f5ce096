<?php

namespace Database\Factories;

use App\Models\CampaignCategoryAssignment;
use App\Models\Campaign;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CampaignCategoryAssignment>
 */
class CampaignCategoryAssignmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CampaignCategoryAssignment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'campaign_id' => Campaign::factory(),
            'category_id' => Category::factory(),
            'assigned_at' => Carbon::now(),
        ];
    }
}
