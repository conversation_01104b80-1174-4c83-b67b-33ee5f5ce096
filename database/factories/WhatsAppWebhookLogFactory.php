<?php

namespace Database\Factories;

use App\Models\WhatsAppWebhookLog;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WhatsAppWebhookLog>
 */
class WhatsAppWebhookLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WhatsAppWebhookLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $eventTypes = ['message', 'status', 'other', 'verification', 'security'];
        $processingStatuses = ['pending', 'success', 'failed'];
        
        return [
            'organization_id' => Organization::factory(),
            'phone_number_id' => $this->faker->numerify('##########'),
            'event_type' => $this->faker->randomElement($eventTypes),
            'webhook_payload' => [
                'object' => 'whatsapp_business_account',
                'entry' => [
                    [
                        'id' => $this->faker->numerify('##########'),
                        'changes' => [
                            [
                                'value' => [
                                    'messaging_product' => 'whatsapp',
                                    'metadata' => [
                                        'display_phone_number' => $this->faker->phoneNumber(),
                                        'phone_number_id' => $this->faker->numerify('##########')
                                    ]
                                ],
                                'field' => 'messages'
                            ]
                        ]
                    ]
                ]
            ],
            'processed_at' => $this->faker->boolean(70) ? $this->faker->dateTimeBetween('-1 hour', 'now') : null,
            'processing_status' => $this->faker->randomElement($processingStatuses),
            'error_message' => $this->faker->boolean(20) ? $this->faker->sentence() : null,
        ];
    }

    /**
     * Indicate that the log is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'processing_status' => 'pending',
            'processed_at' => null,
            'error_message' => null,
        ]);
    }

    /**
     * Indicate that the log was processed successfully.
     */
    public function successful(): static
    {
        return $this->state(fn (array $attributes) => [
            'processing_status' => 'success',
            'processed_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'error_message' => null,
        ]);
    }

    /**
     * Indicate that the log failed processing.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'processing_status' => 'failed',
            'processed_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'error_message' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the log is for a message event.
     */
    public function messageEvent(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'message',
        ]);
    }

    /**
     * Indicate that the log is for a status event.
     */
    public function statusEvent(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'status',
        ]);
    }
}
