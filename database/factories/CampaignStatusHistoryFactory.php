<?php

namespace Database\Factories;

use App\Models\CampaignStatusHistory;
use App\Models\Campaign;
use App\Models\User;
use App\Enums\CampaignStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CampaignStatusHistory>
 */
class CampaignStatusHistoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CampaignStatusHistory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'campaign_id' => Campaign::factory(),
            'old_status' => $this->faker->randomElement(CampaignStatus::cases()),
            'new_status' => $this->faker->randomElement(CampaignStatus::cases()),
            'reason' => $this->faker->sentence(),
            'user_id' => User::factory(),
            'metadata' => [
                'changed_by' => 'system',
                'timestamp' => Carbon::now()->toISOString(),
            ],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Indicate that the status change is from draft to sending.
     */
    public function draftToSending(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'old_status' => CampaignStatus::DRAFT,
                'new_status' => CampaignStatus::SENDING,
                'reason' => 'Campaign activated',
            ];
        });
    }

    /**
     * Indicate that the status change is from sending to completed.
     */
    public function sendingToCompleted(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'old_status' => CampaignStatus::SENDING,
                'new_status' => CampaignStatus::COMPLETED,
                'reason' => 'Campaign completed successfully',
            ];
        });
    }
}
