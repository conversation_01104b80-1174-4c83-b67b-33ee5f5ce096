<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'brand_id' => \App\Models\Brand::factory(),
            'name' => fake()->words(2, true),
            'barcode' => fake()->ean13(),
            'description' => fake()->sentence(),
            'price' => fake()->randomFloat(2, 10, 1000),
            'unity' => fake()->numberBetween(1, 100),
            'last_priced_at' => fake()->optional()->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Create an expensive product.
     */
    public function expensive(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => fake()->randomFloat(2, 1000, 10000),
        ]);
    }

    /**
     * Create a cheap product.
     */
    public function cheap(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => fake()->randomFloat(2, 1, 50),
        ]);
    }

    /**
     * Create a product without a brand.
     */
    public function withoutBrand(): static
    {
        return $this->state(fn (array $attributes) => [
            'brand_id' => null,
        ]);
    }

    /**
     * Create a product with specific unity.
     */
    public function withUnity(string $unity): static
    {
        return $this->state(fn (array $attributes) => [
            'unity' => $unity,
        ]);
    }

    /**
     * Create a recently priced product.
     */
    public function recentlyPriced(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_priced_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }
}
