<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->integer("organization_id")->nullable();
            $table->integer("campaign_id")->nullable();
            $table->integer("template_id")->nullable();
            $table->integer("client_id")->nullable();

            $table->text("message")->nullable();

            $table->integer("status")->nullable()->default(1);

            $table->boolean("is_sent")->default(false);
            $table->boolean("is_fail")->default(false);
            $table->boolean("is_read")->default(false);

            $table->dateTime("sent_at")->nullable()->default(null);
            $table->dateTime("scheduled_at")->nullable()->default(null);

            $table->timestamps();
            $table->softDeletes();

            $table->index('organization_id');
            $table->index('campaign_id');
            $table->index('template_id');
            $table->index('client_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
