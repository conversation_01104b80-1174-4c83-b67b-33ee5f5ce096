<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            // ASAAS Payment Integration
            $table->string('asaas_payment_id')->nullable()->after('id');
            $table->string('asaas_installment_id')->nullable()->after('asaas_payment_id');
            $table->enum('payment_status', [
                'pending',
                'pending_confirmation', 
                'confirmed',
                'received',
                'overdue',
                'refunded',
                'received_in_cash',
                'refund_requested',
                'refund_in_progress',
                'chargeback_requested',
                'chargeback_dispute',
                'awaiting_chargeback_reversal',
                'dunning_requested',
                'dunning_received',
                'awaiting_risk_analysis'
            ])->default('pending')->after('asaas_installment_id');
            
            // Payment Details
            $table->string('billing_type')->nullable()->after('payment_status'); // BOLETO, CREDIT_CARD, PIX, UNDEFINED
            $table->date('due_date')->nullable()->after('billing_type');
            $table->timestamp('payment_date')->nullable()->after('due_date');
            $table->decimal('net_value', 10, 2)->nullable()->after('payment_date');
            $table->decimal('original_value', 10, 2)->nullable()->after('net_value');
            $table->decimal('interest_value', 10, 2)->nullable()->after('original_value');
            $table->decimal('discount_value', 10, 2)->nullable()->after('interest_value');
            
            // Payment URLs and References
            $table->text('invoice_url')->nullable()->after('discount_value');
            $table->text('bank_slip_url')->nullable()->after('invoice_url');
            $table->text('pix_qr_code')->nullable()->after('bank_slip_url');
            $table->string('external_reference')->nullable()->after('pix_qr_code');
            
            // Installment Information
            $table->integer('installment_count')->nullable()->after('external_reference');
            $table->decimal('installment_value', 10, 2)->nullable()->after('installment_count');
            $table->integer('installment_number')->nullable()->after('installment_value');
            
            // Sync Information
            $table->timestamp('asaas_synced_at')->nullable()->after('installment_number');
            $table->json('asaas_sync_errors')->nullable()->after('asaas_synced_at');
            $table->json('asaas_webhook_data')->nullable()->after('asaas_sync_errors');
            
            // Indexes for better performance
            $table->index('asaas_payment_id');
            $table->index('asaas_installment_id');
            $table->index('payment_status');
            $table->index('billing_type');
            $table->index('due_date');
            $table->index('payment_date');
            $table->index('asaas_synced_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex(['asaas_payment_id']);
            $table->dropIndex(['asaas_installment_id']);
            $table->dropIndex(['payment_status']);
            $table->dropIndex(['billing_type']);
            $table->dropIndex(['due_date']);
            $table->dropIndex(['payment_date']);
            $table->dropIndex(['asaas_synced_at']);
            
            // Drop columns
            $table->dropColumn([
                'asaas_payment_id',
                'asaas_installment_id',
                'payment_status',
                'billing_type',
                'due_date',
                'payment_date',
                'net_value',
                'original_value',
                'interest_value',
                'discount_value',
                'invoice_url',
                'bank_slip_url',
                'pix_qr_code',
                'external_reference',
                'installment_count',
                'installment_value',
                'installment_number',
                'asaas_synced_at',
                'asaas_sync_errors',
                'asaas_webhook_data',
            ]);
        });
    }
};
