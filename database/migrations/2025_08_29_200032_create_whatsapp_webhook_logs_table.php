<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_webhook_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('organization_id')->nullable();
            $table->string('phone_number_id')->nullable();
            $table->enum('event_type', ['message', 'status', 'other'])->default('other');
            $table->json('webhook_payload');
            $table->timestamp('processed_at')->nullable();
            $table->enum('processing_status', ['pending', 'success', 'failed'])->default('pending');
            $table->text('error_message')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index('organization_id');
            $table->index('phone_number_id');
            $table->index('event_type');
            $table->index('processing_status');
            $table->index('processed_at');
            $table->index('created_at');

            // Foreign key constraint
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_webhook_logs');
    }
};
