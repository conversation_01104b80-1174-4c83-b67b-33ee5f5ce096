<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->id();
            $table->integer("organization_id");
            $table->integer("user_id");
            $table->integer("template_id");

            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->boolean("is_scheduled")->nullable()->default(false);
            $table->boolean("is_sent")->nullable()->default(false);
            $table->boolean("is_sending")->nullable()->default(false);
            $table->integer("message_count")->nullable()->default(0);

            $table->dateTime("sent_at")->nullable()->default(null);
            $table->dateTime("scheduled_at")->nullable()->default(null);

            $table->timestamps();
            $table->softDeletes();

            $table->index('organization_id');
            $table->index('user_id');
            $table->index('template_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaigns');
    }
};
