<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_engagement_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('message_id')->constrained()->onDelete('cascade');
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->enum('event_type', ['delivered', 'read', 'replied', 'clicked_button', 'clicked_url', 'forwarded', 'failed']);
            $table->json('metadata_json')->nullable(); // Event-specific data
            $table->string('user_phone')->nullable(); // User who triggered the event
            $table->timestamp('event_timestamp');
            $table->timestamps();

            // Indexes
            $table->index(['message_id', 'event_type']);
            $table->index(['campaign_id', 'event_type']);
            $table->index(['event_type', 'event_timestamp']);
            $table->index(['campaign_id', 'event_timestamp']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_engagement_events');
    }
};
