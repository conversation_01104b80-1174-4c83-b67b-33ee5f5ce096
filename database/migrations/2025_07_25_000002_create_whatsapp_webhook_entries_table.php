<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_webhook_entries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('whatsapp_message_id')->constrained('whatsapp_messages');

            // WhatsApp webhook status fields
            $table->string('external_wam_id'); // The WhatsApp message ID from webhook
            $table->string('status'); // delivered, read, sent, failed, etc.
            $table->string('timestamp'); // WhatsApp timestamp
            $table->string('recipient_id')->nullable(); // Phone number that received the message
            $table->string('conversation_id')->nullable(); // WhatsApp conversation ID
            $table->string('conversation_origin_type')->nullable(); // user_initiated, business_initiated, etc.
            $table->text('json'); // Full webhook entry JSON

            $table->timestamps();
            $table->softDeletes();

            $table->engine = 'InnoDB';

            // INDEXES
            $table->index('whatsapp_message_id');
            $table->index('external_wam_id');
            $table->index('status');
            $table->index('recipient_id');
            $table->index('conversation_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_webhook_entries');
    }
};
