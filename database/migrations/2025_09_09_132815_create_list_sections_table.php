<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('list_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('interactive_message_id')->constrained()->onDelete('cascade');

            // Section title (optional)
            $table->string('title')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['interactive_message_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('list_sections');
    }
};
