<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('flows', function (Blueprint $table) {
            $table->integer('inactivity_minutes')->default(60)->after('is_default_flow');
            $table->text('ending_conversation_message')->nullable()->after('inactivity_minutes');
            $table->string('version')->default('1.0')->after('ending_conversation_message');
            $table->string('status')->default('draft')->after('version');
            $table->json('variables')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('flows', function (Blueprint $table) {
            $table->dropColumn([
                'inactivity_minutes',
                'ending_conversation_message', 
                'version',
                'status',
                'variables'
            ]);
        });
    }
};
