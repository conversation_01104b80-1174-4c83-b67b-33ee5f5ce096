<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('asaas_organizations', function (Blueprint $table) {
            // Add missing fields from ASAAS API response
            $table->string('login_email')->nullable()->after('email')->index();
            $table->date('birth_date')->nullable()->after('cpf_cnpj');
            $table->string('person_type', 20)->nullable()->after('birth_date');
            $table->integer('city')->nullable()->after('postal_code')->index();
            $table->string('state', 2)->nullable()->after('city');
            $table->string('country', 50)->nullable()->after('state');
            $table->string('trading_name')->nullable()->after('country');
            $table->json('account_number')->nullable()->after('site');
            $table->json('commercial_info_expiration')->nullable()->after('account_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('asaas_organizations', function (Blueprint $table) {
            $table->dropColumn([
                'login_email',
                'birth_date',
                'person_type',
                'city',
                'state',
                'country',
                'trading_name',
                'account_number',
                'commercial_info_expiration',
            ]);
        });
    }
};
