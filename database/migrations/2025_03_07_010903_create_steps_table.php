<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('steps', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable()->default(null);
            $table->bigInteger('flow_id');
            $table->string('step');
            $table->string('type');
            $table->integer('position')->nullable()->default(null);

            $table->integer('next_step')->nullable()->default(null);
            $table->integer('earlier_step')->nullable()->default(null);
            $table->boolean("is_initial_step")->default(false);
            $table->boolean("is_ending_step")->default(false);
            $table->boolean("is_message")->default(false);
            $table->boolean("is_interactive")->default(false);
            $table->boolean("is_command")->default(false);
            $table->text("json")->nullable()->default(null);
            $table->timestamps();
            $table->softDeletes();

            $table->index('organization_id');
            $table->index('flow_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('steps');
    }
};
