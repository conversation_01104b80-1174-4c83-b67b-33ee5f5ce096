<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('batches', function (Blueprint $table) {
            $table->id();
            $table->integer('organization_id')->nullable();
            $table->string('batch_number')->nullable();
            $table->string('name')->nullable();
            $table->longText('description')->nullable();
            $table->dateTime("produced_at")->nullable();
            $table->dateTime("expired_at")->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index('organization_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('batches');
    }
};
