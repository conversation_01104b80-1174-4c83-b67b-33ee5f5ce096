<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_delivery_attempts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('message_id')->constrained()->onDelete('cascade');
            $table->integer('attempt_number');
            $table->tinyInteger('status'); // Using MessageStatus enum
            $table->text('error_message')->nullable();
            $table->json('whatsapp_response_json')->nullable();
            $table->timestamp('attempted_at');
            $table->timestamps();

            // Indexes
            $table->index(['message_id', 'attempt_number']);
            $table->index(['message_id', 'attempted_at']);
            $table->index(['status', 'attempted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_delivery_attempts');
    }
};
