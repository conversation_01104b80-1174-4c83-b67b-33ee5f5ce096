<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            if (!Schema::hasColumn('organizations', 'email')) {
                $table->string('email')->nullable()->after('default_flow_id');
            }
            if (!Schema::hasColumn('organizations', 'cpf_cnpj')) {
                $table->string('cpf_cnpj')->nullable()->after('email');
            }
            if (!Schema::hasColumn('organizations', 'phone')) {
                $table->string('phone')->nullable()->after('cpf_cnpj');
            }
            if (!Schema::hasColumn('organizations', 'mobile_phone')) {
                $table->string('mobile_phone')->nullable()->after('phone');
            }
            if (!Schema::hasColumn('organizations', 'address')) {
                $table->string('address')->nullable()->after('mobile_phone');
            }
            if (!Schema::hasColumn('organizations', 'address_number')) {
                $table->string('address_number')->nullable()->after('address');
            }
            if (!Schema::hasColumn('organizations', 'complement')) {
                $table->string('complement')->nullable()->after('address_number');
            }
            if (!Schema::hasColumn('organizations', 'province')) {
                $table->string('province')->nullable()->after('complement');
            }
            if (!Schema::hasColumn('organizations', 'postal_code')) {
                $table->string('postal_code')->nullable()->after('province');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropColumn([
                'email',
                'cpf_cnpj',
                'phone',
                'mobile_phone',
                'address',
                'address_number',
                'complement',
                'province',
                'postal_code'
            ]);
        });
    }
};
