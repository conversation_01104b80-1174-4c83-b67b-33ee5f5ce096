<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asaas_sales', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('sale_id')->constrained()->onDelete('cascade');
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->string('asaas_payment_id')->unique();
            $table->string('asaas_customer_id');
            
            // Payment Details
            $table->decimal('value', 10, 2);
            $table->decimal('net_value', 10, 2)->nullable();
            $table->decimal('original_value', 10, 2)->nullable();
            $table->decimal('interest_value', 10, 2)->nullable();
            $table->decimal('discount_value', 10, 2)->nullable();
            $table->text('description')->nullable();
            
            // Payment Configuration
            $table->enum('billing_type', ['BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED']);
            $table->date('due_date');
            $table->date('payment_date')->nullable();
            $table->date('original_due_date')->nullable();
            $table->date('client_payment_date')->nullable();
            
            // Status Control
            $table->enum('status', [
                'PENDING', 'PENDING_CONFIRMATION', 'CONFIRMED', 'RECEIVED', 
                'OVERDUE', 'REFUNDED', 'RECEIVED_IN_CASH', 'REFUND_REQUESTED',
                'REFUND_IN_PROGRESS', 'CHARGEBACK_REQUESTED', 'CHARGEBACK_DISPUTE',
                'AWAITING_CHARGEBACK_REVERSAL', 'DUNNING_REQUESTED', 'DUNNING_RECEIVED',
                'AWAITING_RISK_ANALYSIS'
            ])->default('PENDING');
            
            // URLs and References
            $table->text('invoice_url')->nullable();
            $table->string('invoice_number', 50)->nullable();
            $table->text('bank_slip_url')->nullable();
            $table->string('pix_qr_code_id')->nullable();
            $table->string('external_reference')->nullable();
            
            // Installment Information
            $table->string('installment_id')->nullable();
            $table->integer('installment_count')->nullable();
            $table->decimal('installment_value', 10, 2)->nullable();
            $table->integer('installment_number')->nullable();
            
            // Credit Information
            $table->date('credit_date')->nullable();
            $table->date('estimated_credit_date')->nullable();
            $table->boolean('anticipated')->default(false);
            $table->boolean('anticipable')->default(false);
            
            // Additional Data
            $table->boolean('can_be_paid_after_due_date')->default(true);
            $table->boolean('deleted')->default(false);
            $table->string('nosso_numero', 50)->nullable();
            
            // Sync Control
            $table->timestamp('asaas_synced_at')->nullable();
            $table->json('asaas_sync_errors')->nullable();
            $table->enum('sync_status', ['pending', 'synced', 'error'])->default('pending');
            $table->json('asaas_webhook_data')->nullable();
            
            // Financial Details (JSON)
            $table->json('discount_config')->nullable();
            $table->json('fine_config')->nullable();
            $table->json('interest_config')->nullable();
            $table->json('split_config')->nullable();
            $table->json('credit_card_data')->nullable();
            $table->json('chargeback_data')->nullable();
            $table->json('escrow_data')->nullable();
            $table->json('refunds_data')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for better performance
            $table->index('asaas_payment_id');
            $table->index('sale_id');
            $table->index('client_id');
            $table->index('organization_id');
            $table->index('status');
            $table->index('billing_type');
            $table->index('due_date');
            $table->index('payment_date');
            $table->index('sync_status');
            $table->index('asaas_synced_at');
            $table->index('external_reference');
            $table->index('asaas_customer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asaas_sales');
    }
};
