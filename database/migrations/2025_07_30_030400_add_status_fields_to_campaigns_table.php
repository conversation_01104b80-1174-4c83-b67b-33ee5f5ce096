<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\CampaignStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            // Add new status enum field (default to DRAFT)
            $table->tinyInteger('status')->default(CampaignStatus::DRAFT->value)->after('is_direct_message');
            
            // Add timestamp fields for status tracking
            $table->timestamp('cancelled_at')->nullable()->after('scheduled_at');
            $table->timestamp('failed_at')->nullable()->after('cancelled_at');
            
            // Add index for status queries
            $table->index(['organization_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->dropIndex(['organization_id', 'status']);
            $table->dropColumn(['status', 'cancelled_at', 'failed_at']);
        });
    }
};
