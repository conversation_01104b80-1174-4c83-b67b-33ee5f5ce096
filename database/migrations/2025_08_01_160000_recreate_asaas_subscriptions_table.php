<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing table
        Schema::dropIfExists('asaas_subscriptions');
        
        // Create the new table with correct structure
        Schema::create('asaas_subscriptions', function (Blueprint $table) {
            $table->id();
            
            // Relacionamentos
            $table->foreignId('subscription_id')->unique()->constrained()->onDelete('cascade');
            $table->string('asaas_customer_id');
            
            // Dados do ASAAS
            $table->string('asaas_subscription_id')->unique();
            $table->date('asaas_date_created');
            $table->timestamp('asaas_synced_at')->nullable();
            $table->json('asaas_sync_errors')->nullable();
            $table->enum('sync_status', ['pending', 'synced', 'error'])->default('pending');
            
            // Dados da Assinatura ASAAS
            $table->enum('billing_type', ['BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED']);
            $table->enum('cycle', ['MONTHLY', 'QUARTERLY', 'SEMIANNUAL', 'YEARLY']);
            $table->decimal('value', 10, 2);
            $table->date('next_due_date')->nullable();
            $table->date('end_date')->nullable();
            $table->text('description')->nullable();
            $table->enum('status', ['ACTIVE', 'INACTIVE', 'EXPIRED', 'CANCELLED']);
            $table->integer('max_payments')->nullable();
            $table->string('external_reference')->nullable();
            $table->string('payment_link', 500)->nullable();
            $table->string('checkout_session')->nullable();
            
            // Desconto
            $table->decimal('discount_value', 8, 2)->nullable();
            $table->enum('discount_type', ['FIXED', 'PERCENTAGE'])->nullable();
            $table->integer('discount_due_date_limit_days')->nullable()->default(0);
            
            // Multa e Juros
            $table->decimal('fine_value', 8, 2)->nullable();
            $table->enum('fine_type', ['FIXED', 'PERCENTAGE'])->nullable()->default('FIXED');
            $table->decimal('interest_value', 8, 2)->nullable();
            
            // Cartão de Crédito
            $table->string('credit_card_number', 20)->nullable();
            $table->string('credit_card_brand', 50)->nullable();
            $table->string('credit_card_token')->nullable();
            
            // Split (JSON para múltiplos splits)
            $table->json('split_data')->nullable();
            
            // Controle
            $table->boolean('deleted')->default(false);
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for better performance
            $table->index('subscription_id');
            $table->index('asaas_customer_id');
            $table->index('asaas_subscription_id');
            $table->index('status');
            $table->index('sync_status');
            $table->index('next_due_date');
            $table->index('external_reference');
            $table->index('asaas_synced_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asaas_subscriptions');
    }
};
