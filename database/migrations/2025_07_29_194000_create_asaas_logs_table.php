<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asaas_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('organization_id')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('method', 10); // GET, POST, PUT, DELETE
            $table->string('endpoint', 500);
            $table->json('request_data')->nullable();
            $table->json('response_data')->nullable();
            $table->integer('response_status')->default(0);
            $table->decimal('execution_time', 8, 3)->default(0); // seconds with 3 decimal places
            $table->boolean('is_error')->default(false);
            $table->text('error_message')->nullable();
            $table->string('asaas_error_code', 100)->nullable();
            $table->string('environment', 20)->default('sandbox'); // sandbox or production
            $table->timestamps();

            // Indexes for better query performance
            $table->index('organization_id');
            $table->index('user_id');
            $table->index('method');
            $table->index('is_error');
            $table->index('environment');
            $table->index('response_status');
            $table->index('created_at');
            $table->index(['organization_id', 'is_error']);
            $table->index(['environment', 'is_error']);
            $table->index(['created_at', 'is_error']);

            // Foreign key constraints
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asaas_logs');
    }
};
