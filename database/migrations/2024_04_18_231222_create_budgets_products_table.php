<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('budgets_products', function (Blueprint $table) {
            $table->id();
            $table->integer('budget_id')->nullable();
            $table->integer('product_id')->nullable();
            $table->integer('quantity')->nullable();
            $table->decimal('value')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();

            $table->engine = 'InnoDB';
            // INDEXES
            $table->index('budget_id');
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('budgets_products');
    }
};
