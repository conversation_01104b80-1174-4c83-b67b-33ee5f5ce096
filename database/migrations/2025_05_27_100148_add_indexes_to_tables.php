<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to buttons_bodies table
        Schema::table('buttons_bodies', function (Blueprint $table) {
            $table->index('button_id');
            $table->index('body_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove indexes from buttons_bodies table
        Schema::table('buttons_bodies', function (Blueprint $table) {
            $table->dropIndex('buttons_bodies_button_id_index');
            $table->dropIndex('buttons_bodies_body_id_index');
        });
    }
};
