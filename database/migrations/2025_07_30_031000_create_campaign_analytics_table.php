<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_analytics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->integer('total_messages')->default(0);
            $table->integer('sent_count')->default(0);
            $table->integer('delivered_count')->default(0);
            $table->integer('failed_count')->default(0);
            $table->integer('read_count')->default(0);
            $table->integer('response_count')->default(0);
            $table->decimal('avg_delivery_time', 8, 2)->nullable(); // in minutes
            $table->decimal('delivery_rate', 5, 2)->default(0); // percentage
            $table->decimal('read_rate', 5, 2)->default(0); // percentage
            $table->decimal('response_rate', 5, 2)->default(0); // percentage
            $table->decimal('failure_rate', 5, 2)->default(0); // percentage
            $table->timestamp('calculated_at');
            $table->timestamps();

            // Indexes
            $table->unique('campaign_id');
            $table->index(['calculated_at']);
            $table->index(['delivery_rate']);
            $table->index(['read_rate']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_analytics');
    }
};
