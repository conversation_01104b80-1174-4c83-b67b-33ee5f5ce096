<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_exits', function (Blueprint $table) {
            $table->id();
            $table->integer('organization_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('brand_id')->nullable();
            $table->integer('product_id')->nullable();
            $table->integer('client_id')->nullable();
            $table->integer('project_id')->nullable();

            $table->integer('quantity')->nullable();
            $table->decimal('value')->nullable();
            $table->longText('description')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->engine = 'InnoDB';

            // INDEXES
            $table->index('organization_id');
            $table->index('user_id');
            $table->index('brand_id');
            $table->index('product_id');
            $table->index('client_id');
            $table->index('project_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_exits');
    }
};
