<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('groups_products', function (Blueprint $table) {
            $table->id();
            $table->integer('group_id')->nullable();
            $table->integer('product_id')->nullable();
            $table->timestamps();
            $table->engine = 'InnoDB';

            // INDEXES
            $table->index('group_id');
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('groups_products');
    }
};
