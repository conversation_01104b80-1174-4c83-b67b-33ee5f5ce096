<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            // Add missing ASAAS fields after total_value
            $table->text('description')->nullable()->after('total_value');
            $table->json('discount_config')->nullable()->after('description');
            $table->json('fine_config')->nullable()->after('discount_config');
            $table->json('interest_config')->nullable()->after('fine_config');
            $table->json('split_config')->nullable()->after('interest_config');
            $table->json('credit_card_data')->nullable()->after('split_config');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropColumn([
                'description',
                'discount_config',
                'fine_config',
                'interest_config',
                'split_config',
                'credit_card_data',
            ]);
        });
    }
};
