<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interactions', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable()->default(null);
            $table->integer('user_id')->nullable()->default(null);
            $table->integer('client_id')->nullable()->default(null);
            $table->integer('flow_id')->nullable()->default(null);
            $table->integer('step_id')->nullable()->default(null);
            $table->string('message')->nullable()->default(null);
            $table->string('answer')->nullable()->default(null);
            $table->string('result')->nullable()->default(null);
            $table->text('json')->nullable()->default(null);
            $table->timestamps();
            $table->softDeletes();

            $table->index('organization_id');
            $table->index('user_id');
            $table->index('client_id');
            $table->index('flow_id');
            $table->index('step_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interactions');
    }
};
