<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('components', function (Blueprint $table) {
            $table->text('text')->nullable()->default(null)->change();
            $table->text('type')->nullable()->default(null)->change();
            $table->text('name')->nullable()->default(null)->change();
            $table->text('json')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('components', function (Blueprint $table) {
            $table->text('text')->nullable(false)->change();
            $table->text('type')->nullable(false)->change();
            $table->text('name')->nullable(false)->change();
            $table->text('json')->nullable(false)->change();
        });
    }
};
