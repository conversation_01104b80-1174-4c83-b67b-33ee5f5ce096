<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_status_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->tinyInteger('old_status')->nullable(); // Can be null for initial status
            $table->tinyInteger('new_status');
            $table->string('reason')->nullable(); // Reason for status change
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Who made the change
            $table->json('metadata')->nullable(); // Additional data about the change
            $table->timestamps();

            // Indexes
            $table->index(['campaign_id', 'created_at']);
            $table->index(['campaign_id', 'new_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_status_history');
    }
};
