<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('steps', function (Blueprint $table) {
            // Remove old boolean fields
            $table->dropColumn([
                'is_message',
                'is_interactive',
                'is_command',
                'is_input'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('steps', function (Blueprint $table) {
            // Restore old boolean fields
            $table->boolean('is_message')->default(false)->after('is_ending_step');
            $table->boolean('is_interactive')->default(false)->after('is_message');
            $table->boolean('is_command')->default(false)->after('is_interactive');
            $table->boolean('is_input')->default(false)->after('is_command');
        });
    }
};
