<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\Organization;
use App\Models\Profile;
use Illuminate\Database\Seeder;
use App\Models\User;

class UserSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $user = User::where('email', '<EMAIL>')->first();
        if(!$user){
            User::create([
                'organization_id' => Organization::where('name', 'Zona51')->first()->id,
                'first_name' => 'Fellipe',
                'last_name' => '<PERSON><PERSON>',
                'profile_id' => Profile::where('slug', 'super_admin')->first()->id,
                'email' => '<EMAIL>',
                'username' => 'fellipemanzano',
                'password' => 'password123',
            ]);
        }
    }
}
